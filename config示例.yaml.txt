############################################## - ⚓️ 锚点区开始 - ##############################################
# 锚点 - 远程订阅组
PProviders: &PProviders { type: http, interval: 10800, health-check: { enable: true, max-size: 30, url: 'https://connectivitycheck.platform.hicloud.com/generate_204', interval: 3000 }, filter: '^(?!.*(群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Channel|Author))' }
# 锚点 - 筛选组 
FilterHK: &FilterHK '(?i)香港|🇭🇰|HK'
FilterTW: &FilterTW '(?i)台湾|🇹🇼|TW'
FilterSG: &FilterSG '(?i)新加坡|🇸🇬|SG'
FilterJP: &FilterJP '(?i)日本|🇯🇵|JP'
FilterKR: &FilterKR '(?i)韩|韩国|🇰🇷|KR'
FilterUS: &FilterUS '(?i)美国|🇺🇸|US'
FilterEU: &FilterEU '(?i)(🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|🇬🇧|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU|UK|FR|PO|DE)'
FilterOther: &FilterOther '^(?!.*(DIRECT|新|日|韩|新加坡|日本|韩国|美国|🇭🇰|🇼🇸|🇹🇼|🇸🇬|🇯🇵|🇰🇷|🇺🇸|🇬🇧|🇦🇹|🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|英国|法国|波兰|德国|FR|UK|HK|TW|SG|JP|KR|US|GB|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU|HKG|TPE|TSA|KHH|SIN|XSP|NRT|HND|KIX|CTS|FUK|JFK|LAX|ORD|ATL|DFW|SFO|MIA|SEA|IAD|LHR|LGW|DE|PO))'
FilterAll: &FilterAll '^(?!.*(群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author))'
FilterGPT: &FilterGPT '(?i)(?!.*(🇭🇰|港|澳|🇲🇴|🇯🇵|🇰🇷|🇸🇬))'

# 锚点 - 策略组
UrlTest: &UrlTest { type: url-test, interval: 300, tolerance: 50, url: 'https://connectivitycheck.platform.hicloud.com/generate_204', disable-udp: false, timeout: 2000, max-failed-times: 3, include-all: true }
FallBack: &FallBack { type: fallback, interval: 120, url: 'https://connectivitycheck.platform.hicloud.com/generate_204', disable-udp: false, timeout: 2000, max-failed-times: 3, hidden: false }

# 锚点 - 选择组 
SelectJP: &SelectJP { type: select, proxies: [ 日本, GPT, 欧盟, FallBack, 新加坡, 香港, 台湾, 韩国, 美国, Other, Select ] }
SelectSG: &SelectSG { type: select, proxies: [ 新加坡, FallBack, 日本, 韩国, 美国, 欧盟, Other, Select ] }
SelectUS: &SelectUS { type: select, proxies: [ 美国, FallBack, 新加坡, 日本, 韩国, 欧盟, Other, Select ] }
SelectProxy: &SelectProxy { type: select, proxies: [ Proxy, GPT, FallBack, 台湾, 香港, 新加坡, 日本, 韩国, 美国, 欧盟, Other, Select ] }
# 锚点 - 规则集 
DomainMrs: &DomainMrs { type: http, interval: 86400, behavior: domain, format: mrs }
DomainText: &DomainText { type: http, interval: 86400, behavior: domain, format: text }
domainYaml: &domainYaml { type: http, interval: 86400, behavior: domain, format: yaml }
IPcidrMrs: &IPcidrMrs { type: http, interval: 86400, behavior: ipcidr, format: mrs }
IPcidrText: &IPcidrText { type: http, interval: 86400, behavior: ipcidr, format: text }
ClassicalText: &ClassicalText { type: http, interval: 86400, behavior: classical, format: text }
ClassicalYaml: &ClassicalYaml { type: http, interval: 86400, behavior: classical, format: yaml }
############################################## - ⚓️ 锚点区结束 - ############################################## 

# 远程订阅组
# 注意：请提供您的机场订阅链接，每个链接一行，并为每个机场命名，末尾的[A]为每个节点添加机场名称前缀，可自定义
proxy-providers:
  去cf: { <<: *PProviders, url: 'xxx', override: { additional-prefix: '' } }

  机场: { <<: *PProviders, url: 'xxx', override: { additional-prefix: '' } }
# 全局配置
port: 7890
socks-port: 7891
mixed-port: 7892
redir-port: 9797
tproxy-port: 9898
unified-delay: true
tcp-concurrent: true
find-process-mode: always
global-client-fingerprint: chrome
allow-lan: true
bind-address: '*'
mode: rule
log-level: warning
ipv6: false
udp: true
keep-alive-idle: 600
keep-alive-interval: 15
profile:
  store-selected: true
  store-fake-ip: true

external-controller: 127.0.0.1:9090
external-ui-url: https://github.com/Zephyruso/zashboard/releases/latest/download/dist-cdn-fonts.zip
external-ui: ui
external-ui-name:
secret:

# 自定 GEO 下载地址
geox-url:
  geosite: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
  mmdb: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/Country.mmdb
  geoip: https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat
  asn: https://cdn.jsdelivr.net/gh/Loyalsoldier/geoip@release/Country-asn.mmdb
sniffer:
  enable: false
  sniff:
    HTTP:
      ports: [ 80, 8080-8880 ]
      override-destination: true
    TLS:
      ports: [ 443, 8443 ]
    QUIC:
      ports: [ 443, 8443 ]
  skip-domain:
  # 忽略嗅探
  - 'rule-set:cn_domain,private_domain'
  - '+.googlevideo.com'
  - 'Mijia Cloud'
  - '+.wechat.com'
  - '+.qpic.cn'
  - '+.qq.com'
  - '+.wechatapp.com'
tun:
  enable: false
  stack: mixed
  dns-hijack: [ any:53 ]

dns:
  enable: true
  prefer-h3: false
  use-hosts: true
  use-system-hosts: true
  respect-rules: false
  listen: 0.0.0.0:1053
  ipv6: false
  enhanced-mode: redir-host
  fake-ip-range: **********/15
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
  - '*'
  - '+.lan'
  - "geosite:googlefcm"
  - "geosite:cn"
  default-nameserver:
  - https://*********/dns-query#DIRECT&h3=true

  nameserver:
  - https://*********/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  proxy-server-nameserver:
  - https://*********/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true

# 组 
proxy-groups:
- { name: Proxy, <<: *SelectJP, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Static.png }
- { name: GPT, type: select, proxies: [ GPT自动, 新加坡, 日本, 美国, 欧盟 ], filter: *FilterGPT, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Bot.png }
- { name: FallBack, <<: *FallBack, proxies: [ 日本, 新加坡, 韩国, 美国, 欧盟 ], icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/ULB.png }
- { name: Select, type: select, include-all: true, filter: *FilterAll, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Clubhouse.png }
- { name: Other, type: select, include-all: true, filter: *FilterOther, icon: https://raw.githubusercontent.com/Orz-3/mini/master/Color/XD.png }
- { name: Google, <<: *SelectProxy, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Google.png }
- { name: Game, <<: *SelectProxy, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Game.png }
- { name: Microsoft, <<: *SelectProxy, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Microsoft.png }
#GPT
- { name: Final, type: select, proxies: [ Proxy ], icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Final.png }

# 地区
- { name: 香港, type: select, proxies: [ 香港自动 ], include-all: true, filter: *FilterHK, icon: https://raw.githubusercontent.com/fonttools/region-flags/refs/heads/gh-pages/png/HK.png }
- { name: 台湾, type: select, proxies: [ 台湾自动 ], include-all: true, filter: *FilterTW, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Taiwan.png }
- { name: 新加坡, type: select, proxies: [ 新加坡自动 ], include-all: true, filter: *FilterSG, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Singapore.png }
- { name: 日本, type: select, proxies: [ 日本自动 ], include-all: true, filter: *FilterJP, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Japan.png }
- { name: 韩国, type: select, proxies: [ 韩国自动 ], include-all: true, filter: *FilterKR, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Korea.png }
- { name: 美国, type: select, proxies: [ 美国自动 ], include-all: true, filter: *FilterUS, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/United_States.png }
- { name: 欧盟, type: select, proxies: [ 欧盟自动 ], include-all: true, filter: *FilterEU, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/European_Union.png }
# 自动测速
- { name: 香港自动, <<: *UrlTest, filter: *FilterHK, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 台湾自动, <<: *UrlTest, filter: *FilterTW, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 新加坡自动, <<: *UrlTest, filter: *FilterSG, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 日本自动, <<: *UrlTest, filter: *FilterJP, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 韩国自动, <<: *UrlTest, filter: *FilterKR, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 美国自动, <<: *UrlTest, filter: *FilterUS, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 欧盟自动, <<: *UrlTest, filter: *FilterEU, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: GPT自动, <<: *UrlTest, filter: *FilterGPT, icon: https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }

# 本地自建节点组
proxies:

# 远程规则集
rule-providers:
  GPT:
    <<: *ClassicalYaml
    url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/OpenAI/OpenAI.yaml
  Gemini:
    <<: *ClassicalYaml
    url: https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Providers/Ruleset/Gemini.yaml
  Claude:
    <<: *ClassicalYaml
    url: https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Providers/Ruleset/Claude.yaml

  fix-direct:
    <<: *ClassicalYaml
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/fix-direct.yaml
  download:
    <<: *ClassicalYaml
    url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Download/Download.yaml
  speedtest:
    <<: *ClassicalYaml
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/speedtest.yaml
  openai:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/openai.mrs
  telegram:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram.mrs
  twitter:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/twitter.mrs
  instagram:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/instagram.mrs
  youtube:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/youtube.mrs
  spotify:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/spotify.mrs
  netflix:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix.mrs
  disney:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/disney.mrs
  hbo:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/hbo.mrs
  tiktok:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/tiktok.mrs
  github:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/github.mrs
  microsoft@cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/<EMAIL>
  microsoft:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/microsoft.mrs
  facebook:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/facebook.mrs
  google:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/google.mrs
  cloudflare-cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/cloudflare-cn.mrs
  category-public-tracker:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-public-tracker.mrs
  category-games:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-games.mrs
  category-media-cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-media-cn.mrs
  category-media:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-media.mrs
  category-social-media-!cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-social-media-!cn.mrs
  category-ai-chat-!cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-ai-chat-!cn.mrs
  geolocation-!cn:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/geolocation-!cn.mrs
  private_domain:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/private.mrs
  cn_domain:
    <<: *DomainMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn.mrs
  # IP规则集
  facebook_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/facebook_ip.mrs
  google_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/google_ip.mrs
  netflix_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix_ip.mrs
  twitter_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/twitter_ip.mrs
  telegram_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram_ip.mrs
  private_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/private_ip.mrs
  cn_ip:
    <<: *IPcidrMrs
    url: https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn_ip.mrs
  adb:
    <<: *ClassicalYaml
    url: https://raw.githubusercontent.com/nariahlamb/NoMoreWalls/refs/heads/master/snippets/adblock.yml
# 路由
rules:
# >屏蔽QUIC
- AND,((DST-PORT,443),(NETWORK,UDP)),REJECT-DROP
#- RULE-SET,adb,REJECT
- DOMAIN-SUFFIX,ghproxy.net,DIRECT
- DST-PORT,9876,DIRECT #httpmeta
- PROCESS-NAME,http-meta,DIRECT #httpmeta
# 常用必备
- PROCESS-NAME,com.taptap.global.lite,Game
- PROCESS-NAME,com.byyoung.setting,Proxy #爱玩机
- DOMAIN-SUFFIX,551543.xyz,DIRECT
- DOMAIN-SUFFIX,ggff.net,Proxy #免费域名
- PROCESS-NAME,com.deepseek.chat,DIRECT
- DOMAIN-SUFFIX,browserleaks.com,Proxy #dns泄露
- PROCESS-NAME,cc.arthur63.chatbot,GPT
- DOMAIN-SUFFIX,ping0.cc,Proxy #测节点
- DOMAIN-SUFFIX,gitcode.com,Proxy
- DOMAIN-SUFFIX,xn--l9qyaz082a.cn,Proxy

- PROCESS-NAME,tornaco.apps.shortx,Proxy
- DOMAIN-SUFFIX,linux.do,Proxy
- PROCESS-NAME,docker,Proxy
- PROCESS-NAME,Cherry Studio,GPT
- PROCESS-NAME,com.google.android.apps.bard,GPT
- PROCESS-NAME,com.tencent.androidqqmail,Proxy
- DOMAIN-SUFFIX,423down.com,Proxy
- DOMAIN-SUFFIX,grempt.com,Proxy
- DOMAIN-SUFFIX,subsub123456789.com,Proxy
- IP-CIDR,*************/31,REJECT-DROP,no-resolve
# 防御
- RULE-SET,category-public-tracker,REJECT-DROP
# 域名路由


- RULE-SET,speedtest,Proxy
- RULE-SET,google,Google
- RULE-SET,openai,GPT
- RULE-SET,Gemini,GPT
- RULE-SET,Claude,GPT
- RULE-SET,category-ai-chat-!cn,GPT
- RULE-SET,telegram,Proxy
- RULE-SET,twitter,Proxy
- RULE-SET,youtube,Google
- RULE-SET,spotify,Proxy
- RULE-SET,netflix,Proxy
- RULE-SET,disney,Proxy
- RULE-SET,hbo,Proxy
- RULE-SET,tiktok,Proxy
- RULE-SET,github,Proxy
- RULE-SET,category-media,Proxy
- RULE-SET,category-social-media-!cn,Proxy
- RULE-SET,geolocation-!cn,Proxy
- RULE-SET,microsoft,Microsoft
- RULE-SET,instagram,Proxy
- RULE-SET,facebook,Proxy
- RULE-SET,category-games,Game
- RULE-SET,cloudflare-cn,DIRECT
- RULE-SET,microsoft@cn,DIRECT
- RULE-SET,category-media-cn,DIRECT
- RULE-SET,download,DIRECT
- RULE-SET,fix-direct,DIRECT
- RULE-SET,private_ip,DIRECT
- RULE-SET,cn_domain,DIRECT
- DOMAIN-REGEX,^[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+$,Final
# IP路由
- RULE-SET,facebook_ip,Proxy,no-resolve
- RULE-SET,google_ip,GPT,no-resolve
- RULE-SET,netflix_ip,Proxy,no-resolve
- RULE-SET,twitter_ip,Proxy,no-resolve
- RULE-SET,telegram_ip,Proxy,no-resolve
- RULE-SET,cn_ip,DIRECT
- MATCH,Final
