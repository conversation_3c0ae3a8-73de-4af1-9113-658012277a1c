{"name": "棉花糖乐园v2", "blur_strength": 0, "main_text_color": "rgba(108, 92, 231, 0.9)", "italics_text_color": "rgba(255, 155, 203, 0.8)", "underline_text_color": "rgba(255, 182, 193, 0.8)", "quote_text_color": "rgba(255, 105, 180, 0.95)", "blur_tint_color": "rgba(255, 245, 250, 0.9)", "chat_tint_color": "rgba(255, 228, 225, 0.3)", "user_mes_blur_tint_color": "rgba(255, 218, 235, 0.6)", "bot_mes_blur_tint_color": "rgba(221, 214, 255, 0.6)", "shadow_color": "rgba(255, 192, 203, 0.56)", "shadow_width": 0, "border_color": "rgba(255, 255, 255, 0.8)", "font_scale": 1, "fast_ui_mode": false, "waifuMode": false, "avatar_style": 0, "chat_display": 1, "noShadows": false, "chat_width": 58, "timer_enabled": true, "timestamps_enabled": true, "timestamp_model_icon": false, "mesIDDisplay_enabled": true, "hideChatAvatars_enabled": false, "message_token_count_enabled": true, "expand_message_actions": false, "enableZenSliders": false, "enableLabMode": false, "hotswap_enabled": true, "custom_css": "@import url('https://static.zeoseven.com/zsft/95/main/result.css');\n\nbody {\n   font-family: \"LXGW WenKai GB\";\n   font-weight: normal;\n}\n/* Apply custom font to input fields */\ninput[type=\"text\"],\ninput[type=\"number\"],\ntextarea,\n.text_pole,\n.search_field {\n    font-family: \"LXGW WenKai GB\", sans-serif !important; /* Replace \"undefined\" with your actual font name */\n}\n\n/* Apply custom font to code blocks */\npre,\ncode,\n.hljs,\npre code {\n    font-family: \"LXGW WenKai GB\", monospace !important;\n}\n\n/* 基础消息气泡 */\n#chat .mes_block {\n    background: rgba(255, 245, 250, 0.95) !important;\n    border-radius: 20px !important;\n    border: 2px solid rgba(255, 182, 193, 0.4) !important;\n    box-shadow: 0 4px 12px rgba(255, 182, 193, 0.15) !important;\n}\n\n/* 代码块糖果色 */\npre {\n    background: rgba(255, 228, 225, 0.9) !important;\n    border: 2px dashed rgba(255, 105, 180, 0.3) !important;\n    border-radius: 16px !important;\n}\n\n/* 修改斜体文本气泡效果*/\nem, i:not([class*=\"fa\"]):not([class*=\"icon\"]):not(button i):not(.btn i):not([role=\"button\"] i) {\n    display: inline-block;\n    padding: 4px 12px;\n    background: rgba(255, 230, 240, 0.4) !important;\n    border-radius: 8px;\n    border: 1px solid rgba(255, 155, 203, 0.6) !important;\n    font-style: normal;\n    color: rgba(219, 112, 147, 0.9);\n}\n\n/* 引用文本样式 (美化版) */\nq {\n    color: rgba(255, 105, 180, 0.9); /* 粉红色调，与主题协调 */\n    background: rgba(255, 240, 245, 0.4) !important; /* 非常淡的粉色背景 */\n    padding: 2px 6px;\n    border-radius: 4px;\n    border-bottom: 1px dotted rgba(255, 105, 180, 0.5); /* 底部点状边框 */\n    font-style: italic; /* 添加斜体效果 */\n}\n\n/* 强制覆盖标签页按钮的所有背景相关样式 */\n.inline-drawer-header,\n#extensions_settings .inline-drawer-toggle.inline-drawer-header,\n#extensions_settings2 .inline-drawer-toggle.inline-drawer-header,\n#user-settings-block h4,\n.standoutHeader {\n  background-image: none !important;\n  background: rgba(255, 245, 250, 0.95) !important;\n  border-image: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n}\n/* 复选框和单选框背景 */\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n  background-color: rgba(255, 245, 250, 0.95) !important;\n  border-color: rgba(255, 182, 193, 0.6) !important;\n}\n\n/* 复选框和单选框选中状态 */\ninput[type=\"checkbox\"]:checked,\ninput[type=\"radio\"]:checked {\n  background-color: rgba(255, 182, 193, 0.6) !important;\n  border-color: rgba(255, 182, 193, 0.8) !important;\n}\n\n/* 下拉选择框样式 */\nselect {\n  background-color: rgba(255, 245, 250, 0.95) !important;\n  border-color: rgba(255, 182, 193, 0.6) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n/* 输入框样式 */\ninput[type=\"text\"],\ninput[type=\"number\"],\ntextarea {\n  background-color: rgba(255, 245, 250, 0.95) !important;\n  border-color: rgba(255, 182, 193, 0.6) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n/* 标签和文本样式 */\nlabel, .checkbox_label {\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n/* 可选菜单项样式 */\n.menu_item, .dropdown-item {\n  background-color: rgba(255, 245, 250, 0.95) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n/* 滑块样式 - 纯色风格 */\ninput[type=\"range\"] {\n  background-color: rgba(255, 228, 225, 0.9) !important; /* 淡粉色轨道背景 */\n  height: 6px !important; /* 调整轨道高度 */\n  border-radius: 3px !important; /* 圆角轨道 */\n  border: none !important; /* 移除边框 */\n  box-shadow: inset 0 1px 3px rgba(255, 182, 193, 0.3) !important; /* 内阴影效果 */\n}\n\ninput[type=\"range\"]::-webkit-slider-thumb {\n  background-color: rgba(255, 105, 180, 0.8) !important; /* 亮粉色滑块 */\n  width: 14px !important; /* 滑块宽度 */\n  height: 14px !important; /* 滑块高度 */\n  border-radius: 50% !important; /* 圆形滑块 */\n  border: 2px solid white !important; /* 白色边框 */\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* 轻微阴影 */\n}\n\n/* 滑块悬停效果 */\ninput[type=\"range\"]::-webkit-slider-thumb:hover {\n  background-color: rgba(255, 20, 147, 0.9) !important; /* 悬停时颜色变深 */\n  transform: scale(1.1) !important; /* 轻微放大效果 */\n}\n\n/* 代码块糖果色 */\npre {\n    background: rgba(255, 255, 255, 0.95) !important; /* 白色背景 */\n    border: 2px dashed rgba(255, 105, 180, 0.5) !important; /* 粉色虚线边框 */\n    border-radius: 16px !important;\n    padding: 12px !important;\n    margin: 10px 0 !important;\n    box-shadow: 0 2px 8px rgba(255, 182, 193, 0.15) !important; /* 轻微阴影效果 */\n}\n\n/* 代码文本颜色 */\npre code {\n    color: rgba(108, 92, 231, 0.9) !important; /* 紫色文字，与主文本颜色一致 */\n}\n\n/* 消息气泡悬停特效 */\n.mes_block:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(255, 182, 193, 0.25) !important;\n  transition: all 0.3s ease;\n}\n\n/* 去除代码块内的深色背景 */\n.hljs, \ncode.hljs,\npre .hljs {\n    background: transparent !important; /* 透明背景 */\n    background-color: transparent !important;\n}\n\n/* 确保代码高亮的背景也是透明的 */\n.markdown-body pre, \n.markdown-body code,\ncode[class*=\"language-\"],\npre[class*=\"language-\"] {\n    background: transparent !important;\n    background-color: transparent !important;\n}\n\n/* 添加原地旋转的樱花动画效果 - 不透明版 */\n@keyframes spinSakura {\n  0% {\n    transform: rotate(0deg);\n    opacity: 1;  /* 改为完全不透明 */\n  }\n  100% {\n    transform: rotate(360deg);\n    opacity: 1;  /* 改为完全不透明 */\n  }\n}\n\n/* 创建樱花元素 */\n.mes[is_user=\"true\"] .avatar::before {\n  content: \"🌸\";\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  font-size: 20px;\n  z-index: 10;\n  animation: spinSakura 8s linear infinite;\n  pointer-events: none;\n}\n\n/* AI头像装饰 */\n.mes:not([is_user=\"true\"]) .avatar::before {\n  content: \"⭐\";\n  position: absolute;\n  top: 2px;\n  left: 0px;\n  font-size: 20px;\n  z-index: 10;\n  animation: spinSakura 8s linear infinite;\n  pointer-events: none;\n}\n\n/* 消息气泡悬停特效 */\n.mes_block:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(255, 182, 193, 0.25) !important;\n  transition: all 0.3s ease;\n}\n\n\n/* 滚动条美化 - 纯色风格 */\n::-webkit-scrollbar {\n  width: 12px; /* 略微增加宽度 */\n  background: rgba(255, 245, 250, 0.7); /* 略微增加不透明度 */\n}\n\n::-webkit-scrollbar-thumb {\n  background: rgba(255, 105, 180, 0.7); /* 纯粉色滑块，不使用渐变 */\n  border-radius: 20px;\n  border: 4px solid rgba(255, 245, 250, 0.7); /* 扩大边框使滑块看起来更小 */\n  box-shadow: none; /* 移除任何阴影 */\n}\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 20, 147, 0.8); /* 悬停时颜色加深 */\n}\n\n/* 移除滚动条轨道和角落的边框 */\n::-webkit-scrollbar-track,\n::-webkit-scrollbar-corner {\n  border: none !important;\n  box-shadow: none !important;\n  background: rgba(255, 245, 250, 0.7); /* 确保与滚动条背景一致 */\n}\n\n/* 移除滚动条的任何黑色边框 */\n* {\n  scrollbar-color: rgba(255, 105, 180, 0.7) rgba(255, 245, 250, 0.7) !important;\n  scrollbar-width: thin !important;\n}\n\n/* 输入框和选择框 */\ninput, select, textarea, .text_pole, .search_field {\n    background-color: rgba(255, 245, 250, 0.95) !important;\n    color: rgba(108, 92, 231, 0.9) !important;\n    border: 1px solid rgba(255, 182, 193, 0.5) !important;\n    border-radius: 8px !important;\n}\n\n.mes_block {\n  margin-left: 7px !important; /* 增加消息气泡左侧的间距 */\n}\n\n\n/* 头像悬停效果 */\n.mes .avatar img:hover {\n  transform: scale(1.05); /* 略微放大 */\n  transition: all 0.3s ease;\n  border-style: dotted !important; /* 悬停时变为点状边框 */\n  box-shadow: 0 0 12px rgba(255, 155, 203, 0.5) !important; /* 增强发光效果 */\n}\n\n/* 调整用户名 */\n.mes .mes_name {\n  margin-top: 10px; /* 用户名下移 */\n  margin-left: 10px; /* 用户名右移 */\n}\n\n.mes .name_text {\n  margin-top: 10px; /* 增加顶部边距，让名字往下移 */\n\n}\n\n.mes_text {\n  width: 100% !important; /* 确保文本区域占满可用空间 */\n  padding-right: 10px !important; /* 增加右侧内边距，避免文字太贴近边缘 */\n}\n\n/* 移除顶部菜单栏下方的阴影线 */\n.nav-menu-bar,\n#top-nav,\n#top-bar,\n.nav-header,\n.icon-menu-bar,\n#icon-bar {\n  box-shadow: none !important;\n  border-bottom: none !important;\n}\n\n.drawer-icon::before,\n#leftNavDrawerIcon::before,\n#rightNavDrawerIcon::before {\n    content: '';\n    display: block;\n    width: 40px;\n    height: 40px;\n    background-size: contain;\n    background-repeat: no-repeat;\n    background-position: center;\n    margin-right: px;\n    margin-left: -8px;\n}\n.drawer-icon {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n    padding-left: 12px;\n}\n\n/* 图标通用样式 */\n.drawer-icon::before,\n#leftNavDrawerIcon::before,\n#rightNavDrawerIcon::before {\n    content: '';\n    display: block;\n    width: 40px;\n    height: 40px;\n    background-size: contain;\n    background-repeat: no-repeat;\n    background-position: center;\n    margin-right: 0px;\n    margin-left: -8px;\n}\n\n/* 调整图标容器样式 */\n.drawer-icon {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n    padding-left: 12px;\n}\n\n/* 左侧导航图标 */\n#leftNavDrawerIcon::before {\n    background-image: url('https://files.catbox.moe/ilrehl.png');\n}\n\n/* 系统设置图标 */\n#sys-settings-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/nw5n3z.png');\n}\n\n/* 高级格式图标 */\n#advanced-formatting-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/4nmh71.png');\n}\n\n/* 世界信息图标 */\n#WI-SP-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/qvvgul.png');\n}\n\n/* 用户设置图标 */\n#user-settings-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/s1hunq.png');\n}\n\n/* Logo图标 */\n#logo_block .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/hr3qvq.png');\n}\n\n/* 扩展设置图标 */\n#extensions-settings-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/9pzsbd.png');\n}\n\n/* 角色管理图标 */\n#persona-management-button .drawer-icon::before {\n    background-image: url('https://files.catbox.moe/uwn9zl.png');\n}\n\n/* 右侧导航图标 */\n#rightNavDrawerIcon::before {\n    background-image: url('https://files.catbox.moe/fpijli.png');\n}\n\n/* 修改按钮悬停和激活时的样式 - 棉花糖乐园原版配色 */\nbutton:hover, button:active,\n.menu_button:hover, .menu_button:active,\n.btn:hover, .btn:active,\n.button:hover, .button:active,\ninput[type=\"button\"]:hover, input[type=\"button\"]:active,\ninput[type=\"submit\"]:hover, input[type=\"submit\"]:active {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n  color: rgba(255, 155, 231, 203.80) !important; \n  border-color: rgba(255, 182, 193, 0.6) !important;\n  box-shadow: inset 0 1px 3px rgba(255, 182, 193, 0.3) !important;\n}\n\n/* 针对深色按钮的悬停和激活状态 */\n.btn-primary:hover, .btn-primary:active,\n.primary_button:hover, .primary_button:active {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n  border-color: rgba(255, 182, 193, 0.6) !important;\n}\n\n/* 确保覆盖所有可能的按钮状态 */\n[role=\"button\"]:hover, [role=\"button\"]:active,\n.inline-drawer-toggle:hover, .inline-drawer-toggle:active,\n.menu_item:hover, .menu_item:active,\n.dropdown-item:hover, .dropdown-item:active {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n/* 修复特定图片按钮的悬停和激活状态 */\n#send_but:hover, #send_but:active,\n#option_tabs:hover, #option_tabs:active,\n#options_button:hover, #options_button:active,\n#dialogue_popup:hover, #dialogue_popup:active {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n}\n\n/* 修复可能的导航和侧边栏按钮 */\n.nav-link:hover, .nav-link:active,\n.sidebar-item:hover, .sidebar-item:active,\n.settings-button:hover, .settings-button:active {\n  background-color: rgba(255, 255, 255, 0.9) !important;\n  color: rgba(108, 92, 231, 0.9) !important;\n}\n\n", "bogus_folders": true, "zoomed_avatar_magnification": false, "reduced_motion": false, "compact_input_area": true, "show_swipe_num_all_messages": ""}