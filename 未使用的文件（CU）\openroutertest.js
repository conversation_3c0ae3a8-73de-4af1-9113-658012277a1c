const fs = require('fs');
const path = require('path');
const axios = require('axios');
const express = require('express');
const app = express();
const PORT = process.env.PORT || 3323; // 设置端口为3323

// 配置项
const CONFIG_FILE = path.join(__dirname, 'config.json');

// 加载配置
let config = {
  keys: [],
  baseUrl: 'https://openrouter.ai/api/v1',
  loadBalancingStrategy: 'round-robin', // 'round-robin', 'least-used', 'random'
  keyUsage: {},
  rateLimits: {},
  accessKey: '', // 添加访问密钥用于授权使用该服务
};

// 初始化配置
function initConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const loadedConfig = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
      config = { ...config, ...loadedConfig };
    } else {
      // 创建示例配置文件
      config.keys = ['YOUR_API_KEY_1', 'YOUR_API_KEY_2', 'YOUR_API_KEY_3'];
      config.accessKey = 'YOUR_ACCESS_KEY'; // 默认访问密钥
      saveConfig();
      console.log('已创建默认配置文件，请在config.json中添加您的API密钥和访问密钥');
    }

    // 初始化密钥使用统计
    config.keys.forEach(key => {
      if (!config.keyUsage[key]) {
        config.keyUsage[key] = 0;
      }
      if (!config.rateLimits[key]) {
        config.rateLimits[key] = {
          lastError: null,
          backoffUntil: null
        };
      }
    });
    
    saveConfig();
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

// 保存配置
function saveConfig() {
  fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2), 'utf8');
}

// 选择API密钥逻辑
function selectApiKey() {
  // 过滤掉暂时被禁用的密钥（因为速率限制）
  const availableKeys = config.keys.filter(key => {
    const limit = config.rateLimits[key];
    return !limit.backoffUntil || new Date() > new Date(limit.backoffUntil);
  });

  if (availableKeys.length === 0) {
    // 所有密钥都被暂时禁用，返回最快恢复的那个
    const nextAvailableKey = config.keys.reduce((a, b) => {
      const timeA = new Date(config.rateLimits[a].backoffUntil || 0);
      const timeB = new Date(config.rateLimits[b].backoffUntil || 0);
      return timeA < timeB ? a : b;
    });
    
    console.log(`所有密钥都达到限制，使用最快恢复的密钥: ${nextAvailableKey}`);
    return nextAvailableKey;
  }

  let selectedKey;
  
  switch (config.loadBalancingStrategy) {
    case 'least-used':
      // 选择使用次数最少的密钥
      selectedKey = availableKeys.reduce((a, b) => 
        config.keyUsage[a] < config.keyUsage[b] ? a : b
      );
      break;
      
    case 'random':
      // 随机选择一个密钥
      selectedKey = availableKeys[Math.floor(Math.random() * availableKeys.length)];
      break;
      
    case 'round-robin':
    default:
      // 获取上次使用的密钥索引
      const lastIndex = config.lastKeyIndex || -1;
      const newIndex = (lastIndex + 1) % availableKeys.length;
      selectedKey = availableKeys[newIndex];
      config.lastKeyIndex = newIndex;
      break;
  }
  
  config.keyUsage[selectedKey]++;
  saveConfig();
  return selectedKey;
}

// 处理API请求错误
function handleApiError(key, error) {
  console.error(`API密钥 ${key} 请求失败:`, error.message);
  
  // 处理速率限制错误
  if (error.response && (error.response.status === 429 || error.response.status === 403)) {
    const retryAfter = error.response.headers['retry-after'] || 60; // 默认退避60秒
    const backoffUntil = new Date(Date.now() + retryAfter * 1000);
    
    config.rateLimits[key] = {
      lastError: new Date().toISOString(),
      backoffUntil: backoffUntil.toISOString()
    };
    
    console.log(`密钥 ${key} 达到速率限制，暂停使用直到: ${backoffUntil.toLocaleString()}`);
    saveConfig();
  }
}

// 验证访问密钥中间件
function verifyAccessKey(req, res, next) {
  const authHeader = req.headers['authorization'];
  
  if (!authHeader) {
    return res.status(401).json({ error: '缺少授权头' });
  }
  
  // 支持Bearer token或直接API密钥格式
  const token = authHeader.startsWith('Bearer ') 
    ? authHeader.substring(7) 
    : authHeader;
  
  if (token !== config.accessKey) {
    return res.status(403).json({ error: '无效的访问密钥' });
  }
  
  next();
}

// 设置Express中间件
app.use(express.json());

// 使用OpenAI API格式的路由
// 为所有API路由添加授权验证
app.use('/v1', verifyAccessKey);

// 处理聊天补全
app.post('/v1/chat/completions', async (req, res) => {
  const apiKey = selectApiKey();
  console.log(`使用API密钥: ${apiKey.substring(0, 5)}...`);
  
  try {
    const response = await axios.post(`${config.baseUrl}/chat/completions`, req.body, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    res.json(response.data);
  } catch (error) {
    handleApiError(apiKey, error);
    
    // 尝试使用不同的密钥重试
    try {
      const backupKey = selectApiKey();
      if (backupKey !== apiKey) {
        console.log(`重试使用备用密钥: ${backupKey.substring(0, 100)}...`);
        
        const response = await axios.post(`${config.baseUrl}/chat/completions`, req.body, {
          headers: {
            'Authorization': `Bearer ${backupKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        res.json(response.data);
      } else {
        res.status(429).json({ error: '所有API密钥都达到速率限制' });
      }
    } catch (retryError) {
      res.status(500).json({ error: '请求失败，所有密钥都无法使用' });
    }
  }
});

// 处理补全请求
app.post('/v1/completions', async (req, res) => {
  const apiKey = selectApiKey();
  console.log(`使用API密钥: ${apiKey.substring(0, 5)}...`);
  
  try {
    const response = await axios.post(`${config.baseUrl}/completions`, req.body, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    res.json(response.data);
  } catch (error) {
    handleApiError(apiKey, error);
    
    try {
      const backupKey = selectApiKey();
      if (backupKey !== apiKey) {
        console.log(`重试使用备用密钥: ${backupKey.substring(0, 5)}...`);
        
        const response = await axios.post(`${config.baseUrl}/completions`, req.body, {
          headers: {
            'Authorization': `Bearer ${backupKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        res.json(response.data);
      } else {
        res.status(429).json({ error: '所有API密钥都达到速率限制' });
      }
    } catch (retryError) {
      res.status(500).json({ error: '请求失败，所有密钥都无法使用' });
    }
  }
});

// 处理模型列表请求
app.get('/v1/models', async (req, res) => {
  const apiKey = selectApiKey();
  
  try {
    const response = await axios.get(`${config.baseUrl}/models`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    res.json(response.data);
  } catch (error) {
    handleApiError(apiKey, error);
    res.status(500).json({ error: '获取模型列表失败' });
  }
});

// 查看密钥使用统计 - 需要访问密钥授权
app.get('/stats', verifyAccessKey, (req, res) => {
  res.json({
    total_keys: config.keys.length,
    key_usage: config.keyUsage,
    rate_limits: config.rateLimits,
    strategy: config.loadBalancingStrategy
  });
});

// 更改负载均衡策略 - 需要访问密钥授权
app.post('/settings', verifyAccessKey, (req, res) => {
  if (req.body.strategy && ['round-robin', 'least-used', 'random'].includes(req.body.strategy)) {
    config.loadBalancingStrategy = req.body.strategy;
    saveConfig();
    res.json({ success: true, message: `已更改负载均衡策略为: ${req.body.strategy}` });
  } else {
    res.status(400).json({ error: '无效的负载均衡策略' });
  }
});

// 初始化配置并启动服务器
initConfig();
app.listen(PORT, () => {
  console.log(`OpenRouter API密钥负载均衡服务已启动于 http://localhost:${PORT}`);
  console.log(`负载均衡策略: ${config.loadBalancingStrategy}`);
  console.log(`已加载 ${config.keys.length} 个API密钥`);
});
