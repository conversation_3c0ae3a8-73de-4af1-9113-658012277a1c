### 🤖 Assistant



Based on the user's query, I am transforming it into an AI-friendly English prompt for an AI to answer the question using the provided reference materials. The prompt incorporates the citation rules and ensures the response is in Chinese, as specified by the user.

Here is the optimized English prompt:

"Answer the following question in Chinese, based solely on the provided reference materials. Ensure the response is comprehensive, accurate, and directly addresses the user's requirements for creating a text-based game compatible with SillyTavern. Use the citation rules: cite the context at the end of sentences when appropriate, using the format [number] to reference the corresponding reference material ID. If a sentence draws from multiple contexts, list all relevant citation numbers, e.g., [1][2], without grouping citations at the end.

User's question: The user wants to develop a text game similar to SillyTavern that is fully compatible with all SillyTavern importable files, including world books, character cards, presets, and other settings, to allow direct import. The game must feature an aesthetically pleasing 2D anime-style interface with dynamic image changes based on the current scene. All interface elements and settings should be in Chinese. Additionally, the dialogue interface should include a side panel displaying numerical values relevant to the plot, enhanced with appropriate emojis for better visual appeal and readability. After each dialogue, the system should provide four options for the player to choose a direction or input custom content.

Reference materials are provided with the following IDs and content summaries for citation:
- [1] Content from a blog post on SillyTavern setup and plugins, including updates, memory management, and extensions.
- [2] Instructions on importing and using example character cards in SillyTavern.
- [3] Tutorial on deploying SillyTavern using Docker, covering installation and configuration.
- [4] GitHub page for SillyTavern Chinese documentation, detailing features and extensions.
- [5] Overview of SillyTavern, including supported APIs, extensions, and compatibility.
- [6] Error logs and troubleshooting for SillyTavern installation issues.
- [7] Guide on integrating SillyTavern with various AI models and backends.
- [8] Usage guide for SillyTavern, focusing on card creation and deployment.
- [9] Tutorial for installing SillyTavern and CLEWD on Android Termux.
- [10] Instructions for running multiple SillyTavern instances and updating.
- [11] YouTube video description about SillyTavern features.
- [12] Information on SillyTavern's development branches and stability.
- [13] Tutorial for running SillyTavern on Android devices.
- [14] YouTube video on SillyTavern installation for Windows.
- [15] YouTube video on installing Live2D extension for SillyTavern.
- [16] SillyTavern official website with documentation links.
- [17] A helper site for SillyTavern with tips and materials.

Provide step-by-step guidance, recommendations, and any necessary adaptations to ensure compatibility, drawing only from the referenced materials. If certain aspects are not covered in the references, note the limitations clearly with appropriate citations."

[1] [https://www.rainlain.com/index.php/2025/04/26/3349/](SillyTavern轻松搭：各种技巧/插件的说明 - 梦雨玲音)

[2] [https://sillytavern-stage-girls-dog.readthedocs.io/工具经验/酒馆助手编写环境配置/准备教学环境/](准备教学环境 — SillyTavern 内容 by 青空莉想做舞台少女的狗)

[3] [https://pt4300.github.io/Sillytavern-docker-tutorial/full_docker/](SillyTavern 酒馆完整部署教程 - 保姆级安装指南 - SERN酒馆妙妙屋)

[4] [https://github.com/eigeen/SillyTavern-Docs-CN](GitHub - eigeen/SillyTavern-Docs-CN: SillyTavern 中文文档 (SillyTavern ...)

[5] [https://st-docs.role.fun/](什么是 SillyTavern? | SillyTavern 中文文档)

[6] [https://www.rainlain.com/index.php/2024/11/19/2645/](WINNAS轻松搭：SillyTavern（酒馆）从入门到精通 - 梦雨玲音)

[7] [https://www.meoai.net/sillytavern-ai.html](SillyTavern深度指南：AI酒馆+DeepSeek本地部署，连接各种本地和云端AI模型 | MeoAI)

[8] [https://www.hqshi.cn/info/knowledge/sillytavern-guide](酒馆 (sillytavern)的使用说明 | 空桑)

[9] [https://linux.do/t/topic/272458](Termux (0.118.1) 下安装 SillyTavern（带 CLEWD 支持）及数据备份教程 - 零基础小白版)

[10] [https://pt4300.github.io/Sillytavern-docker-tutorial/simple_docker/](SillyTavern 酒馆一键部署教程 - 零基础超友好版本，按一下就能安装! - SERN酒馆妙妙屋)

[11] [https://www.youtube.com/watch?v=cA8xK9__m44](Tips and advanced features - SillyTavern - YouTube)

[12] [http://sillytavern.wiki/installation/](安装指南 | SillyTavern傻酒馆中文文档)

[13] [https://www.rainlain.com/index.php/2025/04/16/3224/](SillyTavern轻松搭：手机（Android）安装布置教程 - 梦雨玲音)

[14] [https://www.youtube.com/watch?v=vKfLuN4l12M](sillyTavern安裝教學Windows - YouTube)

[15] [https://www.youtube.com/watch?v=uaBuArOiCBM](Animated & interactive characters - SillyTavern - YouTube)

[16] [https://sillytavern.app/?ref=openrouter](SillyTavern - LLM Frontend for Power Users)

[17] [https://fishystony.github.io/sillytavern-helper/](食用指南 · Silly Wiki)