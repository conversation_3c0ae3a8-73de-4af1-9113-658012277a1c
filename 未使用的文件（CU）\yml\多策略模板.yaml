# > 𝐥𝐚𝐨𝐬𝐡𝐮@Mihomo 自用配置
# > 配置下载𝐔𝐑𝐋： https://ghgo.xyz/https://raw.githubusercontent.com/jnlaoshu/MySelf/master/Mihomo/Profile.yaml
# > 𝐅𝐫𝐨𝐦：https://ghgo.xyz/https://github.com/666OS/YYDS/blob/main/mihomo/config/MihomoPro.yaml
# > 𝐔𝐩𝐝𝐚𝐭𝐞: 2024.12.19 08:55

############################################## - ⚓️ 锚点区开始 - ##############################################
# 锚点 - 远程订阅组
PProviders: &PProviders { type: http, interval: 86400, health-check: { enable: true, url: 'https://www.gstatic.com/generate_204', interval: 300 }, filter: '^(?!.*(群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author))' }
# 锚点 - 筛选组 
FilterHK: &FilterHK '(?i)港|香港|🇭🇰|HK|Hong|HKG'
FilterTW: &FilterTW '(?i)台|台湾|🇼🇸|🇹🇼|TW|tai|TPE|TSA|KHH'
FilterSG: &FilterSG '(?i)坡|新加坡|🇸🇬|SG|Sing|SIN|XSP'
FilterJP: &FilterJP '(?i)日|日本|🇯🇵|JP|Japan|NRT|HND|KIX|CTS|FUK'
FilterKR: &FilterKR '(?i)韩|韩国|🇰🇷|韓|韓國|首尔|南朝鲜|KR|KOR|Korea|South'
FilterUS: &FilterUS '(?i)美|美国|🇺🇸|US|USA|JFK|LAX|ORD|ATL|DFW|SFO|MIA|SEA|IAD'
FilterEU: &FilterEU '(?i)(🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|🇬🇧|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU)'
FilterOther: &FilterOther '^(?!.*(直连|港|台|新|日|韩|香港|台湾|新加坡|日本|韩国|美国|🇭🇰|🇼🇸|🇹🇼|🇸🇬|🇯🇵|🇰🇷|🇺🇸|🇬🇧|🇦🇹|🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|HK|TW|SG|JP|KR|US|GB|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU|HKG|TPE|TSA|KHH|SIN|XSP|NRT|HND|KIX|CTS|FUK|JFK|LAX|ORD|ATL|DFW|SFO|MIA|SEA|IAD|LHR|LGW))'
FilterAll: &FilterAll '^(?!.*(直连|群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author))'
# 锚点 - 策略组
UrlTest: &UrlTest { type: url-test, interval: 60, tolerance: 20, lazy: true, url: 'https://www.gstatic.com/generate_204', disable-udp: false, timeout: 2000, max-failed-times: 3, hidden: true, include-all: true }
FallBack: &FallBack { type: fallback, interval: 60, lazy: true, url: 'https://www.gstatic.com/generate_204', disable-udp: false, timeout: 2000, max-failed-times: 3, hidden: false }
LoadBalanceCH: &LoadBalanceCH { type: load-balance, interval: 60, lazy: true, url: 'https://www.gstatic.com/generate_204', disable-udp: false, strategy: consistent-hashing, timeout: 2000, max-failed-times: 3, hidden: true, include-all: true }
LoadBalanceCR: &LoadBalanceCR { type: load-balance, interval: 60, lazy: true, url: 'https://www.gstatic.com/generate_204', disable-udp: false, strategy: round-robin, timeout: 2000, max-failed-times: 3, hidden: true, include-all: true }

# 锚点 - 选择组 
SelectFB: &SelectFB { type: select, proxies: [ 故障转移, 香港策略, 台湾策略, 新加坡策略, 日本策略, 韩国策略, 美国策略, 欧盟策略, 冷门自选, 全球手动, 链式代理, 直连 ] }
SelectHK: &SelectHK { type: select, proxies: [ 香港策略, 台湾策略, 新加坡策略, 日本策略, 韩国策略, 美国策略, 欧盟策略, 冷门自选, 全球手动, 链式代理, 直连 ] }
SelectSG: &SelectSG { type: select, proxies: [ 新加坡策略, 故障转移, 香港策略, 台湾策略, 日本策略, 韩国策略, 美国策略, 欧盟策略, 冷门自选, 全球手动, 链式代理 ] }
SelectUS: &SelectUS { type: select, proxies: [ 美国策略, 故障转移, 香港策略, 台湾策略, 新加坡策略, 日本策略, 韩国策略, 欧盟策略, 冷门自选, 全球手动, 链式代理 ] }
SelectProxy: &SelectProxy { type: select, proxies: [ 默认代理, 故障转移, 香港策略, 台湾策略, 新加坡策略, 日本策略, 韩国策略, 美国策略, 欧盟策略, 冷门自选, 全球手动, 链式代理, 直连 ] }
# 锚点 - 规则集 
DomainMrs: &DomainMrs { type: http, interval: 86400, behavior: domain, format: mrs }
DomainText: &DomainText { type: http, interval: 86400, behavior: domain, format: text }
domainYaml: &domainYaml { type: http, interval: 86400, behavior: domain, format: yaml }
IPcidrMrs: &IPcidrMrs { type: http, interval: 86400, behavior: ipcidr, format: mrs }
IPcidrText: &IPcidrText { type: http, interval: 86400, behavior: ipcidr, format: text }
ClassicalText: &ClassicalText { type: http, interval: 86400, behavior: classical, format: text }
ClassicalYaml: &ClassicalYaml { type: http, interval: 86400, behavior: classical, format: yaml }
############################################## - ⚓️ 锚点区结束 - ############################################## 

# 远程订阅组
# 注意：请提供您的机场订阅链接，每个链接一行，并为每个机场命名，末尾的[A]为每个节点添加机场名称前缀，可自定义
proxy-providers:
  机场A: { <<: *PProviders, url: 'http://127.0.0.1:38324/download/collection/%E5%90%88%E5%B9%B6?target=ClashMeta', override: { additional-prefix: '[agg] ' } }
  机场B: { <<: *PProviders, url: 'http://127.0.0.1:38324/download/%E5%87%8C%E4%BA%91?target=ClashMeta', override: { additional-prefix: '[delay] ' } }

# 全局配置
port: 7890
socks-port: 7891
mixed-port: 7892
redir-port: 9797
tproxy-port: 9898
unified-delay: true
tcp-concurrent: true
find-process-mode: 'strict'
global-client-fingerprint: chrome
allow-lan: true
bind-address: '*'
mode: rule
log-level: warning
ipv6: false
udp: true
keep-alive-idle: 600
keep-alive-interval: 15
profile:
  store-selected: true
  store-fake-ip: true

external-controller: 0.0.0.0:9090
external-ui-url: https://ghgo.xyz/https://github.com/MetaCubeX/metacubexd/archive/refs/heads/gh-pages.zip
external-ui: ui
external-ui-name: metacubexd
secret:

# 自定 GEO 下载地址
geox-url:
  geosite: https://ghgo.xyz/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat
  mmdb: https://ghgo.xyz/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb
  geoip: https://ghgo.xyz/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat
  asn: https://ghgo.xyz/https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb

sniffer:
  enable: true
  sniff:
    HTTP:
      ports: [ 80, 8080-8880 ]
      override-destination: true
    TLS:
      ports: [ 443, 8443 ]
    QUIC:
      ports: [ 443, 8443 ]
  skip-domain:
  # 忽略嗅探
  - 'rule-set:cn_domain,private_domain'
  - '+.googlevideo.com'
  - 'Mijia Cloud'
  - '+.push.apple.com'
  - '+.wechat.com'
  - '+.qpic.cn'
  - '+.qq.com'
  - '+.wechatapp.com'
tun:
  enable: false
  stack: mixed
  dns-hijack: [ any:53 ]

dns:
  enable: true
  listen: :1053
  ipv6: false
  # 路由器个人建议使用 redir-host 以最佳兼容性
  # 其他设备可以使用 fake-ip
  enhanced-mode: redir-host
  fake-ip-range: **********/16
  fake-ip-filter:
  - '*'
  - '*.lan'
  - 'localhost.ptlogin2.qq.com'
  - 'dns.msftncsi.com'
  - 'www.msftncsi.com'
  - 'www.msftconnecttest.com'
  default-nameserver:
  - *********
  - *********
  - ************
  - ***************
  - '[2402:4e00::]'
  - '[2400:3200::1]'
  nameserver:
  - *******
  - *******
  - 'tls://*******#dns'
  - 'tls://*******#dns'
  - 'tls://[2001:4860:4860::8844]#dns'
  - 'tls://[2606:4700:4700::1001]#dns'
  fallback-filter:
    geoip: true
    geoip-code: CN
    ipcidr:
    - 240.0.0.0/4
  proxy-server-nameserver:
  - https://doh.pub/dns-query
  nameserver-policy:
    "geosite:cn,private":
    - https://doh.pub/dns-query
    - https://dns.alidns.com/dns-query
    "geosite:!cn,!private":
    - "tls://dns.google"
    - "tls://cloudflare-dns.com"

# 本地自建节点组
proxies:
- { name: 直连, type: direct, udp: true }

# 策略组 
proxy-groups:
- { name: 默认代理, <<: *SelectFB, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Static.png }
- { name: 全球手动, type: select, include-all: true, filter: *FilterAll, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Clubhouse.png }
- { name: 故障转移, <<: *FallBack, proxies: [ 香港策略, 台湾策略, 新加坡策略, 日本策略, 韩国策略, 美国策略, 欧盟策略, 全球手动, 冷门自选 ], icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/ULB.png }
- { name: 漏网之鱼, type: select, proxies: [ 默认代理, 直连 ], icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Final.png }
- { name: 冷门自选, type: select, include-all: true, filter: *FilterOther, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Orz-3/mini/master/Color/XD.png }
- { name: AI平台, <<: *SelectUS, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Bot.png }
- { name: 流媒体, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/youtube.png }
- { name: 国际新闻, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Apple_News.png }
- { name: 社交平台, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/PBS.png }
- { name: 游戏平台, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Game.png }
- { name: 网速测试, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Speedtest.png }
# 公司
- { name: Github, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/lige47/QuanX-icon-rule/main/icon/github(1).png }
- { name: 微软服务, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Microsoft.png }
- { name: 谷歌服务, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Google_Search.png }
# 地区策略
- { name: 香港策略, hidden: true, type: select, proxies: [ 香港自动, 香港均衡-散列, 香港均衡-轮询 ], include-all: true, filter: *FilterHK, icon: https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Hong_Kong.png }
- { name: 台湾策略, hidden: true, type: select, proxies: [ 台湾自动, 台湾均衡-散列, 台湾均衡-轮询 ], include-all: true, filter: *FilterTW, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/TW.png }
- { name: 新加坡策略, hidden: true, type: select, proxies: [ 新加坡自动, 新加坡均衡-散列, 新加坡均衡-轮询 ], include-all: true, filter: *FilterSG, icon: https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Singapore.png }
- { name: 日本策略, hidden: true, type: select, proxies: [ 日本自动, 日本均衡-散列, 日本均衡-轮询 ], include-all: true, filter: *FilterJP, icon: https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Japan.png }
- { name: 韩国策略, hidden: true, type: select, proxies: [ 韩国自动, 韩国均衡-散列, 韩国均衡-轮询 ], include-all: true, filter: *FilterKR, icon: https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Korea.png }
- { name: 美国策略, hidden: true, type: select, proxies: [ 美国自动, 美国均衡-散列, 美国均衡-轮询 ], include-all: true, filter: *FilterUS, icon: https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/United_States.png }
- { name: 欧盟策略, hidden: true, type: select, proxies: [ 欧盟自动, 欧盟均衡-散列, 欧盟均衡-轮询 ], include-all: true, filter: *FilterEU, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/European_Union.png } # 其他策略
- { name: 链式代理, type: relay, proxies: [ 中转机, 落地机 ], icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/TIDAL_1.png }
- { name: 中转机, type: select, proxies: [ 香港策略, 台湾策略, 日本策略, 新加坡策略, 韩国策略, 美国策略, 欧盟策略, 冷门自选, 全球手动 ], icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Rocket.png }
- { name: 落地机, type: select, include-all: true, filter: *FilterAll, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Domestic.png }
- { name: 国内网站, hidden: true, type: select, proxies: [ 直连 ], icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/China.png }
- { name: 国外网站, <<: *SelectProxy, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Global.png }
# 自动测速
- { name: 香港自动, <<: *UrlTest, filter: *FilterHK, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 台湾自动, <<: *UrlTest, filter: *FilterTW, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 新加坡自动, <<: *UrlTest, filter: *FilterSG, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 日本自动, <<: *UrlTest, filter: *FilterJP, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 韩国自动, <<: *UrlTest, filter: *FilterKR, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 美国自动, <<: *UrlTest, filter: *FilterUS, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
- { name: 欧盟自动, <<: *UrlTest, filter: *FilterEU, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png }
# 负载均衡-散列
- { name: 香港均衡-散列, <<: *LoadBalanceCH, filter: *FilterHK, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 台湾均衡-散列, <<: *LoadBalanceCH, filter: *FilterTW, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 新加坡均衡-散列, <<: *LoadBalanceCH, filter: *FilterSG, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 日本均衡-散列, <<: *LoadBalanceCH, filter: *FilterJP, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 韩国均衡-散列, <<: *LoadBalanceCH, filter: *FilterKR, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 美国均衡-散列, <<: *LoadBalanceCH, filter: *FilterUS, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
- { name: 欧盟均衡-散列, <<: *LoadBalanceCH, filter: *FilterEU, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin_1.png }
# 负载均衡-轮询
- { name: 香港均衡-轮询, <<: *LoadBalanceCR, filter: *FilterHK, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 台湾均衡-轮询, <<: *LoadBalanceCR, filter: *FilterTW, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 新加坡均衡-轮询, <<: *LoadBalanceCR, filter: *FilterSG, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 日本均衡-轮询, <<: *LoadBalanceCR, filter: *FilterJP, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 韩国均衡-轮询, <<: *LoadBalanceCR, filter: *FilterKR, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 美国均衡-轮询, <<: *LoadBalanceCR, filter: *FilterUS, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }
- { name: 欧盟均衡-轮询, <<: *LoadBalanceCR, filter: *FilterEU, hidden: true, icon: https://ghgo.xyz/https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Round_Robin.png }

# 远程规则集
rule-providers:
  fix-direct:
    <<: *ClassicalYaml
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/fix-direct.yaml
  download:
    <<: *ClassicalYaml
    url: https://ghgo.xyz/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Download/Download.yaml
  speedtest:
    <<: *ClassicalYaml
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/speedtest.yaml
  openai:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/openai.mrs
  telegram:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram.mrs
  twitter:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/twitter.mrs
  instagram:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/instagram.mrs
  youtube:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/youtube.mrs
  spotify:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/spotify.mrs
  netflix:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix.mrs
  disney:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/disney.mrs
  hbo:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/hbo.mrs
  tiktok:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/tiktok.mrs
  github:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/github.mrs
  onedrive:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/onedrive.mrs
  microsoft@cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/<EMAIL>
  microsoft:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/microsoft.mrs
  facebook:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/facebook.mrs
  google:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/google.mrs
  cloudflare-cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/cloudflare-cn.mrs
  category-public-tracker:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-public-tracker.mrs
  category-games:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-games.mrs
  category-media-cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-media-cn.mrs
  category-media:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-media.mrs
  category-cryptocurrency:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-cryptocurrency.mrs
  category-social-media-!cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-social-media-!cn.mrs
  category-ai-chat-!cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-ai-chat-!cn.mrs
  geolocation-!cn:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/geolocation-!cn.mrs
  private_domain:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/private.mrs
  cn_domain:
    <<: *DomainMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn.mrs
  # IP规则集
  facebook_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/facebook_ip.mrs
  google_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/google_ip.mrs
  netflix_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix_ip.mrs
  twitter_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/twitter_ip.mrs
  telegram_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram_ip.mrs
  private_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/private_ip.mrs
  cn_ip:
    <<: *IPcidrMrs
    url: https://ghgo.xyz/https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn_ip.mrs

# 路由
rules:
# >屏蔽QUIC
- AND,((DST-PORT,443),(NETWORK,UDP)),REJECT-DROP
# 常用必备
- PROCESS-NAME,sub_store_node,默认代理
- PROCESS-NAME,tornaco.apps.shortx,默认代理
- DOMAIN-SUFFIX,linux.do,默认代理
- PROCESS-NAME,docker,默认代理
- PROCESS-NAME,Cherry Studio.exe,AI平台
- PROCESS-NAME,com.google.android.apps.bard,AI平台
- PROCESS-NAME,com.tencent.androidqqmail,默认代理
- DOMAIN-SUFFIX,423down.com,默认代理
- DOMAIN-SUFFIX,grempt.com,默认代理
- DOMAIN-SUFFIX,subsub123456789.com,默认代理
- IP-CIDR,*************/31,REJECT-DROP,no-resolve
# 防御
- RULE-SET,category-public-tracker,REJECT-DROP
# 域名路由
- RULE-SET,download,直连
- RULE-SET,fix-direct,直连
- RULE-SET,private_ip,国内网站,no-resolve
- RULE-SET,speedtest,网速测试
- RULE-SET,openai,AI平台
- RULE-SET,category-ai-chat-!cn,AI平台
- RULE-SET,telegram,社交平台
- RULE-SET,twitter,社交平台
- RULE-SET,youtube,流媒体
- RULE-SET,spotify,流媒体
#  - RULE-SET,netflix,流媒体
#  - RULE-SET,disney,流媒体
#  - RULE-SET,hbo,流媒体
#  - RULE-SET,tiktok,流媒体
- RULE-SET,github,Github
- RULE-SET,onedrive,微软服务
- RULE-SET,microsoft@cn,国内网站
- RULE-SET,microsoft,微软服务
- RULE-SET,instagram,社交平台
- RULE-SET,facebook,社交平台
- RULE-SET,google,谷歌服务
- RULE-SET,cloudflare-cn,国内网站
- RULE-SET,category-games,游戏平台
- RULE-SET,category-media-cn,国内网站
- RULE-SET,category-media,国际新闻
- RULE-SET,category-social-media-!cn,社交平台
- RULE-SET,cn_domain,国内网站
- RULE-SET,geolocation-!cn,国外网站
- DOMAIN-REGEX,^[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+$,漏网之鱼
# IP路由
- RULE-SET,facebook_ip,社交平台,no-resolve
- RULE-SET,google_ip,谷歌服务,no-resolve
#  - RULE-SET,netflix_ip,NetFlix,no-resolve
- RULE-SET,twitter_ip,社交平台,no-resolve
- RULE-SET,telegram_ip,社交平台,no-resolve
- RULE-SET,cn_ip,国内网站,no-resolve
- MATCH,漏网之鱼
