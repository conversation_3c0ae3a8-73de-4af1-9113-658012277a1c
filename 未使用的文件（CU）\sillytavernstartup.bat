@echo off
echo 正在启动SillyTavern服务...

:: 定义文件路径（请根据实际情况修改）
set "START_BAT=e:\github\SillyTavern-Launcher\SillyTavern\Start.bat"
set "PAKE_EXE=C:\Program Files\Sillytavern\pake.exe"

:: 检查start.bat是否存在
if not exist "%START_BAT%" (
    echo 错误：找不到SillyTavern的启动文件 %START_BAT%
    pause
    exit /b 1
)

:: 检查pake.exe是否存在
if not exist "%PAKE_EXE%" (
    echo 错误：找不到Pake客户端 %PAKE_EXE%
    pause
    exit /b 1
)

:: 静默启动SillyTavern服务
start "" /B "%START_BAT%" >nul 2>&1

:: 等待服务启动
echo 等待SillyTavern服务启动中...
timeout /t 6 /nobreak >nul

:: 启动Pake客户端
echo 启动Pake客户端...
start "" "%PAKE_EXE%"

echo 启动完成！
exit /b 0
