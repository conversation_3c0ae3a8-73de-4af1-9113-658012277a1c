
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"5",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"G-V32RGE91HE"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"}],
  "tags":[{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":["macro",1],"vtp_configSettingsTable":["list",["map","parameter","send_page_view","parameterValue","true"]],"tag_id":4},{"function":"__html","once_per_event":true,"vtp_html":"\u003Cscript type=\"text\/gtmscript\"\u003E(function(a,e,b,f,g,c,d){a[b]=a[b]||function(){(a[b].q=a[b].q||[]).push(arguments)};c=e.createElement(f);c.async=1;c.src=\"https:\/\/www.clarity.ms\/tag\/\"+g+\"?ref\\x3dgtm2\";d=e.getElementsByTagName(f)[0];d.parentNode.insertBefore(c,d)})(window,document,\"clarity\",\"script\",\"c5hs7nqmfl\");\u003C\/script\u003E","vtp_supportDocumentWrite":false,"vtp_enableIframeMode":false,"vtp_enableEditJsMacroBehavior":false,"tag_id":5},{"function":"__html","metadata":["map"],"once_per_event":true,"vtp_html":"\u003Cscript type=\"text\/javascript\"\u003E(function(a,e,b,f,g,c,d){a[b]=a[b]||function(){(a[b].q=a[b].q||[]).push(arguments)};c=e.createElement(f);c.async=1;c.src=\"https:\/\/www.clarity.ms\/tag\/\"+g;d=e.getElementsByTagName(f)[0];d.parentNode.insertBefore(c,d)})(window,document,\"clarity\",\"script\",\"p95xpnwutz\");\u003C\/script\u003E","vtp_supportDocumentWrite":true,"vtp_enableIframeMode":false,"vtp_enableEditJsMacroBehavior":false,"vtp_usePostscribe":true,"tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"}],
  "rules":[[["if",0],["add",0,1,2]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"getProtocol",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"getHost",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"getPort",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"getPath",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"getFirstQueryParam",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"getFragment",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"removeFragment",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"l",[46,"u","v"],[66,"w",[2,[15,"b"],"keys",[7,[15,"v"]]],[46,[53,[43,[15,"u"],[15,"w"],[16,[15,"v"],[15,"w"]]]]]]],[50,"m",[46],[36,[7,[17,[17,[15,"d"],"SCHEMA"],"EP_SERVER_CONTAINER_URL"],[17,[17,[15,"d"],"SCHEMA"],"EP_TRANSPORT_URL"]]]],[50,"n",[46,"u"],[52,"v",["m"]],[65,"w",[15,"v"],[46,[53,[52,"x",[16,[15,"u"],[15,"w"]]],[22,[15,"x"],[46,[36,[15,"x"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",["require","getType"]],[52,"g",["require","internal.loadGoogleTag"]],[52,"h",["require","logToConsole"]],[52,"i",["require","makeNumber"]],[52,"j",["require","makeString"]],[52,"k",["require","makeTableMap"]],[52,"o",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["f",[15,"o"]],"string"],[24,[2,[15,"o"],"indexOf",[7,"-"]],0]],[46,[53,["h",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"o"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"p",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"q",[30,["k",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["l",[15,"p"],[15,"q"]],[52,"r",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"s",[30,["k",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["l",[15,"r"],[15,"s"]],[52,"t",[15,"p"]],["l",[15,"t"],[15,"r"]],[22,[30,[2,[15,"t"],"hasOwnProperty",[7,[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"u",[30,[16,[15,"t"],[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"]],[8]]],["l",[15,"u"],[30,["k",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"t"],[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"],[15,"u"]]]]],[2,[15,"d"],"convertParameters",[7,[15,"t"],[17,[15,"d"],"GOLD_BOOLEAN_FIELDS"],[51,"",[7,"u"],[36,[39,[20,"false",[2,["j",[15,"u"]],"toLowerCase",[7]]],false,[28,[28,[15,"u"]]]]]]]],[2,[15,"d"],"convertParameters",[7,[15,"t"],[17,[15,"d"],"GOLD_NUMERIC_FIELDS"],[51,"",[7,"u"],[36,["i",[15,"u"]]]]]],["g",[15,"o"],[8,"firstPartyUrl",["n",[15,"t"]]]],["e",[15,"o"],[15,"t"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__html",[46,"a"],[52,"b",["require","internal.injectHtml"]],["b",[17,[15,"a"],"html"],[17,[15,"a"],"gtmOnSuccess"],[17,[15,"a"],"gtmOnFailure"],[17,[15,"a"],"useIframe"],[17,[15,"a"],"supportDocumentWrite"]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"getFirstQueryParam",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"getProtocol",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"getHost",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"getPort",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"getPath",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"getExtension",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"getFragment",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"removeFragment",[7,["f",[15,"j"]]]]]]]]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46,"g","h","i"],[65,"j",[15,"h"],[46,[53,[22,[2,[15,"g"],"hasOwnProperty",[7,[15,"j"]]],[46,[53,[43,[15,"g"],[15,"j"],["i",[16,[15,"g"],[15,"j"]]]]]]]]]]],[52,"b",["require","Object"]],[52,"c",[2,[15,"b"],"freeze",[7,[8,"EP_FIRST_PARTY_COLLECTION","first_party_collection","EP_SERVER_CONTAINER_URL","server_container_url","EP_TRANSPORT_URL","transport_url","EP_USER_PROPERTIES","user_properties"]]]],[52,"d",[2,[15,"b"],"freeze",[7,[7,"allow_ad_personalization_signals","allow_direct_google_requests","allow_google_signals","cookie_update","ignore_referrer","update","first_party_collection","send_page_view"]]]],[52,"e",[2,[15,"b"],"freeze",[7,[7,"cookie_expires","event_timeout","session_duration","session_engaged_time","engagement_time_msec"]]]],[36,[8,"SCHEMA",[15,"c"],"GOLD_BOOLEAN_FIELDS",[15,"d"],"GOLD_NUMERIC_FIELDS",[15,"e"],"convertParameters",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"removeFragment",[15,"h"],"getProtocol",[15,"i"],"getHost",[15,"j"],"getPort",[15,"k"],"getPath",[15,"l"],"getExtension",[15,"m"],"getFragment",[15,"n"],"getFirstQueryParam",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__f":{"2":true}
,
"__googtag":{"1":10}
,
"__u":{"2":true}


}
,"blob":{"1":"5"}
,"permissions":{
"__c":{}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__html":{"unsafe_inject_arbitrary_html":{}}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}


}



,"security_groups":{
"customScripts":[
"__html"

]
,
"google":[
"__c"
,
"__e"
,
"__f"
,
"__googtag"
,
"__u"

]


}



};



try{
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var C=this||self,D=function(n,u){for(var x=n.split("."),t=C,q;x.length&&(q=x.shift());)x.length||u===void 0?t=t[q]&&t[q]!==Object.prototype[q]?t[q]:t[q]={}:t[q]=u};/*
 Copyright (c) 2014 Derek Brans, MIT license https://github.com/krux/postscribe/blob/master/LICENSE. Portions derived from simplehtmlparser, which is licensed under the Apache License, Version 2.0 */
var E,F=function(){};
(function(){function n(h,l){h=h||"";l=l||{};for(var y in u)u.hasOwnProperty(y)&&(l.N&&(l["fix_"+y]=!0),l.G=l.G||l["fix_"+y]);var z={comment:/^\x3c!--/,endTag:/^<\//,atomicTag:/^<\s*(script|style|noscript|iframe|textarea)[\s\/>]/i,startTag:/^</,chars:/^[^<]/},e={comment:function(){var a=h.indexOf("--\x3e");if(a>=0)return{content:h.substr(4,a),length:a+3}},endTag:function(){var a=h.match(t);if(a)return{tagName:a[1],length:a[0].length}},atomicTag:function(){var a=e.startTag();if(a){var b=h.slice(a.length);
if(b.match(new RegExp("</\\s*"+a.tagName+"\\s*>","i"))){var c=b.match(new RegExp("([\\s\\S]*?)</\\s*"+a.tagName+"\\s*>","i"));if(c)return{tagName:a.tagName,g:a.g,content:c[1],length:c[0].length+a.length}}}},startTag:function(){var a=h.match(x);if(a){var b={};a[2].replace(q,function(c,d){var k=arguments[2]||arguments[3]||arguments[4]||B.test(d)&&d||null,g=document.createElement("div");g.innerHTML=k;b[d]=g.textContent||g.innerText||k});return{tagName:a[1],g:b,s:!!a[3],length:a[0].length}}},chars:function(){var a=
h.indexOf("<");return{length:a>=0?a:h.length}}},f=function(){for(var a in z)if(z[a].test(h)){var b=e[a]();return b?(b.type=b.type||a,b.text=h.substr(0,b.length),h=h.slice(b.length),b):null}};l.G&&function(){var a=/^(AREA|BASE|BASEFONT|BR|COL|FRAME|HR|IMG|INPUT|ISINDEX|LINK|META|PARAM|EMBED)$/i,b=/^(COLGROUP|DD|DT|LI|OPTIONS|P|TD|TFOOT|TH|THEAD|TR)$/i,c=[];c.H=function(){return this[this.length-1]};c.v=function(m){var p=this.H();return p&&p.tagName&&p.tagName.toUpperCase()===m.toUpperCase()};c.V=function(m){for(var p=
0,w;w=this[p];p++)if(w.tagName===m)return!0;return!1};var d=function(m){m&&m.type==="startTag"&&(m.s=a.test(m.tagName)||m.s);return m},k=f,g=function(){h="</"+c.pop().tagName+">"+h},r={startTag:function(m){var p=m.tagName;p.toUpperCase()==="TR"&&c.v("TABLE")?(h="<TBODY>"+h,v()):l.oa&&b.test(p)&&c.V(p)?c.v(p)?g():(h="</"+m.tagName+">"+h,v()):m.s||c.push(m)},endTag:function(m){c.H()?l.W&&!c.v(m.tagName)?g():c.pop():l.W&&(k(),v())}},v=function(){var m=h,p=d(k());h=m;if(p&&r[p.type])r[p.type](p)};f=function(){v();
return d(k())}}();return{append:function(a){h+=a},ea:f,sa:function(a){for(var b;(b=f())&&(!a[b.type]||a[b.type](b)!==!1););},clear:function(){var a=h;h="";return a},ta:function(){return h},stack:[]}}var u=function(){var h={},l=this.document.createElement("div");l.innerHTML="<P><I></P></I>";h.va=l.innerHTML!=="<P><I></P></I>";l.innerHTML="<P><i><P></P></i></P>";h.ua=l.childNodes.length===2;return h}(),x=/^<([\-A-Za-z0-9_]+)((?:\s+[\w\-]+(?:\s*=?\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,
t=/^<\/([\-A-Za-z0-9_]+)[^>]*>/,q=/([\-A-Za-z0-9_]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,B=/^(checked|compact|declare|defer|disabled|ismap|multiple|nohref|noresize|noshade|nowrap|readonly|selected)$/i;n.supports=u;for(var A in u);E=n})();
(function(){function n(){}function u(e){return e!==void 0&&e!==null}function x(e,f,a){var b,c=e&&e.length||0;for(b=0;b<c;b++)f.call(a,e[b],b)}function t(e,f,a){for(var b in e)e.hasOwnProperty(b)&&f.call(a,b,e[b])}function q(e,f){t(f,function(a,b){e[a]=b});return e}function B(e,f){e=e||{};t(f,function(a,b){u(e[a])||(e[a]=b)});return e}function A(e){try{return y.call(e)}catch(a){var f=[];x(e,function(b){f.push(b)});return f}}var h={J:n,K:n,L:n,M:n,O:n,P:function(e){return e},done:n,error:function(e){throw e;
},fa:!1},l=this;if(!l.postscribe){var y=Array.prototype.slice,z=function(){function e(a,b,c){var d="data-ps-"+b;if(arguments.length===2){var k=a.getAttribute(d);return u(k)?String(k):k}u(c)&&c!==""?a.setAttribute(d,c):a.removeAttribute(d)}function f(a,b){var c=a.ownerDocument;q(this,{root:a,options:b,l:c.defaultView||c.parentWindow,i:c,o:E("",{N:!0}),u:[a],B:"",C:c.createElement(a.nodeName),j:[],h:[]});e(this.C,"proxyof",0)}f.prototype.write=function(){[].push.apply(this.h,arguments);for(var a;!this.m&&
this.h.length;)a=this.h.shift(),"function"===typeof a?this.U(a):this.D(a)};f.prototype.U=function(a){var b={type:"function",value:a.name||a.toString()};this.A(b);a.call(this.l,this.i);this.I(b)};f.prototype.D=function(a){this.o.append(a);for(var b,c=[],d,k;(b=this.o.ea())&&!(d=b&&"tagName"in b?!!~b.tagName.toLowerCase().indexOf("script"):!1)&&!(k=b&&"tagName"in b?!!~b.tagName.toLowerCase().indexOf("style"):!1);)c.push(b);this.ka(c);d&&this.X(b);k&&this.Y(b)};f.prototype.ka=function(a){var b=this.R(a);
b.F&&(b.Z=this.B+b.F,this.B+=b.proxy,this.C.innerHTML=b.Z,this.ia())};f.prototype.R=function(a){var b=this.u.length,c=[],d=[],k=[];x(a,function(g){c.push(g.text);if(g.g){if(!/^noscript$/i.test(g.tagName)){var r=b++;d.push(g.text.replace(/(\/?>)/," data-ps-id="+r+" $1"));g.g.id!=="ps-script"&&g.g.id!=="ps-style"&&k.push(g.type==="atomicTag"?"":"<"+g.tagName+" data-ps-proxyof="+r+(g.s?" />":">"))}}else d.push(g.text),k.push(g.type==="endTag"?g.text:"")});return{wa:a,raw:c.join(""),F:d.join(""),proxy:k.join("")}};
f.prototype.ia=function(){for(var a,b=[this.C];u(a=b.shift());){var c=a.nodeType===1;if(!c||!e(a,"proxyof")){c&&(this.u[e(a,"id")]=a,e(a,"id",null));var d=a.parentNode&&e(a.parentNode,"proxyof");d&&this.u[d].appendChild(a)}b.unshift.apply(b,A(a.childNodes))}};f.prototype.X=function(a){var b=this.o.clear();b&&this.h.unshift(b);a.src=a.g.src||a.g.ma;a.src&&this.j.length?this.m=a:this.A(a);var c=this;this.ja(a,function(){c.I(a)})};f.prototype.Y=function(a){var b=this.o.clear();b&&this.h.unshift(b);a.type=
a.g.type||a.g.TYPE||"text/css";this.la(a);b&&this.write()};f.prototype.la=function(a){var b=this.T(a);this.ba(b);a.content&&(b.styleSheet&&!b.sheet?b.styleSheet.cssText=a.content:b.appendChild(this.i.createTextNode(a.content)))};f.prototype.T=function(a){var b=this.i.createElement(a.tagName);b.setAttribute("type",a.type);t(a.g,function(c,d){b.setAttribute(c,d)});return b};f.prototype.ba=function(a){this.D('<span id="ps-style"/>');var b=this.i.getElementById("ps-style");b.parentNode.replaceChild(a,
b)};f.prototype.A=function(a){a.ca=this.h;this.h=[];this.j.unshift(a)};f.prototype.I=function(a){a!==this.j[0]?this.options.error({message:"Bad script nesting or script finished twice"}):(this.j.shift(),this.write.apply(this,a.ca),!this.j.length&&this.m&&(this.A(this.m),this.m=null))};f.prototype.ja=function(a,b){var c=this.S(a),d=this.ha(c),k=this.options.J;a.src&&(c.src=a.src,this.ga(c,d?k:function(){b();k()}));try{this.aa(c),a.src&&!d||b()}catch(g){this.options.error(g),b()}};f.prototype.S=function(a){var b=
this.i.createElement(a.tagName);t(a.g,function(c,d){b.setAttribute(c,d)});a.content&&(b.text=a.content);return b};f.prototype.aa=function(a){this.D('<span id="ps-script"/>');var b=this.i.getElementById("ps-script");b.parentNode.replaceChild(a,b)};f.prototype.ga=function(a,b){function c(){a=a.onload=a.onreadystatechange=a.onerror=null}var d=this.options.error;q(a,{onload:function(){c();b()},onreadystatechange:function(){/^(loaded|complete)$/.test(a.readyState)&&(c(),b())},onerror:function(){var k=
{message:"remote script failed "+a.src};c();d(k);b()}})};f.prototype.ha=function(a){return!/^script$/i.test(a.nodeName)||!!(this.options.fa&&a.src&&a.hasAttribute("async"))};return f}();l.postscribe=function(){function e(){var d=b.shift(),k;d&&(k=d[d.length-1],k.K(),d.stream=f.apply(null,d),k.L())}function f(d,k,g){function r(w){w=g.P(w);c.write(w);g.M(w)}c=new z(d,g);c.id=a++;c.name=g.name||c.id;var v=d.ownerDocument,m={close:v.close,open:v.open,write:v.write,writeln:v.writeln};q(v,{close:n,open:n,
write:function(){return r(A(arguments).join(""))},writeln:function(){return r(A(arguments).join("")+"\n")}});var p=c.l.onerror||n;c.l.onerror=function(w,G,H){g.error({qa:w+" - "+G+":"+H});p.apply(c.l,arguments)};c.write(k,function(){q(v,m);c.l.onerror=p;g.done();c=null;e()});return c}var a=0,b=[],c=null;return q(function(d,k,g){"function"===typeof g&&(g={done:g});g=B(g,h);d=/^#/.test(d)?l.document.getElementById(d.substr(1)):d.pa?d[0]:d;var r=[d,k,g];d.da={cancel:function(){r.stream?r.stream.abort():
r[1]=n}};g.O(r);b.push(r);c||e();return d.da},{streams:{},ra:b,na:z})}();F=l.postscribe}})();D("google_tag_manager_external.postscribe.installPostscribe",function(){var n=window.google_tag_manager;n&&(n.postscribe||(n.postscribe=window.postscribe||F))});D("google_tag_manager_external.postscribe.getPostscribe",function(){return window.google_tag_manager.postscribe});}).call(this);
} catch {}


var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=da(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ca(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},la;
if(typeof Object.setPrototypeOf=="function")la=Object.setPrototypeOf;else{var na;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;na=pa.a;break a}catch(a){}na=!1}la=na?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=la,ra=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Lq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ta=function(a){return a instanceof Array?a:sa(l(a))},va=function(a){return ua(a,a)},ua=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},wa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||wa});
var xa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ya=this||self;var za=function(a,b){this.type=a;this.data=b};var Aa=function(){this.map={};this.D={}};Aa.prototype.get=function(a){return this.map["dust."+a]};Aa.prototype.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};Aa.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Aa.prototype.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Ba=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Aa.prototype.xa=function(){return Ba(this,1)};Aa.prototype.qc=function(){return Ba(this,2)};Aa.prototype.Nb=function(){return Ba(this,3)};var Ca=function(){};Ca.prototype.reset=function(){};var Da=function(a,b){this.P=a;this.parent=b;this.D=this.J=void 0;this.Ic=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=new Aa};Da.prototype.add=function(a,b){Fa(this,a,b,!1)};var Fa=function(a,b,c,d){if(!a.Ic)if(d){var e=a.values;e.set(b,c);e.D["dust."+b]=!0}else a.values.set(b,c)};Da.prototype.set=function(a,b){this.Ic||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Da.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Da.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ga=function(a){var b=new Da(a.P,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};Da.prototype.be=function(){return this.P};Da.prototype.Wa=function(){this.Ic=!0};var Ha=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.Vl=a;this.Il=c===void 0?!1:c;this.debugInfo=[];this.D=b};ra(Ha,Error);var Ia=function(a){return a instanceof Ha?a:new Ha(a,void 0,!0)};function Ja(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=La(a,e.value),c instanceof za);e=d.next());return c}function La(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ia(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ta(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Ma=function(){this.J=new Ca;this.D=new Da(this.J)};k=Ma.prototype;k.be=function(){return this.J};k.execute=function(a){return this.Aj([a].concat(ta(xa.apply(1,arguments))))};k.Aj=function(){for(var a,b=l(xa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=La(this.D,c.value);return a};k.An=function(a){var b=xa.apply(1,arguments),c=Ga(this.D);c.D=a;for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=La(c,f.value);return d};k.Wa=function(){this.D.Wa()};var Oa=function(){this.za=!1;this.X=new Aa};k=Oa.prototype;k.get=function(a){return this.X.get(a)};k.set=function(a,b){this.za||this.X.set(a,b)};k.has=function(a){return this.X.has(a)};k.remove=function(a){this.za||this.X.remove(a)};k.xa=function(){return this.X.xa()};k.qc=function(){return this.X.qc()};k.Nb=function(){return this.X.Nb()};k.Wa=function(){this.za=!0};k.Ic=function(){return this.za};function Pa(){for(var a=Qa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Ra(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Qa,Sa;function Ta(a){Qa=Qa||Ra();Sa=Sa||Pa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Qa[m],Qa[n],Qa[p],Qa[q])}return b.join("")}
function Ua(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Sa[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Qa=Qa||Ra();Sa=Sa||Pa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Va={};function Wa(a,b){Va[a]=Va[a]||[];Va[a][b]=!0}function Ya(a){var b=Va[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Ta(c.join("")).replace(/\.+$/,"")}function Za(){for(var a=[],b=Va.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function $a(){}function ab(a){return typeof a==="function"}function cb(a){return typeof a==="string"}function db(a){return typeof a==="number"&&!isNaN(a)}function eb(a){return Array.isArray(a)?a:[a]}function fb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function gb(a,b){if(!db(a)||!db(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function hb(a,b){for(var c=new ib,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function jb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function kb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function lb(a){return Math.round(Number(a))||0}function mb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function nb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function ob(a){return a?a.replace(/^\s+|\s+$/g,""):""}function pb(){return new Date(Date.now())}function qb(){return pb().getTime()}var ib=function(){this.prefix="gtm.";this.values={}};ib.prototype.set=function(a,b){this.values[this.prefix+a]=b};ib.prototype.get=function(a){return this.values[this.prefix+a]};ib.prototype.contains=function(a){return this.get(a)!==void 0};
function rb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function sb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function tb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function ub(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function vb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function wb(a,b){var c=y;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function xb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var yb=/^\w{1,9}$/;function zb(a,b){a=a||{};b=b||",";var c=[];jb(a,function(d,e){yb.test(d)&&e&&c.push(d)});return c.join(b)}function Ab(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Cb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Db(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Eb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Fb=globalThis.trustedTypes,Gb;function Hb(){var a=null;if(!Fb)return a;try{var b=function(c){return c};a=Fb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Ib(){Gb===void 0&&(Gb=Hb());return Gb};var Jb=function(a){this.D=a};Jb.prototype.toString=function(){return this.D+""};function Kb(a){var b=a,c=Ib(),d=c?c.createScriptURL(b):b;return new Jb(d)}function Lb(a){if(a instanceof Jb)return a.D;throw Error("");};var Mb=va([""]),Nb=ua(["\x00"],["\\0"]),Ob=ua(["\n"],["\\n"]),Pb=ua(["\x00"],["\\u0000"]);function Qb(a){return a.toString().indexOf("`")===-1}Qb(function(a){return a(Mb)})||Qb(function(a){return a(Nb)})||Qb(function(a){return a(Ob)})||Qb(function(a){return a(Pb)});var Rb=function(a){this.D=a};Rb.prototype.toString=function(){return this.D};var Sb=function(a){this.Zo=a};function Tb(a){return new Sb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Ub=[Tb("data"),Tb("http"),Tb("https"),Tb("mailto"),Tb("ftp"),new Sb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Vb(a){var b;b=b===void 0?Ub:b;if(a instanceof Rb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Sb&&d.Zo(a))return new Rb(a)}}var Wb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Xb(a){var b;if(a instanceof Rb)if(a instanceof Rb)b=a.D;else throw Error("");else b=Wb.test(a)?a:void 0;return b};function Yb(a,b){var c=Xb(b);c!==void 0&&(a.action=c)};function Zb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var $b=function(a){this.D=a};$b.prototype.toString=function(){return this.D+""};var bc=function(){this.D=ac[0].toLowerCase()};bc.prototype.toString=function(){return this.D};function cc(a,b){var c=[new bc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof bc)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var dc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function ec(a){return a===null?"null":a===void 0?"undefined":a};var y=window,fc=window.history,A=document,gc=navigator;function hc(){var a;try{a=gc.serviceWorker}catch(b){return}return a}var ic=A.currentScript,jc=ic&&ic.src;function kc(a,b){var c=y[a];y[a]=c===void 0?b:c;return y[a]}function lc(a){return(gc.userAgent||"").indexOf(a)!==-1}function mc(){return lc("Firefox")||lc("FxiOS")}function nc(){return(lc("GSA")||lc("GoogleApp"))&&(lc("iPhone")||lc("iPad"))}function oc(){return lc("Edg/")||lc("EdgA/")||lc("EdgiOS/")}
var pc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},qc={onload:1,src:1,width:1,height:1,style:1};function rc(a,b,c){b&&jb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function sc(a,b,c,d,e){var f=A.createElement("script");rc(f,d,pc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Kb(ec(a));f.src=Lb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function tc(){if(jc){var a=jc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function uc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);rc(g,c,qc);d&&jb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function vc(a,b,c,d){return wc(a,b,c,d)}function xc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function yc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function C(a){y.setTimeout(a,0)}function zc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Ac(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Bc(a){var b=A.createElement("div"),c=b,d,e=ec("A<div>"+a+"</div>"),f=Ib(),g=f?f.createHTML(e):e;d=new $b(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof $b)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Cc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Dc(a,b,c){var d;try{d=gc.sendBeacon&&gc.sendBeacon(a)}catch(e){Wa("TAGGING",15)}d?b==null||b():wc(a,b,c)}function Ec(a,b){try{return gc.sendBeacon(a,b)}catch(c){Wa("TAGGING",15)}return!1}var Fc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Gc(a,b,c,d,e){if(Hc()){var f=Object.assign({},Fc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=y.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.mj)return e==null||e(),!1;if(b){var h=
Ec(a,b);h?d==null||d():e==null||e();return h}Ic(a,d,e);return!0}function Hc(){return typeof y.fetch==="function"}function Jc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Kc(){var a=y.performance;if(a&&ab(a.now))return a.now()}
function Lc(){var a,b=y.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Mc(){return y.performance||void 0}function Nc(){var a=y.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var wc=function(a,b,c,d){var e=new Image(1,1);rc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Ic=Dc;function Pc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Qc(a,b){return this.evaluate(a)===this.evaluate(b)}function Rc(a,b){return this.evaluate(a)||this.evaluate(b)}function Sc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Tc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Uc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=y.location.href;d instanceof Oa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Vc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Wc=function(a){if(a==null)return String(a);var b=Vc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Xc=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Yc=function(a){if(!a||Wc(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Xc(a,"constructor")&&!Xc(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
Xc(a,b)},Zc=function(a,b){var c=b||(Wc(a)=="array"?[]:{}),d;for(d in a)if(Xc(a,d)){var e=a[d];Wc(e)=="array"?(Wc(c[d])!="array"&&(c[d]=[]),c[d]=Zc(e,c[d])):Yc(e)?(Yc(c[d])||(c[d]={}),c[d]=Zc(e,c[d])):c[d]=e}return c};function $c(a){if(a==void 0||Array.isArray(a)||Yc(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ad(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var bd=function(a){a=a===void 0?[]:a;this.X=new Aa;this.values=[];this.za=!1;for(var b in a)a.hasOwnProperty(b)&&(ad(b)?this.values[Number(b)]=a[Number(b)]:this.X.set(b,a[b]))};k=bd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof bd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.za)if(a==="length"){if(!ad(b))throw Ia(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ad(a)?this.values[Number(a)]=b:this.X.set(a,b)};k.get=function(a){return a==="length"?this.length():ad(a)?this.values[Number(a)]:this.X.get(a)};k.length=function(){return this.values.length};k.xa=function(){for(var a=this.X.xa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.qc=function(){for(var a=this.X.qc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Nb=function(){for(var a=this.X.Nb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){ad(a)?delete this.values[Number(a)]:this.za||this.X.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ta(xa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=xa.apply(2,arguments);return b===void 0&&c.length===0?new bd(this.values.splice(a)):new bd(this.values.splice.apply(this.values,[a,b||0].concat(ta(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ta(xa.apply(0,arguments)))};k.has=function(a){return ad(a)&&this.values.hasOwnProperty(a)||this.X.has(a)};k.Wa=function(){this.za=!0;Object.freeze(this.values)};k.Ic=function(){return this.za};
function cd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var dd=function(a,b){this.functionName=a;this.ae=b;this.X=new Aa;this.za=!1};k=dd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new bd(this.xa())};k.invoke=function(a){return this.ae.call.apply(this.ae,[new ed(this,a)].concat(ta(xa.apply(1,arguments))))};k.sb=function(a){var b=xa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ta(b)))}catch(c){}};k.get=function(a){return this.X.get(a)};
k.set=function(a,b){this.za||this.X.set(a,b)};k.has=function(a){return this.X.has(a)};k.remove=function(a){this.za||this.X.remove(a)};k.xa=function(){return this.X.xa()};k.qc=function(){return this.X.qc()};k.Nb=function(){return this.X.Nb()};k.Wa=function(){this.za=!0};k.Ic=function(){return this.za};var fd=function(a,b){dd.call(this,a,b)};ra(fd,dd);var gd=function(a,b){dd.call(this,a,b)};ra(gd,dd);var ed=function(a,b){this.ae=a;this.K=b};
ed.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?La(b,a):a};ed.prototype.getName=function(){return this.ae.getName()};ed.prototype.be=function(){return this.K.be()};var hd=function(){this.map=new Map};hd.prototype.set=function(a,b){this.map.set(a,b)};hd.prototype.get=function(a){return this.map.get(a)};var id=function(){this.keys=[];this.values=[]};id.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};id.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function jd(){try{return Map?new hd:new id}catch(a){return new id}};var kd=function(a){if(a instanceof kd)return a;if($c(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};kd.prototype.getValue=function(){return this.value};kd.prototype.toString=function(){return String(this.value)};var md=function(a){this.promise=a;this.za=!1;this.X=new Aa;this.X.set("then",ld(this));this.X.set("catch",ld(this,!0));this.X.set("finally",ld(this,!1,!0))};k=md.prototype;k.get=function(a){return this.X.get(a)};k.set=function(a,b){this.za||this.X.set(a,b)};k.has=function(a){return this.X.has(a)};k.remove=function(a){this.za||this.X.remove(a)};k.xa=function(){return this.X.xa()};k.qc=function(){return this.X.qc()};k.Nb=function(){return this.X.Nb()};
var ld=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new fd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof fd||(d=void 0);e instanceof fd||(e=void 0);var f=Ga(this.K),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new kd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new md(h)})};md.prototype.Wa=function(){this.za=!0};md.prototype.Ic=function(){return this.za};function nd(a,b,c){var d=jd(),e=function(g,h){for(var m=g.xa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof bd){var m=[];d.set(g,m);for(var n=g.xa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof md)return g.promise.then(function(u){return nd(u,b,1)},function(u){return Promise.reject(nd(u,b,1))});if(g instanceof Oa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof fd){var r=function(){for(var u=
xa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=od(u[w],b,c);var x=new Da(b?b.be():new Ca);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ta(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof kd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function od(a,b,c){var d=jd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||kb(g)){var m=new bd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(Yc(g)){var p=new Oa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new fd("",function(){for(var u=xa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=nd(this.evaluate(u[w]),b,c);return f((0,this.K.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new kd(g)};return f(a)};var pd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof bd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new bd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new bd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new bd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ta(xa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ia(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ia(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ia(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ia(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=cd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new bd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=cd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ta(xa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ta(xa.apply(1,arguments)))}};var qd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},rd=new za("break"),sd=new za("continue");function td(a,b){return this.evaluate(a)+this.evaluate(b)}function ud(a,b){return this.evaluate(a)&&this.evaluate(b)}
function vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof bd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ia(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=nd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ia(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(qd.hasOwnProperty(e)){var m=2;m=1;var n=nd(f,void 0,m);return od(d[e].apply(d,n),this.K)}throw Ia(Error("TypeError: "+e+" is not a function"));}if(d instanceof bd){if(d.has(e)){var p=d.get(String(e));if(p instanceof fd){var q=cd(f);return p.invoke.apply(p,[this.K].concat(ta(q)))}throw Ia(Error("TypeError: "+e+" is not a function"));}if(pd.supportedMethods.indexOf(e)>=
0){var r=cd(f);return pd[e].call.apply(pd[e],[d,this.K].concat(ta(r)))}}if(d instanceof fd||d instanceof Oa||d instanceof md){if(d.has(e)){var t=d.get(e);if(t instanceof fd){var u=cd(f);return t.invoke.apply(t,[this.K].concat(ta(u)))}throw Ia(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof fd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof kd&&e==="toString")return d.toString();throw Ia(Error("TypeError: Object has no '"+
e+"' property."));}function wd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function xd(){var a=xa.apply(0,arguments),b=Ga(this.K),c=Ja(b,a);if(c instanceof za)return c}function yd(){return rd}function zd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof za)return d}}
function Ad(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Fa(a,c,d,!0)}}}function Bd(){return sd}function Cd(a,b){return new za(a,this.evaluate(b))}function Dd(a,b){for(var c=xa.apply(2,arguments),d=new bd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ta(c));this.K.add(a,this.evaluate(g))}function Ed(a,b){return this.evaluate(a)/this.evaluate(b)}
function Fd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof kd,f=d instanceof kd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Gd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Hd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ja(f,d);if(g instanceof za){if(g.type==="break")break;if(g.type==="return")return g}}}
function Id(a,b,c){if(typeof b==="string")return Hd(a,function(){return b.length},function(f){return f},c);if(b instanceof Oa||b instanceof md||b instanceof bd||b instanceof fd){var d=b.xa(),e=d.length;return Hd(a,function(){return e},function(f){return d[f]},c)}}function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Id(function(h){g.set(d,h);return g},e,f)}
function Kd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Id(function(h){var m=Ga(g);Fa(m,d,h,!0);return m},e,f)}function Ld(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Id(function(h){var m=Ga(g);m.add(d,h);return m},e,f)}function Md(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Nd(function(h){g.set(d,h);return g},e,f)}
function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Nd(function(h){var m=Ga(g);Fa(m,d,h,!0);return m},e,f)}function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Nd(function(h){var m=Ga(g);m.add(d,h);return m},e,f)}
function Nd(a,b,c){if(typeof b==="string")return Hd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof bd)return Hd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ia(Error("The value is not iterable."));}
function Qd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof bd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=Ga(g);for(e(g,m);La(m,b);){var n=Ja(m,h);if(n instanceof za){if(n.type==="break")break;if(n.type==="return")return n}var p=Ga(g);e(m,p);La(p,c);m=p}}
function Rd(a,b){var c=xa.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof bd))throw Error("Error: non-List value given for Fn argument names.");return new fd(a,function(){return function(){var f=xa.apply(0,arguments),g=Ga(d);g.D===void 0&&(g.D=this.K.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new bd(h));var r=Ja(g,c);if(r instanceof za)return r.type===
"return"?r.data:r}}())}function Sd(a){var b=this.evaluate(a),c=this.K;if(Td&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Ud(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ia(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Oa||d instanceof md||d instanceof bd||d instanceof fd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ad(e)&&(c=d[e]);else if(d instanceof kd)return;return c}function Vd(a,b){return this.evaluate(a)>this.evaluate(b)}function Wd(a,b){return this.evaluate(a)>=this.evaluate(b)}
function Xd(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof kd&&(c=c.getValue());d instanceof kd&&(d=d.getValue());return c===d}function Yd(a,b){return!Xd.call(this,a,b)}function Zd(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ja(this.K,d);if(e instanceof za)return e}var Td=!1;
function $d(a,b){return this.evaluate(a)<this.evaluate(b)}function ae(a,b){return this.evaluate(a)<=this.evaluate(b)}function be(){for(var a=new bd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function ce(){for(var a=new Oa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function de(a,b){return this.evaluate(a)%this.evaluate(b)}
function ee(a,b){return this.evaluate(a)*this.evaluate(b)}function fe(a){return-this.evaluate(a)}function ge(a){return!this.evaluate(a)}function he(a,b){return!Fd.call(this,a,b)}function ie(){return null}function je(a,b){return this.evaluate(a)||this.evaluate(b)}function ke(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function le(a){return this.evaluate(a)}function me(){return xa.apply(0,arguments)}function ne(a){return new za("return",this.evaluate(a))}
function oe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ia(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof fd||d instanceof bd||d instanceof Oa)&&d.set(String(e),f);return f}function pe(a,b){return this.evaluate(a)-this.evaluate(b)}
function qe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof za){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof za&&(g.type==="return"||g.type==="continue")))return g}
function re(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function se(a){var b=this.evaluate(a);return b instanceof fd?"function":typeof b}function te(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function ue(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ja(this.K,e);if(f instanceof za){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ja(this.K,e);if(g instanceof za){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function ve(a){return~Number(this.evaluate(a))}function we(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function xe(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function ye(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function ze(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ae(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Be(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ce(){}
function De(a,b,c){try{var d=this.evaluate(b);if(d instanceof za)return d}catch(h){if(!(h instanceof Ha&&h.Il))throw h;var e=Ga(this.K);a!==""&&(h instanceof Ha&&(h=h.Vl),e.add(a,new kd(h)));var f=this.evaluate(c),g=Ja(e,f);if(g instanceof za)return g}}function Ee(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ha&&f.Il))throw f;c=f}var e=this.evaluate(b);if(e instanceof za)return e;if(c)throw c;if(d instanceof za)return d};var Ge=function(){this.D=new Ma;Fe(this)};Ge.prototype.execute=function(a){return this.D.Aj(a)};var Fe=function(a){var b=function(c,d){var e=new gd(String(c),d);e.Wa();a.D.D.set(String(c),e)};b("map",ce);b("and",Pc);b("contains",Sc);b("equals",Qc);b("or",Rc);b("startsWith",Tc);b("variable",Uc)};var Ie=function(){this.J=!1;this.D=new Ma;He(this);this.J=!0};Ie.prototype.execute=function(a){return Je(this.D.Aj(a))};var Ke=function(a,b,c){return Je(a.D.An(b,c))};Ie.prototype.Wa=function(){this.D.Wa()};
var He=function(a){var b=function(c,d){var e=String(c),f=new gd(e,d);f.Wa();a.D.D.set(e,f)};b(0,td);b(1,ud);b(2,vd);b(3,wd);b(56,ze);b(57,we);b(58,ve);b(59,Be);b(60,xe);b(61,ye);b(62,Ae);b(53,xd);b(4,yd);b(5,zd);b(68,De);b(52,Ad);b(6,Bd);b(49,Cd);b(7,be);b(8,ce);b(9,zd);b(50,Dd);b(10,Ed);b(12,Fd);b(13,Gd);b(67,Ee);b(51,Rd);b(47,Jd);b(54,Kd);b(55,Ld);b(63,Qd);b(64,Md);b(65,Od);b(66,Pd);b(15,Sd);b(16,Ud);b(17,Ud);b(18,Vd);b(19,Wd);b(20,Xd);b(21,Yd);b(22,Zd);b(23,$d);b(24,ae);b(25,de);b(26,ee);b(27,
fe);b(28,ge);b(29,he);b(45,ie);b(30,je);b(32,ke);b(33,ke);b(34,le);b(35,le);b(46,me);b(36,ne);b(43,oe);b(37,pe);b(38,qe);b(39,re);b(40,se);b(44,Ce);b(41,te);b(42,ue)};Ie.prototype.be=function(){return this.D.be()};function Je(a){if(a instanceof za||a instanceof fd||a instanceof bd||a instanceof Oa||a instanceof md||a instanceof kd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Le=function(a){this.message=a};function Me(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Le("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Ne(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Oe=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Pe(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Me(e)+c}a<<=2;d||(a|=32);return c=""+Me(a|b)+c};var Qe=function(){function a(b){return{toString:function(){return b}}}return{wm:a("consent"),Nj:a("convert_case_to"),Oj:a("convert_false_to"),Pj:a("convert_null_to"),Qj:a("convert_true_to"),Rj:a("convert_undefined_to"),Sp:a("debug_mode_metadata"),Da:a("function"),li:a("instance_name"),Dn:a("live_only"),En:a("malware_disabled"),METADATA:a("metadata"),In:a("original_activity_id"),lq:a("original_vendor_template_id"),kq:a("once_on_load"),Hn:a("once_per_event"),kl:a("once_per_load"),mq:a("priority_override"),
qq:a("respected_consent_types"),vl:a("setup_tags"),eh:a("tag_id"),Al:a("teardown_tags")}}();var of;var pf=[],qf=[],rf=[],sf=[],tf=[],uf,vf,wf;function xf(a){wf=wf||a}
function yf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)pf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)sf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)rf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||zf(p[r])}qf.push(p)}}
function zf(a){}var Af,Bf=[],Cf=[];function Df(a,b){var c={};c[Qe.Da]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Ef(a,b,c){try{return vf(Ff(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Gf(a){var b=a[Qe.Da];if(!b)throw Error("Error: No function name given for function call.");return!!uf[b]}
var Ff=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Hf(a[e],b,c));return d},Hf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Hf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=pf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Qe.li]);try{var m=Ff(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=If(m,{event:b,index:f,type:2,
name:h});Af&&(d=Af.bo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Hf(a[n],b,c)]=Hf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Hf(a[q],b,c);wf&&(p=p||wf.Wo(r));d.push(r)}return wf&&p?wf.io(d):d.join("");case "escape":d=Hf(a[1],b,c);if(wf&&Array.isArray(a[1])&&a[1][0]==="macro"&&wf.Xo(a))return wf.pp(d);d=String(d);for(var t=2;t<a.length;t++)Xe[a[t]]&&(d=Xe[a[t]](d));return d;
case "tag":var u=a[1];if(!sf[u])throw Error("Unable to resolve tag reference "+u+".");return{Ml:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Qe.Da]=a[1];var w=Ef(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},If=function(a,b){var c=a[Qe.Da],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=uf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Bf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&vb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=pf[q];break;case 1:r=sf[q];break;default:n="";break a}var t=r&&r[Qe.li];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Cf.indexOf(c)===-1){Cf.push(c);
var x=qb();u=e(g);var z=qb()-x,B=qb();v=of(c,h,b);w=z-(qb()-B)}else if(e&&(u=e(g)),!e||f)v=of(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),$c(u)?(Array.isArray(u)?Array.isArray(v):Yc(u)?Yc(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Jf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Jf,Error);Jf.prototype.getMessage=function(){return this.message};function Kf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Kf(a[c],b[c])}};function Lf(){return function(a,b){var c;var d=Mf;a instanceof Ha?(a.D=d,c=a):c=new Ha(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Mf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)db(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Nf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Of(a),f=0;f<qf.length;f++){var g=qf[f],h=Pf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<sf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Pf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Of(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Ef(rf[c],a));return b[c]}};function Qf(a,b){b[Qe.Nj]&&typeof a==="string"&&(a=b[Qe.Nj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Qe.Pj)&&a===null&&(a=b[Qe.Pj]);b.hasOwnProperty(Qe.Rj)&&a===void 0&&(a=b[Qe.Rj]);b.hasOwnProperty(Qe.Qj)&&a===!0&&(a=b[Qe.Qj]);b.hasOwnProperty(Qe.Oj)&&a===!1&&(a=b[Qe.Oj]);return a};var Rf=function(){this.D={}},Tf=function(a,b){var c=Sf.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ta(xa.apply(0,arguments)))})};function Uf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Jf(c,d,g);}}
function Vf(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ta(xa.apply(1,arguments))));Uf(e,b,d,g);Uf(f,b,d,g)}}}};var Zf=function(){var a=data.permissions||{},b=Wf.ctid,c=this;this.J={};this.D=new Rf;var d={},e={},f=Vf(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ta(xa.apply(1,arguments)))):{}});jb(a,function(g,h){function m(p){var q=xa.apply(1,arguments);if(!n[p])throw Xf(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ta(q)))}var n={};jb(h,function(p,q){var r=Yf(p,q);n[p]=r.assert;d[p]||(d[p]=r.R);r.Fl&&!e[p]&&(e[p]=r.Fl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw Xf(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ta(t.slice(1))))}})},$f=function(a){return Sf.J[a]||function(){}};
function Yf(a,b){var c=Df(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=Xf;try{return If(c)}catch(d){return{assert:function(e){throw new Jf(e,{},"Permission "+e+" is unknown.");},R:function(){throw new Jf(a,{},"Permission "+a+" is unknown.");}}}}function Xf(a,b,c){return new Jf(a,b,c)};var ag=!1;var bg={};bg.lm=mb('');bg.oo=mb('');function gg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var hg=[],ig={};function jg(a){return hg[a]===void 0?!1:hg[a]};var kg=[];function lg(a){switch(a){case 1:return 0;case 38:return 13;case 50:return 10;case 51:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 14;case 114:return 12;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function mg(a,b){kg[a]=b;var c=lg(a);c!==void 0&&(hg[c]=b)}function E(a){mg(a,!0)}E(39);E(34);E(35);E(36);
E(56);E(145);E(18);
E(153);E(144);E(74);E(120);
E(58);E(5);E(111);
E(139);E(87);E(92);E(117);
E(159);E(132);E(20);
E(72);E(113);E(154);
E(116);mg(23,!1),E(24);
ig[1]=gg('1',6E4);ig[3]=gg('10',1);ig[2]=gg('',50);E(29);
ng(26,25);E(9);
E(91);E(140);E(123);

E(157);
E(136);E(127);
E(27);E(69);E(70);
E(135);E(51);
E(50);E(95);E(86);
E(112);E(63);E(152);
E(101);
E(108);E(134);
E(115);E(96);E(31);
E(22);E(55);E(14);E(150);
E(151);E(97);
E(11);E(15);
E(98);E(99),E(98);
E(106);E(76);E(77);
E(81);E(79);E(28);E(80);E(90);E(118);
E(13);E(161);function H(a){return!!kg[a]}
function ng(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?E(b):E(a)};var pg={},qg=(pg.uaa=!0,pg.uab=!0,pg.uafvl=!0,pg.uamb=!0,pg.uam=!0,pg.uap=!0,pg.uapv=!0,pg.uaw=!0,pg);
var yg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!wg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!xg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?vb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},xg=/^[a-z$_][\w-$]*$/i,wg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var zg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ag(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Bg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Cg=new ib;function Dg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Cg.get(e);f||(f=new RegExp(b,d),Cg.set(e,f));return f.test(a)}catch(g){return!1}}function Eg(a,b){return String(a).indexOf(String(b))>=0}
function Fg(a,b){return String(a)===String(b)}function Gg(a,b){return Number(a)>=Number(b)}function Hg(a,b){return Number(a)<=Number(b)}function Ig(a,b){return Number(a)>Number(b)}function Jg(a,b){return Number(a)<Number(b)}function Kg(a,b){return vb(String(a),String(b))};var Rg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Sg={Fn:"function",PixieMap:"Object",List:"Array"};
function Ug(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Rg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof fd?n="Fn":m instanceof bd?n="List":m instanceof Oa?n="PixieMap":m instanceof md?n="PixiePromise":m instanceof kd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Sg[n]||n)+", which does not match required type ")+
((Sg[h]||h)+"."));}}}function I(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof fd?d.push("function"):g instanceof bd?d.push("Array"):g instanceof Oa?d.push("Object"):g instanceof md?d.push("Promise"):g instanceof kd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Vg(a){return a instanceof Oa}function Wg(a){return Vg(a)||a===null||Xg(a)}
function Yg(a){return a instanceof fd}function Zg(a){return Yg(a)||a===null||Xg(a)}function $g(a){return a instanceof bd}function ah(a){return a instanceof kd}function bh(a){return typeof a==="string"}function ch(a){return bh(a)||a===null||Xg(a)}function dh(a){return typeof a==="boolean"}function eh(a){return dh(a)||Xg(a)}function fh(a){return dh(a)||a===null||Xg(a)}function gh(a){return typeof a==="number"}function Xg(a){return a===void 0};function hh(a){return""+a}
function ih(a,b){var c=[];return c};function jh(a,b){var c=new fd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ia(g);}});c.Wa();return c}
function kh(a,b){var c=new Oa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ab(e)?c.set(d,jh(a+"_"+d,e)):Yc(e)?c.set(d,kh(a+"_"+d,e)):(db(e)||cb(e)||typeof e==="boolean")&&c.set(d,e)}c.Wa();return c};function lh(a,b){if(!bh(a))throw I(this.getName(),["string"],arguments);if(!ch(b))throw I(this.getName(),["string","undefined"],arguments);var c={},d=new Oa;return d=kh("AssertApiSubject",
c)};function mh(a,b){if(!ch(b))throw I(this.getName(),["string","undefined"],arguments);if(a instanceof md)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Oa;return d=kh("AssertThatSubject",c)};function nh(a){return function(){for(var b=xa.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(nd(b[e],d));return od(a.apply(null,c))}}function oh(){for(var a=Math,b=ph,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=nh(a[e].bind(a)))}return c};function qh(a){return a!=null&&vb(a,"__cvt_")};function rh(a){var b;return b};function sh(a){var b;if(!bh(a))throw I(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function th(a){try{return encodeURI(a)}catch(b){}};function uh(a){try{return encodeURIComponent(String(a))}catch(b){}};function zh(a){if(!ch(a))throw I(this.getName(),["string|undefined"],arguments);};function Ah(a,b){if(!gh(a)||!gh(b))throw I(this.getName(),["number","number"],arguments);return gb(a,b)};function Bh(){return(new Date).getTime()};function Ch(a){if(a===null)return"null";if(a instanceof bd)return"array";if(a instanceof fd)return"function";if(a instanceof kd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Dh(a){function b(c){return function(d){try{return c(d)}catch(e){(ag||bg.lm)&&a.call(this,e.message)}}}return{parse:b(function(c){return od(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(nd(c))}),publicName:"JSON"}};function Eh(a){return lb(nd(a,this.K))};function Fh(a){return Number(nd(a,this.K))};function Gh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Hh(a,b,c){var d=null,e=!1;if(!$g(a)||!bh(b)||!bh(c))throw I(this.getName(),["Array","string","string"],arguments);d=new Oa;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof Oa&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var ph="floor ceil round max min abs pow sqrt".split(" ");function Ih(){var a={};return{Bo:function(b){return a.hasOwnProperty(b)?a[b]:void 0},im:function(b,c){a[b]=c},reset:function(){a={}}}}function Jh(a,b){return function(){return fd.prototype.invoke.apply(a,[b].concat(ta(xa.apply(0,arguments))))}}
function Kh(a,b){if(!bh(a))throw I(this.getName(),["string","any"],arguments);}
function Lh(a,b){if(!bh(a)||!Vg(b))throw I(this.getName(),["string","PixieMap"],arguments);};var Mh={};var Nh=function(a){var b=new Oa;if(a instanceof bd)for(var c=a.xa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof fd)for(var f=a.xa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Mh.keys=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Nh(a);if(a instanceof Oa||a instanceof md)return new bd(a.xa());return new bd};
Mh.values=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Nh(a);if(a instanceof Oa||a instanceof md)return new bd(a.qc());return new bd};
Mh.entries=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Nh(a);if(a instanceof Oa||a instanceof md)return new bd(a.Nb().map(function(b){return new bd(b)}));return new bd};
Mh.freeze=function(a){(a instanceof Oa||a instanceof md||a instanceof bd||a instanceof fd)&&a.Wa();return a};Mh.delete=function(a,b){if(a instanceof Oa&&!a.Ic())return a.remove(b),!0;return!1};function L(a,b){var c=xa.apply(2,arguments),d=a.K.D;if(!d)throw Error("Missing program state.");if(d.wp){try{d.Gl.apply(null,[b].concat(ta(c)))}catch(e){throw Wa("TAGGING",21),e;}return}d.Gl.apply(null,[b].concat(ta(c)))};var Oh=function(){this.J={};this.D={};this.O=!0;};Oh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Oh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Oh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:ab(b)?jh(a,b):kh(a,b)};function Ph(a,b){var c=void 0;return c};function Qh(a,b){}Qh.M="internal.safeInvoke";function Rh(){var a={};
return a};var N={m:{Ha:"ad_personalization",U:"ad_storage",V:"ad_user_data",aa:"analytics_storage",Tb:"region",da:"consent_updated",fg:"wait_for_update",Bm:"app_remove",Cm:"app_store_refund",Dm:"app_store_subscription_cancel",Em:"app_store_subscription_convert",Fm:"app_store_subscription_renew",Gm:"consent_update",Vj:"add_payment_info",Wj:"add_shipping_info",wd:"add_to_cart",xd:"remove_from_cart",Xj:"view_cart",Kc:"begin_checkout",yd:"select_item",Vb:"view_item_list",wc:"select_promotion",Wb:"view_promotion",
eb:"purchase",zd:"refund",ob:"view_item",Yj:"add_to_wishlist",Hm:"exception",Im:"first_open",Jm:"first_visit",na:"gtag.config",ub:"gtag.get",Km:"in_app_purchase",Lc:"page_view",Lm:"screen_view",Mm:"session_start",Nm:"source_update",Om:"timing_complete",Pm:"track_social",Bd:"user_engagement",Qm:"user_id_update",qe:"gclid_link_decoration_source",se:"gclid_storage_source",Xb:"gclgb",fb:"gclid",Zj:"gclid_len",Cd:"gclgs",Dd:"gcllp",Ed:"gclst",sa:"ads_data_redaction",te:"gad_source",ue:"gad_source_src",
Mc:"gclid_url",bk:"gclsrc",ve:"gbraid",Fd:"wbraid",Ba:"allow_ad_personalization_signals",lg:"allow_custom_scripts",we:"allow_direct_google_requests",mg:"allow_display_features",ng:"allow_enhanced_conversions",vb:"allow_google_signals",Pa:"allow_interest_groups",Rm:"app_id",Sm:"app_installer_id",Tm:"app_name",Um:"app_version",Yb:"auid",Vm:"auto_detection_enabled",Nc:"aw_remarketing",Fh:"aw_remarketing_only",og:"discount",pg:"aw_feed_country",qg:"aw_feed_language",oa:"items",rg:"aw_merchant_id",dk:"aw_basket_type",
xe:"campaign_content",ye:"campaign_id",ze:"campaign_medium",Ae:"campaign_name",Be:"campaign",Ce:"campaign_source",De:"campaign_term",Db:"client_id",ek:"rnd",Gh:"consent_update_type",Wm:"content_group",Xm:"content_type",Eb:"conversion_cookie_prefix",Ee:"conversion_id",La:"conversion_linker",Hh:"conversion_linker_disabled",Oc:"conversion_api",sg:"cookie_deprecation",hb:"cookie_domain",ib:"cookie_expires",pb:"cookie_flags",Pc:"cookie_name",Fb:"cookie_path",Za:"cookie_prefix",xc:"cookie_update",Gd:"country",
Qa:"currency",Ih:"customer_buyer_stage",Fe:"customer_lifetime_value",Jh:"customer_loyalty",Kh:"customer_ltv_bucket",Ge:"custom_map",Lh:"gcldc",Qc:"dclid",fk:"debug_mode",wa:"developer_id",Ym:"disable_merchant_reported_purchases",Rc:"dc_custom_params",Zm:"dc_natural_search",gk:"dynamic_event_settings",hk:"affiliation",ug:"checkout_option",Mh:"checkout_step",ik:"coupon",He:"item_list_name",Nh:"list_name",bn:"promotions",Ie:"shipping",Oh:"tax",vg:"engagement_time_msec",wg:"enhanced_client_id",xg:"enhanced_conversions",
jk:"enhanced_conversions_automatic_settings",yg:"estimated_delivery_date",Ph:"euid_logged_in_state",Je:"event_callback",dn:"event_category",Gb:"event_developer_id_string",fn:"event_label",Sc:"event",zg:"event_settings",Ag:"event_timeout",gn:"description",hn:"fatal",jn:"experiments",Qh:"firebase_id",Hd:"first_party_collection",Bg:"_x_20",ac:"_x_19",kk:"fledge_drop_reason",lk:"fledge",mk:"flight_error_code",nk:"flight_error_message",pk:"fl_activity_category",qk:"fl_activity_group",Rh:"fl_advertiser_id",
rk:"fl_ar_dedupe",Ke:"match_id",sk:"fl_random_number",tk:"tran",uk:"u",Cg:"gac_gclid",Id:"gac_wbraid",vk:"gac_wbraid_multiple_conversions",wk:"ga_restrict_domain",xk:"ga_temp_client_id",kn:"ga_temp_ecid",Tc:"gdpr_applies",yk:"geo_granularity",yc:"value_callback",bc:"value_key",Uc:"google_analysis_params",Jd:"_google_ng",Kd:"google_signals",zk:"google_tld",Le:"gpp_sid",Me:"gpp_string",Dg:"groups",Ak:"gsa_experiment_id",Ne:"gtag_event_feature_usage",Bk:"gtm_up",zc:"iframe_state",Oe:"ignore_referrer",
Sh:"internal_traffic_results",Ac:"is_legacy_converted",Bc:"is_legacy_loaded",Eg:"is_passthrough",Vc:"_lps",qb:"language",Fg:"legacy_developer_id_string",Ma:"linker",Ld:"accept_incoming",fc:"decorate_forms",ja:"domains",Cc:"url_position",Gg:"merchant_feed_label",Hg:"merchant_feed_language",Ig:"merchant_id",Ck:"method",ln:"name",Dk:"navigation_type",Pe:"new_customer",Jg:"non_interaction",mn:"optimize_id",Ek:"page_hostname",Qe:"page_path",Ra:"page_referrer",wb:"page_title",Fk:"passengers",Gk:"phone_conversion_callback",
nn:"phone_conversion_country_code",Hk:"phone_conversion_css_class",on:"phone_conversion_ids",Ik:"phone_conversion_number",Jk:"phone_conversion_options",pn:"_platinum_request_status",Th:"_protected_audience_enabled",Re:"quantity",Kg:"redact_device_info",Uh:"referral_exclusion_definition",Vp:"_request_start_time",Ib:"restricted_data_processing",qn:"retoken",rn:"sample_rate",Vh:"screen_name",Dc:"screen_resolution",Kk:"_script_source",sn:"search_term",jb:"send_page_view",Wc:"send_to",Xc:"server_container_url",
Se:"session_duration",Lg:"session_engaged",Wh:"session_engaged_time",hc:"session_id",Mg:"session_number",Te:"_shared_user_id",Ue:"delivery_postal_code",Wp:"_tag_firing_delay",Xp:"_tag_firing_time",Yp:"temporary_client_id",Xh:"_timezone",Yh:"topmost_url",tn:"tracking_id",Zh:"traffic_type",Sa:"transaction_id",ic:"transport_url",Lk:"trip_type",Zc:"update",xb:"url_passthrough",Mk:"uptgs",Ve:"_user_agent_architecture",We:"_user_agent_bitness",Xe:"_user_agent_full_version_list",Ye:"_user_agent_mobile",
Ze:"_user_agent_model",af:"_user_agent_platform",bf:"_user_agent_platform_version",cf:"_user_agent_wow64",Ta:"user_data",ai:"user_data_auto_latency",bi:"user_data_auto_meta",di:"user_data_auto_multi",ei:"user_data_auto_selectors",fi:"user_data_auto_status",Jb:"user_data_mode",Ng:"user_data_settings",Na:"user_id",Kb:"user_properties",Nk:"_user_region",df:"us_privacy_string",Ca:"value",Ok:"wbraid_multiple_conversions",Md:"_fpm_parameters",ji:"_host_name",Xk:"_in_page_command",Yk:"_ip_override",bl:"_is_passthrough_cid",
jc:"non_personalized_ads",yi:"_sst_parameters",Zb:"conversion_label",ya:"page_location",Hb:"global_developer_id_string",Yc:"tc_privacy_string"}};var Sh={},Th=Object.freeze((Sh[N.m.Ba]=1,Sh[N.m.mg]=1,Sh[N.m.ng]=1,Sh[N.m.vb]=1,Sh[N.m.oa]=1,Sh[N.m.hb]=1,Sh[N.m.ib]=1,Sh[N.m.pb]=1,Sh[N.m.Pc]=1,Sh[N.m.Fb]=1,Sh[N.m.Za]=1,Sh[N.m.xc]=1,Sh[N.m.Ge]=1,Sh[N.m.wa]=1,Sh[N.m.gk]=1,Sh[N.m.Je]=1,Sh[N.m.zg]=1,Sh[N.m.Ag]=1,Sh[N.m.Hd]=1,Sh[N.m.wk]=1,Sh[N.m.Uc]=1,Sh[N.m.Kd]=1,Sh[N.m.zk]=1,Sh[N.m.Dg]=1,Sh[N.m.Sh]=1,Sh[N.m.Ac]=1,Sh[N.m.Bc]=1,Sh[N.m.Ma]=1,Sh[N.m.Uh]=1,Sh[N.m.Ib]=1,Sh[N.m.jb]=1,Sh[N.m.Wc]=1,Sh[N.m.Xc]=1,Sh[N.m.Se]=1,Sh[N.m.Wh]=1,Sh[N.m.Ue]=1,Sh[N.m.ic]=
1,Sh[N.m.Zc]=1,Sh[N.m.Ng]=1,Sh[N.m.Kb]=1,Sh[N.m.yi]=1,Sh));Object.freeze([N.m.ya,N.m.Ra,N.m.wb,N.m.qb,N.m.Vh,N.m.Na,N.m.Qh,N.m.Wm]);
var Uh={},Vh=Object.freeze((Uh[N.m.Bm]=1,Uh[N.m.Cm]=1,Uh[N.m.Dm]=1,Uh[N.m.Em]=1,Uh[N.m.Fm]=1,Uh[N.m.Im]=1,Uh[N.m.Jm]=1,Uh[N.m.Km]=1,Uh[N.m.Mm]=1,Uh[N.m.Bd]=1,Uh)),Wh={},Xh=Object.freeze((Wh[N.m.Vj]=1,Wh[N.m.Wj]=1,Wh[N.m.wd]=1,Wh[N.m.xd]=1,Wh[N.m.Xj]=1,Wh[N.m.Kc]=1,Wh[N.m.yd]=1,Wh[N.m.Vb]=1,Wh[N.m.wc]=1,Wh[N.m.Wb]=1,Wh[N.m.eb]=1,Wh[N.m.zd]=1,Wh[N.m.ob]=1,Wh[N.m.Yj]=1,Wh)),Yh=Object.freeze([N.m.Ba,N.m.we,N.m.vb,N.m.xc,N.m.Hd,N.m.Oe,N.m.jb,N.m.Zc]),Zh=Object.freeze([].concat(ta(Yh))),$h=Object.freeze([N.m.ib,
N.m.Ag,N.m.Se,N.m.Wh,N.m.vg]),ai=Object.freeze([].concat(ta($h))),bi={},ci=(bi[N.m.U]="1",bi[N.m.aa]="2",bi[N.m.V]="3",bi[N.m.Ha]="4",bi),di={},ei=Object.freeze((di.search="s",di.youtube="y",di.playstore="p",di.shopping="h",di.ads="a",di.maps="m",di));Object.freeze(N.m);var fi={},gi=(fi[N.m.da]="gcu",fi[N.m.Xb]="gclgb",fi[N.m.fb]="gclaw",fi[N.m.Zj]="gclid_len",fi[N.m.Cd]="gclgs",fi[N.m.Dd]="gcllp",fi[N.m.Ed]="gclst",fi[N.m.Yb]="auid",fi[N.m.og]="dscnt",fi[N.m.pg]="fcntr",fi[N.m.qg]="flng",fi[N.m.rg]="mid",fi[N.m.dk]="bttype",fi[N.m.Db]="gacid",fi[N.m.Zb]="label",fi[N.m.Oc]="capi",fi[N.m.sg]="pscdl",fi[N.m.Qa]="currency_code",fi[N.m.Ih]="clobs",fi[N.m.Fe]="vdltv",fi[N.m.Jh]="clolo",fi[N.m.Kh]="clolb",fi[N.m.fk]="_dbg",fi[N.m.yg]="oedeld",fi[N.m.Gb]="edid",fi[N.m.kk]=
"fdr",fi[N.m.lk]="fledge",fi[N.m.Cg]="gac",fi[N.m.Id]="gacgb",fi[N.m.vk]="gacmcov",fi[N.m.Tc]="gdpr",fi[N.m.Hb]="gdid",fi[N.m.Jd]="_ng",fi[N.m.Le]="gpp_sid",fi[N.m.Me]="gpp",fi[N.m.Ak]="gsaexp",fi[N.m.Ne]="_tu",fi[N.m.zc]="frm",fi[N.m.Eg]="gtm_up",fi[N.m.Vc]="lps",fi[N.m.Fg]="did",fi[N.m.Gg]="fcntr",fi[N.m.Hg]="flng",fi[N.m.Ig]="mid",fi[N.m.Pe]=void 0,fi[N.m.wb]="tiba",fi[N.m.Ib]="rdp",fi[N.m.hc]="ecsid",fi[N.m.Te]="ga_uid",fi[N.m.Ue]="delopc",fi[N.m.Yc]="gdpr_consent",fi[N.m.Sa]="oid",fi[N.m.Mk]=
"uptgs",fi[N.m.Ve]="uaa",fi[N.m.We]="uab",fi[N.m.Xe]="uafvl",fi[N.m.Ye]="uamb",fi[N.m.Ze]="uam",fi[N.m.af]="uap",fi[N.m.bf]="uapv",fi[N.m.cf]="uaw",fi[N.m.ai]="ec_lat",fi[N.m.bi]="ec_meta",fi[N.m.di]="ec_m",fi[N.m.ei]="ec_sel",fi[N.m.fi]="ec_s",fi[N.m.Jb]="ec_mode",fi[N.m.Na]="userId",fi[N.m.df]="us_privacy",fi[N.m.Ca]="value",fi[N.m.Ok]="mcov",fi[N.m.ji]="hn",fi[N.m.Xk]="gtm_ee",fi[N.m.jc]="npa",fi[N.m.Ee]=null,fi[N.m.Dc]=null,fi[N.m.qb]=null,fi[N.m.oa]=null,fi[N.m.ya]=null,fi[N.m.Ra]=null,fi[N.m.Yh]=
null,fi[N.m.Md]=null,fi[N.m.qe]=null,fi[N.m.se]=null,fi[N.m.Uc]=null,fi);function hi(a,b){if(a){var c=a.split("x");c.length===2&&(ii(b,"u_w",c[0]),ii(b,"u_h",c[1]))}}
function ji(a){var b=ki;b=b===void 0?li:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(mi(q.value)),r.push(mi(q.quantity)),r.push(mi(q.item_id)),r.push(mi(q.start_date)),r.push(mi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function li(a){return ni(a.item_id,a.id,a.item_name)}function ni(){for(var a=l(xa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function oi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ii(a,b,c){c===void 0||c===null||c===""&&!qg[b]||(a[b]=c)}function mi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};function ri(a){return si?A.querySelectorAll(a):null}
function ti(a,b){if(!si)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ui=!1;
if(A.querySelectorAll)try{var vi=A.querySelectorAll(":root");vi&&vi.length==1&&vi[0]==A.documentElement&&(ui=!0)}catch(a){}var si=ui;function wi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var yi=/^[0-9A-Fa-f]{64}$/;function zi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Ai(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=y.crypto)==null?0:b.subtle){if(yi.test(a))return Promise.resolve(a);try{var c=zi(a);return y.crypto.subtle.digest("SHA-256",c).then(function(d){var e=Array.from(new Uint8Array(d)).map(function(f){return String.fromCharCode(f)}).join("");return y.btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")};var Bi={ym:'100',zm:'100',Am:'1000',Pn:'101509157~103101750~103101752~103116025~103200001~103231718~103231720~103233427~103251618~103251620~103252644~103252646~103284320~103284322'},Ci={Ti:Number(Bi.ym)||0,Cf:Number(Bi.zm)||0,no:Number(Bi.Am)||0,Op:Bi.Pn};function O(a){Wa("GTM",a)};var ij={},jj=(ij[N.m.Pa]=1,ij[N.m.Xc]=2,ij[N.m.ic]=2,ij[N.m.sa]=3,ij[N.m.Fe]=4,ij[N.m.lg]=5,ij[N.m.xc]=6,ij[N.m.Za]=6,ij[N.m.hb]=6,ij[N.m.Pc]=6,ij[N.m.Fb]=6,ij[N.m.pb]=6,ij[N.m.ib]=7,ij[N.m.Ib]=9,ij[N.m.mg]=10,ij[N.m.vb]=11,ij),kj={},lj=(kj.unknown=13,kj.standard=14,kj.unique=15,kj.per_session=16,kj.transactions=17,kj.items_sold=18,kj);var mj=[];function nj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(jj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=jj[f],h=b;h=h===void 0?!1:h;Wa("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(mj[g]=!0)}}};var oj=function(){this.D=new Set},qj=function(a){var b=pj.Ia;a=a===void 0?[]:a;return Array.from(b.D).concat(a)},rj=function(){var a=pj.Ia,b=Ci.Op;a.D=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var sj={wi:"5560"};sj.vi=Number("2")||0;sj.Cb="dataLayer";sj.Rp="ChAI8InswAYQsMiNyNfx6tgIEiQAZjMgo1B5uZV77wUCuiH4GhWuqYGsVzqCEsw6jYj0xeItUakaAk2l";var tj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},uj={__paused:1,__tg:1},vj;for(vj in tj)tj.hasOwnProperty(vj)&&(uj[vj]=1);var wj=mb(""),xj=!1,yj,zj=!1;yj=zj;var Aj,Bj=!1;Aj=Bj;var Cj,Dj=!1;Cj=Dj;sj.jg="www.googletagmanager.com";var Ej=""+sj.jg+(yj?"/gtag/js":"/gtm.js"),Fj=null,Gj=null,Hj={},Ij={};sj.xm="";var Jj="";sj.zi=Jj;
var pj=new function(){this.Ia=new oj;this.D=!1;this.J=0;this.ka=this.la=this.kb=this.P="";this.T=this.O=!1};function Kj(){var a;a=a===void 0?[]:a;return qj(a).join("~")}function Lj(){var a=pj.P.length;return pj.P[a-1]==="/"?pj.P.substring(0,a-1):pj.P}function Mj(){return pj.D?H(84)?pj.J===0:pj.J!==1:!1}function Nj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Oj=new ib,Pj={},Qj={},Tj={name:sj.Cb,set:function(a,b){Zc(xb(a,b),Pj);Rj()},get:function(a){return Sj(a,2)},reset:function(){Oj=new ib;Pj={};Rj()}};function Sj(a,b){return b!=2?Oj.get(a):Uj(a)}function Uj(a,b){var c=a.split(".");b=b||[];for(var d=Pj,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Vj(a,b){Qj.hasOwnProperty(a)||(Oj.set(a,b),Zc(xb(a,b),Pj),Rj())}
function Wj(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Sj(c,1);if(Array.isArray(d)||Yc(d))d=Zc(d,null);Qj[c]=d}}function Rj(a){jb(Qj,function(b,c){Oj.set(b,c);Zc(xb(b),Pj);Zc(xb(b,c),Pj);a&&delete Qj[b]})}function Xj(a,b){var c,d=(b===void 0?2:b)!==1?Uj(a):Oj.get(a);Wc(d)==="array"||Wc(d)==="object"?c=Zc(d,null):c=d;return c};var ck=/:[0-9]+$/,dk=/^\d+\.fls\.doubleclick\.net$/;function ek(a,b,c,d){for(var e=[],f=l(a.split("&")),g=f.next();!g.done;g=f.next()){var h=l(g.value.split("=")),m=h.next().value,n=sa(h);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function fk(a){try{return decodeURIComponent(a)}catch(b){}}
function gk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=hk(a.protocol)||hk(y.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:y.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||y.location.hostname).replace(ck,"").toLowerCase());return ik(a,b,c,d,e)}
function ik(a,b,c,d,e){var f,g=hk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=jk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(ck,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Wa("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=ek(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function hk(a){return a?a.replace(":","").toLowerCase():""}function jk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var kk={},lk=0;
function mk(a){var b=kk[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Wa("TAGGING",1),d="/"+d);var e=c.hostname.replace(ck,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};lk<5&&(kk[a]=b,lk++)}return b}function nk(a,b,c){var d=mk(a);return Db(b,d,c)}
function ok(a){var b=mk(y.location.href),c=gk(b,"host",!1);if(c&&c.match(dk)){var d=gk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var pk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},qk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function rk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return mk(""+c+b).href}}function sk(a,b){if(Mj()||Aj)return rk(a,b)}
function tk(){return!!sj.zi&&sj.zi.split("@@").join("")!=="SGTM_TOKEN"}function uk(a){for(var b=l([N.m.Xc,N.m.ic]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function vk(a,b,c){c=c===void 0?"":c;if(!Mj())return a;var d=b?pk[a]||"":"";d==="/gs"&&(c="");return""+Lj()+d+c}function wk(a){if(!Mj())return a;for(var b=l(qk),c=b.next();!c.done;c=b.next())if(vb(a,""+Lj()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function xk(a){var b=String(a[Qe.Da]||"").replace(/_/g,"");return vb(b,"cvt")?"cvt":b}var yk=y.location.search.indexOf("?gtm_latency=")>=0||y.location.search.indexOf("&gtm_latency=")>=0;var zk={sampleRate:"0.005000",sm:"",Np:"0.01"};function Ak(){var a=zk.sampleRate;return Number(a)}var Bk=Math.random(),Ck=yk||Bk<Ak(),Dk=Ak()===1||(jc==null?void 0:jc.includes("gtm_debug=d"))||yk||Bk>=1-Number(zk.Np);var Ek,Fk;a:{for(var Gk=["CLOSURE_FLAGS"],Hk=ya,Ik=0;Ik<Gk.length;Ik++)if(Hk=Hk[Gk[Ik]],Hk==null){Fk=null;break a}Fk=Hk}var Jk=Fk&&Fk[610401301];Ek=Jk!=null?Jk:!1;function Kk(){var a=ya.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Lk,Mk=ya.navigator;Lk=Mk?Mk.userAgentData||null:null;function Nk(a){if(!Ek||!Lk)return!1;for(var b=0;b<Lk.brands.length;b++){var c=Lk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Ok(a){return Kk().indexOf(a)!=-1};function Pk(){return Ek?!!Lk&&Lk.brands.length>0:!1}function Qk(){return Pk()?!1:Ok("Opera")}function Rk(){return Ok("Firefox")||Ok("FxiOS")}function Sk(){return Pk()?Nk("Chromium"):(Ok("Chrome")||Ok("CriOS"))&&!(Pk()?0:Ok("Edge"))||Ok("Silk")};function Tk(){return Ek?!!Lk&&!!Lk.platform:!1}function Uk(){return Ok("iPhone")&&!Ok("iPod")&&!Ok("iPad")}function Vk(){Uk()||Ok("iPad")||Ok("iPod")};var Wk=function(a){Wk[" "](a);return a};Wk[" "]=function(){};Qk();Pk()||Ok("Trident")||Ok("MSIE");Ok("Edge");!Ok("Gecko")||Kk().toLowerCase().indexOf("webkit")!=-1&&!Ok("Edge")||Ok("Trident")||Ok("MSIE")||Ok("Edge");Kk().toLowerCase().indexOf("webkit")!=-1&&!Ok("Edge")&&Ok("Mobile");Tk()||Ok("Macintosh");Tk()||Ok("Windows");(Tk()?Lk.platform==="Linux":Ok("Linux"))||Tk()||Ok("CrOS");Tk()||Ok("Android");Uk();Ok("iPad");Ok("iPod");Vk();Kk().toLowerCase().indexOf("kaios");var Xk=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Yk=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var Zk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var $k=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},al=/#|$/,bl=function(a,b){var c=a.search(al),d=$k(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Zk(a.slice(d,e!==-1?e:0))},cl=/[?&]($|#)/,dl=function(a,b,c){for(var d,e=a.search(al),f=0,g,h=[];(g=$k(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(cl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};var el=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Wk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},fl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},gl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},hl=function(a){if(y.top==y)return 0;if(a===void 0?0:a){var b=y.location.ancestorOrigins;
if(b)return b[b.length-1]==y.location.origin?1:2}return el(y.top)?1:2},il=function(a){a=a===void 0?document:a;return a.createElement("img")},jl=function(){for(var a=y,b=a;a&&a!=a.parent;)a=a.parent,el(a)&&(b=a);return b};function kl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function ll(){return kl("join-ad-interest-group")&&ab(gc.joinAdInterestGroup)}
function ml(a,b,c){var d=ig[3]===void 0?1:ig[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(ig[2]===void 0?50:ig[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&qb()-q<(ig[1]===void 0?6E4:ig[1])?(Wa("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)nl(f[0]);else{if(n)return Wa("TAGGING",10),!1}else f.length>=d?nl(f[0]):n&&nl(m[0]);uc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:qb()});return!0}function nl(a){try{a.parentNode.removeChild(a)}catch(b){}}function ol(){return"https://td.doubleclick.net"};function pl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Rk();Uk()||Ok("iPod");Ok("iPad");!Ok("Android")||Sk()||Rk()||Qk()||Ok("Silk");Sk();!Ok("Safari")||Sk()||(Pk()?0:Ok("Coast"))||Qk()||(Pk()?0:Ok("Edge"))||(Pk()?Nk("Microsoft Edge"):Ok("Edg/"))||(Pk()?Nk("Opera"):Ok("OPR"))||Rk()||Ok("Silk")||Ok("Android")||Vk();var rl={},sl=null,tl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!sl){sl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));rl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];sl[q]===void 0&&(sl[q]=p)}}}for(var r=rl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],B=b[v+2],D=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|B>>6],J=r[B&63];t[w++]=""+D+F+G+J}var M=0,U=u;switch(b.length-v){case 2:M=b[v+1],U=r[(M&15)<<2]||u;case 1:var K=b[v];t[w]=""+r[K>>2]+r[(K&3)<<4|M>>4]+U+u}return t.join("")};function ul(a,b,c,d,e,f){var g=bl(c,"fmt");if(d){var h=bl(c,"random"),m=bl(c,"label")||"";if(!h)return!1;var n=tl(Zk(m)+":"+Zk(h));if(!pl(a,n,d))return!1}g&&Number(g)!==4&&(c=dl(c,"rfmt",g));var p=dl(c,"fmt",4);sc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var vl={},wl=(vl[1]={},vl[2]={},vl[3]={},vl[4]={},vl);function xl(a,b,c){var d=yl(b,c);if(d){var e=wl[b][d];e||(e=wl[b][d]=[]);e.push(Object.assign({},a))}}function zl(a,b){var c=yl(a,b);if(c){var d=wl[a][c];d&&(wl[a][c]=d.filter(function(e){return!e.dm}))}}function Al(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function yl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=y.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Bl(a){var b=xa.apply(1,arguments);H(55)&&Dk&&(xl(a,2,b[0]),xl(a,3,b[0]));Dc.apply(null,ta(b))}function Cl(a){var b=xa.apply(1,arguments);H(55)&&Dk&&xl(a,2,b[0]);return Ec.apply(null,ta(b))}function Dl(a){var b=xa.apply(1,arguments);H(55)&&Dk&&xl(a,3,b[0]);vc.apply(null,ta(b))}
function El(a){var b=xa.apply(1,arguments),c=b[0];H(55)&&Dk&&(xl(a,2,c),xl(a,3,c));return Gc.apply(null,ta(b))}function Fl(a){var b=xa.apply(1,arguments);H(55)&&Dk&&xl(a,1,b[0]);sc.apply(null,ta(b))}function Gl(a){var b=xa.apply(1,arguments);b[0]&&H(55)&&Dk&&xl(a,4,b[0]);uc.apply(null,ta(b))}function Hl(a){var b=xa.apply(1,arguments);H(55)&&Dk&&xl(a,1,b[2]);return ul.apply(null,ta(b))}function Il(a){var b=xa.apply(1,arguments);H(55)&&Dk&&xl(a,4,b[0]);ml.apply(null,ta(b))};var Jl=/gtag[.\/]js/,Kl=/gtm[.\/]js/,Ll=!1;function Ml(a){if(Ll)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(Jl.test(c))return"3";if(Kl.test(c))return"2"}return"0"};function Nl(a,b){var c=Ol();c.pending||(c.pending=[]);fb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Pl(){var a=y.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Ql=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Pl()};
function Ol(){var a=kc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Ql,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Pl());return c};var Rl={},Sl=!1,Tl=void 0,Wf={ctid:"GTM-KMHKK5M",canonicalContainerId:"66647525",Wl:"GTM-KMHKK5M",Xl:"GTM-KMHKK5M"};Rl.kf=mb("");function Ul(){return Rl.kf&&Vl().some(function(a){return a===Wf.ctid})}function Wl(){var a=Xl();return Sl?a.map(Yl):a}function Zl(){var a=Vl();return Sl?a.map(Yl):a}
function $l(){var a=Zl();if(!Sl)for(var b=l([].concat(ta(a))),c=b.next();!c.done;c=b.next()){var d=Yl(c.value),e=Ol().destination[d];e&&e.state!==0||a.push(d)}return a}function am(){return bm(Wf.ctid)}function cm(){return bm(Wf.canonicalContainerId||"_"+Wf.ctid)}function Xl(){return Wf.Wl?Wf.Wl.split("|"):[Wf.ctid]}function Vl(){return Wf.Xl?Wf.Xl.split("|").filter(function(a){return H(108)?a.indexOf("GTM-")!==0:!0}):[]}function dm(){var a=em(fm()),b=a&&a.parent;if(b)return em(b)}
function em(a){var b=Ol();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function bm(a){return Sl?Yl(a):a}function Yl(a){return"siloed_"+a}function gm(a){a=String(a);return vb(a,"siloed_")?a.substring(7):a}function hm(){if(pj.O){var a=Ol();if(a.siloed){for(var b=[],c=Xl().map(Yl),d=Vl().map(Yl),e={},f=0;f<a.siloed.length;e={jh:void 0},f++)e.jh=a.siloed[f],!Sl&&fb(e.jh.isDestination?d:c,function(g){return function(h){return h===g.jh.ctid}}(e))?Sl=!0:b.push(e.jh);a.siloed=b}}}
function im(){var a=Ol();if(a.pending){for(var b,c=[],d=!1,e=Wl(),f=Tl?Tl:$l(),g={},h=0;h<a.pending.length;g={Tf:void 0},h++)g.Tf=a.pending[h],fb(g.Tf.target.isDestination?f:e,function(m){return function(n){return n===m.Tf.target.ctid}}(g))?d||(b=g.Tf.onLoad,d=!0):c.push(g.Tf);a.pending=c;if(b)try{b(cm())}catch(m){}}}
function jm(){var a=Wf.ctid,b=Wl(),c=$l();Tl=c;for(var d=function(n,p){var q={canonicalContainerId:Wf.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};ic&&(q.scriptElement=ic);jc&&(q.scriptSource=jc);if(dm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=pj.D,x=mk(v),z=w?x.pathname:""+x.hostname+x.pathname,B=A.scripts,D="",F=0;F<B.length;++F){var G=B[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}D=String(F)}}if(D){t=D;break b}}t=void 0}var J=t;if(J){Ll=!0;r=J;break a}}var M=[].slice.call(A.scripts);r=q.scriptElement?String(M.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Ml(q)}var U=p?e.destination:e.container,K=U[n];K?(p&&K.state===0&&O(93),Object.assign(K,q)):U[n]=q},e=Ol(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[cm()]={};im()}function km(){var a=cm();return!!Ol().canonical[a]}function lm(a){return!!Ol().container[a]}function mm(a){var b=Ol().destination[a];return!!b&&!!b.state}function fm(){return{ctid:am(),isDestination:Rl.kf}}function nm(a,b,c){b.siloed&&om({ctid:a,isDestination:!1});var d=fm();Ol().container[a]={state:1,context:b,parent:d};Nl({ctid:a,isDestination:!1},c)}
function om(a){var b=Ol();(b.siloed=b.siloed||[]).push(a)}function pm(){var a=Ol().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function qm(){var a={};jb(Ol().destination,function(b,c){c.state===0&&(a[gm(b)]=c)});return a}function rm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function sm(){for(var a=Ol(),b=l(Wl()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function tm(a){var b=Ol();return b.destination[a]?1:b.destination[Yl(a)]?2:0};var um={Fa:{Nd:0,Sd:1,si:2}};um.Fa[um.Fa.Nd]="FULL_TRANSMISSION";um.Fa[um.Fa.Sd]="LIMITED_TRANSMISSION";um.Fa[um.Fa.si]="NO_TRANSMISSION";var vm={W:{yb:0,Aa:1,vc:2,Ec:3}};vm.W[vm.W.yb]="NO_QUEUE";vm.W[vm.W.Aa]="ADS";vm.W[vm.W.vc]="ANALYTICS";vm.W[vm.W.Ec]="MONITORING";function wm(){var a=kc("google_tag_data",{});return a.ics=a.ics||new xm}var xm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
xm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Wa("TAGGING",19);b==null?Wa("TAGGING",18):ym(this,a,b==="granted",c,d,e,f,g)};xm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)ym(this,a[d],void 0,void 0,"","",b,c)};
var ym=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&cb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&y.setTimeout(function(){m[b]===t&&t.quiet&&(Wa("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=xm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())zm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())zm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&cb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.D.push({consentTypes:a,ae:b})};var zm=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Yl=!0)}};xm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.Yl){d.Yl=!1;try{d.ae({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Am=!1,Bm=!1,Cm={},Dm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Cm.ad_storage=1,Cm.analytics_storage=1,Cm.ad_user_data=1,Cm.ad_personalization=1,Cm),usedContainerScopedDefaults:!1};function Em(a){var b=wm();b.accessedAny=!0;return(cb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Dm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Fm(a){var b=wm();b.accessedAny=!0;return b.getConsentState(a,Dm)}function Gm(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Dm.corePlatformServices[e]!==!1}return b}function Hm(a){var b=wm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}
function Im(){if(!jg(8))return!1;var a=wm();a.accessedAny=!0;if(a.active)return!0;if(!Dm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Dm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Dm.containerScopedDefaults[c.value]!==1)return!0;return!1}function Jm(a,b){wm().addListener(a,b)}function Km(a,b){wm().notifyListeners(a,b)}
function Lm(a,b){function c(){for(var e=0;e<b.length;e++)if(!Hm(b[e]))return!0;return!1}if(c()){var d=!1;Jm(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function Mm(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Em(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=cb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Jm(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):y.setTimeout(function(){m(c())},500)}}))};var Nm={},Om=(Nm[vm.W.yb]=um.Fa.Nd,Nm[vm.W.Aa]=um.Fa.Nd,Nm[vm.W.vc]=um.Fa.Nd,Nm[vm.W.Ec]=um.Fa.Nd,Nm),Pm=function(a,b){this.D=a;this.consentTypes=b};Pm.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return Em(a)});case 1:return this.consentTypes.some(function(a){return Em(a)});default:Zb(this.D,"consentsRequired had an unknown type")}};
var Qm={},Rm=(Qm[vm.W.yb]=new Pm(0,[]),Qm[vm.W.Aa]=new Pm(0,["ad_storage"]),Qm[vm.W.vc]=new Pm(0,["analytics_storage"]),Qm[vm.W.Ec]=new Pm(1,["ad_storage","analytics_storage"]),Qm);var Tm=function(a){var b=this;this.type=a;this.D=[];Jm(Rm[a].consentTypes,function(){Sm(b)||b.flush()})};Tm.prototype.flush=function(){for(var a=l(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var Sm=function(a){return Om[a.type]===um.Fa.si&&!Rm[a.type].isConsentGranted()},Um=function(a,b){Sm(a)?a.D.push(b):b()},Vm=new Map;function Wm(a){Vm.has(a)||Vm.set(a,new Tm(a));return Vm.get(a)};var Xm="/td?id="+Wf.ctid,Ym="v t pid dl tdp exp".split(" "),Zm=["mcc"],$m={},an={},bn=!1;function cn(a,b,c){an[a]=b;(c===void 0||c)&&dn(a)}function dn(a,b){if($m[a]===void 0||(b===void 0?0:b))$m[a]=!0}function en(a){a=a===void 0?!1:a;var b=Object.keys($m).filter(function(c){return $m[c]===!0&&an[c]!==void 0&&(a||!Zm.includes(c))}).map(function(c){var d=an[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+vk("https://www.googletagmanager.com")+Xm+(""+b+"&z=0")}
function fn(){Object.keys($m).forEach(function(a){Ym.indexOf(a)<0&&($m[a]=!1)})}function gn(a){a=a===void 0?!1:a;if(pj.T&&Dk&&Wf.ctid){var b=Wm(vm.W.Ec);if(Sm(b))bn||(bn=!0,Um(b,gn));else{var c=en(a),d={destinationId:Wf.ctid,endpoint:56};a?El(d,c):Dl(d,c);fn();bn=!1}}}var hn={};function jn(){Object.keys($m).filter(function(a){return $m[a]&&!Ym.includes(a)}).length>0&&gn(!0)}var kn=gb();function ln(){kn=gb()}
function mn(){cn("v","3");cn("t","t");cn("pid",function(){return String(kn)});cn("exp",Kj());xc(y,"pagehide",jn);y.setInterval(ln,864E5)};var nn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],on=[N.m.Xc,N.m.ic,N.m.Hd,N.m.Db,N.m.hc,N.m.Na,N.m.Ma,N.m.Za,N.m.hb,N.m.Fb],pn=!1,qn=!1,rn={},sn={};function tn(){!qn&&pn&&(nn.some(function(a){return Dm.containerScopedDefaults[a]!==1})||un("mbc"));qn=!0}function un(a){Dk&&(cn(a,"1"),gn())}function vn(a,b){if(!rn[b]&&(rn[b]=!0,sn[b]))for(var c=l(on),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){un("erc");break}};function wn(a){Wa("HEALTH",a)};var xn={tl:"service_worker_endpoint",Ai:"shared_user_id",Bi:"shared_user_id_requested",tf:"shared_user_id_source",hg:"cookie_deprecation_label",tm:"aw_user_data_cache",wn:"ga4_user_data_cache",un:"fl_user_data_cache",ml:"pt_listener_set",qf:"pt_data",jl:"nb_data",mi:"ip_geo_fetch_in_progress",ef:"ip_geo_data_cache"},yn;function zn(a){if(!yn){yn={};for(var b=l(Object.keys(xn)),c=b.next();!c.done;c=b.next())yn[xn[c.value]]=!0}return!!yn[a]}
function An(a,b){b=b===void 0?!1:b;if(zn(a)){var c,d,e=(d=(c=kc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Bn(a,b){var c=An(a,!0);c&&c.set(b)}function Cn(a){var b;return(b=An(a))==null?void 0:b.get()}function Dn(a,b){if(typeof b==="function"){var c;return(c=An(a,!0))==null?void 0:c.subscribe(b)}}function En(a,b){var c=An(a);return c?c.unsubscribe(b):!1};var Fn={Ao:"eyIwIjoiVVMiLCIxIjoiVVMtQ1QiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9"},Gn={},Hn=!1;function In(){function a(){c!==void 0&&En(xn.ef,c);try{var e=Cn(xn.ef);Gn=JSON.parse(e)}catch(f){O(123),wn(2),Gn={}}Hn=!0;b()}var b=Jn,c=void 0,d=Cn(xn.ef);d?a(d):(c=Dn(xn.ef,a),Kn())}
function Kn(){function a(c){Bn(xn.ef,c||"{}");Bn(xn.mi,!1)}if(!Cn(xn.mi)){Bn(xn.mi,!0);var b="";try{y.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function Ln(){var a=Fn.Ao;try{return JSON.parse(Ua(a))}catch(b){return O(123),wn(2),{}}}function Mn(){return Gn["0"]||""}function Nn(){return Gn["1"]||""}function On(){var a=!1;return a}function Pn(){return Gn["6"]!==!1}function Qn(){var a="";return a}
function Rn(){var a=!1;return a}function Sn(){var a="";return a};function Tn(a){return typeof a!=="object"||a===null?{}:a}function Un(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Vn(a){if(a!==void 0&&a!==null)return Un(a)}function Wn(a){return typeof a==="number"?a:Vn(a)};function Xn(a){return a&&a.indexOf("pending:")===0?Yn(a.substr(8)):!1}function Yn(a){if(a==null||a.length===0)return!1;var b=Number(a),c=qb();return b<c+3E5&&b>c-9E5};var Zn=!1,$n=!1,ao=!1,bo=0,co=!1,eo=[];function fo(a){if(bo===0)co&&eo&&(eo.length>=100&&eo.shift(),eo.push(a));else if(go()){var b=kc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function ho(){io();yc(A,"TAProdDebugSignal",ho)}function io(){if(!$n){$n=!0;jo();var a=eo;eo=void 0;a==null||a.forEach(function(b){fo(b)})}}
function jo(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Yn(a)?bo=1:!Xn(a)||Zn||ao?bo=2:(ao=!0,xc(A,"TAProdDebugSignal",ho,!1),y.setTimeout(function(){io();Zn=!0},200))}function go(){if(!co)return!1;switch(bo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var ko=!1;function lo(a,b){var c=Xl(),d=Vl();if(go()){var e=mo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;fo(e)}}function no(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ua;e=a.isBatched;if(go()){var f=mo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});f.target=b;f.url=c.url;c.postBody&&(f.postBody=c.postBody);f.parameterEncoding=c.parameterEncoding;f.endpoint=c.endpoint;e!==void 0&&(f.isBatched=e);fo(f)}}
function oo(a){go()&&no(a())}function mo(a,b){b=b===void 0?{}:b;b.groupId=po;var c,d=b,e={publicId:qo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'5',messageType:a};c.containerProduct=ko?"OGT":"GTM";c.key.targetRef=ro;return c}var qo="",ro={ctid:"",isDestination:!1},po;
function so(a){var b=Wf.ctid,c=Ul();bo=0;co=!0;jo();po=a;qo=b;ko=yj;ro={ctid:b,isDestination:c}};var to=[N.m.U,N.m.aa,N.m.V,N.m.Ha],uo,vo;function wo(a){var b=a[N.m.Tb];b||(b=[""]);for(var c={Jf:0};c.Jf<b.length;c={Jf:c.Jf},++c.Jf)jb(a,function(d){return function(e,f){if(e!==N.m.Tb){var g=Un(f),h=b[d.Jf],m=Mn(),n=Nn();Bm=!0;Am&&Wa("TAGGING",20);wm().declare(e,g,h,m,n)}}}(c))}
function xo(a){tn();!vo&&uo&&un("crc");vo=!0;var b=a[N.m.fg];b&&O(41);var c=a[N.m.Tb];c?O(40):c=[""];for(var d={Kf:0};d.Kf<c.length;d={Kf:d.Kf},++d.Kf)jb(a,function(e){return function(f,g){if(f!==N.m.Tb&&f!==N.m.fg){var h=Vn(g),m=c[e.Kf],n=Number(b),p=Mn(),q=Nn();n=n===void 0?0:n;Am=!0;Bm&&Wa("TAGGING",20);wm().default(f,h,m,p,q,n,Dm)}}}(d))}
function yo(a){Dm.usedContainerScopedDefaults=!0;var b=a[N.m.Tb];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(Nn())&&!c.includes(Mn()))return}jb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Dm.usedContainerScopedDefaults=!0;Dm.containerScopedDefaults[d]=e==="granted"?3:2})}
function zo(a,b){tn();uo=!0;jb(a,function(c,d){var e=Un(d);Am=!0;Bm&&Wa("TAGGING",20);wm().update(c,e,Dm)});Km(b.eventId,b.priorityId)}function Ao(a){a.hasOwnProperty("all")&&(Dm.selectedAllCorePlatformServices=!0,jb(ei,function(b){Dm.corePlatformServices[b]=a.all==="granted";Dm.usedCorePlatformServices=!0}));jb(a,function(b,c){b!=="all"&&(Dm.corePlatformServices[b]=c==="granted",Dm.usedCorePlatformServices=!0)})}function Bo(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Em(b)})}
function Co(a,b){Jm(a,b)}function Do(a,b){Mm(a,b)}function Eo(a,b){Lm(a,b)}function Fo(){var a=[N.m.U,N.m.Ha,N.m.V];wm().waitForUpdate(a,500,Dm)}function Go(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;wm().clearTimeout(d,void 0,Dm)}Km()}function Ho(){if(!Cj)for(var a=Pn()?Nj(pj.la):Nj(pj.kb),b=0;b<to.length;b++){var c=to[b],d=c,e=a[c]?"granted":"denied";wm().implicit(d,e)}};var Io=!1,Jo=[];function Ko(){if(!Io){Io=!0;for(var a=Jo.length-1;a>=0;a--)Jo[a]();Jo=[]}};var Lo=y.google_tag_manager=y.google_tag_manager||{};function Mo(a,b){return Lo[a]=Lo[a]||b()}function No(){var a=am(),b=Oo;Lo[a]=Lo[a]||b}function Po(){var a=sj.Cb;return Lo[a]=Lo[a]||{}}function Qo(){var a=Lo.sequence||1;Lo.sequence=a+1;return a};function Ro(){if(Lo.pscdl!==void 0)Cn(xn.hg)===void 0&&Bn(xn.hg,Lo.pscdl);else{var a=function(c){Lo.pscdl=c;Bn(xn.hg,c)},b=function(){a("error")};try{gc.cookieDeprecationLabel?(a("pending"),gc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};function So(a,b){b&&jb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var To=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Uo=/\s/;
function Vo(a,b){if(cb(a)){a=ob(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(To.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Uo.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Wo(a,b){for(var c={},d=0;d<a.length;++d){var e=Vo(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Xo[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Yo={},Xo=(Yo[0]=0,Yo[1]=1,Yo[2]=2,Yo[3]=0,Yo[4]=1,Yo[5]=0,Yo[6]=0,Yo[7]=0,Yo);var Zo=Number('')||500,$o={},ap={},bp={initialized:11,complete:12,interactive:13},cp={},gp=Object.freeze((cp[N.m.jb]=!0,cp)),hp=void 0;function ip(a,b){if(b.length&&Dk){var c;(c=$o)[a]!=null||(c[a]=[]);ap[a]!=null||(ap[a]=[]);var d=b.filter(function(e){return!ap[a].includes(e)});$o[a].push.apply($o[a],ta(d));ap[a].push.apply(ap[a],ta(d));!hp&&d.length>0&&(dn("tdc",!0),hp=y.setTimeout(function(){gn();$o={};hp=void 0},Zo))}}
function jp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function kp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;Wc(t)==="object"?u=t[r]:Wc(t)==="array"&&(u=t[r]);return u===void 0?gp[r]:u},f=jp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=Wc(m)==="object"||Wc(m)==="array",q=Wc(n)==="object"||Wc(n)==="array";if(p&&q)kp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function lp(){cn("tdc",function(){hp&&(y.clearTimeout(hp),hp=void 0);var a=[],b;for(b in $o)$o.hasOwnProperty(b)&&a.push(b+"*"+$o[b].join("."));return a.length?a.join("!"):void 0},!1)};var mp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.P=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},np=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.P);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.P);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.P)}return c},P=function(a,b,c,d){for(var e=l(np(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},op=function(a){for(var b={},c=np(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)},pp=function(a,b,c){function d(n){Yc(n)&&jb(n,function(p,q){f=!0;e[p]=q})}var e={},f=!1,g=np(a,c===void 0?3:c);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[b]);return f?e:void 0},qp=function(a){for(var b=[N.m.Be,N.m.xe,
N.m.ye,N.m.ze,N.m.Ae,N.m.Ce,N.m.De],c=np(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},rp=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.ka={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},sp=function(a,b){a.J=b;return a},tp=function(a,b){a.T=b;
return a},up=function(a,b){a.D=b;return a},vp=function(a,b){a.O=b;return a},wp=function(a,b){a.ka=b;return a},xp=function(a,b){a.P=b;return a},yp=function(a,b){a.eventMetadata=b||{};return a},zp=function(a,b){a.onSuccess=b;return a},Ap=function(a,b){a.onFailure=b;return a},Bp=function(a,b){a.isGtmEvent=b;return a},Cp=function(a){return new mp(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={C:{Fj:"accept_by_default",dg:"add_tag_timing",eg:"allow_ad_personalization",Hj:"batch_on_navigation",Ij:"client_id_source",me:"consent_event_id",ne:"consent_priority_id",Qp:"consent_state",da:"consent_updated",Jc:"conversion_linker_enabled",ra:"cookie_options",ig:"create_dc_join",Bh:"create_fpm_join",pe:"create_google_join",kg:"em_event",Up:"endpoint_for_debug",Uj:"enhanced_client_id_source",Eh:"enhanced_match_result",bd:"euid_mode_enabled",ab:"event_start_timestamp_ms",Sk:"event_usage",Pg:"extra_tag_experiment_ids",
bq:"add_parameter",hi:"attribution_reporting_experiment",ii:"counting_method",Qg:"send_as_iframe",cq:"parameter_order",Rg:"parsed_target",vn:"ga4_collection_subdomain",Vk:"gbraid_cookie_marked",ba:"hit_type",ed:"hit_type_override",yn:"is_config_command",Sg:"is_consent_update",ff:"is_conversion",Zk:"is_ecommerce",fd:"is_external_event",ni:"is_fallback_aw_conversion_ping_allowed",hf:"is_first_visit",al:"is_first_visit_conversion",Tg:"is_fl_fallback_conversion_flow_allowed",Ug:"is_fpm_encryption",Od:"is_fpm_split",
Pd:"is_gcp_conversion",oi:"is_google_signals_allowed",Qd:"is_merchant_center",Vg:"is_new_to_site",Wg:"is_server_side_destination",Rd:"is_session_start",fl:"is_session_start_conversion",hq:"is_sgtm_ga_ads_conversion_study_control_group",iq:"is_sgtm_prehit",il:"is_sgtm_service_worker",ri:"is_split_conversion",zn:"is_syn",Xg:"join_id",jf:"join_timer_sec",Td:"tunnel_updated",nq:"promises",oq:"record_aw_latency",kc:"redact_ads_data",Ud:"redact_click_ids",Kn:"remarketing_only",rl:"send_ccm_parallel_ping",
ah:"send_fledge_experiment",rq:"send_ccm_parallel_test_ping",rf:"send_to_destinations",xi:"send_to_targets",sl:"send_user_data_hit",lb:"source_canonical_id",Ea:"speculative",wl:"speculative_in_message",xl:"suppress_script_load",yl:"syn_or_mod",Ii:"transient_ecsid",uf:"transmission_type",Oa:"user_data",uq:"user_data_from_automatic",wq:"user_data_from_automatic_getter",Wd:"user_data_from_code",fh:"user_data_from_manual",Cl:"user_data_mode",vf:"user_id_updated"}};var Dp={rm:Number("5"),Mq:Number("")},Ep=[],Fp=!1;function Gp(a){Ep.push(a)}var Hp="?id="+Wf.ctid,Ip=void 0,Jp={},Kp=void 0,Lp=new function(){var a=5;Dp.rm>0&&(a=Dp.rm);this.J=a;this.D=0;this.O=[]},Mp=1E3;
function Np(a,b){var c=Ip;if(c===void 0)if(b)c=Qo();else return"";for(var d=[vk("https://www.googletagmanager.com"),"/a",Hp],e=l(Ep),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,vd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Op(){if(pj.T&&(Kp&&(y.clearTimeout(Kp),Kp=void 0),Ip!==void 0&&Pp)){var a=Wm(vm.W.Ec);if(Sm(a))Fp||(Fp=!0,Um(a,Op));else{var b;if(!(b=Jp[Ip])){var c=Lp;b=c.D<c.J?!1:qb()-c.O[c.D%c.J]<1E3}if(b||Mp--<=0)O(1),Jp[Ip]=!0;else{var d=Lp,e=d.D++%d.J;d.O[e]=qb();var f=Np(!0);Dl({destinationId:Wf.ctid,endpoint:56,eventId:Ip},f);Fp=Pp=!1}}}}function Qp(){if(Ck&&pj.T){var a=Np(!0,!0);Dl({destinationId:Wf.ctid,endpoint:56,eventId:Ip},a)}}var Pp=!1;
function Rp(a){Jp[a]||(a!==Ip&&(Op(),Ip=a),Pp=!0,Kp||(Kp=y.setTimeout(Op,500)),Np().length>=2022&&Op())}var Sp=gb();function Tp(){Sp=gb()}function Up(){return[["v","3"],["t","t"],["pid",String(Sp)]]};var Vp={};function Wp(a,b,c){Ck&&a!==void 0&&(Vp[a]=Vp[a]||[],Vp[a].push(c+b),Rp(a))}function Xp(a){var b=a.eventId,c=a.vd,d=[],e=Vp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Vp[b];return d};function Yp(a,b,c){var d=Vo(bm(a),!0);d&&Zp.register(d,b,c)}function $p(a,b,c,d){var e=Vo(c,d.isGtmEvent);e&&(xj&&(d.deferrable=!0),Zp.push("event",[b,a],e,d))}function aq(a,b,c,d){var e=Vo(c,d.isGtmEvent);e&&Zp.push("get",[a,b],e,d)}function bq(a){var b=Vo(bm(a),!0),c;b?c=cq(Zp,b).D:c={};return c}function dq(a,b){var c=Vo(bm(a),!0);if(c){var d=Zp,e=Zc(b,null);Zc(cq(d,c).D,e);cq(d,c).D=e}}
var eq=function(){this.T={};this.D={};this.J={};this.ka=null;this.P={};this.O=!1;this.status=1},fq=function(a,b,c,d){this.J=qb();this.D=b;this.args=c;this.messageContext=d;this.type=a},gq=function(){this.destinations={};this.D={};this.commands=[]},cq=function(a,b){var c=b.destinationId;Sl||(c=gm(c));return a.destinations[c]=a.destinations[c]||new eq},hq=function(a,b,c,d){if(d.D){var e=cq(a,d.D),f=e.ka;if(f){var g=d.D.id;Sl||(g=gm(g));var h=Zc(c,null),m=Zc(e.T[g],null),n=Zc(e.P,null),p=Zc(e.D,null),
q=Zc(a.D,null),r={};if(Ck)try{r=Zc(Pj,null)}catch(x){O(72)}var t=d.D.prefix,u=function(x){Wp(d.messageContext.eventId,t,x)},v=Cp(Bp(Ap(zp(yp(wp(vp(xp(up(tp(sp(new rp(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Wp(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(Dk&&x==="config"){var B,D=(B=Vo(z))==null?void 0:B.ids;if(!(D&&D.length>1)){var F,G=kc("google_tag_data",{});G.td||(G.td={});F=G.td;var J=Zc(v.P);Zc(v.D,J);var M=[],U;for(U in F)F.hasOwnProperty(U)&&kp(F[U],J).length&&M.push(U);M.length&&(ip(z,M),Wa("TAGGING",bp[A.readyState]||14));F[z]=J}}f(d.D.id,b,d.J,v)}catch(K){Wp(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():Um(e.la,w)}}};
gq.prototype.register=function(a,b,c){var d=cq(this,a);d.status!==3&&(d.ka=b,d.status=3,d.la=Wm(c),this.flush())};gq.prototype.push=function(a,b,c,d){c!==void 0&&(cq(this,c).status===1&&(cq(this,c).status=2,this.push("require",[{}],c,{})),cq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.C.rf]||(d.eventMetadata[Q.C.rf]=[c.destinationId]),d.eventMetadata[Q.C.xi]||(d.eventMetadata[Q.C.xi]=[c.id]));this.commands.push(new fq(a,c,b,d));d.deferrable||this.flush()};
gq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={mc:void 0,kh:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||cq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(cq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];jb(h,function(u,v){Zc(xb(u,v),b.D)});nj(h,!0);break;case "config":var m=cq(this,g);
e.mc={};jb(f.args[0],function(u){return function(v,w){Zc(xb(v,w),u.mc)}}(e));var n=!!e.mc[N.m.Zc];delete e.mc[N.m.Zc];var p=g.destinationId===g.id;nj(e.mc,!0);n||(p?m.P={}:m.T[g.id]={});m.O&&n||hq(this,N.m.na,e.mc,f);m.O=!0;p?Zc(e.mc,m.P):(Zc(e.mc,m.T[g.id]),O(70));d=!0;vn(e.mc,g.id);pn=!0;break;case "event":e.kh={};jb(f.args[0],function(u){return function(v,w){Zc(xb(v,w),u.kh)}}(e));nj(e.kh);hq(this,f.args[1],e.kh,f);var q=void 0;!f.D||((q=f.messageContext.eventMetadata)==null?0:q[Q.C.kg])||(sn[f.D.id]=
!0);pn=!0;break;case "get":var r={},t=(r[N.m.bc]=f.args[0],r[N.m.yc]=f.args[1],r);hq(this,N.m.ub,t,f);pn=!0}this.commands.shift();iq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var iq=function(a,b){if(b.type!=="require")if(b.D)for(var c=cq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},Zp=new gq;function jq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function kq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function lq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=il(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=dc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}kq(e,"load",f);kq(e,"error",f)};jq(e,"load",f);jq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function mq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";fl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});nq(c,b)}
function nq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else lq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var oq=function(){this.ka=this.ka;this.P=this.P};oq.prototype.ka=!1;oq.prototype.dispose=function(){this.ka||(this.ka=!0,this.O())};oq.prototype[Symbol.dispose]=function(){this.dispose()};oq.prototype.addOnDisposeCallback=function(a,b){this.ka?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};oq.prototype.O=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function pq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var qq=function(a,b){b=b===void 0?{}:b;oq.call(this);this.D=null;this.la={};this.Fc=0;this.T=null;this.J=a;var c;this.kb=(c=b.timeoutMs)!=null?c:500;var d;this.Ia=(d=b.Aq)!=null?d:!1};ra(qq,oq);qq.prototype.O=function(){this.la={};this.T&&(kq(this.J,"message",this.T),delete this.T);delete this.la;delete this.J;delete this.D;oq.prototype.O.call(this)};var sq=function(a){return typeof a.J.__tcfapi==="function"||rq(a)!=null};
qq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ia},d=Yk(function(){return a(c)}),e=0;this.kb!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.kb));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=pq(c),c.internalBlockOnErrors=b.Ia,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{tq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};qq.prototype.removeEventListener=function(a){a&&a.listenerId&&tq(this,"removeEventListener",null,a.listenerId)};
var vq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=uq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&uq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?uq(a.purpose.legitimateInterests,
b)&&uq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},uq=function(a,b){return!(!a||!a[b])},tq=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(rq(a)){wq(a);var g=++a.Fc;a.la[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},rq=function(a){if(a.D)return a.D;a.D=gl(a.J,"__tcfapiLocator");return a.D},wq=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;jq(a.J,"message",b)}},xq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=pq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(mq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var yq={1:0,3:0,4:0,7:3,9:3,10:3};function zq(){return Mo("tcf",function(){return{}})}var Aq=function(){return new qq(y,{timeoutMs:-1})};
function Bq(){var a=zq(),b=Aq();sq(b)&&!Cq()&&!Dq()&&O(124);if(!a.active&&sq(b)){Cq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,wm().active=!0,a.tcString="tcunavailable");Fo();try{b.addEventListener(function(c){if(c.internalErrorState!==0)Eq(a),Go([N.m.U,N.m.Ha,N.m.V]),wm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,Dq()&&(a.active=!0),!Fq(c)||Cq()||Dq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in yq)yq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(Fq(c)){var g={},h;for(h in yq)if(yq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={zo:!0};p=p===void 0?{}:p;m=xq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.zo)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?vq(n,"1",0):!0:!1;g["1"]=m}else g[h]=vq(c,h,yq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Go([N.m.U,N.m.Ha,N.m.V]),wm().active=!0):(r[N.m.Ha]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Go([N.m.V]),zo(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:Gq()||""}))}}else Go([N.m.U,N.m.Ha,N.m.V])})}catch(c){Eq(a),Go([N.m.U,N.m.Ha,N.m.V]),wm().active=!0}}}
function Eq(a){a.type="e";a.tcString="tcunavailable"}function Fq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function Cq(){return y.gtag_enable_tcf_support===!0}function Dq(){return zq().enableAdvertiserConsentMode===!0}function Gq(){var a=zq();if(a.active)return a.tcString}function Hq(){var a=zq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Iq(a){if(!yq.hasOwnProperty(String(a)))return!0;var b=zq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Jq=[N.m.U,N.m.aa,N.m.V,N.m.Ha],Kq={},Lq=(Kq[N.m.U]=1,Kq[N.m.aa]=2,Kq);function Mq(a){if(a===void 0)return 0;switch(P(a,N.m.Ba)){case void 0:return 1;case !1:return 3;default:return 2}}function Nq(a){if(Nn()==="US-CO"&&gc.globalPrivacyControl===!0)return!1;var b=Mq(a);if(b===3)return!1;switch(Fm(N.m.Ha)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Oq(){return Im()||!Em(N.m.U)||!Em(N.m.aa)}
function Pq(){var a={},b;for(b in Lq)Lq.hasOwnProperty(b)&&(a[Lq[b]]=Fm(b));return"G1"+Ne(a[1]||0)+Ne(a[2]||0)}var Qq={},Rq=(Qq[N.m.U]=0,Qq[N.m.aa]=1,Qq[N.m.V]=2,Qq[N.m.Ha]=3,Qq);function Sq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Tq(a){for(var b="1",c=0;c<Jq.length;c++){var d=b,e,f=Jq[c],g=Dm.delegatedConsentTypes[f];e=g===void 0?0:Rq.hasOwnProperty(g)?12|Rq[g]:8;var h=wm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Sq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Sq(m.declare)<<4|Sq(m.default)<<2|Sq(m.update)])}var n=b,p=(Nn()==="US-CO"&&gc.globalPrivacyControl===!0?1:0)<<3,q=(Im()?1:0)<<2,r=Mq(a);b=
n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Dm.containerScopedDefaults.ad_storage<<4|Dm.containerScopedDefaults.analytics_storage<<2|Dm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Dm.usedContainerScopedDefaults?1:0)<<2|Dm.containerScopedDefaults.ad_personalization]}
function Uq(){if(!Em(N.m.V))return"-";for(var a=Object.keys(ei),b=Gm(a),c="",d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;b[f]&&(c+=ei[f])}(Dm.usedCorePlatformServices?Dm.selectedAllCorePlatformServices:1)&&(c+="o");return c||"-"}function Vq(){return Pn()||(Cq()||Dq())&&Hq()==="1"?"1":"0"}function Wq(){return(Pn()?!0:!(!Cq()&&!Dq())&&Hq()==="1")||!Em(N.m.V)}
function Xq(){var a="0",b="0",c;var d=zq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=zq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;Pn()&&(h|=1);Hq()==="1"&&(h|=2);Cq()&&(h|=4);var m;var n=zq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);wm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Yq(){return Nn()==="US-CO"};function Zq(){var a=!1;return a};var $q={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function ar(a){a=a===void 0?{}:a;var b=Wf.ctid.split("-")[0].toUpperCase(),c={ctid:Wf.ctid,vp:sj.vi,xp:sj.wi,ap:Rl.kf?2:1,Ep:a.hm,zf:Wf.canonicalContainerId};c.zf!==a.Ja&&(c.Ja=a.Ja);var d=dm();c.jp=d?d.canonicalContainerId:void 0;yj?(c.vh=$q[b],c.vh||(c.vh=0)):c.vh=Cj?13:10;pj.D?(c.sh=0,c.Xn=2):Aj?c.sh=1:Zq()?c.sh=2:c.sh=3;var e={};e[6]=Sl;pj.J===2?e[7]=!0:pj.J===1&&(e[2]=!0);if(jc){var f=gk(mk(jc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.ao=e;var g=a.gh,h;var m=c.vh,
n=c.sh;m===void 0?h="":(n||(n=0),h=""+Pe(1,1)+Me(m<<2|n));var p=c.Xn,q="4"+h+(p?""+Pe(2,1)+Me(p):""),r,t=c.xp;r=t&&Oe.test(t)?""+Pe(3,2)+t:"";var u,v=c.vp;u=v?""+Pe(4,1)+Me(v):"";var w;var x=c.ctid;if(x&&g){var z=x.split("-"),B=z[0].toUpperCase();if(B!=="GTM"&&B!=="OPT")w="";else{var D=z[1];w=""+Pe(5,3)+Me(1+D.length)+(c.ap||0)+D}}else w="";var F=c.Ep,G=c.zf,J=c.Ja,M=c.Jq,U=q+r+u+w+(F?""+Pe(6,1)+Me(F):"")+(G?""+Pe(7,3)+Me(G.length)+G:"")+(J?""+Pe(8,3)+Me(J.length)+J:"")+(M?""+Pe(9,3)+Me(M.length)+
M:""),K;var ba=c.ao;ba=ba===void 0?{}:ba;for(var Y=[],ea=l(Object.keys(ba)),V=ea.next();!V.done;V=ea.next()){var R=V.value;Y[Number(R)]=ba[R]}if(Y.length){var ka=Pe(10,3),ja;if(Y.length===0)ja=Me(0);else{for(var ma=[],Ka=0,Na=!1,Ea=0;Ea<Y.length;Ea++){Na=!0;var Xa=Ea%6;Y[Ea]&&(Ka|=1<<Xa);Xa===5&&(ma.push(Me(Ka)),Ka=0,Na=!1)}Na&&ma.push(Me(Ka));ja=ma.join("")}var bb=ja;K=""+ka+Me(bb.length)+bb}else K="";var Bb=c.jp;return U+K+(Bb?""+Pe(11,3)+Me(Bb.length)+Bb:"")};function br(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var cr={N:{Ln:0,Gj:1,gg:2,Lj:3,zh:4,Jj:5,Kj:6,Mj:7,Ah:8,Qk:9,Pk:10,gi:11,Rk:12,Og:13,Uk:14,nf:15,Jn:16,Vd:17,Ei:18,Fi:19,Gi:20,zl:21,Hi:22,Ch:23,Tj:24}};cr.N[cr.N.Ln]="RESERVED_ZERO";cr.N[cr.N.Gj]="ADS_CONVERSION_HIT";cr.N[cr.N.gg]="CONTAINER_EXECUTE_START";cr.N[cr.N.Lj]="CONTAINER_SETUP_END";cr.N[cr.N.zh]="CONTAINER_SETUP_START";cr.N[cr.N.Jj]="CONTAINER_BLOCKING_END";cr.N[cr.N.Kj]="CONTAINER_EXECUTE_END";cr.N[cr.N.Mj]="CONTAINER_YIELD_END";cr.N[cr.N.Ah]="CONTAINER_YIELD_START";cr.N[cr.N.Qk]="EVENT_EXECUTE_END";
cr.N[cr.N.Pk]="EVENT_EVALUATION_END";cr.N[cr.N.gi]="EVENT_EVALUATION_START";cr.N[cr.N.Rk]="EVENT_SETUP_END";cr.N[cr.N.Og]="EVENT_SETUP_START";cr.N[cr.N.Uk]="GA4_CONVERSION_HIT";cr.N[cr.N.nf]="PAGE_LOAD";cr.N[cr.N.Jn]="PAGEVIEW";cr.N[cr.N.Vd]="SNIPPET_LOAD";cr.N[cr.N.Ei]="TAG_CALLBACK_ERROR";cr.N[cr.N.Fi]="TAG_CALLBACK_FAILURE";cr.N[cr.N.Gi]="TAG_CALLBACK_SUCCESS";cr.N[cr.N.zl]="TAG_EXECUTE_END";cr.N[cr.N.Hi]="TAG_EXECUTE_START";cr.N[cr.N.Ch]="CUSTOM_PERFORMANCE_START";cr.N[cr.N.Tj]="CUSTOM_PERFORMANCE_END";var dr=[],er={},fr={};var gr=["1"];function hr(a){return a.origin!=="null"};function ir(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return jg(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function jr(a,b,c,d){if(!kr(d))return[];if(dr.includes("1")){var e;(e=Mc())==null||e.mark("1-"+cr.N.Ch+"-"+(fr["1"]||0))}var f=ir(a,String(b||lr()),c);if(dr.includes("1")){var g="1-"+cr.N.Tj+"-"+(fr["1"]||0),h={start:"1-"+cr.N.Ch+"-"+(fr["1"]||0),end:g},m;(m=Mc())==null||m.mark(g);var n,p,q=(p=(n=Mc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(fr["1"]=(fr["1"]||0)+1,er["1"]=q+(er["1"]||0))}return f}
function mr(a,b,c,d,e){if(kr(e)){var f=nr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=or(f,function(g){return g.lo},b);if(f.length===1)return f[0];f=or(f,function(g){return g.lp},c);return f[0]}}}function pr(a,b,c,d){var e=lr(),f=window;hr(f)&&(f.document.cookie=a);var g=lr();return e!==g||c!==void 0&&jr(b,g,!1,d).indexOf(c)>=0}
function qr(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!kr(c.Rb))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=rr(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.ep);g=e(g,"samesite",c.yp);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=sr(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!tr(u,c.path)&&pr(v,a,b,c.Rb))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return tr(n,c.path)?1:pr(g,a,b,c.Rb)?0:1}function ur(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return qr(a,b,c)}
function or(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function nr(a,b,c){for(var d=[],e=jr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({co:e[f],eo:g.join("."),lo:Number(n[0])||1,lp:Number(n[1])||1})}}}return d}function rr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var vr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,wr=/(^|\.)doubleclick\.net$/i;function tr(a,b){return a!==void 0&&(wr.test(window.document.location.hostname)||b==="/"&&vr.test(a))}function xr(a){if(!a)return 1;var b=a;jg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function yr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function zr(a,b){var c=""+xr(a),d=yr(b);d>1&&(c+="-"+d);return c}
var lr=function(){return hr(window)?window.document.cookie:""},kr=function(a){return a&&jg(8)?(Array.isArray(a)?a:[a]).every(function(b){return Hm(b)&&Em(b)}):!0},sr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;wr.test(e)||vr.test(e)||a.push("none");return a};function Ar(a){var b=Math.round(Math.random()*2147483647);return a?String(b^br(a)&2147483647):String(b)}function Br(a){return[Ar(a),Math.round(qb()/1E3)].join(".")}function Cr(a,b,c,d,e){var f=xr(b),g;return(g=mr(a,f,yr(c),d,e))==null?void 0:g.eo}function Dr(a,b,c,d){return[b,zr(c,d),a].join(".")};function Er(a,b,c,d){var e,f=Number(a.Qb!=null?a.Qb:void 0);f!==0&&(e=new Date((b||qb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Rb:d}};var Fr=["ad_storage","ad_user_data"];function Gr(a,b){if(!a)return Wa("TAGGING",32),10;if(b===null||b===void 0||b==="")return Wa("TAGGING",33),11;var c=Hr(!1);if(c.error!==0)return Wa("TAGGING",34),c.error;if(!c.value)return Wa("TAGGING",35),2;c.value[a]=b;var d=Ir(c);d!==0&&Wa("TAGGING",36);return d}
function Jr(a){if(!a)return Wa("TAGGING",27),{error:10};var b=Hr();if(b.error!==0)return Wa("TAGGING",29),b;if(!b.value)return Wa("TAGGING",30),{error:2};if(!(a in b.value))return Wa("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(Wa("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Hr(a){a=a===void 0?!0:a;if(!Em(Fr))return Wa("TAGGING",43),{error:3};try{if(!y.localStorage)return Wa("TAGGING",44),{error:1}}catch(f){return Wa("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=y.localStorage.getItem("_gcl_ls")}catch(f){return Wa("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return Wa("TAGGING",47),{error:12}}}catch(f){return Wa("TAGGING",48),{error:8}}if(b.schema!=="gcl")return Wa("TAGGING",49),{error:4};
if(b.version!==1)return Wa("TAGGING",50),{error:5};try{var e=Kr(b);a&&e&&Ir({value:b,error:0})}catch(f){return Wa("TAGGING",48),{error:8}}return{value:b,error:0}}
function Kr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,Wa("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Kr(a[e.value])||c;return c}return!1}
function Ir(a){if(a.error)return a.error;if(!a.value)return Wa("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return Wa("TAGGING",52),6}try{y.localStorage.setItem("_gcl_ls",c)}catch(d){return Wa("TAGGING",53),7}return 0};function Lr(){if(!Mr())return-1;var a=Nr();return a!==-1&&Or(a+1)?a+1:-1}function Nr(){if(!Mr())return-1;var a=Jr("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Mr(){return Em(["ad_storage","ad_user_data"])?jg(11):!1}
function Or(a,b){b=b||{};var c=qb();return Gr("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(Er(b,c,!0).expires)})===0?!0:!1};var Pr;function Qr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Rr,d=Sr,e=Tr();if(!e.init){xc(A,"mousedown",a);xc(A,"keyup",a);xc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ur(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Tr().decorators.push(f)}
function Vr(a,b,c){for(var d=Tr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&tb(e,g.callback())}}return e}
function Tr(){var a=kc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Wr=/(.*?)\*(.*?)\*(.*)/,Xr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Yr=/^(?:www\.|m\.|amp\.)+/,Zr=/([^?#]+)(\?[^#]*)?(#.*)?/;function $r(a){var b=Zr.exec(a);if(b)return{rj:b[1],query:b[2],fragment:b[3]}}function as(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function bs(a,b){var c=[gc.userAgent,(new Date).getTimezoneOffset(),gc.userLanguage||gc.language,Math.floor(qb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Pr)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Pr=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Pr[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function cs(a){return function(b){var c=mk(y.location.href),d=c.search.replace("?",""),e=ek(d,"_gl",!1,!0)||"";b.query=ds(e)||{};var f=gk(c,"fragment"),g;var h=-1;if(vb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=ds(g||"")||{};a&&es(c,d,f)}}function fs(a,b){var c=as(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function es(a,b,c){function d(g,h){var m=fs("_gl",g);m.length&&(m=h+m);return m}if(fc&&fc.replaceState){var e=as("_gl");if(e.test(b)||e.test(c)){var f=gk(a,"path");b=d(b,"?");c=d(c,"#");fc.replaceState({},"",""+f+b+c)}}}function gs(a,b){var c=cs(!!b),d=Tr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(tb(e,f.query),a&&tb(e,f.fragment));return e}
var ds=function(a){try{var b=hs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Ua(d[e+1]);c[f]=g}Wa("TAGGING",6);return c}}catch(h){Wa("TAGGING",8)}};function hs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Wr.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===bs(h,p)){m=!0;break a}m=!1}if(m)return h;Wa("TAGGING",7)}}}
function is(a,b,c,d,e){function f(p){p=fs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=$r(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.rj+h+m}
function js(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Ta(String(x))))}var z=v.join("*");u=["1",bs(z),z].join("*");d?(jg(3)||jg(1)||!p)&&ks("_gl",u,a,p,q):ls("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Vr(b,1,d),f=Vr(b,2,d),g=Vr(b,4,d),h=Vr(b,3,d);c(e,!1,!1);c(f,!0,!1);jg(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ms(m,h[m],a)}function ms(a,b,c){c.tagName.toLowerCase()==="a"?ls(a,b,c):c.tagName.toLowerCase()==="form"&&ks(a,b,c)}function ls(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!jg(5)||d)){var h=y.location.href,m=$r(c.href),n=$r(h);g=!(m&&n&&m.rj===n.rj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=is(a,b,c.href,d,e);Wb.test(p)&&(c.href=p)}}
function ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=is(a,b,f,d,e);Wb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Rr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||js(e,e.hostname)}}catch(g){}}function Sr(a){try{var b=a.getAttribute("action");if(b){var c=gk(mk(b),"host");js(a,c)}}catch(d){}}function ns(a,b,c,d){Qr();var e=c==="fragment"?2:1;d=!!d;Ur(a,b,e,d,!1);e===2&&Wa("TAGGING",23);d&&Wa("TAGGING",24)}
function os(a,b){Qr();Ur(a,[ik(y.location,"host",!0)],b,!0,!0)}function ps(){var a=A.location.hostname,b=Xr.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Yr,""),m=e.replace(Yr,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function qs(a,b){return a===!1?!1:a||b||ps()};var rs=["1"],ss={},ts={};function us(a,b){b=b===void 0?!0:b;var c=vs(a.prefix);if(ss[c])ws(a);else if(xs(c,a.path,a.domain)){var d=ts[vs(a.prefix)]||{id:void 0,rh:void 0};b&&ys(a,d.id,d.rh);ws(a)}else{var e=ok("auiddc");if(e)Wa("TAGGING",17),ss[c]=e;else if(b){var f=vs(a.prefix),g=Br();zs(f,g,a);xs(c,a.path,a.domain);ws(a,!0)}}}
function ws(a,b){if((b===void 0?0:b)&&Mr()){var c=Hr(!1);c.error!==0?Wa("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Ir(c)!==0&&Wa("TAGGING",41)):Wa("TAGGING",40):Wa("TAGGING",39)}Em(["ad_storage","ad_user_data"])&&jg(10)&&Nr()===-1&&Or(0,a)}function ys(a,b,c){var d=vs(a.prefix),e=ss[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(qb()/1E3)));zs(d,h,a,g*1E3)}}}}
function zs(a,b,c,d){var e=Dr(b,"1",c.domain,c.path),f=Er(c,d);f.Rb=As();ur(a,e,f)}function xs(a,b,c){var d=Cr(a,b,c,rs,As());if(!d)return!1;Bs(a,d);return!0}function Bs(a,b){var c=b.split(".");c.length===5?(ss[a]=c.slice(0,2).join("."),ts[a]={id:c.slice(2,4).join("."),rh:Number(c[4])||0}):c.length===3?ts[a]={id:c.slice(0,2).join("."),rh:Number(c[2])||0}:ss[a]=b}function vs(a){return(a||"_gcl")+"_au"}function Cs(a){function b(){Em(c)&&a()}var c=As();Lm(function(){b();Em(c)||Mm(b,c)},c)}
function Ds(a){var b=gs(!0),c=vs(a.prefix);Cs(function(){var d=b[c];if(d){Bs(c,d);var e=Number(ss[c].split(".")[1])*1E3;if(e){Wa("TAGGING",16);var f=Er(a,e);f.Rb=As();var g=Dr(d,"1",a.domain,a.path);ur(c,g,f)}}})}function Es(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Cr(a,e.path,e.domain,rs,As());h&&(g[a]=h);return g};Cs(function(){ns(f,b,c,d)})}function As(){return["ad_storage","ad_user_data"]};function Fs(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Dj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Gs(a,b){var c=Fs(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Dj]||(d[c[e].Dj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Dj].push(g)}}return d};var Hs={},Is=(Hs.k={Z:/^[\w-]+$/},Hs.b={Z:/^[\w-]+$/,yj:!0},Hs.i={Z:/^[1-9]\d*$/},Hs.h={Z:/^\d+$/},Hs.t={Z:/^[1-9]\d*$/},Hs.d={Z:/^[A-Za-z0-9_-]+$/},Hs.j={Z:/^\d+$/},Hs.u={Z:/^[1-9]\d*$/},Hs.l={Z:/^[01]$/},Hs.o={Z:/^[1-9]\d*$/},Hs.g={Z:/^[01]$/},Hs.s={Z:/^.+$/},Hs);var Js={},Ns=(Js[5]={xh:{2:Ks},jj:"2",hh:["k","i","b","u"]},Js[4]={xh:{2:Ks,GCL:Ls},jj:"2",hh:["k","i","b"]},Js[2]={xh:{GS2:Ks,GS1:Ms},jj:"GS2",hh:"sogtjlhd".split("")},Js);function Os(a,b,c){var d=Ns[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.xh[e];if(f)return f(a,b)}}}
function Ks(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ns[b];if(f){for(var g=f.hh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Is[p];r&&(r.yj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ps(a,b,c){var d=Ns[b];if(d)return[d.jj,c||"1",Qs(a,b)].join(".")}
function Qs(a,b){var c=Ns[b];if(c){for(var d=[],e=l(c.hh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Is[g];if(h){var m=a[g];if(m!==void 0)if(h.yj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ls(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ms(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Rs=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ss(a,b,c){if(Ns[b]){for(var d=[],e=jr(a,void 0,void 0,Rs.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Os(g.value,b,c);h&&d.push(Ts(h))}return d}}function Us(a,b,c,d,e){d=d||{};var f=zr(d.domain,d.path),g=Ps(b,c,f);if(!g)return 1;var h=Er(d,e,void 0,Rs.get(c));return ur(a,g,h)}function Vs(a,b){var c=b.Z;return typeof c==="function"?c(a):c.test(a)}
function Ts(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Bf:void 0},c=b.next()){var e=c.value,f=a[e];d.Bf=Is[e];d.Bf?d.Bf.yj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Vs(h,g.Bf)}}(d)):void 0:typeof f==="string"&&Vs(f,d.Bf)||(a[e]=void 0):a[e]=void 0}return a};function Ws(){var a=String,b=y.location.hostname,c=y.location.pathname,d=b=Eb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Eb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(br((""+b+e).toLowerCase()))};var Xs=/^\w+$/,Ys=/^[\w-]+$/,Zs={},$s=(Zs.aw="_aw",Zs.dc="_dc",Zs.gf="_gf",Zs.gp="_gp",Zs.gs="_gs",Zs.ha="_ha",Zs.ag="_ag",Zs.gb="_gb",Zs);function at(){return["ad_storage","ad_user_data"]}function bt(a){return!jg(8)||Em(a)}function ct(a,b){function c(){var d=bt(b);d&&a();return d}Lm(function(){c()||Mm(c,b)},b)}function dt(a){return et(a).map(function(b){return b.gclid})}function ft(a){return gt(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}
function gt(a){var b=ht(a.prefix),c=it("gb",b),d=it("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=et(c).map(e("gb")),g=jt(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}function kt(a,b,c,d,e,f){var g=fb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.nd=f),g.labels=lt(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,nd:f})}
function jt(a){for(var b=Ss(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=mt(f);if(n){var p=void 0;jg(9)&&(p=f.u);kt(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function et(a){for(var b=[],c=jr(a,A.cookie,void 0,at()),d=l(c),e=d.next();!e.done;e=d.next()){var f=nt(e.value);if(f!=null){var g=f;kt(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ot(b)}
function pt(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function qt(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.fa===b.fa&&(e=h)}d?(d.fa=d.fa?b.fa?b.fa===5?d.fa<5?b.fa+d.fa:d.fa:d.timestamp<b.timestamp?b.fa:d.fa:d.fa||0:b.fa||0,d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.nd=b.nd),d.labels=pt(d.labels||[],b.labels||[]),d.tb=pt(d.tb||[],b.tb||[])):c&&e?Object.assign(e,b):a.push(b)}
function rt(){var a=Jr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;return d&&d.match(Ys)?{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],fa:c.linkDecorationSource||0,tb:[2]}:null}catch(e){return null}}
function st(){var a=Jr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Ys))return b;b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],fa:d.linkDecorationSource||0,tb:[2]});return b},[])}catch(b){return null}}
function tt(a){for(var b=[],c=jr(a,A.cookie,void 0,at()),d=l(c),e=d.next();!e.done;e=d.next()){var f=nt(e.value);f!=null&&(f.nd=void 0,f.fa=0,f.tb=[1],qt(b,f))}var g=rt();g&&(g.nd=void 0,g.fa=g.fa||0,g.tb=g.tb||[2],qt(b,g));if(jg(14)){var h=st();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.nd=void 0;p.fa=p.fa||0;p.tb=p.tb||[2];qt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ot(b)}
function lt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ht(a){return a&&typeof a==="string"&&a.match(Xs)?a:"_gcl"}
function ut(a,b,c){var d=mk(a),e=gk(d,"query",!1,void 0,"gclsrc"),f={value:gk(d,"query",!1,void 0,"gclid"),fa:c?4:2};if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=ek(g,"gclid",!1),f.fa=3);e||(e=ek(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function vt(a,b){var c=mk(a),d=gk(c,"query",!1,void 0,"gclid"),e=gk(c,"query",!1,void 0,"gclsrc"),f=gk(c,"query",!1,void 0,"wbraid");f=Cb(f);var g=gk(c,"query",!1,void 0,"gbraid"),h=gk(c,"query",!1,void 0,"gad_source"),m=gk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||ek(n,"gclid",!1);e=e||ek(n,"gclsrc",!1);f=f||ek(n,"wbraid",!1);g=g||ek(n,"gbraid",!1);h=h||ek(n,"gad_source",!1)}return wt(d,e,m,f,g,h)}function xt(){return vt(y.location.href,!0)}
function wt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Ys))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Ys.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Ys.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Ys.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function zt(a){for(var b=xt(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=vt(y.document.referrer,!1),b.gad_source=void 0);At(b,!1,a)}
function Bt(a){zt(a);var b=ut(y.location.href,!0,!1);b.length||(b=ut(y.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=qb(),e=Er(a,d,!0),f=at(),g=function(){bt(f)&&e.expires!==void 0&&Gr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSource:c.fa},expires:Number(e.expires)})};Lm(function(){g();bt(f)||Mm(g,f)},f)}}
function Ct(a,b){b=b||{};var c=qb(),d=Er(b,c,!0),e=at(),f=function(){if(bt(e)&&d.expires!==void 0){var g=st()||[];qt(g,{version:"",gclid:a,timestamp:c,expires:Number(d.expires),fa:5},!0);Gr("gcl_aw",g.map(function(h){return{value:{value:h.gclid,Bq:h.timestamp,fa:h.fa},expires:Number(h.expires)}}))}};Lm(function(){bt(e)?f():Mm(f,e)},e)}
function At(a,b,c,d,e){c=c||{};e=e||[];var f=ht(c.prefix),g=d||qb(),h=Math.round(g/1E3),m=at(),n=!1,p=!1,q=function(){if(bt(m)){var r=Er(c,g,!0);r.Rb=m;for(var t=function(M,U){var K=it(M,f);K&&(ur(K,U,r),M!=="gb"&&(n=!0))},u=function(M){var U=["GCL",h,M];e.length>0&&U.push(e.join("."));return U.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],B=it("gb",f);!b&&et(B).some(function(M){return M.gclid===z&&M.labels&&
M.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&bt("ad_storage")&&(p=!0,!n)){var D=a.gbraid,F=it("ag",f);if(b||!jt(F).some(function(M){return M.gclid===D&&M.labels&&M.labels.length>0})){var G={},J=(G.k=D,G.i=""+h,G.b=e,G);Us(F,J,5,c,g)}}Dt(a,f,g,c)};Lm(function(){q();bt(m)||Mm(q,m)},m)}
function Dt(a,b,c,d){if(a.gad_source!==void 0&&bt("ad_storage")){if(jg(4)){var e=Lc();if(e==="r"||e==="h")return}var f=a.gad_source,g=it("gs",b);if(g){var h=Math.floor((qb()-(Kc()||0))/1E3),m;if(jg(9)){var n=Ws(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Us(g,m,5,d,c)}}}
function Et(a,b){var c=gs(!0);ct(function(){for(var d=ht(b.prefix),e=0;e<a.length;++e){var f=a[e];if($s[f]!==void 0){var g=it(f,d),h=c[g];if(h){var m=Math.min(Ft(h),qb()),n;b:{for(var p=m,q=jr(g,A.cookie,void 0,at()),r=0;r<q.length;++r)if(Ft(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Er(b,m,!0);t.Rb=at();ur(g,h,t)}}}}At(wt(c.gclid,c.gclsrc),!1,b)},at())}
function Gt(a){var b=["ag"],c=gs(!0),d=ht(a.prefix);ct(function(){for(var e=0;e<b.length;++e){var f=it(b[e],d);if(f){var g=c[f];if(g){var h=Os(g,5);if(h){var m=mt(h);m||(m=qb());var n;a:{for(var p=m,q=Ss(f,5),r=0;r<q.length;++r)if(mt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Us(f,h,5,a,m)}}}}},["ad_storage"])}function it(a,b){var c=$s[a];if(c!==void 0)return b+c}function Ft(a){return Ht(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function mt(a){return a?(Number(a.i)||0)*1E3:0}function nt(a){var b=Ht(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ht(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Ys.test(a[2])?[]:a}
function It(a,b,c,d,e){if(Array.isArray(b)&&hr(y)){var f=ht(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=it(a[m],f);if(n){var p=jr(n,A.cookie,void 0,at());p.length&&(h[n]=p.sort()[p.length-1])}}return h};ct(function(){ns(g,b,c,d)},at())}}
function Jt(a,b,c,d){if(Array.isArray(a)&&hr(y)){var e=["ag"],f=ht(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=it(e[m],f);if(!n)return{};var p=Ss(n,5);if(p.length){var q=p.sort(function(r,t){return mt(t)-mt(r)})[0];h[n]=Ps(q,5)}}return h};ct(function(){ns(g,a,b,c)},["ad_storage"])}}function ot(a){return a.filter(function(b){return Ys.test(b.gclid)})}
function Kt(a,b){if(hr(y)){for(var c=ht(b.prefix),d={},e=0;e<a.length;e++)$s[a[e]]&&(d[a[e]]=$s[a[e]]);ct(function(){jb(d,function(f,g){var h=jr(c+g,A.cookie,void 0,at());h.sort(function(t,u){return Ft(u)-Ft(t)});if(h.length){var m=h[0],n=Ft(m),p=Ht(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ht(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];At(q,!0,b,n,p)}})},at())}}
function Lt(a){var b=["ag"],c=["gbraid"];ct(function(){for(var d=ht(a.prefix),e=0;e<b.length;++e){var f=it(b[e],d);if(!f)break;var g=Ss(f,5);if(g.length){var h=g.sort(function(q,r){return mt(r)-mt(q)})[0],m=mt(h),n=h.b,p={};p[c[e]]=h.k;At(p,!0,a,m,n)}}},["ad_storage"])}function Mt(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Nt(a){function b(h,m,n){n&&(h[m]=n)}if(Im()){var c=xt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:gs(!1)._gs);if(Mt(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);os(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);os(function(){return g},1)}}}
function Ot(a){if(!jg(1))return null;var b=gs(!0).gad_source;if(b!=null)return y.location.hash="",b;if(jg(2)){var c=mk(y.location.href);b=gk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=xt();if(Mt(d,a))return"0"}return null}function Pt(a){var b=Ot(a);b!=null&&os(function(){var c={};return c.gad_source=b,c},4)}
function Qt(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Rt(a,b,c,d){var e=[];c=c||{};if(!bt(at()))return e;var f=et(a),g=Qt(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Er(c,p,!0);r.Rb=at();ur(a,q,r)}return e}
function St(a,b){var c=[];b=b||{};var d=gt(b),e=Qt(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ht(b.prefix),n=it(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Us(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=Er(b,u,!0);B.Rb=at();ur(n,z,B)}}return c}
function Tt(a,b){var c=ht(b),d=it(a,c);if(!d)return 0;var e;e=a==="ag"?jt(d):et(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Ut(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Vt(a){var b=Math.max(Tt("aw",a),Ut(bt(at())?Gs():{})),c=Math.max(Tt("gb",a),Ut(bt(at())?Gs("_gac_gb",!0):{}));c=Math.max(c,Tt("ag",a));return c>b};function ku(){return Mo("dedupe_gclid",function(){return Br()})};var lu=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,mu=/^www.googleadservices.com$/;function nu(a){a||(a=ou());return a.Mp?!1:a.Lo||a.Mo||a.Po||a.No||a.Hf||a.yo||a.Oo||a.Do?!0:!1}function ou(){var a={},b=gs(!0);a.Mp=!!b._up;var c=xt();a.Lo=c.aw!==void 0;a.Mo=c.dc!==void 0;a.Po=c.wbraid!==void 0;a.No=c.gbraid!==void 0;a.Oo=c.gclsrc==="aw.ds";a.Hf=Yt().Hf;var d=A.referrer?gk(mk(A.referrer),"host"):"";a.Do=lu.test(d);a.yo=mu.test(d);return a};function pu(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function qu(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function ru(){return["ad_storage","ad_user_data"]}function su(a){if(H(38)&&!Cn(xn.jl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{pu(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Bn(xn.jl,function(d){d.gclid&&Ct(d.gclid,a)}),qu(c)||O(178))})}catch(c){O(177)}};Lm(function(){bt(ru())?b():Mm(b,ru())},ru())}};var tu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];
function uu(){if(H(119)){if(Cn(xn.qf))return O(176),xn.qf;if(Cn(xn.ml))return O(170),xn.qf;var a=jl();if(!a)O(171);else if(a.opener){var b=function(e){if(tu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?Bn(xn.qf,{gadSource:e.data.gadSource}):O(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);kq(a,"message",b)}else O(172)};if(jq(a,"message",b)){Bn(xn.ml,!0);for(var c=l(tu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);O(174);return xn.qf}O(175)}}}
;var vu=function(){this.D=this.gppString=void 0};vu.prototype.reset=function(){this.D=this.gppString=void 0};var wu=new vu;var xu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),yu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,zu=/^\d+\.fls\.doubleclick\.net$/,Au=/;gac=([^;?]+)/,Bu=/;gacgb=([^;?]+)/;
function Cu(a,b){if(zu.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(xu)?fk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Du(a,b,c){for(var d=bt(at())?Gs("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Rt("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{xo:f?e.join(";"):"",wo:Cu(d,Bu)}}function Eu(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(yu)?b[1]:void 0}
function Fu(a){var b=jg(9),c={},d,e,f;zu.test(A.location.host)&&(d=Eu("gclgs"),e=Eu("gclst"),b&&(f=Eu("gcllp")));if(d&&e&&(!b||f))c.lh=d,c.nh=e,c.mh=f;else{var g=qb(),h=jt((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.nd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.lh=m.join("."),c.nh=n.join("."),b&&p.length>0&&(c.mh=p.join(".")))}return c}
function Gu(a,b,c,d){d=d===void 0?!1:d;if(zu.test(A.location.host)){var e=Eu(c);if(e)return d?e.split(".").map(function(g){return{gclid:g,fa:1,tb:[1]}}):e.split(".").map(function(g){return{gclid:g}})}else{if(b==="gclid"){var f=(a||"_gcl")+"_aw";return d?tt(f):et(f)}if(b==="wbraid")return et((a||"_gcl")+"_gb");if(b==="braids")return gt({prefix:a})}return[]}function Hu(a){return zu.test(A.location.host)?!(Eu("gclaw")||Eu("gac")):Vt(a)}
function Iu(a,b,c){var d;d=c?St(a,b):Rt((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Ju(){var a=y.__uspapi;if(ab(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function Wu(a){var b=P(a.F,N.m.Bc),c=P(a.F,N.m.Ac);b&&!c?(a.eventName!==N.m.na&&a.eventName!==N.m.Bd&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function Xu(a){var b=Bo(N.m.U)?Lo.pscdl:"denied";b!=null&&W(a,N.m.sg,b)}
function Yu(a){var b=hl(!0);W(a,N.m.zc,b)}function Zu(a){Yq()&&W(a,N.m.Jd,1)}function Nu(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&fk(a.substring(0,b))===void 0;)b--;return fk(a.substring(0,b))||""}function $u(a){av(a,"ce",P(a.F,N.m.ib))}function av(a,b,c){Mu(a,N.m.Md)||W(a,N.m.Md,{});Mu(a,N.m.Md)[b]=c}function bv(a){T(a,Q.C.uf,vm.W.Aa)}function cv(a){var b=Ya("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,N.m.Ne,b),Va.GTAG_EVENT_FEATURE_CHANNEL=mj)}
function dv(a){var b=pp(a.F,N.m.Uc);b&&W(a,N.m.Uc,b)}function ev(a,b){b=b===void 0?!1:b;if(H(108)){var c=S(a,Q.C.rf);if(c)if(c.indexOf(a.target.destinationId)<0){if(T(a,Q.C.Fj,!1),b||!fv(a,"custom_event_accept_rules",!1))a.isAborted=!0}else T(a,Q.C.Fj,!0)}};function qv(a,b,c,d){var e=tc(),f;if(e===1)a:{var g=Ej;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==y.location.protocol?a:b)+c};function Cv(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Mu(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Mu(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return S(a,b)},setMetadata:function(b,c){T(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.F,b)},oc:function(){return a},getHitKeys:function(){return Object.keys(a.D)}}};function Jv(a,b){return arguments.length===1?Kv("set",a):Kv("set",a,b)}function Lv(a,b){return arguments.length===1?Kv("config",a):Kv("config",a,b)}function Mv(a,b,c){c=c||{};c[N.m.Wc]=a;return Kv("event",b,c)}function Kv(){return arguments};var Ov=function(){this.messages=[];this.D=[]};Ov.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};Ov.prototype.listen=function(a){this.D.push(a)};
Ov.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Ov.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Pv(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.C.lb]=Wf.canonicalContainerId;Qv().enqueue(a,b,c)}
function Rv(){var a=Sv;Qv().listen(a)}function Qv(){return Mo("mb",function(){return new Ov})};var Tv,Uv=!1;function Vv(){Uv=!0;Tv=Tv||{}}function Wv(a){Uv||Vv();return Tv[a]};function Xv(){var a=y.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Yv(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!y.getComputedStyle)return!0;var c=y.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=y.getComputedStyle(d,null))}return!1}var Sf;var vx=Number('')||5,wx=Number('')||50,xx=gb();
var zx=function(a,b){a&&(yx("sid",a.targetId,b),yx("cc",a.clientCount,b),yx("tl",a.totalLifeMs,b),yx("hc",a.heartbeatCount,b),yx("cl",a.clientLifeMs,b))},yx=function(a,b,c){b!=null&&c.push(a+"="+b)},Ax=function(){var a=A.referrer;if(a){var b;return gk(mk(a),"host")===((b=y.location)==null?void 0:b.host)?1:2}return 0},Cx=function(){this.T=Bx;this.O=0};Cx.prototype.J=function(a,b,c,d){var e=Ax(),f,g=[];f=y===y.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&yx("si",a.Nf,g);yx("m",0,g);yx("iss",f,g);yx("if",c,g);zx(b,g);d&&yx("fm",encodeURIComponent(d.substring(0,wx)),g);this.P(g);};Cx.prototype.D=function(a,b,c,d,e){var f=[];yx("m",1,f);yx("s",a,f);yx("po",Ax(),f);b&&(yx("st",b.state,f),yx("si",b.Nf,f),yx("sm",b.Xf,f));zx(c,f);yx("c",d,f);e&&yx("fm",encodeURIComponent(e.substring(0,wx)),f);this.P(f);};
Cx.prototype.P=function(a){a=a===void 0?[]:a;!Ck||this.O>=vx||(yx("pid",xx,a),yx("bc",++this.O,a),a.unshift("ctid="+Wf.ctid+"&t=s"),this.T("https://www.googletagmanager.com/a?"+a.join("&")))};var Dx=Number('')||500,Ex=Number('')||5E3,Fx=Number('20')||10,Gx=Number('')||5E3;function Hx(a){return a.performance&&a.performance.now()||Date.now()}
var Ix=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{Tl:function(){},Ul:function(){},Sl:function(){},onFailure:function(){}}:g;this.Rn=e;this.D=f;this.O=g;this.ka=this.la=this.heartbeatCount=this.Qn=0;this.Zg=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.Nf=Hx(this.D);this.Xf=Hx(this.D);this.T=10};d.prototype.init=function(){this.P(1);this.Ia()};d.prototype.getState=function(){return{state:this.state,
Nf:Math.round(Hx(this.D)-this.Nf),Xf:Math.round(Hx(this.D)-this.Xf)}};d.prototype.P=function(e){this.state!==e&&(this.state=e,this.Xf=Hx(this.D))};d.prototype.Bl=function(){return String(this.Qn++)};d.prototype.Ia=function(){var e=this;this.heartbeatCount++;this.kb({type:0,clientId:this.id,requestId:this.Bl(),maxDelay:this.bh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ka++,f.isDead||e.ka>Fx){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.P(4);e.On();var m,n;(n=(m=e.O).Sl)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.P(3),e.Dl();else{if(e.heartbeatCount>f.stats.heartbeatCount+Fx){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.P(2);if(r!==2)if(e.Zg){var t,u;(u=(t=e.O).Ul)==null||u.call(t)}else{e.Zg=!0;var v,w;(w=(v=e.O).Tl)==null||w.call(v)}e.ka=0;e.Sn();e.Dl()}}})};d.prototype.bh=function(){return this.state===2?
Ex:Dx};d.prototype.Dl=function(){var e=this;this.D.setTimeout(function(){e.Ia()},Math.max(0,this.bh()-(Hx(this.D)-this.la)))};d.prototype.Vn=function(e,f,g){var h=this;this.kb({type:1,clientId:this.id,requestId:this.Bl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.kb=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.lf(r,7)},(n=e.maxDelay)!=null?n:Gx),q={request:e,gm:f,Zl:h,cp:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.la=Hx(this.D);e.Zl=!1;this.Rn(e.request)};d.prototype.Sn=function(){for(var e=l(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.Zl&&this.sendRequest(g)}};d.prototype.On=function(){for(var e=
l(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.lf(this.J[f.value],this.T)};d.prototype.lf=function(e,f){this.Fc(e);var g=e.request;g.failure={failureType:f};e.gm(g)};d.prototype.Fc=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.cp)};d.prototype.Jo=function(e){this.la=Hx(this.D);var f=this.J[e.requestId];if(f)this.Fc(f),f.gm(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,y,b);return c};var Jx;
var Kx=function(){Jx||(Jx=new Cx);return Jx},Bx=function(a){Um(Wm(vm.W.Ec),function(){wc(a)})},Lx=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Mx=function(a){var b=a,c=pj.ka;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Nx=function(a){var b=Cn(xn.tl);return b&&b[a]},Ox=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.P=!1;this.ka=null;this.initTime=c;this.D=15;this.O=this.ho(a);y.setTimeout(function(){f.initialize()},1E3);C(function(){f.To(a,b,e)})};k=Ox.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),Nf:this.initTime,Xf:Math.round(qb())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.Vn(a,b,c)};k.getState=function(){return this.O.getState().state};k.To=function(a,b,c){var d=y.location.origin,e=this,
f=uc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Lx(h):"",p;H(133)&&(p={sandbox:"allow-same-origin allow-scripts"});uc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ka=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.Jo(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};k.ho=function(a){var b=this,c=Ix(function(d){var e;(e=b.ka)==null||e.postMessage(d,a.origin)},{Tl:function(){b.P=!0;b.J.J(c.getState(),c.stats)},Ul:function(){},Sl:function(d){b.P?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.T||this.O.init();this.T=!0};function Px(){var a=Vf(Sf.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Qx(a,b,c){c=c===void 0?!1:c;var d=y.location.origin;if(!d||!Px())return;Mj()&&(a=""+d+Lj()+"/_/service_worker");var e=Mx(a);if(e===null||Nx(e.origin))return;if(!hc()){Kx().J(void 0,void 0,6);return}var f=new Ox(e,!!a,b||Math.round(qb()),Kx(),c),g;a:{var h=xn.tl,m={},n=An(h);if(!n){n=An(h,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var Rx=function(a,b,c,d){var e;if((e=Nx(a))==null||!e.delegate){var f=hc()?16:6;Kx().D(f,void 0,void 0,b.commandType);d({failureType:f});return}Nx(a).delegate(b,c,d);};
function Sx(a,b,c,d,e){var f=Mx();if(f===null){d(hc()?16:6);return}var g,h=(g=Nx(f.origin))==null?void 0:g.initTime,m=Math.round(qb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Rx(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Tx(a,b,c,d){var e=Mx(a);if(e===null){d("_is_sw=f"+(hc()?16:6)+"te");return}var f=b?1:0,g=Math.round(qb()),h,m=(h=Nx(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0;Rx(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,sinceInit:n,attributionReporting:!0,referer:y.location.href}},function(){},function(p){var q="_is_sw=f"+p.failureType,r,t=(r=Nx(e.origin))==null?void 0:r.getState();t!==void 0&&(q+="s"+
t);d(n?q+("t"+n):q+"te")});};var Ux="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Vx(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Wx(){var a=y.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Xx(){var a,b;return(b=(a=y.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Yx(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Zx(){var a=y;if(!Yx(a))return null;var b=Vx(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Ux).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function fy(a){var b=a.location.href;if(a===a.top)return{url:b,Yo:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Yo:c}};function Xy(a,b){var c=!!Mj();switch(a){case 45:return c&&!H(76)?Lj()+"/g/ccm/collect":"https://www.google.com/ccm/collect";case 46:return c?Lj()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return c&&!H(80)?Lj()+"/travel/flights/click/conversion":"https://www.google.com/travel/flights/click/conversion";case 9:return!H(77)&&c?Lj()+"/pagead/viewthroughconversion":"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c&&!H(82)?(H(90)?Qn():
"").toLowerCase()==="region1"?""+Lj()+"/r1ag/g/c":""+Lj()+"/ag/g/c":Vy();case 16:return c?""+Lj()+(H(15)?"/ga/g/c":"/g/collect"):Wy();case 1:return!H(81)&&c?Lj()+"/activity;":"https://ad.doubleclick.net/activity;";case 2:return c?Lj()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return!H(81)&&c?Lj()+"/activity;register_conversion=1;":"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?H(79)?Lj()+"/d/pagead/form-data":Lj()+"/pagead/form-data":
H(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return!H(81)&&c?Lj()+"/activityi/"+b+";":"https://"+b+".fls.doubleclick.net/activityi;";case 5:case 6:case 7:case 8:case 12:case 13:case 14:case 15:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 0:throw Error("Unsupported endpoint");
default:Zb(a,"Unknown endpoint")}};function Yy(a){a=a===void 0?[]:a;return qj(a).join("~")}function Zy(){if(!H(118))return"";var a,b;return(((a=em(fm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var gz={};gz.N=cr.N;var hz={jq:"L",Mn:"S",xq:"Y",Pp:"B",aq:"E",gq:"I",tq:"TC",fq:"HTC"},iz={Mn:"S",Zp:"V",Tp:"E",sq:"tag"},jz={},kz=(jz[gz.N.Fi]="6",jz[gz.N.Gi]="5",jz[gz.N.Ei]="7",jz);function lz(){function a(c,d){var e=Ya(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var mz=!1;function Cz(a){}
function Dz(a){}function Ez(){}
function Fz(a){}function Gz(a){}
function Hz(a){}
function Iz(){}
function Jz(a,b){}
function Kz(a,b,c){}
function Lz(){};var Mz=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Nz(a,b,c,d,e,f,g){var h=Object.assign({},Mz);c&&(h.body=c,h.method="POST");Object.assign(h,e);y.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});Oz(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():H(128)&&(b+="&_z=retryFetch",c?Cl(a,b,c):Bl(a,b))})};var Pz=function(a){this.P=a;this.D=""},Qz=function(a,b){a.J=b;return a},Rz=function(a,b){a.O=b;return a},Oz=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}Sz(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},Tz=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};Sz(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},Sz=function(a,b){b&&(Uz(b.send_pixel,b.options,a.P),Uz(b.create_iframe,b.options,a.J),Uz(b.fetch,b.options,a.O))};function Vz(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Uz(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=Yc(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function BA(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function CA(a,b,c){c=c===void 0?!1:c;DA().addRestriction(0,a,b,c)}function EA(a,b,c){c=c===void 0?!1:c;DA().addRestriction(1,a,b,c)}function FA(){var a=cm();return DA().getRestrictions(1,a)}var GA=function(){this.container={};this.D={}},HA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
GA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=HA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
GA.prototype.getRestrictions=function(a,b){var c=HA(this,b);if(a===0){var d,e;return[].concat(ta((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ta((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ta((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ta((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
GA.prototype.getExternalRestrictions=function(a,b){var c=HA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};GA.prototype.removeExternalRestrictions=function(a){var b=HA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function DA(){return Mo("r",function(){return new GA})};var IA=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),JA={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},KA={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},LA="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function MA(){var a=Sj("gtm.allowlist")||Sj("gtm.whitelist");a&&O(9);yj&&(a=["google","gtagfl","lcl","zone"],H(48)&&a.push("cmpPartners"));IA.test(y.location&&y.location.hostname)&&(yj?O(116):(O(117),NA&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&ub(nb(a),JA),c=Sj("gtm.blocklist")||Sj("gtm.blacklist");c||(c=Sj("tagTypeBlacklist"))&&O(3);c?O(8):c=[];IA.test(y.location&&y.location.hostname)&&(c=nb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));nb(c).indexOf("google")>=0&&O(2);var d=c&&ub(nb(c),KA),e={};return function(f){var g=f&&f[Qe.Da];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Ij[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(H(48)&&yj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=hb(d,h||
[]);t&&O(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:H(48)&&yj&&h.indexOf("cmpPartners")>=0?!OA():b&&b.indexOf("sandboxedScripts")!==-1?0:hb(d,LA))&&(u=!0);return e[g]=u}}function OA(){var a=Vf(Sf.D,am(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var NA=!1;NA=!0;
function PA(){Sl&&CA(cm(),function(a){var b=Df(a.entityId),c;if(Gf(b)){var d=b[Qe.Da];if(!d)throw Error("Error: No function name given for function call.");var e=uf[d];c=!!e&&!!e.runInSiloedMode}else c=!!BA(b[Qe.Da],4);return c})};function QA(a,b,c,d,e){if(!RA()){var f=d.siloed?Yl(a):a;if(!lm(f)){d.loadExperiments=qj();nm(f,d,e);var g=SA(a),h=function(){Ol().container[f]&&(Ol().container[f].state=3);TA()},m={destinationId:f,endpoint:0};if(Mj())Fl(m,Lj()+"/"+g,void 0,h);else{var n=vb(a,"GTM-"),p=tk(),q=c?"/gtag/js":"/gtm.js",r=sk(b,q+g);if(!r){var t=sj.jg+q;p&&jc&&n&&(t=jc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=qv("https://","http://",t+g)}Fl(m,r,void 0,h)}}}}
function TA(){pm()||jb(qm(),function(a,b){UA(a,b.transportUrl,b.context);O(92)})}
function UA(a,b,c,d){if(!RA()){var e=c.siloed?Yl(a):a;if(!mm(e))if(c.loadExperiments||(c.loadExperiments=qj()),pm())Ol().destination[e]={state:0,transportUrl:b,context:c,parent:fm()},Nl({ctid:e,isDestination:!0},d),O(91);else{c.siloed&&om({ctid:e,isDestination:!0});Ol().destination[e]={state:1,context:c,parent:fm()};Nl({ctid:e,isDestination:!0},d);var f={destinationId:e,endpoint:0};if(Mj())Fl(f,Lj()+("/gtd"+SA(a,!0)));else{var g="/gtag/destination"+SA(a,!0),h=sk(b,g);h||(h=qv("https://","http://",
sj.jg+g));Fl(f,h)}}}}function SA(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);H(124)&&sj.Cb==="dataLayer"||(c+="&l="+sj.Cb);if(!vb(a,"GTM-")||b)c=H(130)?c+(Mj()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+ar();tk()&&(c+="&sign="+sj.zi);var d=pj.J;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");H(70)&&Kj()&&(c+="&tag_exp="+Kj());return c}function RA(){if(Zq()){return!0}return!1};var VA=function(){this.J=0;this.D={}};VA.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,Sb:c};return d};VA.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var XA=function(a,b){var c=[];jb(WA.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.Sb===void 0||b.indexOf(e.Sb)>=0)&&c.push(e.listener)});return c};function YA(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:am()}};var $A=function(a,b){this.D=!1;this.P=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;ZA(this,a,b)},aB=function(a,b,c,d){if(uj.hasOwnProperty(b)||b==="__zone")return-1;var e={};Yc(d)&&(e=Zc(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},bB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},cB=function(a){if(!a.D){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.D=!0;a.P.length=0}},ZA=function(a,b,c){b!==void 0&&a.wf(b);c&&y.setTimeout(function(){cB(a)},
Number(c))};$A.prototype.wf=function(a){var b=this,c=sb(function(){C(function(){a(am(),b.eventData)})});this.D?c():this.P.push(c)};var dB=function(a){a.O++;return sb(function(){a.J++;a.T&&a.J>=a.O&&cB(a)})},eB=function(a){a.T=!0;a.J>=a.O&&cB(a)};var fB={};function gB(){return y[hB()]}
function hB(){return y.GoogleAnalyticsObject||"ga"}function kB(){var a=am();}
function lB(a,b){return function(){var c=gB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var rB=["es","1"],sB={},tB={};function uB(a,b){if(Ck){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";sB[a]=[["e",c],["eid",a]];Rp(a)}}function vB(a){var b=a.eventId,c=a.vd;if(!sB[b])return[];var d=[];tB[b]||d.push(rB);d.push.apply(d,ta(sB[b]));c&&(tB[b]=!0);return d};var wB={},xB={},yB={};function zB(a,b,c,d){Ck&&H(120)&&((d===void 0?0:d)?(yB[b]=yB[b]||0,++yB[b]):c!==void 0?(xB[a]=xB[a]||{},xB[a][b]=Math.round(c)):(wB[a]=wB[a]||{},wB[a][b]=(wB[a][b]||0)+1))}function AB(a){var b=a.eventId,c=a.vd,d=wB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete wB[b];return e.length?[["md",e.join(".")]]:[]}
function BB(a){var b=a.eventId,c=a.vd,d=xB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete xB[b];return e.length?[["mtd",e.join(".")]]:[]}function CB(){for(var a=[],b=l(Object.keys(yB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+yB[d])}return a.length?[["mec",a.join(".")]]:[]};var DB={},EB={};function FB(a,b,c){if(Ck&&b){var d=xk(b);DB[a]=DB[a]||[];DB[a].push(c+d);var e=(Gf(b)?"1":"2")+d;EB[a]=EB[a]||[];EB[a].push(e);Rp(a)}}function GB(a){var b=a.eventId,c=a.vd,d=[],e=DB[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=EB[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete DB[b],delete EB[b]);return d};function HB(a,b,c,d){var e=sf[a],f=IB(a,b,c,d);if(!f)return null;var g=Hf(e[Qe.vl],c,[]);if(g&&g.length){var h=g[0];f=HB(h.index,{onSuccess:f,onFailure:h.Ml===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function IB(a,b,c,d){function e(){function w(){wn(3);var J=qb()-G;FB(c.id,f,"7");bB(c.Gc,D,"exception",J);H(109)&&Kz(c,f,gz.N.Ei);F||(F=!0,h())}if(f[Qe.En])h();else{var x=Ff(f,c,[]),z=x[Qe.wm];if(z!=null)for(var B=0;B<z.length;B++)if(!Bo(z[B])){h();return}var D=aB(c.Gc,String(f[Qe.Da]),Number(f[Qe.eh]),x[Qe.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var J=qb()-G;FB(c.id,sf[a],"5");bB(c.Gc,D,"success",J);H(109)&&Kz(c,f,gz.N.Gi);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var J=qb()-
G;FB(c.id,sf[a],"6");bB(c.Gc,D,"failure",J);H(109)&&Kz(c,f,gz.N.Fi);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);FB(c.id,f,"1");H(109)&&Jz(c,f);var G=qb();try{If(x,{event:c,index:a,type:1})}catch(J){w(J)}H(109)&&Kz(c,f,gz.N.zl)}}var f=sf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Hf(f[Qe.Al],c,[]);if(n&&n.length){var p=n[0],q=HB(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Ml===
2?m:q}if(f[Qe.kl]||f[Qe.Hn]){var r=f[Qe.kl]?tf:c.Fp,t=g,u=h;if(!r[a]){var v=JB(a,r,sb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function JB(a,b,c){var d=[],e=[];b[a]=KB(d,e,c);return{onSuccess:function(){b[a]=LB;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=MB;for(var f=0;f<e.length;f++)e[f]()}}}function KB(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function LB(a){a()}function MB(a,b){b()};var PB=function(a,b){for(var c=[],d=0;d<sf.length;d++)if(a[d]){var e=sf[d];var f=dB(b.Gc);try{var g=HB(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Qe.Da];if(!h)throw Error("Error: No function name given for function call.");var m=uf[h];c.push({km:d,priorityOverride:(m?m.priorityOverride||0:0)||BA(e[Qe.Da],1)||0,execute:g})}else NB(d,b),f()}catch(p){f()}}c.sort(OB);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function QB(a,b){if(!WA)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=XA(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=dB(b);try{d[e](a,f)}catch(g){f()}}return!0}function OB(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.km,h=b.km;f=g>h?1:g<h?-1:0}return f}
function NB(a,b){if(Ck){var c=function(d){var e=b.isBlocked(sf[d])?"3":"4",f=Hf(sf[d][Qe.vl],b,[]);f&&f.length&&c(f[0].index);FB(b.id,sf[d],e);var g=Hf(sf[d][Qe.Al],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var RB=!1,WA;function SB(){WA||(WA=new VA);return WA}
function TB(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(H(109)){}if(d==="gtm.js"){if(RB)return!1;RB=!0}var e=!1,f=FA(),g=Zc(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}uB(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:UB(g,e),Fp:[],logMacroError:function(){O(6);wn(0)},cachedModelValues:VB(),Gc:new $A(function(){if(H(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};H(120)&&Ck&&(n.reportMacroDiscrepancy=zB);H(109)&&Gz(n.id);var p=Nf(n);H(109)&&Hz(n.id);e&&(p=WB(p));H(109)&&Fz(b);var q=PB(p,n),r=QB(a,n.Gc);eB(n.Gc);d!=="gtm.js"&&d!=="gtm.sync"||kB();return XB(p,q)||r}function VB(){var a={};a.event=Xj("event",1);a.ecommerce=Xj("ecommerce",1);a.gtm=Xj("gtm");a.eventModel=Xj("eventModel");return a}
function UB(a,b){var c=MA();return function(d){if(c(d))return!0;var e=d&&d[Qe.Da];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=cm();f=DA().getRestrictions(0,g);var h=a;b&&(h=Zc(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Ij[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function WB(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(sf[c][Qe.Da]);if(tj[d]||sf[c][Qe.In]!==void 0||BA(d,2))b[c]=!0}return b}function XB(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&sf[c]&&!uj[String(sf[c][Qe.Da])])return!0;return!1};function YB(){SB().addListener("gtm.init",function(a,b){pj.T=!0;gn();b()})};var ZB=!1,$B=0,aC=[];function bC(a){if(!ZB){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){ZB=!0;for(var e=0;e<aC.length;e++)C(aC[e])}aC.push=function(){for(var f=xa.apply(0,arguments),g=0;g<f.length;g++)C(f[g]);return 0}}}function cC(){if(!ZB&&$B<140){$B++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");bC()}catch(c){y.setTimeout(cC,50)}}}
function dC(){ZB=!1;$B=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")bC();else{xc(A,"DOMContentLoaded",bC);xc(A,"readystatechange",bC);if(A.createEventObject&&A.documentElement.doScroll){var a=!0;try{a=!y.frameElement}catch(b){}a&&cC()}xc(y,"load",bC)}}function eC(a){ZB?a():aC.push(a)};var fC=0;var gC={},hC={};function iC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={tj:void 0,Zi:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.tj=Vo(g,b),e.tj){var h=Tl?Tl:$l();fb(h,function(r){return function(t){return r.tj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=gC[g]||[];e.Zi={};m.forEach(function(r){return function(t){r.Zi[t]=!0}}(e));for(var n=Wl(),p=0;p<n.length;p++)if(e.Zi[n[p]]){c=c.concat(Zl());break}var q=hC[g]||[];q.length&&(c=c.concat(q))}}return{lj:c,fp:d}}
function jC(a){jb(gC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function kC(a){jb(hC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var lC=!1,mC=!1;function nC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=Zc(b,null),b[N.m.Je]&&(d.eventCallback=b[N.m.Je]),b[N.m.Ag]&&(d.eventTimeout=b[N.m.Ag]));return d}function oC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Qo()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function pC(a,b){var c=a&&a[N.m.Wc];c===void 0&&(c=Sj(N.m.Wc,2),c===void 0&&(c="default"));if(cb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?cb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=iC(d,b.isGtmEvent),f=e.lj,g=e.fp;if(g.length)for(var h=qC(a),m=0;m<g.length;m++){var n=Vo(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=vb(p,"siloed_"))){var r=n.destinationId,t=Ol().destination[r];q=!!t&&t.state===0}q||UA(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{lj:Wo(f,b.isGtmEvent),Wn:Wo(u,b.isGtmEvent)}}}var rC=void 0,sC=void 0;function tC(a,b,c){var d=Zc(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=Zc(b,null);Zc(c,e);Pv(Lv(Wl()[0],e),a.eventId,d)}function qC(a){for(var b=l([N.m.Xc,N.m.ic]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Zp.D[d];if(e)return e}}
var uC={config:function(a,b){var c=oC(a,b);if(!(a.length<2)&&cb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!Yc(a[2])||a.length>3)return;d=a[2]}var e=Vo(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Rl.kf){var m=em(fm());if(rm(m)){var n=m.parent,p=n.isDestination;h={kp:em(n),bp:p};break a}}h=void 0}var q=h;q&&(f=q.kp,g=q.bp);uB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Zl().indexOf(r)===-1:Wl().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.Bc]){var u=qC(d);if(t)UA(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;rC?tC(b,v,rC):sC||(sC=Zc(v,null))}else QA(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;sC?(tC(b,sC,x),w=!1):(!x[N.m.Zc]&&wj&&rC||(rC=Zc(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Dk&&(fC===1&&($m.mcc=!1),fC=2);if(wj&&!t&&!d[N.m.Zc]){var z=mC;mC=!0;if(z)return}lC||O(43);if(!b.noTargetGroup)if(t){kC(e.id);
var B=e.id,D=d[N.m.Dg]||"default";D=String(D).split(",");for(var F=0;F<D.length;F++){var G=hC[D[F]]||[];hC[D[F]]=G;G.indexOf(B)<0&&G.push(B)}}else{jC(e.id);var J=e.id,M=d[N.m.Dg]||"default";M=M.toString().split(",");for(var U=0;U<M.length;U++){var K=gC[M[U]]||[];gC[M[U]]=K;K.indexOf(J)<0&&K.push(J)}}delete d[N.m.Dg];var ba=b.eventMetadata||{};ba.hasOwnProperty(Q.C.fd)||(ba[Q.C.fd]=!b.fromContainerExecution);b.eventMetadata=ba;delete d[N.m.Je];for(var Y=t?[e.id]:Zl(),ea=0;ea<Y.length;ea++){var V=d,
R=Y[ea],ka=Zc(b,null),ja=Vo(R,ka.isGtmEvent);ja&&Zp.push("config",[V],ja,ka)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=oC(a,b),d=a[1],e={},f=Tn(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===N.m.fg?Array.isArray(h)?NaN:Number(h):g===N.m.Tb?(Array.isArray(h)?h:[h]).map(Un):Vn(h)}b.fromContainerExecution||(e[N.m.V]&&O(139),e[N.m.Ha]&&O(140));d==="default"?xo(e):d==="update"?zo(e,c):d==="declare"&&b.fromContainerExecution&&wo(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&cb(c)){var d=void 0;if(a.length>2){if(!Yc(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=nC(c,d),f=oC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=pC(d,b);if(m){var n=m.lj,p=m.Wn,q,r,t;if(!Sl&&H(108)){q=p.map(function(J){return J.id});r=p.map(function(J){return J.destinationId});t=n.map(function(J){return J.id});for(var u=l(Tl?Tl:$l()),v=u.next();!v.done;v=u.next()){var w=v.value;
!vb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(Yl(w))<0&&t.push(w)}}else q=n.map(function(J){return J.id}),r=n.map(function(J){return J.destinationId}),t=q;uB(g,c);for(var x=l(t),z=x.next();!z.done;z=x.next()){var B=z.value,D=Zc(b,null),F=Zc(d,null);delete F[N.m.Je];var G=D.eventMetadata||{};G.hasOwnProperty(Q.C.fd)||(G[Q.C.fd]=!D.fromContainerExecution);G[Q.C.xi]=q.slice();G[Q.C.rf]=r.slice();D.eventMetadata=G;$p(c,F,B,D);Dk&&G[Q.C.lb]===void 0&&fC===0&&(cn("mcc","1"),fC=1)}e.eventModel=e.eventModel||
{};q.length>0?e.eventModel[N.m.Wc]=q.join(","):delete e.eventModel[N.m.Wc];lC||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.C.yl]&&(b.noGtmEvent=!0);e.eventModel[N.m.Ac]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&cb(a[1])&&cb(a[2])&&ab(a[3])){var c=Vo(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){lC||O(43);var f=qC();if(fb(Zl(),function(h){return c.destinationId===h})){oC(a,b);var g={};Zc((g[N.m.bc]=d,g[N.m.yc]=e,g),null);aq(d,function(h){C(function(){e(h)})},
c.id,b)}else UA(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){lC=!0;var c=oC(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&cb(a[1])&&ab(a[2])){if(Tf(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](am(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;
a.length===2&&Yc(a[1])?c=Zc(a[1],null):a.length===3&&cb(a[1])&&(c={},Yc(a[2])||Array.isArray(a[2])?c[a[1]]=Zc(a[2],null):c[a[1]]=a[2]);if(c){var d=oC(a,b),e=d.eventId,f=d.priorityId;Zc(c,null);var g=Zc(c,null);Zp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},vC={policy:!0};var xC=function(a){if(wC(a))return a;this.value=a};xC.prototype.getUntrustedMessageValue=function(){return this.value};var wC=function(a){return!a||Wc(a)!=="object"||Yc(a)?!1:"getUntrustedMessageValue"in a};xC.prototype.getUntrustedMessageValue=xC.prototype.getUntrustedMessageValue;var yC=!1,zC=[];function AC(){if(!yC){yC=!0;for(var a=0;a<zC.length;a++)C(zC[a])}}function BC(a){yC?C(a):zC.push(a)};var CC=0,DC={},EC=[],FC=[],GC=!1,HC=!1;function IC(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function JC(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return KC(a)}function LC(a,b){if(!db(b)||b<0)b=0;var c=Lo[sj.Cb],d=0,e=!1,f=void 0;f=y.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(y.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function MC(a,b){var c=a._clear||b.overwriteModelFields;jb(a,function(e,f){e!=="_clear"&&(c&&Vj(e),Vj(e,f))});Fj||(Fj=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Qo(),a["gtm.uniqueEventId"]=d,Vj("gtm.uniqueEventId",d));return TB(a)}function NC(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(kb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function OC(){var a;if(FC.length)a=FC.shift();else if(EC.length)a=EC.shift();else return;var b;var c=a;if(GC||!NC(c.message))b=c;else{GC=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Qo(),f=Qo(),c.message["gtm.uniqueEventId"]=Qo());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};EC.unshift(n,c);b=h}return b}
function PC(){for(var a=!1,b;!HC&&(b=OC());){HC=!0;delete Pj.eventModel;Rj();var c=b,d=c.message,e=c.messageContext;if(d==null)HC=!1;else{e.fromContainerExecution&&Wj();try{if(ab(d))try{d.call(Tj)}catch(u){}else if(Array.isArray(d)){if(cb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Sj(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(kb(d))a:{if(d.length&&cb(d[0])){var p=uC[d[0]];if(p&&(!e.fromContainerExecution||!vC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=MC(n,e)||a)}}finally{e.fromContainerExecution&&Rj(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=DC[String(q)]||[],t=0;t<r.length;t++)FC.push(QC(r[t]));r.length&&FC.sort(IC);delete DC[String(q)];q>CC&&(CC=q)}HC=!1}}}return!a}
function RC(){if(H(109)){var a=!pj.O;}var c=PC();if(H(109)){}try{var e=am(),f=y[sj.Cb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Sv(a){if(CC<a.notBeforeEventId){var b=String(a.notBeforeEventId);DC[b]=DC[b]||[];DC[b].push(a)}else FC.push(QC(a)),FC.sort(IC),C(function(){HC||PC()})}function QC(a){return{message:a.message,messageContext:a.messageContext}}
function SC(){function a(f){var g={};if(wC(f)){var h=f;f=wC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=kc(sj.Cb,[]),c=Po();c.pruned===!0&&O(83);DC=Qv().get();Rv();eC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});BC(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Lo.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new xC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});EC.push.apply(EC,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return PC()&&p};var e=b.slice(0).map(function(f){return a(f)});EC.push.apply(EC,e);if(!pj.O){if(H(109)){}C(RC)}}var KC=function(a){return y[sj.Cb].push(a)};function TC(a){KC(a)};function UC(){var a,b=mk(y.location.href);(a=b.hostname+b.pathname)&&cn("dl",encodeURIComponent(a));var c;var d=Wf.ctid;if(d){var e=Rl.kf?1:0,f,g=em(fm());f=g&&g.context;c=d+";"+Wf.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&cn("tdp",h);var m=hl(!0);m!==void 0&&cn("frm",String(m))};function VC(){H(55)&&Dk&&y.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){var b=Al(a.effectiveDirective);if(b){var c;var d=yl(b,a.blockedURI);c=d?wl[b][d]:void 0;var e;if(e=c)a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(q){}e=void 0}if(e){for(var h=l(c),m=h.next();!m.done;m=h.next()){var n=m.value;if(!n.dm){n.dm=!0;var p=String(n.endpoint);hn.hasOwnProperty(p)||(hn[p]=
!0,cn("csp",Object.keys(hn).join("~")))}}zl(b,a.blockedURI)}}}})};function WC(){var a;var b=dm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&cn("pcid",e)};var XC=/^(https?:)?\/\//;
function YC(){var a;var b=em(fm());if(b){for(;b.parent;){var c=em(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Mc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=l(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(XC,"")===g.replace(XC,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
cn("rtg",String(d.canonicalContainerId)),cn("slo",String(t)),cn("hlo",d.htmlLoadOrder||"-1"),cn("lst",String(d.loadScriptType||"0")))}else O(144)};
function sD(){};var tD=function(){};tD.prototype.toString=function(){return"undefined"};var uD=new tD;
var wD=function(){Mo("rm",function(){return{}})[cm()]=function(a){if(vD.hasOwnProperty(a))return vD[a]}},zD=function(a,b,c){if(a instanceof xD){var d=a,e=d.resolve,f=b,g=String(Qo());yD[g]=[f,c];a=e.call(d,g);b=$a}return{Ro:a,onSuccess:b}},AD=function(a){var b=a?0:1;return function(c){O(a?134:135);var d=yD[c];if(d&&typeof d[b]==="function")d[b]();yD[c]=void 0}},xD=function(a){this.valueOf=this.toString;this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===uD?b:a[d]);return c.join("")}};
xD.prototype.toString=function(){return this.resolve("undefined")};var vD={},yD={};function BD(a,b){function c(g){var h=mk(g),m=gk(h,"protocol"),n=gk(h,"host",!0),p=gk(h,"port"),q=gk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function CD(a){return DD(a)?1:0}
function DD(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=Zc(a,{});Zc({arg1:c[d],any_of:void 0},e);if(CD(e))return!0}return!1}switch(a["function"]){case "_cn":return Eg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<zg.length;g++){var h=zg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ag(b,c);case "_eq":return Fg(b,c);case "_ge":return Gg(b,c);case "_gt":return Ig(b,c);case "_lc":return Bg(b,c);case "_le":return Hg(b,
c);case "_lt":return Jg(b,c);case "_re":return Dg(b,c,a.ignore_case);case "_sw":return Kg(b,c);case "_um":return BD(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var ED=function(a,b,c,d){oq.call(this);this.Zg=b;this.lf=c;this.Fc=d;this.kb=new Map;this.bh=0;this.la=new Map;this.Ia=new Map;this.T=void 0;this.J=a};ra(ED,oq);ED.prototype.O=function(){delete this.D;this.kb.clear();this.la.clear();this.Ia.clear();this.T&&(kq(this.J,"message",this.T),delete this.T);delete this.J;delete this.Fc;oq.prototype.O.call(this)};
var FD=function(a){if(a.D)return a.D;a.lf&&a.lf(a.J)?a.D=a.J:a.D=gl(a.J,a.Zg);var b;return(b=a.D)!=null?b:null},HD=function(a,b,c){if(FD(a))if(a.D===a.J){var d=a.kb.get(b);d&&d(a.D,c)}else{var e=a.la.get(b);if(e&&e.kj){GD(a);var f=++a.bh;a.Ia.set(f,{uh:e.uh,ko:e.Pl(c),persistent:b==="addEventListener"});a.D.postMessage(e.kj(c,f),"*")}}},GD=function(a){a.T||(a.T=function(b){try{var c;c=a.Fc?a.Fc(b):void 0;if(c){var d=c.np,e=a.Ia.get(d);if(e){e.persistent||a.Ia.delete(d);var f;(f=e.uh)==null||f.call(e,
e.ko,c.payload)}}}catch(g){}},jq(a.J,"message",a.T))};var ID=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},JD=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},KD={Pl:function(a){return a.listener},kj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},uh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},LD={Pl:function(a){return a.listener},kj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},uh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function MD(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,np:b.__gppReturn.callId}}
var ND=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;oq.call(this);this.caller=new ED(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},MD);this.caller.kb.set("addEventListener",ID);this.caller.la.set("addEventListener",KD);this.caller.kb.set("removeEventListener",JD);this.caller.la.set("removeEventListener",LD);this.timeoutMs=c!=null?c:500};ra(ND,oq);ND.prototype.O=function(){this.caller.dispose();oq.prototype.O.call(this)};
ND.prototype.addEventListener=function(a){var b=this,c=Yk(function(){a(OD,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);HD(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(PD,!0);return}a(QD,!0)}}})};
ND.prototype.removeEventListener=function(a){HD(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var QD={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},OD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},PD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function RD(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){wu.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");wu.D=d}}function SD(){try{if(H(106)){var a=new ND(y,{timeoutMs:-1});FD(a.caller)&&a.addEventListener(RD)}}catch(b){}};function TD(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function UD(){var a=[["cv",H(140)?TD():"5"],["rv",sj.wi],["tc",sf.filter(function(b){return b}).length]];sj.vi&&a.push(["x",sj.vi]);Kj()&&a.push(["tag_exp",Kj()]);return a};var VD={},WD={};function XD(a){var b=a.eventId,c=a.vd,d=[],e=VD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=WD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete VD[b],delete WD[b]);return d};function YD(){return!1}function ZD(){var a={};return function(b,c,d){}};function $D(){var a=aE;return function(b,c,d){var e=d&&d.event;bE(c);var f=qh(b)?void 0:1,g=new Oa;jb(c,function(r,t){var u=od(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.D.D.J=Lf();var h={Gl:$f(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,wf:e!==void 0?function(r){e.Gc.wf(r)}:void 0,zb:function(){return b},log:function(){},so:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},wp:!!BA(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(YD()){var m=ZD(),n,p;h.nb={Cj:[],xf:{},Ob:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},th:Ih()};h.log=function(r){var t=xa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Ke(a,h,[b,g]);a.D.D.J=void 0;q instanceof za&&(q.type==="return"?q=q.data:q=void 0);return nd(q,void 0,f)}}function bE(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ab(b)&&(a.gtmOnSuccess=function(){C(b)});ab(c)&&(a.gtmOnFailure=function(){C(c)})};function cE(a){}cE.M="internal.addAdsClickIds";function dE(a,b){var c=this;}dE.publicName="addConsentListener";var eE=!1;function fE(a){for(var b=0;b<a.length;++b)if(eE)try{a[b]()}catch(c){O(77)}else a[b]()}function gE(a,b,c){var d=this,e;return e}gE.M="internal.addDataLayerEventListener";function hE(a,b,c){}hE.publicName="addDocumentEventListener";function iE(a,b,c,d){}iE.publicName="addElementEventListener";function jE(a){return a.K.D};function kE(a){}kE.publicName="addEventCallback";
function AE(a){}AE.M="internal.addFormAbandonmentListener";function BE(a,b,c,d){}
BE.M="internal.addFormData";var CE={},DE=[],EE={},FE=0,GE=0;
function NE(a,b){}NE.M="internal.addFormInteractionListener";
function UE(a,b){}UE.M="internal.addFormSubmitListener";
function ZE(a){}ZE.M="internal.addGaSendListener";function $E(a){if(!a)return{};var b=a.so;return YA(b.type,b.index,b.name)}function aF(a){return a?{originatingEntity:$E(a)}:{}};
var cF=function(a,b,c){bF().updateZone(a,b,c)},eF=function(a,b,c,d,e,f){var g=bF();c=c&&ub(c,dF);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,am(),h)){var p=n,q=a,r=d,t=e,u=f;if(vb(p,"GTM-"))QA(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Kv("js",pb());QA(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};H(146)||Pv(v,q,w);Pv(Lv(p,r),q,w)}}}return h},bF=function(){return Mo("zones",function(){return new fF})},
gF={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},dF={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},fF=function(){this.D={};this.J={};this.O=0};k=fF.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.D[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.sj],b))return!1;for(var e=0;e<c.cg.length;e++)if(this.J[c.cg[e]].ce(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.D[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.cg.length;f++){var g=this.J[c.cg[f]];g.ce(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.sj],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].O(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.D[a[b]]};k.createZone=function(a,b){var c=String(++this.O);this.J[c]=new hF(a,b);return c};k.updateZone=function(a,
b,c){var d=this.J[a];d&&d.P(b,c)};k.registerChild=function(a,b,c){var d=this.D[a];if(!d&&Lo[a]||!d&&lm(a)||d&&d.sj!==b)return!1;if(d)return d.cg.push(c),!1;this.D[a]={sj:b,cg:[c]};return!0};var hF=function(a,b){this.J=null;this.D=[{eventId:a,ce:!0}];if(b){this.J={};for(var c=0;c<b.length;c++)this.J[b[c]]=!0}};hF.prototype.P=function(a,b){var c=this.D[this.D.length-1];a<=c.eventId||c.ce!==b&&this.D.push({eventId:a,ce:b})};hF.prototype.ce=function(a){for(var b=this.D.length-1;b>=0;b--)if(this.D[b].eventId<=
a)return this.D[b].ce;return!1};hF.prototype.O=function(a,b){b=b||[];if(!this.J||gF[a]||this.J[a])return!0;for(var c=0;c<b.length;++c)if(this.J[b[c]])return!0;return!1};function iF(a){var b=Lo.zones;return b?b.getIsAllowedFn(Wl(),a):function(){return!0}}function jF(){var a=Lo.zones;a&&a.unregisterChild(Wl())}
function kF(){EA(cm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Lo.zones;return c?c.isActive(Wl(),b):!0});CA(cm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return iF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var lF=function(a,b){this.tagId=a;this.zf=b};
function mF(a,b){var c=this,d=void 0;if(!bh(a)||!Vg(b)&&!Xg(b))throw I(this.getName(),["string","Object|undefined"],arguments);var e=nd(b,this.K,1)||{},f=e.firstPartyUrl,g=e.onLoad,h=e.loadByDestination===!0,m=e.isGtmEvent===!0,n=e.siloed===!0;d=n?Yl(a):a;fE([function(){L(c,"load_google_tags",a,f)}]);if(h){if(mm(a))return d}else if(lm(a))return d;var p=6,q=jE(this);m&&(p=7);q.zb()==="__zone"&&(p=1);var r={source:p,fromContainerExecution:!0,
siloed:n},t=function(u){CA(u,function(v){for(var w=DA().getExternalRestrictions(0,cm()),x=l(w),z=x.next();!z.done;z=x.next()){var B=z.value;if(!B(v))return!1}return!0},!0);EA(u,function(v){for(var w=DA().getExternalRestrictions(1,cm()),x=l(w),z=x.next();!z.done;z=x.next()){var B=z.value;if(!B(v))return!1}return!0},!0);g&&g(new lF(a,u))};h?UA(a,f,r,t):QA(a,f,!vb(a,"GTM-"),r,t);g&&q.zb()==="__zone"&&eF(Number.MIN_SAFE_INTEGER,[a],null,{},$E(jE(this)));
return d}mF.M="internal.loadGoogleTag";function nF(a){return new fd("",function(b){var c=this.evaluate(b);if(c instanceof fd)return new fd("",function(){var d=xa.apply(0,arguments),e=this,f=Zc(jE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Ga(this.K);h.D=f;return c.sb.apply(c,[h].concat(ta(g)))})})};function oF(a,b,c){var d=this;}oF.M="internal.addGoogleTagRestriction";var pF={},qF=[];
function xF(a,b){}
xF.M="internal.addHistoryChangeListener";function yF(a,b,c){}yF.publicName="addWindowEventListener";function zF(a,b){return!0}zF.publicName="aliasInWindow";function AF(a,b,c){}AF.M="internal.appendRemoteConfigParameter";function BF(a){var b;return b}
BF.publicName="callInWindow";function CF(a){}CF.publicName="callLater";function DF(a){}DF.M="callOnDomReady";function EF(a){}EF.M="callOnWindowLoad";function FF(a,b){var c;return c}FF.M="internal.computeGtmParameter";function GF(a,b){var c=this;}GF.M="internal.consentScheduleFirstTry";function HF(a,b){var c=this;}HF.M="internal.consentScheduleRetry";function IF(a){var b;return b}IF.M="internal.copyFromCrossContainerData";function JF(a,b){var c;if(!bh(a)||!gh(b)&&b!==null&&!Xg(b))throw I(this.getName(),["string","number|undefined"],arguments);L(this,"read_data_layer",a);c=(b||2)!==2?Sj(a,1):Uj(a,[y,A]);var d=od(c,this.K,qh(jE(this).zb())?2:1);d===void 0&&c!==void 0&&O(45);return d}JF.publicName="copyFromDataLayer";
function KF(a){var b=void 0;return b}KF.M="internal.copyFromDataLayerCache";function LF(a){var b;return b}LF.publicName="copyFromWindow";function MF(a){var b=void 0;return od(b,this.K,1)}MF.M="internal.copyKeyFromWindow";var NF=function(a){return a===vm.W.Aa&&Om[a]===um.Fa.Sd&&!Bo(N.m.U)};var OF=function(){return"0"},PF=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];H(102)&&b.push("gbraid");return nk(a,b,"0")};var QF={},RF={},SF={},TF={},UF={},VF={},WF={},XF={},YF={},ZF={},$F={},aG={},bG={},cG={},dG={},eG={},fG={},gG={},hG={},iG={},jG={},kG={},lG={},mG={},nG={},oG={},pG=(oG[N.m.Na]=(QF[2]=[NF],QF),oG[N.m.Te]=(RF[2]=[NF],RF),oG[N.m.Ke]=(SF[2]=[NF],SF),oG[N.m.ai]=(TF[2]=[NF],TF),oG[N.m.bi]=(UF[2]=[NF],UF),oG[N.m.di]=(VF[2]=[NF],VF),oG[N.m.ei]=(WF[2]=[NF],WF),oG[N.m.fi]=(XF[2]=[NF],XF),oG[N.m.Jb]=(YF[2]=[NF],YF),oG[N.m.Ve]=(ZF[2]=[NF],ZF),oG[N.m.We]=($F[2]=[NF],$F),oG[N.m.Xe]=(aG[2]=[NF],aG),oG[N.m.Ye]=(bG[2]=
[NF],bG),oG[N.m.Ze]=(cG[2]=[NF],cG),oG[N.m.af]=(dG[2]=[NF],dG),oG[N.m.bf]=(eG[2]=[NF],eG),oG[N.m.cf]=(fG[2]=[NF],fG),oG[N.m.fb]=(gG[1]=[NF],gG),oG[N.m.Mc]=(hG[1]=[NF],hG),oG[N.m.Qc]=(iG[1]=[NF],iG),oG[N.m.Fd]=(jG[1]=[NF],jG),oG[N.m.ve]=(kG[1]=[function(a){return H(102)&&NF(a)}],kG),oG[N.m.Rc]=(lG[1]=[NF],lG),oG[N.m.ya]=(mG[1]=[NF],mG),oG[N.m.Ra]=(nG[1]=[NF],nG),oG),qG={},rG=(qG[N.m.fb]=OF,qG[N.m.Mc]=OF,qG[N.m.Qc]=OF,qG[N.m.Fd]=OF,qG[N.m.ve]=OF,qG[N.m.Rc]=function(a){if(!Yc(a))return{};var b=Zc(a,
null);delete b.match_id;return b},qG[N.m.ya]=PF,qG[N.m.Ra]=PF,qG),sG={},tG={},uG=(tG[Q.C.Oa]=(sG[2]=[NF],sG),tG),vG={};var wG=function(a,b,c,d){this.D=a;this.O=b;this.P=c;this.T=d};wG.prototype.getValue=function(a){a=a===void 0?vm.W.yb:a;if(!this.O.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.T(this.D):this.D};wG.prototype.J=function(){return Wc(this.D)==="array"||Yc(this.D)?Zc(this.D,null):this.D};
var xG=function(){},yG=function(a,b){this.conditions=a;this.D=b},zG=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new wG(c,e,g,a.D[b]||xG)},AG,BG;var CG=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;T(this,g,d[g])}},Mu=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,S(a,Q.C.uf))},W=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:(AG!=null||(AG=new yG(pG,rG)),e=zG(AG,b,c));d[b]=e},DG=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.D)),d=c.next();!d.done;d=
c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};CG.prototype.copyToHitData=function(a,b,c){var d=P(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&cb(d)&&H(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var S=function(a,b){var c=a.metadata[b];if(b===Q.C.uf){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,S(a,Q.C.uf))},T=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(BG!=null||(BG=new yG(uG,vG)),e=zG(BG,b,c));d[b]=e},EG=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},fv=function(a,b,c){var d=a.target.destinationId;Sl||(d=gm(d));var e=Wv(d);return e&&e[b]!==void 0?e[b]:c};function FG(a,b){var c;return c}FG.M="internal.copyPreHit";function GG(a,b){var c=null;if(!bh(a)||!bh(b))throw I(this.getName(),["string","string"],arguments);L(this,"access_globals","readwrite",a);L(this,"access_globals","readwrite",b);var d=[y,A],e=a.split("."),f=wb(e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return ab(h)?od(h,this.K,2):null;var m;h=function(){if(!ab(m.push))throw Error("Object at "+b+" in window is not an array.");m.push.call(m,
arguments)};f[g]=h;var n=b.split("."),p=wb(n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");m=p[q];m===void 0&&(m=[],p[q]=m);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return od(c,this.K,2)}GG.publicName="createArgumentsQueue";function HG(a){return od(function(c){var d=gB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
gB(),n=m&&m.getByName&&m.getByName(f);return(new y.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}HG.M="internal.createGaCommandQueue";function IG(a){return od(function(){if(!ab(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
qh(jE(this).zb())?2:1)}IG.publicName="createQueue";function JG(a,b){var c=null;if(!bh(a)||!ch(b))throw I(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new kd(new RegExp(a,d))}catch(e){}return c}JG.M="internal.createRegex";function KG(){var a={};return a};function LG(a){}LG.M="internal.declareConsentState";function MG(a){var b="";return b}MG.M="internal.decodeUrlHtmlEntities";function NG(a,b,c){var d;return d}NG.M="internal.decorateUrlWithGaCookies";function OG(){}OG.M="internal.deferCustomEvents";function PG(a){var b;return b}PG.M="internal.detectUserProvidedData";
function UG(a,b){return f}UG.M="internal.enableAutoEventOnClick";
function bH(a,b){return p}bH.M="internal.enableAutoEventOnElementVisibility";function cH(){}cH.M="internal.enableAutoEventOnError";var dH={},eH=[],fH={},gH=0,hH=0;
function nH(a,b){var c=this;return d}nH.M="internal.enableAutoEventOnFormInteraction";
function sH(a,b){var c=this;return f}sH.M="internal.enableAutoEventOnFormSubmit";
function xH(){var a=this;}xH.M="internal.enableAutoEventOnGaSend";var yH={},zH=[];
function GH(a,b){var c=this;return f}GH.M="internal.enableAutoEventOnHistoryChange";var HH=["http://","https://","javascript:","file://"];
function LH(a,b){var c=this;return h}LH.M="internal.enableAutoEventOnLinkClick";var MH,NH;
function YH(a,b){var c=this;return d}YH.M="internal.enableAutoEventOnScroll";function ZH(a){return function(){if(a.limit&&a.oj>=a.limit)a.qh&&y.clearInterval(a.qh);else{a.oj++;var b=qb();KC({event:a.eventName,"gtm.timerId":a.qh,"gtm.timerEventNumber":a.oj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.jm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.jm,"gtm.triggers":a.Kp})}}}
function $H(a,b){
return f}$H.M="internal.enableAutoEventOnTimer";var ac=va(["data-gtm-yt-inspected-"]),bI=["www.youtube.com","www.youtube-nocookie.com"],cI,dI=!1;
function nI(a,b){var c=this;return e}nI.M="internal.enableAutoEventOnYouTubeActivity";dI=!1;function oI(a,b){if(!bh(a)||!Wg(b))throw I(this.getName(),["string","Object|undefined"],arguments);var c=b?nd(b):{},d=a,e=!1;return e}oI.M="internal.evaluateBooleanExpression";var pI;function qI(a){var b=!1;return b}qI.M="internal.evaluateMatchingRules";function $I(){return Iq(7)&&Iq(9)&&Iq(10)};function eK(a,b,c,d){}eK.M="internal.executeEventProcessor";function fK(a){var b;return od(b,this.K,1)}fK.M="internal.executeJavascriptString";function gK(a){var b;return b};function hK(a){var b={};return od(b)}hK.M="internal.getAdsCookieWritingOptions";function iK(a,b){var c=!1;return c}iK.M="internal.getAllowAdPersonalization";function jK(a,b){b=b===void 0?!0:b;var c;return c}jK.M="internal.getAuid";var kK=null;
function lK(){var a=new Oa;return a}
lK.publicName="getContainerVersion";function mK(a,b){b=b===void 0?!0:b;var c;return c}mK.publicName="getCookieValues";function nK(){var a="";return a}nK.M="internal.getCorePlatformServicesParam";function oK(){return Mn()}oK.M="internal.getCountryCode";function pK(){var a=[];return od(a)}pK.M="internal.getDestinationIds";function qK(a){var b=new Oa;return b}qK.M="internal.getDeveloperIds";function rK(a,b){var c=null;return c}rK.M="internal.getElementAttribute";function sK(a){var b=null;return b}sK.M="internal.getElementById";function tK(a){var b="";return b}tK.M="internal.getElementInnerText";function uK(a,b){var c=null;return od(c)}uK.M="internal.getElementProperty";function vK(a){var b;return b}vK.M="internal.getElementValue";function wK(a){var b=0;return b}wK.M="internal.getElementVisibilityRatio";function xK(a){var b=null;return b}xK.M="internal.getElementsByCssSelector";
function yK(a){var b;if(!bh(a))throw I(this.getName(),["string"],arguments);L(this,"read_event_data",a);var c;a:{var d=a,e=jE(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=l(n),B=z.next();!B.done;B=
z.next()){var D=B.value;D===m?(w.push(x),x=""):x=D===g?x+"\\":D===h?x+".":x+D}x&&w.push(x);for(var F=l(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=od(c,this.K,1);return b}yK.M="internal.getEventData";var zK={};zK.enableAWFledge=H(34);zK.enableAdsConversionValidation=H(18);zK.enableAdsSupernovaParams=H(30);zK.enableAutoPhoneAndAddressDetection=H(32);zK.enableAutoPiiOnPhoneAndAddress=H(33);zK.enableCachedEcommerceData=H(40);zK.enableCcdSendTo=H(41);zK.enableCloudRecommentationsErrorLogging=H(42);zK.enableCloudRecommentationsSchemaIngestion=H(43);zK.enableCloudRetailInjectPurchaseMetadata=H(45);zK.enableCloudRetailLogging=H(44);zK.enableCloudRetailPageCategories=H(46);zK.enableDCFledge=H(56);
zK.enableDataLayerSearchExperiment=H(129);zK.enableDecodeUri=H(92);zK.enableDeferAllEnhancedMeasurement=H(58);zK.enableFormSkipValidation=H(74);zK.enableGa4OutboundClicksFix=H(96);zK.enableGaAdsConversions=H(122);zK.enableGaAdsConversionsClientId=H(121);zK.enableGppForAds=H(106);zK.enableMerchantRenameForBasketData=H(113);zK.enableUrlDecodeEventUsage=H(139);zK.enableZoneConfigInChildContainers=H(142);zK.useEnableAutoEventOnFormApis=H(156);function AK(){return od(zK)}AK.M="internal.getFlags";function BK(){return new kd(uD)}BK.M="internal.getHtmlId";function CK(a){var b;return b}CK.M="internal.getIframingState";function DK(a,b){var c={};return od(c)}DK.M="internal.getLinkerValueFromLocation";function EK(){var a=new Oa;return a}EK.M="internal.getPrivacyStrings";function FK(a,b){var c;return c}FK.M="internal.getProductSettingsParameter";function GK(a,b){var c;return c}GK.publicName="getQueryParameters";function HK(a,b){var c;return c}HK.publicName="getReferrerQueryParameters";function IK(a){var b="";if(!ch(a))throw I(this.getName(),["string|undefined"],arguments);L(this,"get_referrer",a);b=ik(mk(A.referrer),a);return b}IK.publicName="getReferrerUrl";function JK(){return Nn()}JK.M="internal.getRegionCode";function KK(a,b){var c;return c}KK.M="internal.getRemoteConfigParameter";function LK(){var a=new Oa;a.set("width",0);a.set("height",0);return a}LK.M="internal.getScreenDimensions";function MK(){var a="";return a}MK.M="internal.getTopSameDomainUrl";function NK(){var a="";return a}NK.M="internal.getTopWindowUrl";function OK(a){var b="";if(!ch(a))throw I(this.getName(),["string|undefined"],arguments);L(this,"get_url",a);b=gk(mk(y.location.href),a);return b}OK.publicName="getUrl";function PK(){L(this,"get_user_agent");return gc.userAgent}PK.M="internal.getUserAgent";function QK(){var a;return a?od($x(a)):a}QK.M="internal.getUserAgentClientHints";function YK(){return y.gaGlobal=y.gaGlobal||{}}function ZK(){var a=YK();a.hid=a.hid||gb();return a.hid}function $K(a,b){var c=YK();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function zL(a){(tx(a)||Mj())&&W(a,N.m.Nk,Nn()||Mn());!tx(a)&&Mj()&&W(a,N.m.Yk,"::")}function AL(a){if(H(78)&&Mj()){$u(a);av(a,"cpf",Wn(P(a.F,N.m.Za)));var b=P(a.F,N.m.xc);av(a,"cu",b===!0?1:b===!1?0:void 0);av(a,"cf",Wn(P(a.F,N.m.pb)));av(a,"cd",zr(Vn(P(a.F,N.m.hb)),Vn(P(a.F,N.m.Fb))))}};var WL={AW:xn.tm,G:xn.wn,DC:xn.un};function XL(a){var b=Hi(a);return""+br(b.map(function(c){return c.value}).join("!"))}function YL(a){var b=Vo(a);return b&&WL[b.prefix]}function ZL(a,b){var c=a[b];c&&(c.clearTimerId&&y.clearTimeout(c.clearTimerId),c.clearTimerId=y.setTimeout(function(){delete a[b]},36E5))};var EM=window,FM=document,GM=function(a){var b=EM._gaUserPrefs;if(b&&b.ioo&&b.ioo()||FM.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&EM["ga-disable-"+a]===!0)return!0;try{var c=EM.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(FM.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m;(m=g.slice(1).join("=").replace(/^\s*|\s*$/g,""))&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return FM.getElementById("__gaOptOutExtension")?!0:!1};function RM(a){jb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Kb]||{};jb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function xN(a,b){}function yN(a,b){var c=function(){};return c}
function zN(a,b,c){};var AN=yN;var BN=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function CN(a,b,c){var d=this;if(!bh(a)||!Wg(b)||!Wg(c))throw I(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?nd(b):{};fE([function(){return L(d,"configure_google_tags",a,e)}]);var f=c?nd(c):{},g=jE(this);f.originatingEntity=$E(g);Pv(Lv(a,e),g.eventId,f);}CN.M="internal.gtagConfig";
function EN(a,b){}
EN.publicName="gtagSet";function FN(){var a={};return a};function GN(a,b){}GN.publicName="injectHiddenIframe";var HN=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function IN(a,b,c,d,e){if(!((bh(a)||ah(a))&&Yg(b)&&Yg(c)&&fh(d)&&fh(e)))throw I(this.getName(),["string|OpaqueValue","function","function","boolean|undefined","boolean|undefined"],arguments);var f=jE(this);d&&HN(3);e&&(HN(1),HN(2));var g=f.eventId,h=f.zb(),m=HN(void 0);if(Ck){var n=String(m)+h;VD[g]=VD[g]||[];VD[g].push(n);WD[g]=WD[g]||[];WD[g].push("p"+h)}
if(d&&e)throw Error("useIframe and supportDocumentWrite cannot both be true.");L(this,"unsafe_inject_arbitrary_html",d,e);var p=nd(b,this.K),q=nd(c,this.K),r=nd(a,this.K,1);JN(r,p,q,!!d,!!e,f);}
var KN=function(a,b,c,d){return function(){try{if(b.length>0){var e=b.shift(),f=KN(a,b,c,d),g=e;if(String(g.nodeName).toUpperCase()==="SCRIPT"&&g.type==="text/gtmscript"){var h=g.text||g.textContent||g.innerHTML||"",m=g.getAttribute("data-gtmsrc"),n=g.charset||"";m?sc(m,f,d,{async:!1,id:e.id,text:h,charset:n},a):(g=A.createElement("script"),g.async=!1,g.type="text/javascript",g.id=e.id,g.text=h,g.charset=n,f&&(g.onload=f),a.insertBefore(g,null));m||f()}else if(e.innerHTML&&e.innerHTML.toLowerCase().indexOf("<script")>=
0){for(var p=[];e.firstChild;)p.push(e.removeChild(e.firstChild));a.insertBefore(e,null);KN(e,p,f,d)()}else a.insertBefore(e,null),f()}else c()}catch(q){d()}}},JN=function(a,b,c,d,e,f){if(A.body){var g=zD(a,b,c);a=g.Ro;b=g.onSuccess;if(d){}else e?
LN(a,b,c):KN(A.body,Bc(a),b,c)()}else y.setTimeout(function(){JN(a,b,c,d,e,f)})};var LN=function(a,b,c){eC(function(){var d=google_tag_manager_external.postscribe.getPostscribe(),e={done:b},f=document.createElement("div");f.style.display="none";f.style.visibility="hidden";A.body.appendChild(f);try{d(f,a,e)}catch(g){c()}})};IN.M="internal.injectHtml";var MN={};
function ON(a,b,c,d){}var PN={dl:1,id:1},QN={};
function RN(a,b,c,d){}H(160)?RN.publicName="injectScript":ON.publicName="injectScript";RN.M="internal.injectScript";function SN(){return Rn()}SN.M="internal.isAutoPiiEligible";function TN(a){var b=!0;return b}TN.publicName="isConsentGranted";function UN(a){var b=!1;return b}UN.M="internal.isDebugMode";function VN(){return Pn()}VN.M="internal.isDmaRegion";function WN(a){var b=!1;return b}WN.M="internal.isEntityInfrastructure";function XN(){var a=!1;return a}XN.M="internal.isLandingPage";function YN(){var a=Dh(function(b){jE(this).log("error",b)});a.publicName="JSON";return a};function ZN(a){var b=void 0;if(!bh(a))throw I(this.getName(),["string"],arguments);b=mk(a);return od(b)}ZN.M="internal.legacyParseUrl";function $N(){return!1}
var aO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function bO(){try{L(this,"logging")}catch(c){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=nd(a[b],this.K);console.log.apply(console,a);}bO.publicName="logToConsole";function cO(a,b){}cO.M="internal.mergeRemoteConfig";function dO(a,b,c){c=c===void 0?!0:c;var d=[];return od(d)}dO.M="internal.parseCookieValuesFromString";function eO(a){var b=void 0;if(typeof a!=="string")return;a&&vb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=od({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=mk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=fk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=od(n);
return b}eO.publicName="parseUrl";function fO(a){}fO.M="internal.processAsNewEvent";function gO(a,b,c){var d;return d}gO.M="internal.pushToDataLayer";function hO(a){var b=xa.apply(1,arguments),c=!1;return c}hO.publicName="queryPermission";function iO(a){var b=this;}iO.M="internal.queueAdsTransmission";function jO(){var a="";return a}jO.publicName="readCharacterSet";function kO(){return sj.Cb}kO.M="internal.readDataLayerName";function lO(){var a="";return a}lO.publicName="readTitle";function mO(a,b){var c=this;}mO.M="internal.registerCcdCallback";function nO(a){
return!0}nO.M="internal.registerDestination";var oO=["config","event","get","set"];function pO(a,b,c){}pO.M="internal.registerGtagCommandListener";function qO(a,b){var c=!1;return c}qO.M="internal.removeDataLayerEventListener";function rO(a,b){}
rO.M="internal.removeFormData";function sO(){}sO.publicName="resetDataLayer";function tO(a,b,c){var d=void 0;return d}tO.M="internal.scrubUrlParams";function uO(a){}uO.M="internal.sendAdsHit";function vO(a,b,c,d){}vO.M="internal.sendGtagEvent";function wO(a,b,c){}wO.publicName="sendPixel";function xO(a,b){}xO.M="internal.setAnchorHref";function yO(a){}yO.M="internal.setContainerConsentDefaults";function zO(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}zO.publicName="setCookie";function AO(a){}AO.M="internal.setCorePlatformServices";function BO(a,b){}BO.M="internal.setDataLayerValue";function CO(a){}CO.publicName="setDefaultConsentState";function DO(a,b){}DO.M="internal.setDelegatedConsentType";function EO(a,b){}EO.M="internal.setFormAction";function FO(a,b,c){c=c===void 0?!1:c;}FO.M="internal.setInCrossContainerData";function GO(a,b,c){return!1}GO.publicName="setInWindow";function HO(a,b,c){}HO.M="internal.setProductSettingsParameter";function IO(a,b,c){}IO.M="internal.setRemoteConfigParameter";function JO(a,b){}JO.M="internal.setTransmissionMode";function KO(a,b,c,d){var e=this;}KO.publicName="sha256";function LO(a,b,c){}
LO.M="internal.sortRemoteConfigParameters";function MO(a,b){var c=void 0;return c}MO.M="internal.subscribeToCrossContainerData";var NO={},OO={};NO.getItem=function(a){var b=null;return b};NO.setItem=function(a,b){};
NO.removeItem=function(a){};NO.clear=function(){};NO.publicName="templateStorage";function PO(a,b){var c=!1;return c}PO.M="internal.testRegex";function QO(a){var b;return b};function RO(a){var b;return b}RO.M="internal.unsiloId";function SO(a,b){var c;return c}SO.M="internal.unsubscribeFromCrossContainerData";function TO(a){}TO.publicName="updateConsentState";var UO;function VO(a,b,c){UO=UO||new Oh;UO.add(a,b,c)}function WO(a,b){var c=UO=UO||new Oh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=ab(b)?jh(a,b):kh(a,b)}
function XO(){return function(a){var b;var c=UO;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.K.D;if(e){var f=!1,g=e.zb();if(g){qh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function YO(){var a=function(c){return void WO(c.M,c)},b=function(c){return void VO(c.publicName,c)};b(dE);b(kE);b(zF);b(BF);b(CF);b(JF);b(LF);b(GG);b(YN());b(IG);b(lK);b(mK);b(GK);b(HK);b(IK);b(OK);b(EN);b(GN);b(TN);b(bO);b(eO);b(hO);b(jO);b(lO);b(wO);b(zO);b(CO);b(GO);b(KO);b(NO);b(TO);VO("Math",oh());VO("Object",Mh);VO("TestHelper",Rh());VO("assertApi",lh);VO("assertThat",mh);VO("decodeUri",rh);VO("decodeUriComponent",sh);VO("encodeUri",th);VO("encodeUriComponent",uh);VO("fail",zh);VO("generateRandom",
Ah);VO("getTimestamp",Bh);VO("getTimestampMillis",Bh);VO("getType",Ch);VO("makeInteger",Eh);VO("makeNumber",Fh);VO("makeString",Gh);VO("makeTableMap",Hh);VO("mock",Kh);VO("mockObject",Lh);VO("fromBase64",gK,!("atob"in y));VO("localStorage",aO,!$N());VO("toBase64",QO,!("btoa"in y));a(cE);a(gE);a(BE);a(NE);a(UE);a(ZE);a(oF);a(xF);a(AF);a(DF);a(EF);a(FF);a(GF);a(HF);a(IF);a(KF);a(MF);a(FG);a(HG);a(JG);a(LG);a(MG);a(NG);a(OG);a(PG);a(UG);a(bH);a(cH);a(nH);a(sH);a(xH);a(GH);a(LH);a(YH);a($H);a(nI);a(oI);
a(qI);a(eK);a(fK);a(hK);a(iK);a(jK);a(oK);a(pK);a(qK);a(rK);a(sK);a(tK);a(uK);a(vK);a(wK);a(xK);a(yK);a(AK);a(BK);a(CK);a(DK);a(EK);a(FK);a(JK);a(KK);a(LK);a(MK);a(NK);a(QK);a(CN);a(IN);a(RN);a(SN);a(UN);a(VN);a(WN);a(XN);a(ZN);a(mF);a(cO);a(dO);a(fO);a(gO);a(iO);a(kO);a(mO);a(nO);a(pO);a(qO);a(rO);a(Qh);a(tO);a(uO);a(vO);a(xO);a(yO);a(AO);a(BO);a(DO);a(EO);a(FO);a(HO);a(IO);a(JO);a(LO);a(MO);a(PO);a(RO);a(SO);WO("internal.CrossContainerSchema",KG());WO("internal.IframingStateSchema",FN());H(104)&&a(nK);H(160)?b(RN):b(ON);return XO()};var aE;
function ZO(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;aE=new Ie;$O();of=$D();var e=aE,f=YO(),g=new gd("require",f);g.Wa();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Kf(n,d[m]);try{aE.execute(n),H(120)&&Ck&&n[0]===50&&h.push(n[1])}catch(r){}}H(120)&&(Bf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Ij[q]=
["sandboxedScripts"]}aP(b)}function $O(){aE.D.D.O=function(a,b,c){Lo.SANDBOXED_JS_SEMAPHORE=Lo.SANDBOXED_JS_SEMAPHORE||0;Lo.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Lo.SANDBOXED_JS_SEMAPHORE--}}}function aP(a){a&&jb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Ij[e]=Ij[e]||[];Ij[e].push(b)}})};function bP(a){Pv(Jv("developer_id."+a,!0),0,{})};var cP=Array.isArray;function dP(a,b){return Zc(a,b||null)}function X(a){return window.encodeURIComponent(a)}function eP(a,b,c){wc(a,b,c)}function fP(a,b){if(!a)return!1;var c=gk(mk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function gP(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var pP=y.clearTimeout,qP=y.setTimeout;function rP(a,b,c){if(Zq()){b&&C(b)}else return sc(a,b,c,void 0)}function sP(){return y.location.href}function tP(a,b){return Sj(a,b||2)}function uP(a,b){y[a]=b}function vP(a,b,c){b&&(y[a]===void 0||c&&!y[a])&&(y[a]=b);return y[a]}function wP(a,b){if(Zq()){b&&C(b)}else uc(a,b)}
var xP={};var Z={securityGroups:{}};


Z.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){Z.__access_globals=b;Z.__access_globals.H="access_globals";Z.__access_globals.isVendorTemplate=!0;Z.__access_globals.priorityOverride=0;Z.__access_globals.isInfrastructure=!1;
Z.__access_globals.runInSiloedMode=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var m=c[h],n=m.key;m.read&&e.push(n);m.write&&f.push(n);m.execute&&g.push(n)}return{assert:function(p,q,r){if(!cb(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},R:a}})}();

Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.H="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&
c.push("extension"),b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!cb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!cb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},
"Prohibited query key: "+h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},R:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!cb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&yg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},R:a}})}();
Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.H="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!cb(g))throw e(f,{},"Keys must be strings.");if(c!==
"any"){try{if(yg(g,d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},R:a}})}();





Z.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){Z.__load_google_tags=b;Z.__load_google_tags.H="load_google_tags";Z.__load_google_tags.isVendorTemplate=!0;Z.__load_google_tags.priorityOverride=0;Z.__load_google_tags.isInfrastructure=!1;Z.__load_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||
[],h=b.vtp_createPermissionError;return{assert:function(m,n,p){(function(q){if(!cb(q))throw h(m,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(m,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!cb(q))throw h(m,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(Qg(mk(q),f))return}catch(r){throw h(m,{},"Invalid first party URL filter.");}}throw h(m,{},"Prohibited first party URL: "+q);}})(p)},R:a}})}();




Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.H="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!cb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!cb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},R:a}})}();



Z.securityGroups.unsafe_inject_arbitrary_html=["google"],function(){function a(b,c,d){return{useIframe:c,supportDocumentWrite:d}}(function(b){Z.__unsafe_inject_arbitrary_html=b;Z.__unsafe_inject_arbitrary_html.H="unsafe_inject_arbitrary_html";Z.__unsafe_inject_arbitrary_html.isVendorTemplate=!0;Z.__unsafe_inject_arbitrary_html.priorityOverride=0;Z.__unsafe_inject_arbitrary_html.isInfrastructure=!1;Z.__unsafe_inject_arbitrary_html.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;
return{assert:function(d,e,f){if(e&&f)throw c(d,{},"Only one of useIframe and supportDocumentWrite can be true.");if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"useIframe must be a boolean.");if(f!==void 0&&typeof f!=="boolean")throw c(d,{},"supportDocumentWrite must be a boolean.");},R:a}})}();

Z.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){Z.__logging=b;Z.__logging.H="logging";Z.__logging.isVendorTemplate=!0;Z.__logging.priorityOverride=0;Z.__logging.isInfrastructure=!1;Z.__logging.runInSiloedMode=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},R:a}})}();

Z.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){Z.__configure_google_tags=b;Z.__configure_google_tags.H="configure_google_tags";Z.__configure_google_tags.isVendorTemplate=!0;Z.__configure_google_tags.priorityOverride=0;Z.__configure_google_tags.isInfrastructure=!1;Z.__configure_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!cb(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},R:a}})}();





var Oo={dataLayer:Tj,callback:function(a){Hj.hasOwnProperty(a)&&ab(Hj[a])&&Hj[a]();delete Hj[a]},bootstrap:0};Oo.onHtmlSuccess=AD(!0),Oo.onHtmlFailure=AD(!1);
function yP(){No();jm();TA();tb(Ij,Z.securityGroups);var a=em(fm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;lo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);wD(),xf({Wo:function(d){return d===uD},io:function(d){return new xD(d)},Xo:function(d){for(var e=!1,f=!1,g=2;g<d.length;g++)e=e||d[g]===8,f=f||d[g]===16;return e&&f},pp:function(d){var e;if(d===uD)e=d;else{var f=Qo();vD[f]=d;e='google_tag_manager["rm"]["'+cm()+'"]('+f+")"}return e}});
Af={bo:Qf}}var zP=!1;
function Jn(){try{if(zP||!sm()){rj();pj.P="";pj.kb="ad_storage|analytics_storage|ad_user_data|ad_personalization";
pj.la="ad_storage|analytics_storage|ad_user_data";pj.ka="5510";pj.ka="5510";hm();if(H(109)){}hg[8]=
!0;var a=Mo("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});so(a);Ko();SD();Bq();Ro();if(km()){jF();DA().removeExternalRestrictions(cm());}else{PA();yf();uf=Z;vf=CD;Sf=new Zf;ZO();yP();Hn||(Gn=Ln());Ho();SC();dC();yC=!1;A.readyState==="complete"?AC():xc(y,"load",AC);YB();Ck&&(Gp(Up),y.setInterval(Tp,864E5),Gp(UD),Gp(vB),Gp(lz),Gp(Xp),Gp(XD),Gp(GB),H(120)&&(Gp(AB),Gp(BB),Gp(CB)));Dk&&(mn(),lp(),UC(),YC(),WC(),cn("bt",String(pj.D?2:Aj?1:0)),cn("ct",String(pj.D?0:Aj?1:Zq()?2:3)),VC());google_tag_manager_external.postscribe.installPostscribe();sD();wn(1);kF();Gj=qb();Oo.bootstrap=Gj;pj.O&&RC();H(109)&&Ez();H(134)&&(typeof y.name==="string"&&
vb(y.name,"web-pixel-sandbox-CUSTOM")&&Nc()?bP("dMDg0Yz"):y.Shopify&&(bP("dN2ZkMj"),Nc()&&bP("dNTU0Yz")))}}}catch(b){wn(4),Qp()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Yn(n)&&(m=h.Tk)}function c(){m&&jc?g(m):a()}if(!y["__TAGGY_INSTALLED"]){var d=!1;if(A.referrer){var e=mk(A.referrer);d=ik(e,"host")==="cct.google"}if(!d){var f=jr("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(y["__TAGGY_INSTALLED"]=!0,sc("https://cct.google/taggy/agent.js"))}var g=function(u){var v="GTM",w="GTM";yj&&(v="OGT",w="GTAG");var x=y["google.tagmanager.debugui2.queue"];x||(x=
[],y["google.tagmanager.debugui2.queue"]=x,sc("https://"+sj.jg+"/debug/bootstrap?id="+Wf.ctid+"&src="+w+"&cond="+u+"&gtm="+ar()));var z={messageType:"CONTAINER_STARTING",data:{scriptSource:jc,containerProduct:v,debug:!1,id:Wf.ctid,targetRef:{ctid:Wf.ctid,isDestination:Ul()},aliases:Xl(),destinations:Vl()}};z.data.resume=function(){a()};sj.xm&&(z.data.initialPublish=!0);x.push(z)},h={xn:1,Wk:2,pl:3,Sj:4,Tk:5};h[h.xn]="GTM_DEBUG_LEGACY_PARAM";h[h.Wk]="GTM_DEBUG_PARAM";h[h.pl]="REFERRER";h[h.Sj]="COOKIE";h[h.Tk]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=gk(y.location,"query",!1,void 0,"gtm_debug");Yn(p)&&(m=h.Wk);if(!m&&A.referrer){var q=mk(A.referrer);ik(q,"host")==="tagassistant.google.com"&&(m=h.pl)}if(!m){var r=jr("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Sj)}m||b();if(!m&&Xn(n)){var t=!1;xc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);y.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){H(83)&&zP&&!Ln()["0"]?In():Jn()});

})()

