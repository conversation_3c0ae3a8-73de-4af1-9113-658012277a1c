{"marketplace:installed:spicetify/spicetify-cli/Extensions/webnowplaying.js": "{\"manifest\":{\"name\":\"Web Now Playing\",\"description\":\"For Rainmeter users, establish connection with WebNowPlaying plugin to send track metadata and control players.\",\"preview\":null,\"main\":\"Extensions/webnowplaying.js\"},\"type\":\"extension\",\"title\":\"Web Now Playing\",\"subtitle\":\"For Rainmeter users, establish connection with WebNowPlaying plugin to send track metadata and control players.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/null\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/webnowplaying.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed:JulienMaille/spicetify-dynamic-theme/user.css": "{\"manifest\":{\"name\":\"Default Dynamic\",\"description\":\"A theme for Spicetify with support for light/dark modes and album art based colors.\",\"preview\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/Dark.gif\",\"readme\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/README.md\",\"tags\":[\"dynamic-color\",\"flat\",\"external JS\"],\"usercss\":\"user.css\",\"schemes\":\"color.ini\",\"include\":[\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/default-dynamic.js\",\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/Vibrant.min.js\"]},\"type\":\"theme\",\"title\":\"Default Dynamic\",\"subtitle\":\"A theme for Spicetify with support for light/dark modes and album art based colors.\",\"authors\":[{\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"url\":\"https://github.com/<PERSON>Maille\"}],\"user\":\"JulienMaille\",\"repo\":\"spicetify-dynamic-theme\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/Dark.gif\",\"readmeURL\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/README.md\",\"stars\":222,\"tags\":[\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"external JS\",\"dynamic-color\",\"flat\"],\"cssURL\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/user.css\",\"schemesURL\":\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/color.ini\",\"include\":[\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/default-dynamic.js\",\"https://raw.githubusercontent.com/JulienMaille/spicetify-dynamic-theme/main/Vibrant.min.js\"],\"schemes\":{\"Base\":{\"animation\":\"010101\"},\"NoAnimation\":{\"animation\":\"000000\"}},\"activeScheme\":\"Base\",\"lastUpdated\":\"2024-03-02T02:23:55Z\",\"created\":\"2021-05-13T19:15:08Z\"}", "marketplace:installed:Pithaya/spicetify-apps/extensions/extended-copy/dist/extended-copy.js": "{\"manifest\":{\"name\":\"Extended copy\",\"description\":\"Copy names, IDs, URIs or data to the clipboard.\",\"preview\":\"extensions/extended-copy/preview.png\",\"main\":\"extensions/extended-copy/dist/extended-copy.js\",\"readme\":\"extensions/extended-copy/README.md\"},\"type\":\"extension\",\"title\":\"Extended copy\",\"subtitle\":\"Copy names, IDs, URIs or data to the clipboard.\",\"authors\":[{\"name\":\"Pithaya\",\"url\":\"https://github.com/Pithaya\"}],\"user\":\"Pithaya\",\"repo\":\"spicetify-apps\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/extended-copy/preview.png\",\"extensionURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/extended-copy/dist/extended-copy.js\",\"readmeURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/extended-copy/README.md\",\"stars\":23,\"lastUpdated\":\"2023-12-24T16:38:48Z\",\"created\":\"2022-11-23T14:46:37Z\"}", "marketplace:installed:huhridge/huh-spicetify-extensions/fullAlbumDate/fullAlbumDate.js": "{\"manifest\":{\"name\":\"Display full Album date\",\"description\":\"Display the full album date instead of just year\",\"preview\":\"fullAlbumDate/preview.jpg\",\"main\":\"fullAlbumDate/fullAlbumDate.js\",\"readme\":\"fullAlbumDate/README.md\"},\"type\":\"extension\",\"title\":\"Display full Album date\",\"subtitle\":\"Display the full album date instead of just year\",\"authors\":[{\"name\":\"huhridge\",\"url\":\"https://github.com/huhridge\"}],\"user\":\"huhridge\",\"repo\":\"huh-spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/huhridge/huh-spicetify-extensions/main/fullAlbumDate/preview.jpg\",\"extensionURL\":\"https://raw.githubusercontent.com/huhridge/huh-spicetify-extensions/main/fullAlbumDate/fullAlbumDate.js\",\"readmeURL\":\"https://raw.githubusercontent.com/huhridge/huh-spicetify-extensions/main/fullAlbumDate/README.md\",\"stars\":172,\"lastUpdated\":\"2023-12-06T15:54:50Z\",\"created\":\"2021-12-16T08:00:02Z\"}", "marketplace:installed:snippet:fix-sidebar": "{\"title\":\"fix-sidebar\",\"code\":\".BeautifulLyricsBackground:is(aside) .BeautifulLyricsBackground-Container {\\n    filter: saturate(3) brightness(.25) !important;\\n}\\n\\n.Root__right-sidebar:has(.main-nowPlayingView-section, canvas) .main-nowPlayingView-section {\\n    background-color: rgba(0, 0, 0, 0.5) !important;\\n}\\n\\n#BeautifulLyrics-CardView, .LoadingLyricsCard {\\n    background-color: rgba(0, 0, 0, 0.5) !important;\\n}\",\"description\":\"Fix sidebar look.\",\"imageURL\":\"\",\"custom\":true}", "marketplace:installed:afonsojramos/spotify-details-extractor/spicetify/extractor.js": "{\"manifest\":{\"name\":\"Spotify Details Extractor\",\"description\":\"Spicetify extension to extract Spotify details from an album page in a specific JSON object.\",\"preview\":\"chromium/icons/icon128.png\",\"main\":\"spicetify/extractor.js\",\"readme\":\"README.md\"},\"type\":\"extension\",\"title\":\"Spotify Details Extractor\",\"subtitle\":\"Spicetify extension to extract Spotify details from an album page in a specific JSON object.\",\"authors\":[{\"name\":\"afonsojramos\",\"url\":\"https://github.com/afonsojramos\"}],\"user\":\"afonsojramos\",\"repo\":\"spotify-details-extractor\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/afonsojramos/spotify-details-extractor/main/chromium/icons/icon128.png\",\"extensionURL\":\"https://raw.githubusercontent.com/afonsojramos/spotify-details-extractor/main/spicetify/extractor.js\",\"readmeURL\":\"https://raw.githubusercontent.com/afonsojramos/spotify-details-extractor/main/README.md\",\"stars\":12,\"lastUpdated\":\"2023-05-23T19:44:02Z\",\"created\":\"2021-11-21T20:03:48Z\"}", "marketplace:installed:FoxRefire/spiceDL/spiceDL.js": "{\"manifest\":{\"name\":\"SpiceDL\",\"description\":\"Spicetify extension for download musics using spotDL \",\"preview\":\"https://github.com/FoxRefire/spiceDL/raw/Screenshot/Screenshot1.png?raw=true\",\"main\":\"spiceDL.js\",\"readme\":\"README.md\",\"authors\":[{\"name\":\"FoxRefire\",\"url\":\"https://github.com/FoxRefire\"}]},\"type\":\"extension\",\"title\":\"SpiceDL\",\"subtitle\":\"Spicetify extension for download musics using spotDL \",\"authors\":[{\"name\":\"<PERSON>Ref<PERSON>\",\"url\":\"https://github.com/FoxRefire\"}],\"user\":\"FoxRefire\",\"repo\":\"spiceDL\",\"branch\":\"main\",\"imageURL\":\"https://github.com/FoxRefire/spiceDL/raw/Screenshot/Screenshot1.png?raw=true\",\"extensionURL\":\"https://raw.githubusercontent.com/FoxRefire/spiceDL/main/spiceDL.js\",\"readmeURL\":\"https://raw.githubusercontent.com/FoxRefire/spiceDL/main/README.md\",\"stars\":1,\"lastUpdated\":\"2024-02-09T02:04:00Z\",\"created\":\"2024-01-30T21:59:08Z\"}", "marketplace:installed:snippet:Default-Progress-Bar": "{\"code\":\".playback-bar {position: relative !important; display: block !important; --playback-bar-grid-gap: 8px !important; -webkit-box-orient: horizontal !important; -webkit-box-direction: normal !important; -webkit-box-pack: justify !important; -ms-flex-pack: justify !important; -webkit-box-align: center !important; -ms-flex-align: center !important; align-items: center !important; display: -webkit-box !important; display: -ms-flexbox !important; display: flex !important; -ms-flex-direction: row !important; flex-direction: row !important; gap: var(--playback-bar-grid-gap) !important; justify-content: space-between !important; height: 12px !important;} .x-progressBar-progressBarBg {--progress-bar-height: 6px !important; --progress-bar-radius: 10px !important;} :root .Root__now-playing-bar .playback-bar > div {height: 17.59px !important;} .player-controls__buttons--new-icons { margin-bottom: 12px !importan;} .main-nowPlayingBar-nowPlayingBar {padding-bottom: 0px !important;}\",\"title\":\"Default Progress Bar\",\"description\":\"Return progress bar to default size and location on themes that change it\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/resources/assets/snippets/default-progress-bar.png\"}", "marketplace:installed:Taeko-ar/spicetify-last-fm/src/lastfm.js": "{\"manifest\":{\"name\":\"Last.fm Stats\",\"description\":\"Get info from your Last.fm account\",\"preview\":\"images/song_stats.png\",\"main\":\"src/lastfm.js\",\"readme\":\"README.md\",\"authors\":[{\"name\":\"<PERSON>ek<PERSON>\",\"url\":\"https://github.com/LucasBares\"}],\"tags\":[\"lastfm\"]},\"type\":\"extension\",\"title\":\"Last.fm Stats\",\"subtitle\":\"Get info from your Last.fm account\",\"authors\":[{\"name\":\"<PERSON>ek<PERSON>\",\"url\":\"https://github.com/LucasBares\"}],\"user\":\"Taeko-ar\",\"repo\":\"spicetify-last-fm\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/Taeko-ar/spicetify-last-fm/main/images/song_stats.png\",\"extensionURL\":\"https://raw.githubusercontent.com/Taeko-ar/spicetify-last-fm/main/src/lastfm.js\",\"readmeURL\":\"https://raw.githubusercontent.com/Taeko-ar/spicetify-last-fm/main/README.md\",\"stars\":19,\"lastUpdated\":\"2022-11-21T02:04:35Z\",\"created\":\"2022-06-05T03:43:23Z\"}", "marketplace:installed:SPOTLAB-Live/Spicetify-waveform/waveform.js": "{\"manifest\":{\"name\":\"Waveform\",\"description\":\"Waveform seekbar generated from Spotify audio analysis API.\",\"preview\":\"assets/waveform-preview.png\",\"main\":\"waveform.js\",\"readme\":\"README.md\",\"authors\":[{\"name\":\"SPOTLAB\",\"url\":\"https://github.com/SPOTLAB-Live\"}],\"tags\":[\"spicetify\",\"waveform\",\"wave\",\"dj\",\"soundcloud\"]},\"type\":\"extension\",\"title\":\"Waveform\",\"subtitle\":\"Waveform seekbar generated from Spotify audio analysis API.\",\"authors\":[{\"name\":\"SPOTLAB\",\"url\":\"https://github.com/SPOTLAB-Live\"}],\"user\":\"SPOTLAB-Live\",\"repo\":\"Spicetify-waveform\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/SPOTLAB-Live/Spicetify-waveform/main/assets/waveform-preview.png\",\"extensionURL\":\"https://raw.githubusercontent.com/SPOTLAB-Live/Spicetify-waveform/main/waveform.js\",\"readmeURL\":\"https://raw.githubusercontent.com/SPOTLAB-Live/Spicetify-waveform/main/README.md\",\"stars\":19,\"lastUpdated\":\"2024-07-10T03:42:07Z\",\"created\":\"2024-07-08T23:14:42Z\"}", "marketplace:installed:spicetify/spicetify-cli/Extensions/bookmark.js": "{\"manifest\":{\"name\":\"Bookmark\",\"description\":\"Easily store and browse pages, play tracks or tracks in specific time. Useful for who wants to check out an artist, album later without following them or writing their name down.\",\"preview\":\"https://i.imgur.com/isgU4TS.png\",\"main\":\"Extensions/bookmark.js\"},\"type\":\"extension\",\"title\":\"Bookmark\",\"subtitle\":\"Easily store and browse pages, play tracks or tracks in specific time. Useful for who wants to check out an artist, album later without following them or writing their name down.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/isgU4TS.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/bookmark.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed:spicetify/spicetify-cli/Extensions/loopyLoop.js": "{\"manifest\":{\"name\":\"Loopy Loop\",\"description\":\"Provide ability to mark start and end points on progress bar and automatically loop over that track portion.\",\"preview\":\"https://i.imgur.com/YEkbjLC.png\",\"main\":\"Extensions/loopyLoop.js\"},\"type\":\"extension\",\"title\":\"Loopy Loop\",\"subtitle\":\"Provide ability to mark start and end points on progress bar and automatically loop over that track portion.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/YEkbjLC.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/loopyLoop.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed:Pithaya/spicetify-apps/extensions/made-for-you-shortcut/dist/made-for-you-shortcut.js": "{\"manifest\":{\"name\":\"\\\"Made for you\\\" shortcut\",\"description\":\"Add a shortcut to the \\\"Made for you\\\" page to the sidebar.\",\"preview\":\"extensions/made-for-you-shortcut/preview.png\",\"main\":\"extensions/made-for-you-shortcut/dist/made-for-you-shortcut.js\",\"readme\":\"extensions/made-for-you-shortcut/README.md\"},\"type\":\"extension\",\"title\":\"\\\"Made for you\\\" shortcut\",\"subtitle\":\"Add a shortcut to the \\\"Made for you\\\" page to the sidebar.\",\"authors\":[{\"name\":\"Pithaya\",\"url\":\"https://github.com/Pithaya\"}],\"user\":\"Pithaya\",\"repo\":\"spicetify-apps\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/made-for-you-shortcut/preview.png\",\"extensionURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/made-for-you-shortcut/dist/made-for-you-shortcut.js\",\"readmeURL\":\"https://raw.githubusercontent.com/Pithaya/spicetify-apps/main/extensions/made-for-you-shortcut/README.md\",\"stars\":23,\"lastUpdated\":\"2023-12-24T16:38:48Z\",\"created\":\"2022-11-23T14:46:37Z\"}", "marketplace:installed:snippet:Hide-download-button": "{\"code\":\"div.x-downloadButton-DownloadButton { display: none; }\",\"title\":\"Hide download button\",\"description\":\"Hide download button in EPs and albums\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/resources/assets/snippets/hide-download-button.png\"}", "marketplace:installed:Vexcited/better-spotify-genres/spotifyGenres.js": "{\"manifest\":{\"name\":\"Better Spotify Genres\",\"description\":\"See what genres you are listening to.\",\"preview\":\"assets/preview.png\",\"main\":\"spotifyGenres.js\",\"readme\":\"README.md\",\"branch\":\"build\",\"authors\":[{\"name\":\"Vexcited\",\"url\":\"https://github.com/Vexcited\"},{\"name\":\"Tetrax-10\",\"url\":\"https://github.com/Tetrax-10\"}]},\"type\":\"extension\",\"title\":\"Better Spotify Genres\",\"subtitle\":\"See what genres you are listening to.\",\"authors\":[{\"name\":\"Vexcited\",\"url\":\"https://github.com/Vexcited\"},{\"name\":\"Tetrax-10\",\"url\":\"https://github.com/Tetrax-10\"}],\"user\":\"Vexcited\",\"repo\":\"better-spotify-genres\",\"branch\":\"build\",\"imageURL\":\"https://raw.githubusercontent.com/Vexcited/better-spotify-genres/build/assets/preview.png\",\"extensionURL\":\"https://raw.githubusercontent.com/Vexcited/better-spotify-genres/build/spotifyGenres.js\",\"readmeURL\":\"https://raw.githubusercontent.com/Vexcited/better-spotify-genres/build/README.md\",\"stars\":12,\"lastUpdated\":\"2024-01-08T06:35:35Z\",\"created\":\"2023-12-20T06:42:57Z\"}", "marketplace:installed-snippets": "[\"marketplace:installed:snippet:Hide-download-button\",\"marketplace:installed:snippet:Smaller-right-sidebar-covert-art\",\"marketplace:installed:snippet:Smooth-Progress/Volume-bar\",\"marketplace:installed:snippet:Default-Progress-Bar\",\"marketplace:installed:snippet:Fix-main-view-width\",\"marketplace:installed:snippet:Better-connect-bar\",\"marketplace:installed:snippet:fix-nowPlayingView\",\"marketplace:installed:snippet:fix-sidebar\"]", "marketplace:installed:daksh2k/Spicetify-stuff/Extensions/Wrappers/fullScreenWrapper.js": "{\"manifest\":{\"name\":\"Full Screen\",\"description\":\"Fancy artwork display. Modified from FAD with extra features.\",\"preview\":\"https://i.imgur.com/uPbnZkz.png\",\"main\":\"Extensions/Wrappers/fullScreenWrapper.js\",\"readme\":\"Extensions/full-screen/README.md\"},\"type\":\"extension\",\"title\":\"Full Screen\",\"subtitle\":\"Fancy artwork display. Modified from FAD with extra features.\",\"authors\":[{\"name\":\"daksh2k\",\"url\":\"https://github.com/daksh2k\"}],\"user\":\"daksh2k\",\"repo\":\"Spicetify-stuff\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/uPbnZkz.png\",\"extensionURL\":\"https://raw.githubusercontent.com/daksh2k/Spicetify-stuff/master/Extensions/Wrappers/fullScreenWrapper.js\",\"readmeURL\":\"https://raw.githubusercontent.com/daksh2k/Spicetify-stuff/master/Extensions/full-screen/README.md\",\"stars\":155,\"lastUpdated\":\"2024-01-08T10:02:42Z\",\"created\":\"2021-06-22T18:26:31Z\"}", "marketplace:installed:CharlieS1103/spicetify-extensions/songstats/songstats.js": "{\"manifest\":{\"name\":\"Song Stats\",\"description\":\"Spicetify extension to display a song's audio features\",\"preview\":\"songstats/songstats.png\",\"main\":\"songstats/songstats.js\",\"readme\":\"songstats/README.md\"},\"type\":\"extension\",\"title\":\"Song Stats\",\"subtitle\":\"Spicetify extension to display a song's audio features\",\"authors\":[{\"name\":\"CharlieS1103\",\"url\":\"https://github.com/CharlieS1103\"}],\"user\":\"CharlieS1103\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/songstats/songstats.png\",\"extensionURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/songstats/songstats.js\",\"readmeURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/songstats/README.md\",\"stars\":481,\"lastUpdated\":\"2023-10-01T23:12:12Z\",\"created\":\"2021-07-09T23:11:13Z\"}", "marketplace:installed:CharlieS1103/spicetify-extensions/adblock/adblock.js": "{\"manifest\":{\"name\":\"Adblock\",\"description\":\"Block all audio ads and UI ads!\",\"preview\":\"adblock/adblock.png\",\"main\":\"adblock/adblock.js\",\"readme\":\"adblock/README.md\"},\"type\":\"extension\",\"title\":\"Adblock\",\"subtitle\":\"Block all audio ads and UI ads!\",\"authors\":[{\"name\":\"CharlieS1103\",\"url\":\"https://github.com/CharlieS1103\"}],\"user\":\"CharlieS1103\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/adblock/adblock.png\",\"extensionURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/adblock/adblock.js\",\"readmeURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/adblock/README.md\",\"stars\":498,\"lastUpdated\":\"2023-10-01T23:12:12Z\",\"created\":\"2021-07-09T23:11:13Z\"}", "marketplace:themeDevTools": "true", "marketplace:installed:snippet:Smaller-right-sidebar-covert-art": "{\"code\":\":root { --right-sidebar-cover-art-size: 85px; } \\n.main-nowPlayingView-coverArt { width: var(--right-sidebar-cover-art-size); } \\n.main-nowPlayingView-coverArtContainer { min-height: unset !important; height: var(--right-sidebar-cover-art-size) !important; } \\n.main-nowPlayingView-nowPlayingGrid { flex-direction: unset; } \\n.main-nowPlayingView-contextItemInfo .main-trackInfo-name { font-size: 1.25rem; } \\n.main-nowPlayingView-contextItemInfo .main-trackInfo-artists { font-size: 0.85rem; }\",\"title\":\"Smaller right sidebar covert art\",\"description\":\"Makes the right sidebar cover art smaller and move the track info to the right\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/resources/assets/snippets/smaller-right-sidebar-cover.png\"}", "marketplace:installed:snippet:Smooth-Progress/Volume-bar": "{\"code\":\".x-progressBar-fillColor { transition: 500ms; } .progress-bar__slider { transition: 500ms; }\",\"title\":\"Smooth Progress/Volume bar\",\"description\":\"Makes the Progress/Volume bar glide\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/resources/assets/snippets/smooth-progress-bar.png\"}", "marketplace:installed:ohitstom/spicetify-extensions/volumePercentage/volumePercentage.js": "{\"manifest\":{\"name\":\"Volume Percentage\",\"description\":\"View/Modify volume percentage in a hoverable Tippy.\",\"preview\":\"volumePercentage/marketplace.png\",\"main\":\"volumePercentage/volumePercentage.js\",\"readme\":\"volumePercentage/README.md\",\"tags\":[\"minimal\",\"audio\"]},\"type\":\"extension\",\"title\":\"Volume Percentage\",\"subtitle\":\"View/Modify volume percentage in a hoverable Tippy.\",\"authors\":[{\"name\":\"ohitstom\",\"url\":\"https://github.com/ohitstom\"}],\"user\":\"ohitstom\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/volumePercentage/marketplace.png\",\"extensionURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/volumePercentage/volumePercentage.js\",\"readmeURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/volumePercentage/README.md\",\"stars\":12,\"lastUpdated\":\"2023-12-17T13:19:31Z\",\"created\":\"2023-05-12T00:35:27Z\"}", "marketplace:active-tab": "Snippets", "marketplace:showArchived": "true", "marketplace:installed:ohitstom/spicetify-extensions/scannables/scannables.js": "{\"manifest\":{\"name\":\"Scannables\",\"description\":\"View a scannable code for any track or playlist (like mobile).\",\"preview\":\"scannables/marketplace.png\",\"main\":\"scannables/scannables.js\",\"readme\":\"scannables/README.md\",\"tags\":[\"mobile\",\"code\"]},\"type\":\"extension\",\"title\":\"Scannables\",\"subtitle\":\"View a scannable code for any track or playlist (like mobile).\",\"authors\":[{\"name\":\"ohitstom\",\"url\":\"https://github.com/ohitstom\"}],\"user\":\"ohitstom\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/scannables/marketplace.png\",\"extensionURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/scannables/scannables.js\",\"readmeURL\":\"https://raw.githubusercontent.com/ohitstom/spicetify-extensions/main/scannables/README.md\",\"stars\":36,\"lastUpdated\":\"2024-05-16T10:25:56Z\",\"created\":\"2023-05-12T00:35:27Z\"}", "marketplace:installed:spicetify/spicetify-cli/Extensions/fullAppDisplay.js": "{\"manifest\":{\"name\":\"Full App Display\",\"description\":\"Minimal album cover art display with beautiful blur effect background. Activating button locates in top bar. While in display mode, double click anywhere to exit. Right click anywhere to open setting menu.\",\"preview\":\"https://i.imgur.com/S7CPQ2s.png\",\"main\":\"Extensions/fullAppDisplay.js\"},\"type\":\"extension\",\"title\":\"Full App Display\",\"subtitle\":\"Minimal album cover art display with beautiful blur effect background. Activating button locates in top bar. While in display mode, double click anywhere to exit. Right click anywhere to open setting menu.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/S7CPQ2s.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/fullAppDisplay.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:theme-installed": "marketplace:installed:JulienMaille/spicetify-dynamic-theme/user.css", "marketplace:installed:spicetify/spicetify-cli/Extensions/keyboardShortcut.js": "{\"manifest\":{\"name\":\"Keyboard Shortcut\",\"description\":\"Register some useful keybinds to support keyboard-driven navigation in Spotify client. Less time touching the mouse.\",\"preview\":\"https://i.imgur.com/evkGv9q.png\",\"main\":\"Extensions/keyboardShortcut.js\"},\"type\":\"extension\",\"title\":\"Keyboard Shortcut\",\"subtitle\":\"Register some useful keybinds to support keyboard-driven navigation in Spotify client. Less time touching the mouse.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/evkGv9q.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/keyboardShortcut.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed:spicetify/spicetify-cli/Extensions/popupLyrics.js": "{\"manifest\":{\"name\":\"Pop-up Lyrics\",\"description\":\"Have easy access to a pop-up window with the current song's lyrics. Click at microphone icon on top bar to open lyrics windows. Right click at the same icon to open config menu to customize looks and lyrics providers priorities.\",\"preview\":\"https://i.imgur.com/Nx9Lx7D.png\",\"main\":\"Extensions/popupLyrics.js\"},\"type\":\"extension\",\"title\":\"Pop-up Lyrics\",\"subtitle\":\"Have easy access to a pop-up window with the current song's lyrics. Click at microphone icon on top bar to open lyrics windows. Right click at the same icon to open config menu to customize looks and lyrics providers priorities.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/Nx9Lx7D.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/popupLyrics.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed-extensions": "[\"marketplace:installed:CharlieS1103/spicetify-extensions/songstats/songstats.js\",\"marketplace:installed:CharlieS1103/spicetify-extensions/wikify/wikify.js\",\"marketplace:installed:pnthach95/spicetify-extensions/dist/copytoclipboard.js\",\"marketplace:installed:daksh2k/Spicetify-stuff/Extensions/Wrappers/fullScreenWrapper.js\",\"marketplace:installed:huhridge/huh-spicetify-extensions/fullAlbumDate/fullAlbumDate.js\",\"marketplace:installed:Theblockbuster1/spicetify-extensions/QueueTime/QueueTime.js\",\"marketplace:installed:Pithaya/spicetify-apps/extensions/extended-copy/dist/extended-copy.js\",\"marketplace:installed:Pithaya/spicetify-apps/extensions/made-for-you-shortcut/dist/made-for-you-shortcut.js\",\"marketplace:installed:afonsojramos/spotify-details-extractor/spicetify/extractor.js\",\"marketplace:installed:ohitstom/spicetify-extensions/volumePercentage/volumePercentage.js\",\"marketplace:installed:Taeko-ar/spicetify-last-fm/src/lastfm.js\",\"marketplace:installed:FoxRefire/spiceDL/spiceDL.js\",\"marketplace:installed:H3r3zy/spicetify-pip/pip.js\",\"marketplace:installed:CharlieS1103/spicetify-extensions/adblock/adblock.js\",\"marketplace:installed:Vexcited/better-spotify-genres/spotifyGenres.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/bookmark.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/fullAppDisplay.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/keyboardShortcut.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/loopyLoop.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/popupLyrics.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/shuffle+.js\",\"marketplace:installed:spicetify/spicetify-cli/Extensions/webnowplaying.js\",\"marketplace:installed:rxri/spicetify-extensions/songstats/songstats.js\",\"marketplace:installed:ohitstom/spicetify-extensions/scannables/scannables.js\",\"marketplace:installed:surfbryce/beautiful-lyrics/./Builds/Release/beautiful-lyrics.mjs\",\"marketplace:installed:SPOTLAB-Live/Spicetify-waveform/waveform.js\"]", "marketplace:sort": "stars", "marketplace:installed-themes": "[\"marketplace:installed:JulienMaille/spicetify-dynamic-theme/user.css\"]", "marketplace:installed:snippet:fix-nowPlayingView": "{\"title\":\"fix-nowPlayingView\",\"code\":\".main-nowPlayingView-nowPlayingGrid {\\n    flex-direction: column !important;\\n}\",\"description\":\"Just fix nowPlayingView.\",\"imageURL\":\"\",\"custom\":true}", "marketplace:installed:CharlieS1103/spicetify-extensions/wikify/wikify.js": "{\"manifest\":{\"name\":\"WikiFy\",\"description\":\"Shows an Artists wikipedia page to learn more about them\",\"preview\":\"wikify/wikify.png\",\"main\":\"wikify/wikify.js\",\"readme\":\"wikify/README.md\"},\"type\":\"extension\",\"title\":\"WikiFy\",\"subtitle\":\"Shows an Artists wikipedia page to learn more about them\",\"authors\":[{\"name\":\"CharlieS1103\",\"url\":\"https://github.com/CharlieS1103\"}],\"user\":\"CharlieS1103\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/wikify/wikify.png\",\"extensionURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/wikify/wikify.js\",\"readmeURL\":\"https://raw.githubusercontent.com/CharlieS1103/spicetify-extensions/main/wikify/README.md\",\"stars\":481,\"lastUpdated\":\"2023-10-01T23:12:12Z\",\"created\":\"2021-07-09T23:11:13Z\"}", "marketplace:installed:spicetify/spicetify-cli/Extensions/shuffle+.js": "{\"manifest\":{\"name\":\"<PERSON><PERSON>+\",\"description\":\"<PERSON><PERSON> using <PERSON><PERSON> algorithm with zero bias. After installing extensions, right click album/playlist/artist item, there will be an option \\\"Play with Shuffle+\\\". You can also multiple select tracks and choose to \\\"Play with Shuffle+\\\". Moreover, enable option \\\"Auto Shuffle+\\\" in Profile menu to inject Shuffle+ into every play buttons, no need to right click anymore.\",\"preview\":\"https://i.imgur.com/gxbnqSN.png\",\"main\":\"Extensions/shuffle+.js\"},\"type\":\"extension\",\"title\":\"Shuffle+\",\"subtitle\":\"Shuffles using <PERSON><PERSON> algorithm with zero bias. After installing extensions, right click album/playlist/artist item, there will be an option \\\"Play with Shuffle+\\\". You can also multiple select tracks and choose to \\\"Play with Shuffle+\\\". Moreover, enable option \\\"Auto Shuffle+\\\" in Profile menu to inject Shuffle+ into every play buttons, no need to right click anymore.\",\"authors\":[{\"name\":\"spicetify\",\"url\":\"https://github.com/spicetify\"}],\"user\":\"spicetify\",\"repo\":\"spicetify-cli\",\"branch\":\"master\",\"imageURL\":\"https://i.imgur.com/gxbnqSN.png\",\"extensionURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/Extensions/shuffle+.js\",\"readmeURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-cli/master/undefined\",\"stars\":16218,\"lastUpdated\":\"2024-03-01T16:52:59Z\",\"created\":\"2018-12-01T19:55:18Z\"}", "marketplace:installed:H3r3zy/spicetify-pip/pip.js": "{\"manifest\":{\"name\":\"<PERSON><PERSON>\",\"description\":\"PIP mode for spotify\",\"preview\":\"assets/preview.png\",\"readme\":\"README.md\",\"authors\":[{\"name\":\"H3r3zy\",\"email\":\"https://github.com/H3r3zy\"}],\"main\":\"pip.js\"},\"type\":\"extension\",\"title\":\"Pip\",\"subtitle\":\"PIP mode for spotify\",\"authors\":[{\"name\":\"H3r3zy\"}],\"user\":\"H3r3zy\",\"repo\":\"spicetify-pip\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/H3r3zy/spicetify-pip/main/assets/preview.png\",\"extensionURL\":\"https://raw.githubusercontent.com/H3r3zy/spicetify-pip/main/pip.js\",\"readmeURL\":\"https://raw.githubusercontent.com/H3r3zy/spicetify-pip/main/README.md\",\"stars\":6,\"lastUpdated\":\"2024-01-13T14:17:19Z\",\"created\":\"2024-01-11T17:08:38Z\"}", "marketplace:tabs": "[{\"name\":\"Extensions\",\"enabled\":true},{\"name\":\"Themes\",\"enabled\":true},{\"name\":\"Snippets\",\"enabled\":true},{\"name\":\"Apps\",\"enabled\":true},{\"name\":\"Installed\",\"enabled\":true}]", "marketplace:installed:Theblockbuster1/spicetify-extensions/QueueTime/QueueTime.js": "{\"manifest\":{\"name\":\"Queue Time\",\"description\":\"Display time remaining in the current queue.\",\"preview\":\"QueueTime/QueueTime.png\",\"main\":\"QueueTime/QueueTime.js\",\"readme\":\"QueueTime/README.md\"},\"type\":\"extension\",\"title\":\"Queue Time\",\"subtitle\":\"Display time remaining in the current queue.\",\"authors\":[{\"name\":\"Theblockbuster1\",\"url\":\"https://github.com/Theblockbuster1\"}],\"user\":\"Theblockbuster1\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/Theblockbuster1/spicetify-extensions/main/QueueTime/QueueTime.png\",\"extensionURL\":\"https://raw.githubusercontent.com/Theblockbuster1/spicetify-extensions/main/QueueTime/QueueTime.js\",\"readmeURL\":\"https://raw.githubusercontent.com/Theblockbuster1/spicetify-extensions/main/QueueTime/README.md\",\"stars\":52,\"lastUpdated\":\"2023-12-19T18:27:14Z\",\"created\":\"2022-02-15T17:49:44Z\"}", "marketplace:installed:rxri/spicetify-extensions/songstats/songstats.js": "{\"manifest\":{\"name\":\"Song Stats\",\"description\":\"Spicetify extension to display a song's audio features\",\"preview\":\"songstats/songstats.png\",\"main\":\"songstats/songstats.js\",\"readme\":\"songstats/README.md\",\"authors\":[{\"name\":\"CharlieS1103\",\"url\":\"https://github.com/CharlieS1103\"}]},\"type\":\"extension\",\"title\":\"Song Stats\",\"subtitle\":\"Spicetify extension to display a song's audio features\",\"authors\":[{\"name\":\"<PERSON>S1103\",\"url\":\"https://github.com/CharlieS1103\"}],\"user\":\"rxri\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/rxri/spicetify-extensions/main/songstats/songstats.png\",\"extensionURL\":\"https://raw.githubusercontent.com/rxri/spicetify-extensions/main/songstats/songstats.js\",\"readmeURL\":\"https://raw.githubusercontent.com/rxri/spicetify-extensions/main/songstats/README.md\",\"stars\":19,\"lastUpdated\":\"2024-05-18T22:57:12Z\",\"created\":\"2024-05-09T16:03:19Z\"}", "marketplace:albumArtBasedColors": "false", "marketplace:installed:snippet:Better-connect-bar": "{\"title\":\"Better-connect-bar\",\"code\":\".main-connectBar-connectBar {\\n  position: absolute !important;\\n  overflow: visible !important;\\n  left: 75% !important;\\n  height: 20px !important;\\n  bottom: 1% !important;\\n  padding: 2px !important;\\n}\",\"description\":\"Make connect bar a better look.\",\"imageURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAk4AAAIpCAYAAABUqLcXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAHYcAAB2HAY/l8WUAAGRaSURBVHhe7d0J9C1XQef7+t+bO89TyDyRBBIMKGOAMAgi2o39UATDoD6f7y3feq1tXNqtz3bZ9mpXv36LuJRu214PW/u1gKgIgviUBkdMICgoYxgCITcjJLm59yb35k7J/b/61Tn7/99n/3eNp6rO3lXfD2uT/zn3DFW7dlX9ateuOkvXXvfs5QRojZrT0uTPSuzmV/V9ZU22rc8BAGDWuul/gQWrEnYUdPoMTfosU6qy32MXAMAQEJzQkiqhxlb39VVUCShdfK9BQAKAoSM4oQV2EKkaXmxN3uNadGghNAHAGBCcMCcTaBQc6gagJu/xqRpayj7HpdebUqTo+6u8HwAQC4ITFqRqYJontNiqhBd9lilVFb2WwAQAQ0NwQkN2qKkSNOzXt2VR32sQmgBgbAhOaMAOBVXDi63Je1xVPgMAgHYRnFCTCTQKLlXDi3lt1fe0FZrKPsel15tShf16twAAhojghIBUCR1thqY6Yc6o+3oAwJAQnFCRHWoWFRyqfG+V8NUUgQkAxo7ghBJdBhFb2Xe0HVoIQQCA+ghOKGEHDP29iMBR9TurBrym82BCZB9BEgAQIoITKuoyMBUFkXlCk97rK66y7/e9BwAwRgQnLFhXoamqNr4/ZAQ/AGgTwQkVLGLHO29oIiysmidYAgBsBCcskG+H3kYPSZ33F71W02dKrAhNANAmghMWJC801eF+RtPQZd5nFwAA1iI4oWd5PTh1wkreZ1Qxz3tDRNADgD4RnBCZvNAz5vAwpCAIAGEjOGHB2ugxqfP+su+LrUeK0AQAfSI4YYHqBiY3JJSFoLoIIQCAYgQnLEidwDNPL1DV94YamtoOhwCAeRCckAo1NEjetI0pTIS8fABgXAhO6FkbPSh13l/2fQolMQZHAMAiEJwQMDc0tBG6bIsKJWY+2p4fAEDXCE7oQd2Aotc3DTVV39v08+flC0qEJwCIBcEJqS533HUDSt7rq0zjosLQvGKdbgAYH4ITOqbAM28wK3q/QocpRpXXL4o9vYueFgBAXQQnBMQNEWWhyxc66r6+C2a6y6YfABAbghM6UDeg6PVN3lNHn6HJRXgCgKEgOKFlbQWaIYWNvkIbAKBrBCcEqCg0KYTUDVt9BhczfXYBAAwFwQktqhsS3Ncr+JSFpjwhhCYAwNARnNAChZM6AaXu6yXv9UVhq+53SFl4c9V9/bz6/j4AgI3ghDn5wknRjr0oAC1a02loEtAAADEiOKFFTXtDmgaWovc1CTPmPXXe23do0vf1/Z0AAIPghJZUCT/uDr9K0MoLCnnvaxIs7M+q+*************************************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\",\"custom\":true}", "marketplace:installed:snippet:Fix-main-view-width": "{\"code\":\".contentSpacing {\\n  max-width: 100% !important;\\n}\",\"title\":\"Fix main view width\",\"description\":\"Makes main view fill up all available space\",\"imageURL\":\"https://raw.githubusercontent.com/spicetify/spicetify-marketplace/main/resources/assets/snippets/fix-main-view-width.png\"}", "marketplace:installed:pnthach95/spicetify-extensions/dist/copytoclipboard.js": "{\"manifest\":{\"name\":\"Copy to Clipboard\",\"description\":\"Spicetify extension to copy text (song name, artist name, album name) to clipboard\",\"preview\":\"screenshot.png\",\"main\":\"dist/copytoclipboard.js\",\"authors\":[{\"name\":\"pnthach95\",\"url\":\"https://github.com/pnthach95\"},{\"name\":\"Tetrax-10\",\"url\":\"https://github.com/Tetrax-10\"}],\"readme\":\"README.md\"},\"type\":\"extension\",\"title\":\"Copy to Clipboard\",\"subtitle\":\"Spicetify extension to copy text (song name, artist name, album name) to clipboard\",\"authors\":[{\"name\":\"pnthach95\",\"url\":\"https://github.com/pnthach95\"},{\"name\":\"Tetrax-10\",\"url\":\"https://github.com/Tetrax-10\"}],\"user\":\"pnthach95\",\"repo\":\"spicetify-extensions\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/pnthach95/spicetify-extensions/main/screenshot.png\",\"extensionURL\":\"https://raw.githubusercontent.com/pnthach95/spicetify-extensions/main/dist/copytoclipboard.js\",\"readmeURL\":\"https://raw.githubusercontent.com/pnthach95/spicetify-extensions/main/README.md\",\"stars\":25,\"lastUpdated\":\"2023-09-10T04:07:58Z\",\"created\":\"2020-06-20T17:38:50Z\"}", "marketplace:installed:surfbryce/beautiful-lyrics/./Builds/Release/beautiful-lyrics.mjs": "{\"manifest\":{\"name\":\"Beautiful Lyrics\",\"description\":\"Adds Synced Lyrics, Dynamic Background, Fullscreen, Romanization, and more!\",\"preview\":\"./previews/icon.gif\",\"main\":\"./Builds/Release/beautiful-lyrics.mjs\",\"readme\":\"README.md\",\"authors\":[{\"name\":\"SoCalifornian\",\"url\":\"https://github.com/surfbryce\"}],\"tags\":[\"lyrics\",\"fullscreen\",\"background\",\"romanization\",\"romaji\",\"romaja\",\"pinyin\",\"japanese\",\"korean\",\"chinese\"]},\"type\":\"extension\",\"title\":\"Beautiful Lyrics\",\"subtitle\":\"Adds Synced Lyrics, Dynamic Background, Fullscreen, Romanization, and more!\",\"authors\":[{\"name\":\"SoCalifornian\",\"url\":\"https://github.com/surfbryce\"}],\"user\":\"surfbryce\",\"repo\":\"beautiful-lyrics\",\"branch\":\"main\",\"imageURL\":\"https://raw.githubusercontent.com/surfbryce/beautiful-lyrics/main/./previews/icon.gif\",\"extensionURL\":\"https://raw.githubusercontent.com/surfbryce/beautiful-lyrics/main/./Builds/Release/beautiful-lyrics.mjs\",\"readmeURL\":\"https://raw.githubusercontent.com/surfbryce/beautiful-lyrics/main/README.md\",\"stars\":772,\"lastUpdated\":\"2024-05-29T07:37:51Z\",\"created\":\"2023-05-16T09:11:09Z\"}", "marketplace:local-theme": "marketplace"}