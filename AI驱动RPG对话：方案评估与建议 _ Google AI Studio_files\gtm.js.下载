
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"41",
  
  "macros":[{"function":"__v","convert_case_to":1,"vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"pagePath"},{"function":"__e"},{"function":"__j","vtp_name":"navigator.userAgent"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":["macro",3],"vtp_name":"originalLocation"},{"function":"__u","convert_case_to":1,"vtp_component":"QUERY","vtp_queryKey":"utm_source","vtp_customUrlSource":["macro",4],"vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__c","vtp_value":["template",["macro",2],":",["macro",5]]},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",7],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",7],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","(.*\\.google)(\\.com)","value","$1-b197145817$2"]]},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",0],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":["macro",9],"vtp_map":["list",["map","key","undefined","value",["macro",9]],["map","key",".+","value",["macro",0]]]},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",10],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",10],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","(?:\/intl\/[^\\\/]+)?\/(index\\.html?)?","value","\/"],["map","key","(?:\/intl\/[^\\\/]+)?\/?(.+\\.[a-z]+)","value","\/$1"],["map","key","(?:\/intl\/[^\\\/]+)?\/?(.+?)\/?","value","\/$1\/"]]},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",11],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",11],"vtp_ignoreCase":true,"vtp_map":["list",["map","key",".*(\\\/tunedmodels\\\/)[^\\\/]*(\\\/.*)?","value","$1[redacted]$2"],["map","key",".*(\\\/prompts\\\/)(?!new_chat)[^\\\/]*(\\\/.*)?","value","$1[redacted]$2"]]},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",12],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",12],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","(?:\\\/app)?(?:\\\/u\\\/[^\\\/]*)?(.*)","value","$1"]]},{"function":"__c","convert_case_to":1,"vtp_value":["macro",13]},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",4],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":"","vtp_ignoreCase":true,"vtp_map":["list",["map","key",".*(\u0026|\\?)(dclid=[^\u0026]*).*","value","$2\u0026"]]},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",4],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":"","vtp_ignoreCase":true,"vtp_map":["list",["map","key","..*(\u0026|\\?)(gclid=[^\u0026]*).*","value","$2\u0026"]]},{"function":"__c","vtp_value":["template",["macro",15],["macro",16]]},{"function":"__remm","vtp_setDefaultValue":false,"vtp_input":["macro",17],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_map":["list",["map","key","(.+)\u0026","value","?$1"]]},{"function":"__c","vtp_value":["template","https:\/\/",["macro",8],["macro",14],["macro",18]]},{"function":"__v","vtp_name":"gtm.scrollThreshold","vtp_dataLayerVersion":1},{"function":"__smm","vtp_setDefaultValue":true,"vtp_input":["macro",20],"vtp_defaultValue":"0","vtp_map":["list",["map","key","25","value","25"],["map","key","50","value","25"],["map","key","75","value","25"],["map","key","100","value","25"]]},{"function":"__dbg"},{"function":"__c","vtp_value":"G-7QY5XWENNV"},{"function":"__c","vtp_value":"aistudio\\.google\\..*"},{"function":"__c","vtp_value":"G-RJSPDF5Y0Q"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",7],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",23],"vtp_ignoreCase":true,"vtp_map":["list",["map","key",".*(internal|staging|dogfood|corp|\\.prod\\.|test|qa|appspot|preview|kintaro).*","value",["macro",23]],["map","key",["macro",24],"value",["macro",25]]]},{"function":"__smm","vtp_setDefaultValue":true,"vtp_input":["macro",22],"vtp_defaultValue":["macro",26],"vtp_map":["list",["map","key","true","value",["macro",23]]]},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",28],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":"","vtp_ignoreCase":true,"vtp_map":["list",["map","key","(tel|mailto):.*","value","$1"]]},{"function":"__v","vtp_name":"gtm.triggers","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":""},{"function":"__v","vtp_name":"gtm.elementId","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementClasses","vtp_dataLayerVersion":1},{"function":"__aev","vtp_setDefaultValue":false,"vtp_stripWww":false,"vtp_varType":"URL","vtp_component":"HOST"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",33],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":"false","vtp_ignoreCase":true,"vtp_map":["list",["map","key",["macro",7],"value","true"],["map","key",["macro",24],"value","true"]]},{"function":"__c","vtp_value":"(http.:\\\/\\\/[^\\\/]*)(.*\\.)(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)| 7z|pkg|rar|gz|zip|avi|mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)([\\?#].*)?"},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",28],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":"","vtp_map":["list",["map","key",["macro",35],"value","$3"]]},{"function":"__e"},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"URL","vtp_component":"PATH","vtp_defaultPages":["list"]},{"function":"__gtcs","vtp_configSettingsTable":["list",["map","parameter","page_title","parameterValue","-"]]},{"function":"__f","vtp_component":"URL"},{"function":"__remm","vtp_setDefaultValue":false,"vtp_input":["macro",40],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_map":["list",["map","key","(.*\/\/[^\\?\\#]*).*","value","$1"]]},{"function":"__u","convert_case_to":1,"vtp_component":"QUERY","vtp_queryKey":"utm_medium","vtp_customUrlSource":["macro",4],"vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","convert_case_to":1,"vtp_component":"QUERY","vtp_queryKey":"utm_campaign","vtp_customUrlSource":["macro",4],"vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","convert_case_to":1,"vtp_component":"QUERY","vtp_queryKey":"utm_content","vtp_customUrlSource":["macro",4],"vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","convert_case_to":1,"vtp_component":"QUERY","vtp_queryKey":"utm_term","vtp_customUrlSource":["macro",4],"vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__v","convert_case_to":1,"vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"experiments"},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",9],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":"default","vtp_map":["list",["map","key","^\/intl\/([^\\\/]*).*","value","$1"]]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"isEEA"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"adsStorage"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"analyticsStorage"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"internalUser"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"signedIn"},{"function":"__v","vtp_name":"gtm.videoCurrentTime","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoDuration","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoPercent","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoProvider","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoTitle","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoVisible","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoStatus","vtp_dataLayerVersion":1},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":false,"vtp_input":["macro",28],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_map":["list",["map","key",["macro",35],"value","$1$2$3"]]},{"function":"__aev","vtp_varType":"TEXT"},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":false,"vtp_input":["macro",28],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_map":["list",["map","key",["macro",35],"value","$2$3"]]},{"function":"__v","convert_case_to":1,"vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"scrollDepth"},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",37],"vtp_fullMatch":false,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":["macro",37],"vtp_map":["list",["map","key","\\s","value","_"]]},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",65],"vtp_fullMatch":false,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":["macro",65],"vtp_map":["list",["map","key","_-_","value","-"]]},{"function":"__remm","convert_case_to":1,"vtp_setDefaultValue":true,"vtp_input":["macro",66],"vtp_fullMatch":false,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_defaultValue":["macro",66],"vtp_map":["list",["map","key","^.*clicked_zero_state_promptsuggestion_card.*$","value","prompt_suggestion_card_select"],["map","key","clicked_terms_of_service_dialog","value","tos"],["map","key","^run-","value",""]]},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.action"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.category"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"eventModel.label"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"event"},{"function":"__c","vtp_value":"G-11Z3M2QH2W"},{"function":"__c","vtp_value":"G-P1DBVKWT6V"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",7],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",73],"vtp_ignoreCase":true,"vtp_map":["list",["map","key",".*(internal|staging|dogfood|corp|\\.prod\\.|test|qa|appspot|preview|kintaro).*","value",["macro",73]],["map","key",["macro",24],"value",["macro",74]]]},{"function":"__smm","vtp_setDefaultValue":true,"vtp_input":["macro",22],"vtp_defaultValue":["macro",75],"vtp_map":["list",["map","key","true","value",["macro",73]]]},{"function":"__gtcs","vtp_configSettingsTable":["list",["map","parameter","page_title","parameterValue","-"]]},{"function":"__r"},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"ATTRIBUTE","vtp_attribute":"aria-label"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",7],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",23],"vtp_ignoreCase":true,"vtp_map":["list",["map","key",".*(internal|staging|dogfood|corp|\\.prod\\.|test|qa|appspot|preview|kintaro).*","value",["macro",23]],["map","key",["macro",24],"value",["macro",23]]]},{"function":"__smm","vtp_setDefaultValue":true,"vtp_input":["macro",22],"vtp_defaultValue":["macro",80],"vtp_map":["list",["map","key","true","value",["macro",23]]]}],
  "tags":[{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","percent_scrolled","parameterValue","0"],["map","parameter","scroll_increment","parameterValue",["macro",21]],["map","parameter","scroll_instance","parameterValue","1"]],"vtp_enhancedUserId":false,"vtp_eventName":"page_view","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":32},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_eventSettingsTable":["list",["map","parameter","link_id","parameterValue",["macro",31]],["map","parameter","link_classes","parameterValue",["macro",32]],["map","parameter","link_url","parameterValue",["template",["macro",29],":[redacted]"]],["map","parameter","page_location","parameterValue",["macro",19]]],"vtp_eventName":["template",["macro",29],"_link_click"],"vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":35},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":37},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","link_id","parameterValue",["macro",31]],["map","parameter","link_classes","parameterValue",["macro",32]],["map","parameter","link_url","parameterValue",["template","https:\/\/",["macro",33],["macro",38]]],["map","parameter","link_domain","parameterValue",["macro",33]],["map","parameter","outbound","parameterValue","true"],["map","parameter","page_location","parameterValue",["macro",19]]],"vtp_enhancedUserId":false,"vtp_eventName":"outbound_click","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":51},{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":["macro",27],"vtp_configSettingsVariable":["macro",39],"vtp_configSettingsTable":["list",["map","parameter","page_referrer","parameterValue",["macro",41]],["map","parameter","campaign_source","parameterValue",["macro",5]],["map","parameter","campaign_medium","parameterValue",["macro",42]],["map","parameter","campaign_name","parameterValue",["macro",43]],["map","parameter","campaign_content","parameterValue",["macro",44]],["map","parameter","campaign_term","parameterValue",["macro",45]],["map","parameter","experiments","parameterValue",["macro",46]],["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","page_locale","parameterValue",["macro",47]],["map","parameter","send_page_view","parameterValue","false"],["map","parameter","is_eea","parameterValue",["macro",48]],["map","parameter","ads_storage","parameterValue",["macro",49]],["map","parameter","analytics_storage","parameterValue",["macro",50]],["map","parameter","internal_user","parameterValue",["macro",51]],["map","parameter","signed_in","parameterValue",["macro",52]]],"tag_id":57},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","video_current_time","parameterValue",["macro",53]],["map","parameter","video_duration","parameterValue",["macro",54]],["map","parameter","video_percent","parameterValue",["macro",55]],["map","parameter","video_provider","parameterValue",["macro",56]],["map","parameter","video_title","parameterValue",["macro",57]],["map","parameter","video_url","parameterValue",["macro",58]],["map","parameter","visible","parameterValue",["macro",59]]],"vtp_eventName":["template","video_",["macro",60]],"vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":58},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_eventSettingsTable":["list",["map","parameter","link_id","parameterValue",["macro",31]],["map","parameter","link_url","parameterValue",["macro",61]],["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","link_text","parameterValue",["macro",62]],["map","parameter","file_name","parameterValue",["macro",63]],["map","parameter","file_extension","parameterValue",["macro",36]]],"vtp_eventName":"file_download","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":60},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","full_event_name","parameterValue",["macro",67]]],"vtp_enhancedUserId":false,"vtp_eventName":["macro",67],"vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":63},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_eventSettingsTable":["list",["map","parameter","percent_scrolled","parameterValue",["macro",64]],["map","parameter","page_location","parameterValue",["macro",19]]],"vtp_eventName":["macro",37],"vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":64},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","page_path","parameterValue",["macro",14]],["map","parameter","event_action","parameterValue",["macro",69]],["map","parameter","event_category","parameterValue",["macro",70]],["map","parameter","event_label","parameterValue",["macro",71]],["map","parameter","event","parameterValue",["macro",72]],["map","parameter","click_text","parameterValue",["macro",62]]],"vtp_eventName":"all_content_click","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":70},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":79},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"get_api_key","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":81},{"function":"__gaawe","metadata":["map"],"setup_tags":["list",["tag",0,0]],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","percent_scrolled","parameterValue","0"],["map","parameter","scroll_increment","parameterValue",["macro",21]],["map","parameter","scroll_instance","parameterValue","1"]],"vtp_enhancedUserId":false,"vtp_eventName":"page_view","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":86},{"function":"__googtag","metadata":["map"],"setup_tags":["list",["tag",4,0]],"once_per_event":true,"vtp_tagId":["macro",76],"vtp_configSettingsVariable":["macro",77],"vtp_configSettingsTable":["list",["map","parameter","page_referrer","parameterValue",["macro",41]],["map","parameter","campaign_source","parameterValue",["macro",5]],["map","parameter","campaign_medium","parameterValue",["macro",42]],["map","parameter","campaign_name","parameterValue",["macro",43]],["map","parameter","campaign_content","parameterValue",["macro",44]],["map","parameter","campaign_term","parameterValue",["macro",45]],["map","parameter","experiments","parameterValue",["macro",46]],["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","page_locale","parameterValue",["macro",47]],["map","parameter","send_page_view","parameterValue","false"],["map","parameter","is_eea","parameterValue",["macro",48]],["map","parameter","iseea","parameterValue",["macro",48]],["map","parameter","ads_storage","parameterValue",["macro",49]],["map","parameter","analytics_storage","parameterValue",["macro",50]],["map","parameter","internal_user","parameterValue",["macro",51]],["map","parameter","signed_in","parameterValue",["macro",52]]],"tag_id":87},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","page_path","parameterValue",["macro",14]],["map","parameter","event_action","parameterValue",["macro",69]],["map","parameter","event_category","parameterValue",["macro",70]],["map","parameter","event_label","parameterValue",["macro",71]],["map","parameter","event","parameterValue",["macro",72]],["map","parameter","click_text","parameterValue",["macro",62]]],"vtp_eventName":"all_content_click","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":90},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"get_api_key","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":92},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":93},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"tos_accepted","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":95},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"tos_accepted","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":96},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","event_label","parameterValue",["macro",71]]],"vtp_eventName":"api_key_generated_successfully","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":103},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","event_label","parameterValue",["macro",71]]],"vtp_eventName":"api_key_generated_successfully","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":105},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","transaction_id","parameterValue",["template","tos_accepted_",["macro",78]]],["map","parameter","value","parameterValue","1"],["map","parameter","item_id","parameterValue",["template","tos_accepted_",["macro",78]]],["map","parameter","item_name","parameterValue","tos_accepted"],["map","parameter","currency","parameterValue","USD"]],"vtp_eventName":"purchase","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":106},{"function":"__paused","vtp_originalTagType":"googtag","tag_id":110},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":117},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","event_category","parameterValue",["macro",70]],["map","parameter","event_action","parameterValue",["macro",69]],["map","parameter","event_label","parameterValue",["macro",71]]],"vtp_eventName":"email_opt_in","vtp_measurementIdOverride":["macro",27],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":119},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventSettingsTable":["list",["map","parameter","event_category","parameterValue",["macro",70]],["map","parameter","event_action","parameterValue",["macro",69]],["map","parameter","event_label","parameterValue",["macro",71]]],"vtp_eventName":"email_opt_in","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":120},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","page_location","parameterValue",["macro",19]],["map","parameter","full_event_name","parameterValue",["macro",67]]],"vtp_enhancedUserId":false,"vtp_eventName":["macro",67],"vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":137},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","link_id","parameterValue",["macro",31]],["map","parameter","link_classes","parameterValue",["macro",32]],["map","parameter","link_url","parameterValue",["template","https:\/\/",["macro",33],["macro",38]]],["map","parameter","link_domain","parameterValue",["macro",33]],["map","parameter","outbound","parameterValue","true"],["map","parameter","page_location","parameterValue",["macro",19]]],"vtp_enhancedUserId":false,"vtp_eventName":"outbound_click","vtp_measurementIdOverride":["macro",76],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":138},{"function":"__ytl","vtp_progressThresholdsPercent":"10,25,50,75","vtp_captureComplete":true,"vtp_captureStart":true,"vtp_fixMissingApi":false,"vtp_triggerStartOption":"DOM_READY","vtp_radioButtonGroup1":"PERCENTAGE","vtp_capturePause":false,"vtp_captureProgress":true,"vtp_uniqueTriggerId":"168426611_33","vtp_enableTriggerStartOption":true,"tag_id":139},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"168426611_34","tag_id":140},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"25,50,75,100","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"168426611_36","vtp_enableTriggerStartOption":true,"tag_id":141},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"168426611_48","tag_id":142},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"168426611_50","tag_id":143},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"168426611_68","tag_id":144},{"function":"__cl","tag_id":145},{"function":"__cl","tag_id":146},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"168426611_107","tag_id":147}],
  "predicates":[{"function":"_re","arg0":["macro",0],"arg1":".+"},{"function":"_eq","arg0":["macro",1],"arg1":"page_view"},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.js"},{"function":"_re","arg0":["macro",6],"arg1":"prober|inquisition","ignore_case":true},{"function":"_re","arg0":["macro",1],"arg1":".*"},{"function":"_re","arg0":["macro",29],"arg1":".+","ignore_case":true},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.linkClick"},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_34($|,)))"},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.scrollDepth"},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_36($|,)))"},{"function":"_eq","arg0":["macro",34],"arg1":"false"},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_48($|,)))"},{"function":"_re","arg0":["macro",36],"arg1":".+","ignore_case":true},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_50($|,)))"},{"function":"_re","arg0":["macro",37],"arg1":"^(gtm|optimize|dataLayer_initialized)\\.","ignore_case":true},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.video"},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_33($|,)))"},{"function":"_re","arg0":["macro",64],"arg1":".+"},{"function":"_eq","arg0":["macro",1],"arg1":"scroll"},{"function":"_css","arg0":["macro",68],"arg1":"run-button *"},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.click"},{"function":"_css","arg0":["macro",68],"arg1":"button[name=\"rerun-button\"]"},{"function":"_cn","arg0":["macro",28],"arg1":"https:\/\/aistudio.google.com\/app\/prompts\/"},{"function":"_re","arg0":["macro",30],"arg1":"(^$|((^|,)168426611_107($|,)))"},{"function":"_eq","arg0":["macro",1],"arg1":"click"},{"function":"_eq","arg0":["macro",1],"arg1":"dataLayer_initialized"},{"function":"_eq","arg0":["macro",1],"arg1":"MULTITURN - Clicked Testing Panel Run Button"},{"function":"_eq","arg0":["macro",1],"arg1":"FOOTER - Clicked Run Prompt Button"},{"function":"_eq","arg0":["macro",1],"arg1":"NAV - Clicked Create API Key Button"},{"function":"_eq","arg0":["macro",1],"arg1":"SIGNUP - Clicked Terms of Service Dialog Action - confirm"},{"function":"_re","arg0":["macro",1],"arg1":"API - API Key Generated Successfully.*"},{"function":"_eq","arg0":["macro",1],"arg1":"SIGNUP - Clicked Terms of Service Dialog Email Opt In - subscribed"},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.dom"},{"function":"_eq","arg0":["macro",1],"arg1":"gtm.load"}],
  "rules":[[["if",0,1],["add",0,4,12,13,22,23],["block",9,14]],[["if",2],["add",0,4,12,13,22,23,29,31,32,33,34,35,36]],[["if",5,6,7],["add",1]],[["if",8,9],["add",2]],[["if",6,10,11],["add",3,27]],[["if",15,16],["add",5]],[["if",6,12,13],["add",6],["block",3,27]],[["if",4],["unless",14],["add",7,9,14,26],["block",3,27]],[["if",17,18],["add",8],["block",7,9,14,26]],[["if",19,20],["add",9,10,14,16]],[["if",20,21],["add",9,14]],[["if",6,22,23],["add",9,14]],[["if",26],["add",10,16]],[["if",27],["add",10,16]],[["if",28],["add",11,15]],[["if",29],["add",17,18,21]],[["if",30],["add",19,20]],[["if",31],["add",24,25]],[["if",32],["add",28]],[["if",33],["add",30]],[["if",3,4],["block",0,1,2,3,4,5,6,7,8,9,12,13,14,22,23,26,27]],[["if",24],["block",9,14]],[["if",25],["block",9,14]]]
},
"runtime":[ [50,"__aev",[46,"a"],[50,"aC",[46,"aJ"],[22,[2,[15,"v"],"hasOwnProperty",[7,[15,"aJ"]]],[46,[53,[36,[16,[15,"v"],[15,"aJ"]]]]]],[52,"aK",[16,[15,"z"],"element"]],[22,[28,[15,"aK"]],[46,[36,[44]]]],[52,"aL",["g",[15,"aK"]]],["aD",[15,"aJ"],[15,"aL"]],[36,[15,"aL"]]],[50,"aD",[46,"aJ","aK"],[43,[15,"v"],[15,"aJ"],[15,"aK"]],[2,[15,"w"],"push",[7,[15,"aJ"]]],[22,[18,[17,[15,"w"],"length"],[15,"s"]],[46,[53,[52,"aL",[2,[15,"w"],"shift",[7]]],[2,[15,"b"],"delete",[7,[15,"v"],[15,"aL"]]]]]]],[50,"aE",[46,"aJ","aK"],[52,"aL",["n",[30,[30,[16,[15,"z"],"elementUrl"],[15,"aJ"]],""]]],[52,"aM",["n",[30,[17,[15,"aK"],"component"],"URL"]]],[38,[15,"aM"],[46,"URL","IS_OUTBOUND","PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT"],[46,[5,[46,[36,[15,"aL"]]]],[5,[46,[36,["aG",[15,"aL"],[17,[15,"aK"],"affiliatedDomains"]]]]],[5,[46,[36,[2,[15,"l"],"B",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"C",[7,[15,"aL"],[17,[15,"aK"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"l"],"D",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"E",[7,[15,"aL"],[17,[15,"aK"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"l"],"F",[7,[15,"aL"]]]]]],[5,[46,[22,[17,[15,"aK"],"queryKey"],[46,[53,[36,[2,[15,"l"],"H",[7,[15,"aL"],[17,[15,"aK"],"queryKey"]]]]]],[46,[53,[36,[2,[17,["m",[15,"aL"]],"search"],"replace",[7,"?",""]]]]]]]],[5,[46,[36,[2,[15,"l"],"G",[7,[15,"aL"]]]]]],[9,[46,[36,[17,["m",[15,"aL"]],"href"]]]]]]],[50,"aF",[46,"aJ","aK"],[52,"aL",[8,"ATTRIBUTE","elementAttribute","CLASSES","elementClasses","ELEMENT","element","ID","elementId","HISTORY_CHANGE_SOURCE","historyChangeSource","HISTORY_NEW_STATE","newHistoryState","HISTORY_NEW_URL_FRAGMENT","newUrlFragment","HISTORY_OLD_STATE","oldHistoryState","HISTORY_OLD_URL_FRAGMENT","oldUrlFragment","TARGET","elementTarget"]],[52,"aM",[16,[15,"z"],[16,[15,"aL"],[15,"aJ"]]]],[36,[39,[21,[15,"aM"],[44]],[15,"aM"],[15,"aK"]]]],[50,"aG",[46,"aJ","aK"],[22,[28,[15,"aJ"]],[46,[53,[36,false]]]],[52,"aL",["aI",[15,"aJ"]]],[22,["aH",[15,"aL"],["k"]],[46,[53,[36,false]]]],[22,[28,["q",[15,"aK"]]],[46,[53,[3,"aK",[2,[2,["n",[30,[15,"aK"],""]],"replace",[7,["c","\\s+","g"],""]],"split",[7,","]]]]]],[65,"aM",[15,"aK"],[46,[53,[22,[20,["j",[15,"aM"]],"object"],[46,[53,[22,[16,[15,"aM"],"is_regex"],[46,[53,[52,"aN",["c",[16,[15,"aM"],"domain"]]],[22,[20,[15,"aN"],[45]],[46,[6]]],[22,["p",[15,"aN"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[16,[15,"aM"],"domain"]],[46,[53,[36,false]]]]]]]]],[46,[22,[20,["j",[15,"aM"]],"RegExp"],[46,[53,[22,["p",[15,"aM"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[15,"aM"]],[46,[53,[36,false]]]]]]]]]]]],[36,true]],[50,"aH",[46,"aJ","aK"],[22,[28,[15,"aK"]],[46,[36,false]]],[22,[19,[2,[15,"aJ"],"indexOf",[7,[15,"aK"]]],0],[46,[36,true]]],[3,"aK",["aI",[15,"aK"]]],[22,[28,[15,"aK"]],[46,[36,false]]],[3,"aK",[2,[15,"aK"],"toLowerCase",[7]]],[41,"aL"],[3,"aL",[37,[17,[15,"aJ"],"length"],[17,[15,"aK"],"length"]]],[22,[1,[18,[15,"aL"],0],[29,[2,[15,"aK"],"charAt",[7,0]],"."]],[46,[53,[34,[3,"aL",[37,[15,"aL"],1]]],[3,"aK",[0,".",[15,"aK"]]]]]],[36,[1,[19,[15,"aL"],0],[12,[2,[15,"aJ"],"indexOf",[7,[15,"aK"],[15,"aL"]]],[15,"aL"]]]]],[50,"aI",[46,"aJ"],[22,[28,["p",[15,"r"],[15,"aJ"]]],[46,[53,[3,"aJ",[0,"http://",[15,"aJ"]]]]]],[36,[2,[15,"l"],"C",[7,[15,"aJ"],true]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","internal.getElementAttribute"]],[52,"e",["require","internal.getElementValue"]],[52,"f",["require","internal.getEventData"]],[52,"g",["require","internal.getElementInnerText"]],[52,"h",["require","internal.getElementProperty"]],[52,"i",["require","internal.copyFromDataLayerCache"]],[52,"j",["require","getType"]],[52,"k",["require","getUrl"]],[52,"l",[15,"__module_legacyUrls"]],[52,"m",["require","internal.legacyParseUrl"]],[52,"n",["require","makeString"]],[52,"o",["require","templateStorage"]],[52,"p",["require","internal.testRegex"]],[52,"q",[51,"",[7,"aJ"],[36,[20,["j",[15,"aJ"]],"array"]]]],[52,"r",["c","^https?:\\/\\/","i"]],[52,"s",35],[52,"t","eq"],[52,"u","evc"],[52,"v",[30,[2,[15,"o"],"getItem",[7,[15,"u"]]],[8]]],[2,[15,"o"],"setItem",[7,[15,"u"],[15,"v"]]],[52,"w",[30,[2,[15,"o"],"getItem",[7,[15,"t"]]],[7]]],[2,[15,"o"],"setItem",[7,[15,"t"],[15,"w"]]],[52,"x",[17,[15,"a"],"defaultValue"]],[52,"y",[17,[15,"a"],"varType"]],[52,"z",["i","gtm"]],[38,[15,"y"],[46,"TAG_NAME","TEXT","URL","ATTRIBUTE"],[46,[5,[46,[52,"aA",[16,[15,"z"],"element"]],[52,"aB",[1,[15,"aA"],["h",[15,"aA"],"tagName"]]],[36,[30,[15,"aB"],[15,"x"]]]]],[5,[46,[36,[30,["aC",["f","gtm\\.uniqueEventId"]],[15,"x"]]]]],[5,[46,[36,["aE",[15,"x"],[15,"a"]]]]],[5,[46,[22,[20,[17,[15,"a"],"attribute"],[44]],[46,[53,[36,["aF",[15,"y"],[15,"x"]]]]],[46,[53,[52,"aJ",[16,[15,"z"],"element"]],[52,"aK",[1,[15,"aJ"],[39,[20,[17,[15,"a"],"attribute"],"value"],["e",[15,"aJ"]],["d",[15,"aJ"],[17,[15,"a"],"attribute"]]]]],[36,[30,[30,[15,"aK"],[15,"x"]],""]]]]]]],[9,[46,[36,["aF",[15,"y"],[15,"x"]]]]]]]]
 ,[50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__cl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnClick"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__dbg",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"debugMode"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"m",[46,"v","w"],[66,"x",[2,[15,"b"],"keys",[7,[15,"w"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],[15,"x"]]]]]]],[50,"n",[46],[36,[7,[17,[15,"f"],"CB"],[17,[15,"f"],"CI"]]]],[50,"o",[46,"v"],[52,"w",["n"]],[65,"x",[15,"w"],[46,[53,[52,"y",[16,[15,"v"],[15,"x"]]],[22,[15,"y"],[46,[36,[15,"y"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g",["require","getType"]],[52,"h",["require","internal.loadGoogleTag"]],[52,"i",["require","logToConsole"]],[52,"j",["require","makeNumber"]],[52,"k",["require","makeString"]],[52,"l",["require","makeTableMap"]],[52,"p",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["g",[15,"p"]],"string"],[24,[2,[15,"p"],"indexOf",[7,"-"]],0]],[46,[53,["i",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"p"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"q",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"r",[30,["l",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"q"],[15,"r"]],[52,"s",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"t",[30,["l",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"s"],[15,"t"]],[52,"u",[15,"q"]],["m",[15,"u"],[15,"s"]],[22,[30,[2,[15,"u"],"hasOwnProperty",[7,[17,[15,"f"],"CZ"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"v",[30,[16,[15,"u"],[17,[15,"f"],"CZ"]],[8]]],["m",[15,"v"],[30,["l",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"u"],[17,[15,"f"],"CZ"],[15,"v"]]]]],[2,[15,"d"],"C",[7,[15,"u"],[17,[15,"d"],"A"],[51,"",[7,"v"],[36,[39,[20,"false",[2,["k",[15,"v"]],"toLowerCase",[7]]],false,[28,[28,[15,"v"]]]]]]]],[2,[15,"d"],"C",[7,[15,"u"],[17,[15,"d"],"B"],[51,"",[7,"v"],[36,["j",[15,"v"]]]]]],["h",[15,"p"],[8,"firstPartyUrl",["o",[15,"u"]]]],["e",[15,"p"],[15,"u"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__gtcs",[46,"a"],[52,"b",["require","makeTableMap"]],[36,[30,["b",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]]]
 ,[50,"__j",[46,"a"],[52,"b",["require","internal.copyKeyFromWindow"]],[36,["b",[17,[15,"a"],"name"]]]]
 ,[50,"__lcl",[46,"a"],[52,"b",["require","makeInteger"]],[52,"c",["require","makeString"]],[52,"d",["require","internal.enableAutoEventOnLinkClick"]],[52,"e",[8]],[22,[17,[15,"a"],"waitForTags"],[46,[53,[43,[15,"e"],"waitForTags",true],[43,[15,"e"],"waitForTagsTimeout",["b",[17,[15,"a"],"waitForTagsTimeout"]]]]]],[22,[17,[15,"a"],"checkValidation"],[46,[53,[43,[15,"e"],"checkValidation",true]]]],[52,"f",[30,[17,[15,"a"],"uniqueTriggerId"],"0"]],["d",[15,"e"],[15,"f"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__paused",[46,"a"],[2,[15,"a"],"gtmOnFailure",[7]]]
 ,[50,"__r",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","generateRandom"]],["$0",[30,[17,[15,"a"],"min"],0],[30,[17,[15,"a"],"max"],2.147483647E9]]]]]
 ,[50,"__sdl",[46,"a"],[50,"f",[46,"h"],[2,[15,"h"],"gtmOnSuccess",[7]],[52,"i",[17,[15,"h"],"horizontalThresholdUnits"]],[52,"j",[17,[15,"h"],"verticalThresholdUnits"]],[52,"k",[8]],[43,[15,"k"],"horizontalThresholdUnits",[15,"i"]],[38,[15,"i"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],[43,[15,"k"],"verticalThresholdUnits",[15,"j"]],[38,[15,"j"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],["c",[15,"k"],[17,[15,"h"],"uniqueTriggerId"]]],[50,"g",[46,"h"],[52,"i",[7]],[52,"j",[2,["e",[15,"h"]],"split",[7,","]]],[53,[41,"k"],[3,"k",0],[63,[7,"k"],[23,[15,"k"],[17,[15,"j"],"length"]],[33,[15,"k"],[3,"k",[0,[15,"k"],1]]],[46,[53,[52,"l",["d",[16,[15,"j"],[15,"k"]]]],[22,[29,[15,"l"],[15,"l"]],[46,[53,[36,[7]]]],[46,[22,[29,[17,[2,[16,[15,"j"],[15,"k"]],"trim",[7]],"length"],0],[46,[53,[2,[15,"i"],"push",[7,[15,"l"]]]]]]]]]]]],[36,[15,"i"]]],[52,"b",["require","callOnWindowLoad"]],[52,"c",["require","internal.enableAutoEventOnScroll"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[22,[17,[15,"a"],"triggerStartOption"],[46,[53,["f",[15,"a"]]]],[46,[53,["b",[51,"",[7],[36,["f",[15,"a"]]]]]]]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[50,"__v",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getType"]],[52,"e",[17,[15,"a"],"name"]],[22,[30,[28,[15,"e"]],[21,["d",[15,"e"]],"string"]],[46,[36,false]]],[52,"f",[2,[15,"e"],"replace",[7,["c","\\\\.","g"],"."]]],[52,"g",["b",[15,"f"],[30,[17,[15,"a"],"dataLayerVersion"],1]]],[36,[39,[21,[15,"g"],[44]],[15,"g"],[17,[15,"a"],"defaultValue"]]]]
 ,[50,"__ytl",[46,"a"],[50,"f",[46,"h"],[52,"i",[39,[20,[17,[15,"h"],"uniqueTriggerId"],[44]],"",[17,[15,"h"],"uniqueTriggerId"]]],[52,"j",[8,"captureStart",[17,[15,"h"],"captureStart"],"captureComplete",[17,[15,"h"],"captureComplete"],"capturePause",[17,[15,"h"],"capturePause"],"fixMissingApi",[17,[15,"h"],"fixMissingApi"],"progressThresholdsPercent",["g",[17,[15,"h"],"progressThresholdsPercent"]],"progressThresholdsTimeInSeconds",["g",[17,[15,"h"],"progressThresholdsTimeInSeconds"]]]],["c",[15,"j"],[15,"i"]],[2,[15,"h"],"gtmOnSuccess",[7]]],[50,"g",[46,"h"],[52,"i",[2,["e",[15,"h"]],"split",[7,","]]],[52,"j",[17,[15,"i"],"length"]],[52,"k",[7]],[66,"l",[15,"i"],[46,[53,[22,[12,[17,[2,[15,"l"],"trim",[7]],"length"],0],[46,[53,[6]]]],[52,"m",["d",[15,"l"]]],[22,[21,[15,"m"],[15,"m"]],[46,[53,[6]]]],[2,[15,"k"],"push",[7,[15,"m"]]]]]],[36,[15,"k"]]],[52,"b",["require","callOnDomReady"]],[52,"c",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[22,[17,[15,"a"],"triggerStartOption"],[46,[53,["f",[15,"a"]]]],[46,[53,["b",[51,"",[7],[36,["f",[15,"a"]]]]]]]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46,"g","h","i"],[65,"j",[15,"h"],[46,[53,[22,[2,[15,"g"],"hasOwnProperty",[7,[15,"j"]]],[46,[53,[43,[15,"g"],[15,"j"],["i",[16,[15,"g"],[15,"j"]]]]]]]]]]],[52,"b",["require","Object"]],[52,"c",[15,"__module_gtagSchema"]],[52,"d",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"X"],[17,[15,"c"],"Y"],[17,[15,"c"],"Z"],[17,[15,"c"],"AN"],[17,[15,"c"],"BK"],[17,[15,"c"],"CJ"],[17,[15,"c"],"BB"],[17,[15,"c"],"CA"]]]]],[52,"e",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"AM"],[17,[15,"c"],"BA"],[17,[15,"c"],"CC"],[17,[15,"c"],"CD"],[17,[15,"c"],"AW"]]]]],[36,[8,"A",[15,"d"],"B",[15,"e"],"C",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__aev":{"2":true}
,
"__c":{"2":true,"4":true}
,
"__dbg":{"2":true}
,
"__e":{"2":true,"4":true}
,
"__f":{"2":true}
,
"__googtag":{"1":10}
,
"__j":{"2":true}
,
"__r":{"2":true}
,
"__u":{"2":true}
,
"__v":{"2":true}


}
,"blob":{"1":"41"}
,"permissions":{
"__aev":{"read_data_layer":{"allowedKeys":"specific","keyPatterns":["gtm"]},"read_event_data":{"eventDataAccess":"any"},"read_dom_element_text":{},"get_element_attributes":{"allowedAttributes":"any"},"get_url":{"urlParts":"any"},"access_dom_element_properties":{"properties":[{"property":"tagName","read":true}]},"access_template_storage":{},"access_element_values":{"allowRead":[true],"allowWrite":[false]}}
,
"__c":{}
,
"__cl":{"detect_click_events":{}}
,
"__dbg":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__gtcs":{}
,
"__j":{"unsafe_access_globals":{},"access_globals":{}}
,
"__lcl":{"detect_link_click_events":{"allowWaitForTags":true}}
,
"__paused":{}
,
"__r":{}
,
"__sdl":{"process_dom_events":{"targets":[{"targetType":"window","eventName":"load"}]},"detect_scroll_events":{}}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}
,
"__v":{"read_data_layer":{"allowedKeys":"any"}}
,
"__ytl":{"process_dom_events":{"targets":[{"targetType":"document","eventName":"DOMContentLoaded"},{"targetType":"document","eventName":"readystatechange"},{"targetType":"window","eventName":"load"}]},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":true}}


}



,"security_groups":{
"google":[
"__aev"
,
"__c"
,
"__cl"
,
"__dbg"
,
"__e"
,
"__f"
,
"__googtag"
,
"__gtcs"
,
"__j"
,
"__r"
,
"__sdl"
,
"__u"
,
"__v"
,
"__ytl"

]


}



};




var ba,ca=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&da(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;da(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ma;
if(typeof Object.setPrototypeOf=="function")ma=Object.setPrototypeOf;else{var oa;a:{var pa={a:!0},qa={};try{qa.__proto__=pa;oa=qa.a;break a}catch(a){}oa=!1}ma=oa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ra=ma,sa=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(ra)ra(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.wq=b.prototype},k=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ta=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:ta(k(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.wq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.vr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map={};this.D={}};Ca.prototype.get=function(a){return this.map["dust."+a]};Ca.prototype.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};Ca.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ca.prototype.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Da=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ca.prototype.Aa=function(){return Da(this,1)};Ca.prototype.zc=function(){return Da(this,2)};Ca.prototype.Xb=function(){return Da(this,3)};var Ea=function(){};Ea.prototype.reset=function(){};var Fa=function(a,b){this.R=a;this.parent=b;this.D=this.J=void 0;this.Rc=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=new Ca};Fa.prototype.add=function(a,b){Ga(this,a,b,!1)};var Ga=function(a,b,c,d){if(!a.Rc)if(d){var e=a.values;e.set(b,c);e.D["dust."+b]=!0}else a.values.set(b,c)};Fa.prototype.set=function(a,b){this.Rc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Fa.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Fa.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ha=function(a){var b=new Fa(a.R,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};Fa.prototype.ue=function(){return this.R};Fa.prototype.hb=function(){this.Rc=!0};var Ia=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.vm=a;this.Yl=c===void 0?!1:c;this.debugInfo=[];this.D=b};sa(Ia,Error);var Ka=function(a){return a instanceof Ia?a:new Ia(a,void 0,!0)};function La(a,b){for(var c,d=k(b),e=d.next();!e.done&&!(c=Ma(a,e.value),c instanceof Ba);e=d.next());return c}function Ma(a,b){try{var c=k(b),d=c.next().value,e=ta(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ka(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Na=function(){this.J=new Ea;this.D=new Fa(this.J)};ba=Na.prototype;ba.ue=function(){return this.J};ba.execute=function(a){return this.Nj([a].concat(ua(ya.apply(1,arguments))))};ba.Nj=function(){for(var a,b=k(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ma(this.D,c.value);return a};ba.lo=function(a){var b=ya.apply(1,arguments),c=Ha(this.D);c.D=a;for(var d,e=k(b),f=e.next();!f.done;f=e.next())d=Ma(c,f.value);return d};ba.hb=function(){this.D.hb()};var Oa=function(){this.Da=!1;this.ba=new Ca};ba=Oa.prototype;ba.get=function(a){return this.ba.get(a)};ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.zc=function(){return this.ba.zc()};ba.Xb=function(){return this.ba.Xb()};ba.hb=function(){this.Da=!0};ba.Rc=function(){return this.Da};function Pa(){for(var a=Qa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Ra(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Qa,Ta;function Ua(a){Qa=Qa||Ra();Ta=Ta||Pa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Qa[m],Qa[n],Qa[p],Qa[q])}return b.join("")}
function Va(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ta[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Qa=Qa||Ra();Ta=Ta||Pa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Wa={};function Ya(a,b){Wa[a]=Wa[a]||[];Wa[a][b]=!0}function Za(){Wa.GTAG_EVENT_FEATURE_CHANNEL=$a}function ab(a){var b=Wa[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Ua(c.join("")).replace(/\.+$/,"")}function cb(){for(var a=[],b=Wa.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function db(){}function eb(a){return typeof a==="function"}function fb(a){return typeof a==="string"}function gb(a){return typeof a==="number"&&!isNaN(a)}function hb(a){return Array.isArray(a)?a:[a]}function jb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function kb(a,b){if(!gb(a)||!gb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function lb(a,b){for(var c=new mb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function nb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ob(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function pb(a){return Math.round(Number(a))||0}function qb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function rb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function sb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function tb(){return new Date(Date.now())}function ub(){return tb().getTime()}var mb=function(){this.prefix="gtm.";this.values={}};mb.prototype.set=function(a,b){this.values[this.prefix+a]=b};mb.prototype.get=function(a){return this.values[this.prefix+a]};mb.prototype.contains=function(a){return this.get(a)!==void 0};
function vb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function wb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function xb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function yb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function zb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Ab(a,b){var c=l;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Bb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Cb=/^\w{1,9}$/;function Db(a,b){a=a||{};b=b||",";var c=[];nb(a,function(d,e){Cb.test(d)&&e&&c.push(d)});return c.join(b)}function Eb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Fb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Gb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Hb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ib=globalThis.trustedTypes,Jb;function Kb(){var a=null;if(!Ib)return a;try{var b=function(c){return c};a=Ib.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Lb(){Jb===void 0&&(Jb=Kb());return Jb};var Mb=function(a){this.D=a};Mb.prototype.toString=function(){return this.D+""};function Nb(a){var b=a,c=Lb(),d=c?c.createScriptURL(b):b;return new Mb(d)}function Ob(a){if(a instanceof Mb)return a.D;throw Error("");};var Pb=wa([""]),Qb=va(["\x00"],["\\0"]),Rb=va(["\n"],["\\n"]),Sb=va(["\x00"],["\\u0000"]);function Tb(a){return a.toString().indexOf("`")===-1}Tb(function(a){return a(Pb)})||Tb(function(a){return a(Qb)})||Tb(function(a){return a(Rb)})||Tb(function(a){return a(Sb)});var Ub=function(a){this.D=a};Ub.prototype.toString=function(){return this.D};var Vb=function(a){this.Rp=a};function Wb(a){return new Vb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Xb=[Wb("data"),Wb("http"),Wb("https"),Wb("mailto"),Wb("ftp"),new Vb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Yb(a){var b;b=b===void 0?Xb:b;if(a instanceof Ub)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Vb&&d.Rp(a))return new Ub(a)}}var Zb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function $b(a){var b;if(a instanceof Ub)if(a instanceof Ub)b=a.D;else throw Error("");else b=Zb.test(a)?a:void 0;return b};function ac(a,b){var c=$b(b);c!==void 0&&(a.action=c)};function bc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var cc=function(a){this.D=a};cc.prototype.toString=function(){return this.D+""};var ec=function(){this.D=dc[0].toLowerCase()};ec.prototype.toString=function(){return this.D};function fc(a,b){var c=[new ec];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof ec)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var hc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function ic(a){return a===null?"null":a===void 0?"undefined":a};var l=window,jc=window.history,y=document,kc=navigator;function lc(){var a;try{a=kc.serviceWorker}catch(b){return}return a}var mc=y.currentScript,nc=mc&&mc.src;function oc(a,b){var c=l[a];l[a]=c===void 0?b:c;return l[a]}function pc(a){return(kc.userAgent||"").indexOf(a)!==-1}function qc(){return pc("Firefox")||pc("FxiOS")}function rc(){return(pc("GSA")||pc("GoogleApp"))&&(pc("iPhone")||pc("iPad"))}function sc(){return pc("Edg/")||pc("EdgA/")||pc("EdgiOS/")}
var tc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},uc={onload:1,src:1,width:1,height:1,style:1};function wc(a,b,c){b&&nb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function xc(a,b,c,d,e){var f=y.createElement("script");wc(f,d,tc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Nb(ic(a));f.src=Ob(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=y.getElementsByTagName("script")[0]||y.body||y.head;r.parentNode.insertBefore(f,r)}return f}
function yc(){if(nc){var a=nc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function zc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=y.createElement("iframe"),h=!0);wc(g,c,uc);d&&nb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=y.body&&y.body.lastChild||y.body||y.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Ac(a,b,c,d){return Bc(a,b,c,d)}function Cc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Dc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function A(a){l.setTimeout(a,0)}function Ec(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Fc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Gc(a){var b=y.createElement("div"),c=b,d,e=ic("A<div>"+a+"</div>"),f=Lb(),g=f?f.createHTML(e):e;d=new cc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof cc)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Hc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Ic(a,b,c){var d;try{d=kc.sendBeacon&&kc.sendBeacon(a)}catch(e){Ya("TAGGING",15)}d?b==null||b():Bc(a,b,c)}function Jc(a,b){try{return kc.sendBeacon(a,b)}catch(c){Ya("TAGGING",15)}return!1}var Kc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Lc(a,b,c,d,e){if(Mc()){var f=Object.assign({},Kc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=l.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ih)return e==null||e(),!1;if(b){var h=
Jc(a,b);h?d==null||d():e==null||e();return h}Nc(a,d,e);return!0}function Mc(){return typeof l.fetch==="function"}function Oc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Pc(){var a=l.performance;if(a&&eb(a.now))return a.now()}
function Qc(){var a,b=l.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Rc(){return l.performance||void 0}function Sc(){var a=l.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Bc=function(a,b,c,d){var e=new Image(1,1);wc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Nc=Ic;function Tc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Uc(a,b){return this.evaluate(a)===this.evaluate(b)}function Vc(a,b){return this.evaluate(a)||this.evaluate(b)}function Wc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Xc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Yc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=l.location.href;d instanceof Oa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Zc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,$c=function(a){if(a==null)return String(a);var b=Zc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},ad=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},bd=function(a){if(!a||$c(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!ad(a,"constructor")&&!ad(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
ad(a,b)},cd=function(a,b){var c=b||($c(a)=="array"?[]:{}),d;for(d in a)if(ad(a,d)){var e=a[d];$c(e)=="array"?($c(c[d])!="array"&&(c[d]=[]),c[d]=cd(e,c[d])):bd(e)?(bd(c[d])||(c[d]={}),c[d]=cd(e,c[d])):c[d]=e}return c};function dd(a){if(a==void 0||Array.isArray(a)||bd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ed(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var fd=function(a){a=a===void 0?[]:a;this.ba=new Ca;this.values=[];this.Da=!1;for(var b in a)a.hasOwnProperty(b)&&(ed(b)?this.values[Number(b)]=a[Number(b)]:this.ba.set(b,a[b]))};ba=fd.prototype;ba.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof fd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
ba.set=function(a,b){if(!this.Da)if(a==="length"){if(!ed(b))throw Ka(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ed(a)?this.values[Number(a)]=b:this.ba.set(a,b)};ba.get=function(a){return a==="length"?this.length():ed(a)?this.values[Number(a)]:this.ba.get(a)};ba.length=function(){return this.values.length};ba.Aa=function(){for(var a=this.ba.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
ba.zc=function(){for(var a=this.ba.zc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};ba.Xb=function(){for(var a=this.ba.Xb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};ba.remove=function(a){ed(a)?delete this.values[Number(a)]:this.Da||this.ba.remove(a)};ba.pop=function(){return this.values.pop()};ba.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};
ba.shift=function(){return this.values.shift()};ba.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new fd(this.values.splice(a)):new fd(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};ba.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};ba.has=function(a){return ed(a)&&this.values.hasOwnProperty(a)||this.ba.has(a)};ba.hb=function(){this.Da=!0;Object.freeze(this.values)};ba.Rc=function(){return this.Da};
function gd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var hd=function(a,b){this.functionName=a;this.te=b;this.ba=new Ca;this.Da=!1};ba=hd.prototype;ba.toString=function(){return this.functionName};ba.getName=function(){return this.functionName};ba.getKeys=function(){return new fd(this.Aa())};ba.invoke=function(a){return this.te.call.apply(this.te,[new id(this,a)].concat(ua(ya.apply(1,arguments))))};ba.Jb=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};ba.get=function(a){return this.ba.get(a)};
ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.zc=function(){return this.ba.zc()};ba.Xb=function(){return this.ba.Xb()};ba.hb=function(){this.Da=!0};ba.Rc=function(){return this.Da};var jd=function(a,b){hd.call(this,a,b)};sa(jd,hd);var kd=function(a,b){hd.call(this,a,b)};sa(kd,hd);var id=function(a,b){this.te=a;this.M=b};
id.prototype.evaluate=function(a){var b=this.M;return Array.isArray(a)?Ma(b,a):a};id.prototype.getName=function(){return this.te.getName()};id.prototype.ue=function(){return this.M.ue()};var ld=function(){this.map=new Map};ld.prototype.set=function(a,b){this.map.set(a,b)};ld.prototype.get=function(a){return this.map.get(a)};var md=function(){this.keys=[];this.values=[]};md.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};md.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function nd(){try{return Map?new ld:new md}catch(a){return new md}};var od=function(a){if(a instanceof od)return a;if(dd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};od.prototype.getValue=function(){return this.value};od.prototype.toString=function(){return String(this.value)};var qd=function(a){this.promise=a;this.Da=!1;this.ba=new Ca;this.ba.set("then",pd(this));this.ba.set("catch",pd(this,!0));this.ba.set("finally",pd(this,!1,!0))};ba=qd.prototype;ba.get=function(a){return this.ba.get(a)};ba.set=function(a,b){this.Da||this.ba.set(a,b)};ba.has=function(a){return this.ba.has(a)};ba.remove=function(a){this.Da||this.ba.remove(a)};ba.Aa=function(){return this.ba.Aa()};ba.zc=function(){return this.ba.zc()};ba.Xb=function(){return this.ba.Xb()};
var pd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new jd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof jd||(d=void 0);e instanceof jd||(e=void 0);var f=Ha(this.M),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new od(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new qd(h)})};qd.prototype.hb=function(){this.Da=!0};qd.prototype.Rc=function(){return this.Da};function rd(a,b,c){var d=nd(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof fd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof qd)return g.promise.then(function(u){return rd(u,b,1)},function(u){return Promise.reject(rd(u,b,1))});if(g instanceof Oa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof jd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=sd(u[w],b,c);var x=new Fa(b?b.ue():new Ea);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof od&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function sd(a,b,c){var d=nd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ob(g)){var m=new fd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(bd(g)){var p=new Oa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new jd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=rd(this.evaluate(u[w]),b,c);return f((0,this.M.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new od(g)};return f(a)};var td={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof fd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new fd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new fd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new fd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ka(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ka(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=gd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new fd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=gd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var ud={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},vd=new Ba("break"),wd=new Ba("continue");function xd(a,b){return this.evaluate(a)+this.evaluate(b)}function yd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof fd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ka(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=rd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ka(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(ud.hasOwnProperty(e)){var m=2;m=1;var n=rd(f,void 0,m);return sd(d[e].apply(d,n),this.M)}throw Ka(Error("TypeError: "+e+" is not a function"));}if(d instanceof fd){if(d.has(e)){var p=d.get(String(e));if(p instanceof jd){var q=gd(f);return p.invoke.apply(p,[this.M].concat(ua(q)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(td.supportedMethods.indexOf(e)>=
0){var r=gd(f);return td[e].call.apply(td[e],[d,this.M].concat(ua(r)))}}if(d instanceof jd||d instanceof Oa||d instanceof qd){if(d.has(e)){var t=d.get(e);if(t instanceof jd){var u=gd(f);return t.invoke.apply(t,[this.M].concat(ua(u)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof jd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof od&&e==="toString")return d.toString();throw Ka(Error("TypeError: Object has no '"+
e+"' property."));}function Ad(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.M;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Bd(){var a=ya.apply(0,arguments),b=Ha(this.M),c=La(b,a);if(c instanceof Ba)return c}function Cd(){return vd}function Dd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Ed(){for(var a=this.M,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ga(a,c,d,!0)}}}function Fd(){return wd}function Gd(a,b){return new Ba(a,this.evaluate(b))}function Hd(a,b){for(var c=ya.apply(2,arguments),d=new fd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.M.add(a,this.evaluate(g))}function Id(a,b){return this.evaluate(a)/this.evaluate(b)}
function Jd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof od,f=d instanceof od;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Kd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ld(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=La(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Md(a,b,c){if(typeof b==="string")return Ld(a,function(){return b.length},function(f){return f},c);if(b instanceof Oa||b instanceof qd||b instanceof fd||b instanceof jd){var d=b.Aa(),e=d.length;return Ld(a,function(){return e},function(f){return d[f]},c)}}function Nd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){g.set(d,h);return g},e,f)}
function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){var m=Ha(g);Ga(m,d,h,!0);return m},e,f)}function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){var m=Ha(g);m.add(d,h);return m},e,f)}function Qd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Rd(function(h){g.set(d,h);return g},e,f)}
function Sd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Rd(function(h){var m=Ha(g);Ga(m,d,h,!0);return m},e,f)}function Ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Rd(function(h){var m=Ha(g);m.add(d,h);return m},e,f)}
function Rd(a,b,c){if(typeof b==="string")return Ld(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof fd)return Ld(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ka(Error("The value is not iterable."));}
function Vd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof fd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.M,h=this.evaluate(d),m=Ha(g);for(e(g,m);Ma(m,b);){var n=La(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=Ha(g);e(m,p);Ma(p,c);m=p}}
function Wd(a,b){var c=ya.apply(2,arguments),d=this.M,e=this.evaluate(b);if(!(e instanceof fd))throw Error("Error: non-List value given for Fn argument names.");return new jd(a,function(){return function(){var f=ya.apply(0,arguments),g=Ha(d);g.D===void 0&&(g.D=this.M.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new fd(h));var r=La(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function Xd(a){var b=this.evaluate(a),c=this.M;if(Yd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Zd(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ka(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Oa||d instanceof qd||d instanceof fd||d instanceof jd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ed(e)&&(c=d[e]);else if(d instanceof od)return;return c}function $d(a,b){return this.evaluate(a)>this.evaluate(b)}function ae(a,b){return this.evaluate(a)>=this.evaluate(b)}
function be(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof od&&(c=c.getValue());d instanceof od&&(d=d.getValue());return c===d}function ce(a,b){return!be.call(this,a,b)}function de(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=La(this.M,d);if(e instanceof Ba)return e}var Yd=!1;
function ee(a,b){return this.evaluate(a)<this.evaluate(b)}function fe(a,b){return this.evaluate(a)<=this.evaluate(b)}function ge(){for(var a=new fd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function he(){for(var a=new Oa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ie(a,b){return this.evaluate(a)%this.evaluate(b)}
function je(a,b){return this.evaluate(a)*this.evaluate(b)}function ke(a){return-this.evaluate(a)}function le(a){return!this.evaluate(a)}function me(a,b){return!Jd.call(this,a,b)}function ne(){return null}function oe(a,b){return this.evaluate(a)||this.evaluate(b)}function pe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function qe(a){return this.evaluate(a)}function re(){return ya.apply(0,arguments)}function se(a){return new Ba("return",this.evaluate(a))}
function te(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ka(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof jd||d instanceof fd||d instanceof Oa)&&d.set(String(e),f);return f}function ue(a,b){return this.evaluate(a)-this.evaluate(b)}
function ve(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function we(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function xe(a){var b=this.evaluate(a);return b instanceof jd?"function":typeof b}function ye(){for(var a=this.M,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ae(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=La(this.M,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=La(this.M,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Be(a){return~Number(this.evaluate(a))}function Ce(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function De(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ee(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Fe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ge(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function He(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ie(){}
function Je(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Ia&&h.Yl))throw h;var e=Ha(this.M);a!==""&&(h instanceof Ia&&(h=h.vm),e.add(a,new od(h)));var f=this.evaluate(c),g=La(e,f);if(g instanceof Ba)return g}}function Ke(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ia&&f.Yl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Me=function(){this.D=new Na;Le(this)};Me.prototype.execute=function(a){return this.D.Nj(a)};var Le=function(a){var b=function(c,d){var e=new kd(String(c),d);e.hb();a.D.D.set(String(c),e)};b("map",he);b("and",Tc);b("contains",Wc);b("equals",Uc);b("or",Vc);b("startsWith",Xc);b("variable",Yc)};var Oe=function(){this.J=!1;this.D=new Na;Ne(this);this.J=!0};Oe.prototype.execute=function(a){return Pe(this.D.Nj(a))};var Qe=function(a,b,c){return Pe(a.D.lo(b,c))};Oe.prototype.hb=function(){this.D.hb()};
var Ne=function(a){var b=function(c,d){var e=String(c),f=new kd(e,d);f.hb();a.D.D.set(e,f)};b(0,xd);b(1,yd);b(2,zd);b(3,Ad);b(56,Fe);b(57,Ce);b(58,Be);b(59,He);b(60,De);b(61,Ee);b(62,Ge);b(53,Bd);b(4,Cd);b(5,Dd);b(68,Je);b(52,Ed);b(6,Fd);b(49,Gd);b(7,ge);b(8,he);b(9,Dd);b(50,Hd);b(10,Id);b(12,Jd);b(13,Kd);b(67,Ke);b(51,Wd);b(47,Nd);b(54,Od);b(55,Pd);b(63,Vd);b(64,Qd);b(65,Sd);b(66,Ud);b(15,Xd);b(16,Zd);b(17,Zd);b(18,$d);b(19,ae);b(20,be);b(21,ce);b(22,de);b(23,ee);b(24,fe);b(25,ie);b(26,je);b(27,
ke);b(28,le);b(29,me);b(45,ne);b(30,oe);b(32,pe);b(33,pe);b(34,qe);b(35,qe);b(46,re);b(36,se);b(43,te);b(37,ue);b(38,ve);b(39,we);b(40,xe);b(44,Ie);b(41,ye);b(42,Ae)};Oe.prototype.ue=function(){return this.D.ue()};function Pe(a){if(a instanceof Ba||a instanceof jd||a instanceof fd||a instanceof Oa||a instanceof qd||a instanceof od||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Re=function(a){this.message=a};function Se(a){a.Cr=!0;return a};var Te=Se(function(a){return typeof a==="string"});function Ue(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Re("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Ve(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var We=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Xe(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Ue(e)+c}a<<=2;d||(a|=32);return c=""+Ue(a|b)+c}
function Ye(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+Xe(1,1)+Ue(d<<2|e));var f=a.Xl,g="4"+c+(f?""+Xe(2,1)+Ue(f):""),h,m=a.Oj;h=m&&We.test(m)?""+Xe(3,2)+m:"";var n,p=a.Kj;n=p?""+Xe(4,1)+Ue(p):"";var q;var r=a.ctid;if(r&&b){var t=Xe(5,3),u=r.split("-"),v=u[0].toUpperCase();if(v!=="GTM"&&v!=="OPT")q="";else{var w=u[1];q=""+t+Ue(1+w.length)+(a.km||0)+w}}else q="";var x=a.uq,z=a.pe,B=a.Pa,C=a.Gr,F=g+h+n+q+(x?""+Xe(6,1)+Ue(x):"")+(z?""+Xe(7,3)+Ue(z.length)+z:"")+(B?""+Xe(8,3)+Ue(B.length)+
B:"")+(C?""+Xe(9,3)+Ue(C.length)+C:""),G;var I=a.Zl;I=I===void 0?{}:I;for(var L=[],V=k(Object.keys(I)),Q=V.next();!Q.done;Q=V.next()){var na=Q.value;L[Number(na)]=I[na]}if(L.length){var T=Xe(10,3),aa;if(L.length===0)aa=Ue(0);else{for(var Y=[],U=0,ka=!1,ja=0;ja<L.length;ja++){ka=!0;var la=ja%6;L[ja]&&(U|=1<<la);la===5&&(Y.push(Ue(U)),U=0,ka=!1)}ka&&Y.push(Ue(U));aa=Y.join("")}var Sa=aa;G=""+T+Ue(Sa.length)+Sa}else G="";var Xa=a.wm;return F+G+(Xa?""+Xe(11,3)+Ue(Xa.length)+Xa:"")};var Ze=function(){function a(b){return{toString:function(){return b}}}return{Vm:a("consent"),ek:a("convert_case_to"),fk:a("convert_false_to"),gk:a("convert_null_to"),hk:a("convert_true_to"),ik:a("convert_undefined_to"),Iq:a("debug_mode_metadata"),Ha:a("function"),Di:a("instance_name"),oo:a("live_only"),po:a("malware_disabled"),METADATA:a("metadata"),so:a("original_activity_id"),Zq:a("original_vendor_template_id"),Yq:a("once_on_load"),ro:a("once_per_event"),Bl:a("once_per_load"),er:a("priority_override"),
ir:a("respected_consent_types"),Kl:a("setup_tags"),sh:a("tag_id"),Pl:a("teardown_tags")}}();var vf;var wf=[],xf=[],yf=[],zf=[],Af=[],Bf,Cf,Df;function Ef(a){Df=Df||a}
function Ff(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)wf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)zf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)yf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Gf(p[r])}xf.push(p)}}
function Gf(a){}var Hf,If=[],Jf=[];function Kf(a,b){var c={};c[Ze.Ha]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Lf(a,b,c){try{return Cf(Mf(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Nf(a){var b=a[Ze.Ha];if(!b)throw Error("Error: No function name given for function call.");return!!Bf[b]}
var Mf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Of(a[e],b,c));return d},Of=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Of(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=wf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Ze.Di]);try{var m=Mf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Pf(m,{event:b,index:f,type:2,
name:h});Hf&&(d=Hf.No(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Of(a[n],b,c)]=Of(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Of(a[q],b,c);Df&&(p=p||Df.Op(r));d.push(r)}return Df&&p?Df.So(d):d.join("");case "escape":d=Of(a[1],b,c);if(Df&&Array.isArray(a[1])&&a[1][0]==="macro"&&Df.Pp(a))return Df.cq(d);d=String(d);for(var t=2;t<a.length;t++)ff[a[t]]&&(d=ff[a[t]](d));return d;
case "tag":var u=a[1];if(!zf[u])throw Error("Unable to resolve tag reference "+u+".");return{hm:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Ze.Ha]=a[1];var w=Lf(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Pf=function(a,b){var c=a[Ze.Ha],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Bf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&If.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&zb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=wf[q];break;case 1:r=zf[q];break;default:n="";break a}var t=r&&r[Ze.Di];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Jf.indexOf(c)===-1){Jf.push(c);
var x=ub();u=e(g);var z=ub()-x,B=ub();v=vf(c,h,b);w=z-(ub()-B)}else if(e&&(u=e(g)),!e||f)v=vf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),dd(u)?(Array.isArray(u)?Array.isArray(v):bd(u)?bd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Qf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Qf,Error);Qf.prototype.getMessage=function(){return this.message};function Rf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Rf(a[c],b[c])}};function Sf(){return function(a,b){var c;var d=Tf;a instanceof Ia?(a.D=d,c=a):c=new Ia(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Tf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)gb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Uf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Vf(a),f=0;f<xf.length;f++){var g=xf[f],h=Wf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<zf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Wf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Vf(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Lf(yf[c],a));return b[c]}};function Xf(a,b){b[Ze.ek]&&typeof a==="string"&&(a=b[Ze.ek]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Ze.gk)&&a===null&&(a=b[Ze.gk]);b.hasOwnProperty(Ze.ik)&&a===void 0&&(a=b[Ze.ik]);b.hasOwnProperty(Ze.hk)&&a===!0&&(a=b[Ze.hk]);b.hasOwnProperty(Ze.fk)&&a===!1&&(a=b[Ze.fk]);return a};var Yf=function(){this.D={}},$f=function(a,b){var c=Zf.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function ag(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Qf(c,d,g);}}
function bg(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));ag(e,b,d,g);ag(f,b,d,g)}}}};var fg=function(){var a=data.permissions||{},b=cg.ctid,c=this;this.J={};this.D=new Yf;var d={},e={},f=bg(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});nb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw dg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};nb(h,function(p,q){var r=eg(p,q);n[p]=r.assert;d[p]||(d[p]=r.U);r.Vl&&!e[p]&&(e[p]=r.Vl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw dg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},gg=function(a){return Zf.J[a]||function(){}};
function eg(a,b){var c=Kf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=dg;try{return Pf(c)}catch(d){return{assert:function(e){throw new Qf(e,{},"Permission "+e+" is unknown.");},U:function(){throw new Qf(a,{},"Permission "+a+" is unknown.");}}}}function dg(a,b,c){return new Qf(a,b,c)};var hg=!1;var ig={};ig.Nm=qb('');ig.bp=qb('');function ng(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var og=[],pg={};function qg(a){return og[a]===void 0?!1:og[a]};var rg=[];function sg(a){switch(a){case 1:return 0;case 38:return 13;case 50:return 10;case 51:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 14;case 197:return 15;case 114:return 12;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function tg(a,b){rg[a]=b;var c=sg(a);c!==void 0&&(og[c]=b)}function D(a){tg(a,!0)}
D(39);D(34);D(35);D(36);
D(56);D(145);D(153);D(144);D(120);
D(58);D(5);D(111);
D(139);D(87);D(92);D(117);
D(159);D(132);D(20);
D(72);D(113);D(154);
D(116);tg(23,!1),D(24);pg[1]=ng('1',6E4);pg[3]=ng('10',1);
pg[2]=ng('',50);D(29);ug(26,25);
D(9);D(91);
D(123);D(157);
D(158);D(71);D(136);D(127);D(27);D(69);D(135);
D(51);D(50);D(95);D(86);
D(38);D(103);D(112);D(63);
D(152);
D(101);
D(122);D(121);
D(108);D(134);
D(115);D(96);D(31);
D(22);D(97);D(19);D(12);
D(28);
D(90);
D(59);D(13);
D(163);D(167);
D(166);D(175);D(176);D(180);D(182);
D(185);
D(187);
D(192);function E(a){return!!rg[a]}
function ug(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?D(b):D(a)};var wg={},xg=(wg.uaa=!0,wg.uab=!0,wg.uafvl=!0,wg.uamb=!0,wg.uam=!0,wg.uap=!0,wg.uapv=!0,wg.uaw=!0,wg);
var Fg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Dg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Eg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?zb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Eg=/^[a-z$_][\w-$]*$/i,Dg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Gg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Hg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Ig(a,b){return String(a).split(",").indexOf(String(b))>=0}var Jg=new mb;function Kg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Jg.get(e);f||(f=new RegExp(b,d),Jg.set(e,f));return f.test(a)}catch(g){return!1}}function Lg(a,b){return String(a).indexOf(String(b))>=0}
function Mg(a,b){return String(a)===String(b)}function Ng(a,b){return Number(a)>=Number(b)}function Og(a,b){return Number(a)<=Number(b)}function Pg(a,b){return Number(a)>Number(b)}function Qg(a,b){return Number(a)<Number(b)}function Rg(a,b){return zb(String(a),String(b))};var Yg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Zg={Fn:"function",PixieMap:"Object",List:"Array"};
function $g(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Yg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof jd?n="Fn":m instanceof fd?n="List":m instanceof Oa?n="PixieMap":m instanceof qd?n="PixiePromise":m instanceof od&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Zg[n]||n)+", which does not match required type ")+
((Zg[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=k(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof jd?d.push("function"):g instanceof fd?d.push("Array"):g instanceof Oa?d.push("Object"):g instanceof qd?d.push("Promise"):g instanceof od?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ah(a){return a instanceof Oa}function bh(a){return ah(a)||a===null||ch(a)}
function dh(a){return a instanceof jd}function eh(a){return dh(a)||a===null||ch(a)}function fh(a){return a instanceof fd}function gh(a){return a instanceof od}function hh(a){return typeof a==="string"}function ih(a){return hh(a)||a===null||ch(a)}function jh(a){return typeof a==="boolean"}function kh(a){return jh(a)||ch(a)}function lh(a){return jh(a)||a===null||ch(a)}function mh(a){return typeof a==="number"}function ch(a){return a===void 0};function nh(a){return""+a}
function oh(a,b){var c=[];return c};function ph(a,b){var c=new jd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ka(g);}});c.hb();return c}
function qh(a,b){var c=new Oa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];eb(e)?c.set(d,ph(a+"_"+d,e)):bd(e)?c.set(d,qh(a+"_"+d,e)):(gb(e)||fb(e)||typeof e==="boolean")&&c.set(d,e)}c.hb();return c};function rh(a,b){if(!hh(a))throw H(this.getName(),["string"],arguments);if(!ih(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Oa;return d=qh("AssertApiSubject",
c)};function sh(a,b){if(!ih(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof qd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Oa;return d=qh("AssertThatSubject",c)};function th(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.M,e=0;e<b.length;++e)c.push(rd(b[e],d));return sd(a.apply(null,c))}}function uh(){for(var a=Math,b=vh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=th(a[e].bind(a)))}return c};function wh(a){return a!=null&&zb(a,"__cvt_")};function xh(a){var b;return b};function yh(a){var b;if(!hh(a))throw H(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function zh(a){try{return encodeURI(a)}catch(b){}};function Ah(a){try{return encodeURIComponent(String(a))}catch(b){}};function Fh(a){if(!ih(a))throw H(this.getName(),["string|undefined"],arguments);};function Gh(a,b){if(!mh(a)||!mh(b))throw H(this.getName(),["number","number"],arguments);return kb(a,b)};function Hh(){return(new Date).getTime()};function Ih(a){if(a===null)return"null";if(a instanceof fd)return"array";if(a instanceof jd)return"function";if(a instanceof od){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Jh(a){function b(c){return function(d){try{return c(d)}catch(e){(hg||ig.Nm)&&a.call(this,e.message)}}}return{parse:b(function(c){return sd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(rd(c))}),publicName:"JSON"}};function Kh(a){return pb(rd(a,this.M))};function Lh(a){return Number(rd(a,this.M))};function Mh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Nh(a,b,c){var d=null,e=!1;if(!fh(a)||!hh(b)||!hh(c))throw H(this.getName(),["Array","string","string"],arguments);d=new Oa;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof Oa&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var vh="floor ceil round max min abs pow sqrt".split(" ");function Oh(){var a={};return{qp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Hm:function(b,c){a[b]=c},reset:function(){a={}}}}function Ph(a,b){return function(){return jd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Qh(a,b){if(!hh(a))throw H(this.getName(),["string","any"],arguments);}
function Rh(a,b){if(!hh(a)||!ah(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Sh={};var Th=function(a){var b=new Oa;if(a instanceof fd)for(var c=a.Aa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof jd)for(var f=a.Aa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Sh.keys=function(a){$g(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof qd)return new fd(a.Aa());return new fd};
Sh.values=function(a){$g(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof qd)return new fd(a.zc());return new fd};
Sh.entries=function(a){$g(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Th(a);if(a instanceof Oa||a instanceof qd)return new fd(a.Xb().map(function(b){return new fd(b)}));return new fd};
Sh.freeze=function(a){(a instanceof Oa||a instanceof qd||a instanceof fd||a instanceof jd)&&a.hb();return a};Sh.delete=function(a,b){if(a instanceof Oa&&!a.Rc())return a.remove(b),!0;return!1};function J(a,b){var c=ya.apply(2,arguments),d=a.M.D;if(!d)throw Error("Missing program state.");if(d.iq){try{d.Wl.apply(null,[b].concat(ua(c)))}catch(e){throw Ya("TAGGING",21),e;}return}d.Wl.apply(null,[b].concat(ua(c)))};var Uh=function(){this.J={};this.D={};this.O=!0;};Uh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Uh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Uh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:eb(b)?ph(a,b):qh(a,b)};function Vh(a,b){var c=void 0;return c};function Wh(){var a={};
return a};var K={m:{Oa:"ad_personalization",V:"ad_storage",W:"ad_user_data",ja:"analytics_storage",bc:"region",ka:"consent_updated",ug:"wait_for_update",jn:"app_remove",kn:"app_store_refund",ln:"app_store_subscription_cancel",mn:"app_store_subscription_convert",nn:"app_store_subscription_renew",on:"consent_update",mk:"add_payment_info",nk:"add_shipping_info",Nd:"add_to_cart",Od:"remove_from_cart",pk:"view_cart",Wc:"begin_checkout",Pd:"select_item",hc:"view_item_list",Gc:"select_promotion",jc:"view_promotion",
nb:"purchase",Qd:"refund",wb:"view_item",qk:"add_to_wishlist",pn:"exception",qn:"first_open",rn:"first_visit",ra:"gtag.config",Cb:"gtag.get",sn:"in_app_purchase",Xc:"page_view",tn:"screen_view",un:"session_start",vn:"source_update",wn:"timing_complete",xn:"track_social",Rd:"user_engagement",yn:"user_id_update",Fe:"gclid_link_decoration_source",Ge:"gclid_storage_source",kc:"gclgb",ob:"gclid",rk:"gclid_len",Sd:"gclgs",Td:"gcllp",Ud:"gclst",za:"ads_data_redaction",He:"gad_source",Ie:"gad_source_src",
Yc:"gclid_url",sk:"gclsrc",Je:"gbraid",Vd:"wbraid",Fa:"allow_ad_personalization_signals",Bg:"allow_custom_scripts",Ke:"allow_direct_google_requests",Cg:"allow_display_features",Dg:"allow_enhanced_conversions",Lb:"allow_google_signals",pb:"allow_interest_groups",zn:"app_id",An:"app_installer_id",Bn:"app_name",Cn:"app_version",Mb:"auid",Dn:"auto_detection_enabled",Zc:"aw_remarketing",Th:"aw_remarketing_only",Eg:"discount",Fg:"aw_feed_country",Gg:"aw_feed_language",wa:"items",Hg:"aw_merchant_id",tk:"aw_basket_type",
Le:"campaign_content",Me:"campaign_id",Ne:"campaign_medium",Oe:"campaign_name",Pe:"campaign",Qe:"campaign_source",Re:"campaign_term",Nb:"client_id",uk:"rnd",Uh:"consent_update_type",En:"content_group",Gn:"content_type",Ob:"conversion_cookie_prefix",Se:"conversion_id",Ra:"conversion_linker",Vh:"conversion_linker_disabled",bd:"conversion_api",Ig:"cookie_deprecation",qb:"cookie_domain",rb:"cookie_expires",xb:"cookie_flags",dd:"cookie_name",Pb:"cookie_path",kb:"cookie_prefix",Hc:"cookie_update",Wd:"country",
Wa:"currency",Wh:"customer_buyer_stage",Te:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",Ue:"custom_map",Zh:"gcldc",ed:"dclid",vk:"debug_mode",qa:"developer_id",Hn:"disable_merchant_reported_purchases",fd:"dc_custom_params",In:"dc_natural_search",wk:"dynamic_event_settings",xk:"affiliation",Jg:"checkout_option",ai:"checkout_step",yk:"coupon",Ve:"item_list_name",bi:"list_name",Jn:"promotions",We:"shipping",di:"tax",Kg:"engagement_time_msec",Lg:"enhanced_client_id",Mg:"enhanced_conversions",
zk:"enhanced_conversions_automatic_settings",Ng:"estimated_delivery_date",ei:"euid_logged_in_state",Xe:"event_callback",Kn:"event_category",Qb:"event_developer_id_string",Ln:"event_label",gd:"event",Og:"event_settings",Pg:"event_timeout",Mn:"description",Nn:"fatal",On:"experiments",fi:"firebase_id",Xd:"first_party_collection",Qg:"_x_20",nc:"_x_19",Ak:"fledge_drop_reason",Bk:"fledge",Ck:"flight_error_code",Dk:"flight_error_message",Ek:"fl_activity_category",Fk:"fl_activity_group",gi:"fl_advertiser_id",
Gk:"fl_ar_dedupe",Ye:"match_id",Hk:"fl_random_number",Ik:"tran",Jk:"u",Rg:"gac_gclid",Yd:"gac_wbraid",Kk:"gac_wbraid_multiple_conversions",Lk:"ga_restrict_domain",hi:"ga_temp_client_id",Pn:"ga_temp_ecid",hd:"gdpr_applies",Mk:"geo_granularity",Ic:"value_callback",oc:"value_key",qc:"google_analysis_params",Zd:"_google_ng",ae:"google_signals",Nk:"google_tld",Ze:"gpp_sid",af:"gpp_string",Sg:"groups",Ok:"gsa_experiment_id",bf:"gtag_event_feature_usage",Pk:"gtm_up",Jc:"iframe_state",cf:"ignore_referrer",
ii:"internal_traffic_results",Qk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Tg:"is_passthrough",jd:"_lps",yb:"language",Ug:"legacy_developer_id_string",Sa:"linker",be:"accept_incoming",rc:"decorate_forms",na:"domains",Mc:"url_position",Vg:"merchant_feed_label",Wg:"merchant_feed_language",Xg:"merchant_id",Rk:"method",Qn:"name",Sk:"navigation_type",df:"new_customer",Yg:"non_interaction",Rn:"optimize_id",Tk:"page_hostname",ef:"page_path",Xa:"page_referrer",Db:"page_title",Uk:"passengers",
Vk:"phone_conversion_callback",Sn:"phone_conversion_country_code",Wk:"phone_conversion_css_class",Tn:"phone_conversion_ids",Xk:"phone_conversion_number",Yk:"phone_conversion_options",Un:"_platinum_request_status",Vn:"_protected_audience_enabled",ff:"quantity",Zg:"redact_device_info",ji:"referral_exclusion_definition",Lq:"_request_start_time",Sb:"restricted_data_processing",Wn:"retoken",Xn:"sample_rate",ki:"screen_name",Nc:"screen_resolution",Zk:"_script_source",Yn:"search_term",sb:"send_page_view",
kd:"send_to",ld:"server_container_url",hf:"session_duration",ah:"session_engaged",li:"session_engaged_time",sc:"session_id",bh:"session_number",jf:"_shared_user_id",kf:"delivery_postal_code",Mq:"_tag_firing_delay",Nq:"_tag_firing_time",Oq:"temporary_client_id",mi:"_timezone",ni:"topmost_url",Zn:"tracking_id",oi:"traffic_type",Ya:"transaction_id",uc:"transport_url",al:"trip_type",nd:"update",Eb:"url_passthrough",bl:"uptgs",lf:"_user_agent_architecture",nf:"_user_agent_bitness",pf:"_user_agent_full_version_list",
qf:"_user_agent_mobile",rf:"_user_agent_model",tf:"_user_agent_platform",uf:"_user_agent_platform_version",vf:"_user_agent_wow64",Za:"user_data",ri:"user_data_auto_latency",si:"user_data_auto_meta",ui:"user_data_auto_multi",wi:"user_data_auto_selectors",xi:"user_data_auto_status",Tb:"user_data_mode",eh:"user_data_settings",Ta:"user_id",Ub:"user_properties",fl:"_user_region",wf:"us_privacy_string",Ga:"value",il:"wbraid_multiple_conversions",rd:"_fpm_parameters",Bi:"_host_name",sl:"_in_page_command",
tl:"_ip_override",xl:"_is_passthrough_cid",vc:"non_personalized_ads",Ni:"_sst_parameters",mc:"conversion_label",Ba:"page_location",Rb:"global_developer_id_string",md:"tc_privacy_string"}};var Xh={},Yh=(Xh[K.m.ka]="gcu",Xh[K.m.kc]="gclgb",Xh[K.m.ob]="gclaw",Xh[K.m.rk]="gclid_len",Xh[K.m.Sd]="gclgs",Xh[K.m.Td]="gcllp",Xh[K.m.Ud]="gclst",Xh[K.m.Mb]="auid",Xh[K.m.Eg]="dscnt",Xh[K.m.Fg]="fcntr",Xh[K.m.Gg]="flng",Xh[K.m.Hg]="mid",Xh[K.m.tk]="bttype",Xh[K.m.Nb]="gacid",Xh[K.m.mc]="label",Xh[K.m.bd]="capi",Xh[K.m.Ig]="pscdl",Xh[K.m.Wa]="currency_code",Xh[K.m.Wh]="clobs",Xh[K.m.Te]="vdltv",Xh[K.m.Xh]="clolo",Xh[K.m.Yh]="clolb",Xh[K.m.vk]="_dbg",Xh[K.m.Ng]="oedeld",Xh[K.m.Qb]="edid",Xh[K.m.Ak]=
"fdr",Xh[K.m.Bk]="fledge",Xh[K.m.Rg]="gac",Xh[K.m.Yd]="gacgb",Xh[K.m.Kk]="gacmcov",Xh[K.m.hd]="gdpr",Xh[K.m.Rb]="gdid",Xh[K.m.Zd]="_ng",Xh[K.m.Ze]="gpp_sid",Xh[K.m.af]="gpp",Xh[K.m.Ok]="gsaexp",Xh[K.m.bf]="_tu",Xh[K.m.Jc]="frm",Xh[K.m.Tg]="gtm_up",Xh[K.m.jd]="lps",Xh[K.m.Ug]="did",Xh[K.m.Vg]="fcntr",Xh[K.m.Wg]="flng",Xh[K.m.Xg]="mid",Xh[K.m.df]=void 0,Xh[K.m.Db]="tiba",Xh[K.m.Sb]="rdp",Xh[K.m.sc]="ecsid",Xh[K.m.jf]="ga_uid",Xh[K.m.kf]="delopc",Xh[K.m.md]="gdpr_consent",Xh[K.m.Ya]="oid",Xh[K.m.bl]=
"uptgs",Xh[K.m.lf]="uaa",Xh[K.m.nf]="uab",Xh[K.m.pf]="uafvl",Xh[K.m.qf]="uamb",Xh[K.m.rf]="uam",Xh[K.m.tf]="uap",Xh[K.m.uf]="uapv",Xh[K.m.vf]="uaw",Xh[K.m.ri]="ec_lat",Xh[K.m.si]="ec_meta",Xh[K.m.ui]="ec_m",Xh[K.m.wi]="ec_sel",Xh[K.m.xi]="ec_s",Xh[K.m.Tb]="ec_mode",Xh[K.m.Ta]="userId",Xh[K.m.wf]="us_privacy",Xh[K.m.Ga]="value",Xh[K.m.il]="mcov",Xh[K.m.Bi]="hn",Xh[K.m.sl]="gtm_ee",Xh[K.m.vc]="npa",Xh[K.m.Se]=null,Xh[K.m.Nc]=null,Xh[K.m.yb]=null,Xh[K.m.wa]=null,Xh[K.m.Ba]=null,Xh[K.m.Xa]=null,Xh[K.m.ni]=
null,Xh[K.m.rd]=null,Xh[K.m.Fe]=null,Xh[K.m.Ge]=null,Xh[K.m.qc]=null,Xh);function Zh(a,b){if(a){var c=a.split("x");c.length===2&&($h(b,"u_w",c[0]),$h(b,"u_h",c[1]))}}
function ai(a){var b=bi;b=b===void 0?ci:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(di(q.value)),r.push(di(q.quantity)),r.push(di(q.item_id)),r.push(di(q.start_date)),r.push(di(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ci(a){return ei(a.item_id,a.id,a.item_name)}function ei(){for(var a=k(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function fi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function $h(a,b,c){c===void 0||c===null||c===""&&!xg[b]||(a[b]=c)}function di(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var gi={},ii={lq:hi};function ji(a,b){var c=gi[b],d=c.Jm;if(!(gi[b].active||gi[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;ii.lq(a,b)}}function hi(a,b){var c=gi[b];if(!(kb(0,9999)<c.percent*2*100))return a;ki(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function ki(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=kb(0,1)===0,e=kb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var M={K:{Wj:"call_conversion",X:"conversion",ao:"floodlight",yf:"ga_conversion",Ji:"landing_page",Ia:"page_view",oa:"remarketing",Va:"user_data_lead",La:"user_data_web"}};function ni(a){return oi?y.querySelectorAll(a):null}
function pi(a,b){if(!oi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!y.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var qi=!1;
if(y.querySelectorAll)try{var ri=y.querySelectorAll(":root");ri&&ri.length==1&&ri[0]==y.documentElement&&(qi=!0)}catch(a){}var oi=qi;function si(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function ti(){this.blockSize=-1};function ui(a,b){this.blockSize=-1;this.blockSize=64;this.O=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.J=0;this.D=[];this.ia=a;this.T=b;this.la=za.Int32Array?new Int32Array(64):Array(64);vi===void 0&&(za.Int32Array?vi=new Int32Array(wi):vi=wi);this.reset()}Aa(ui,ti);for(var xi=[],yi=0;yi<63;yi++)xi[yi]=0;var zi=[].concat(128,xi);
ui.prototype.reset=function(){this.R=this.J=0;var a;if(za.Int32Array)a=new Int32Array(this.T);else{var b=this.T,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.D=a};
var Ai=function(a){for(var b=a.O,c=a.la,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.D[0]|0,n=a.D[1]|0,p=a.D[2]|0,q=a.D[3]|0,r=a.D[4]|0,t=a.D[5]|0,u=a.D[6]|0,v=a.D[7]|0,w=0;w<64;w++){var x=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(vi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+x|0}a.D[0]=a.D[0]+m|0;a.D[1]=a.D[1]+n|0;a.D[2]=a.D[2]+p|0;a.D[3]=a.D[3]+q|0;a.D[4]=a.D[4]+r|0;a.D[5]=a.D[5]+t|0;a.D[6]=a.D[6]+u|0;a.D[7]=a.D[7]+v|0};
ui.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.J;if(typeof a==="string")for(;c<b;)this.O[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ai(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.O[d++]=g;d==this.blockSize&&(Ai(this),d=0)}else throw Error("message must be string or array");
}this.J=d;this.R+=b};ui.prototype.digest=function(){var a=[],b=this.R*8;this.J<56?this.update(zi,56-this.J):this.update(zi,this.blockSize-(this.J-56));for(var c=63;c>=56;c--)this.O[c]=b&255,b/=256;Ai(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.D[e]>>f&255;return a};
var wi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],vi;function Bi(){ui.call(this,8,Ci)}Aa(Bi,ui);var Ci=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Di=/^[0-9A-Fa-f]{64}$/;function Ei(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Fi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=l.crypto)==null?0:b.subtle){if(Di.test(a))return Promise.resolve(a);try{var c=Ei(a);return l.crypto.subtle.digest("SHA-256",c).then(function(d){return Gi(d,l)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Gi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Hi=[],Ii;function Ji(a){Ii?Ii(a):Hi.push(a)}function Ki(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Ji(a),b):c}function Li(a,b){if(!E(190))return b;var c=Mi(a,"");return c!==b?(Ji(a),b):c}function Mi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ni(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Ji(a),b)}function Oi(){Ii=Pi;for(var a=k(Hi),b=a.next();!b.done;b=a.next())Ii(b.value);Hi.length=0};var Qi={fn:'100',gn:'10',hn:'1000',fo:'US-CO',ho:'US-CO',Bo:Li(44,'101509157~103116026~103200004~103233427~103351869~103351871~104617979~104617981~104661466~104661468~104684204~104684207~104718208~104736445~104736447')},Ri={Xo:Number(Qi.fn)||0,Yo:Number(Qi.gn)||0,ap:Number(Qi.hn)||0,vp:Qi.fo.split("~"),wp:Qi.ho.split("~"),Fq:Qi.Bo};Object.assign({},Ri);function N(a){Ya("GTM",a)};var Cj={},Dj=(Cj[K.m.pb]=1,Cj[K.m.ld]=2,Cj[K.m.uc]=2,Cj[K.m.za]=3,Cj[K.m.Te]=4,Cj[K.m.Bg]=5,Cj[K.m.Hc]=6,Cj[K.m.kb]=6,Cj[K.m.qb]=6,Cj[K.m.dd]=6,Cj[K.m.Pb]=6,Cj[K.m.xb]=6,Cj[K.m.rb]=7,Cj[K.m.Sb]=9,Cj[K.m.Cg]=10,Cj[K.m.Lb]=11,Cj),Ej={},Fj=(Ej.unknown=13,Ej.standard=14,Ej.unique=15,Ej.per_session=16,Ej.transactions=17,Ej.items_sold=18,Ej);var $a=[];function Gj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=k(Object.keys(Dj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Dj[f],h=b;h=h===void 0?!1:h;Ya("GTAG_EVENT_FEATURE_CHANNEL",g);h&&($a[g]=!0)}}};var Hj=function(){this.D=new Set;this.J=new Set},Jj=function(a){var b=Ij.ia;a=a===void 0?[]:a;var c=[].concat(ua(b.D)).concat([].concat(ua(b.J))).concat(a);c.sort(function(d,e){return d-e});return c},Kj=function(){var a=[].concat(ua(Ij.ia.D));a.sort(function(b,c){return b-c});return a},Lj=function(){var a=Ij.ia,b=Ri.Fq;a.D=new Set;if(b!=="")for(var c=k(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var Mj={},Nj=Li(14,"56b1"),Oj=Ni(15,Number("0")),Pj=Li(19,"dataLayer");Li(20,"");Li(16,"ChAI8Lu0wgYQ5q+bqpbvpKR4EiUAG1C8TcZnUTL7kYy6Cx9xPxdU78JSjrY1AvfHawqn56zwEtb0GgKYug\x3d\x3d");var Qj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Rj={__paused:1,__tg:1},Sj;for(Sj in Qj)Qj.hasOwnProperty(Sj)&&(Rj[Sj]=1);var Tj=Ki(11,qb("")),Uj=!1,Vj,Wj=!1;
Vj=Wj;var Xj,Yj=!1;Xj=Yj;Mj.Ag=Li(3,"www.googletagmanager.com");var Zj=""+Mj.Ag+(Vj?"/gtag/js":"/gtm.js"),ak=null,bk=null,ck={},dk={};Mj.Wm=Ki(2,qb(""));var ek="";Mj.Oi=ek;
var Ij=new function(){this.ia=new Hj;this.D=this.O=!1;this.J=0;this.Ca=this.ab=this.Gb=this.T="";this.la=this.R=!1};function fk(){var a;a=a===void 0?[]:a;return Jj(a).join("~")}function gk(){var a=Ij.T.length;return Ij.T[a-1]==="/"?Ij.T.substring(0,a-1):Ij.T}function hk(){return Ij.D?E(84)?Ij.J===0:Ij.J!==1:!1}function ik(a){for(var b={},c=k(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var jk=new mb,kk={},lk={},ok={name:Pj,set:function(a,b){cd(Bb(a,b),kk);mk()},get:function(a){return nk(a,2)},reset:function(){jk=new mb;kk={};mk()}};function nk(a,b){return b!=2?jk.get(a):pk(a)}function pk(a,b){var c=a.split(".");b=b||[];for(var d=kk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function qk(a,b){lk.hasOwnProperty(a)||(jk.set(a,b),cd(Bb(a,b),kk),mk())}
function rk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=nk(c,1);if(Array.isArray(d)||bd(d))d=cd(d,null);lk[c]=d}}function mk(a){nb(lk,function(b,c){jk.set(b,c);cd(Bb(b),kk);cd(Bb(b,c),kk);a&&delete lk[b]})}function sk(a,b){var c,d=(b===void 0?2:b)!==1?pk(a):jk.get(a);$c(d)==="array"||$c(d)==="object"?c=cd(d,null):c=d;return c};var Dk=/:[0-9]+$/,Ek=/^\d+\.fls\.doubleclick\.net$/;function Fk(a,b,c,d){var e=Gk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Gk(a,b,c){for(var d={},e=k(a.split("&")),f=e.next();!f.done;f=e.next()){var g=k(f.value.split("=")),h=g.next().value,m=ta(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Hk(a){try{return decodeURIComponent(a)}catch(b){}}function Ik(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Jk(a.protocol)||Jk(l.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:l.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||l.location.hostname).replace(Dk,"").toLowerCase());return Kk(a,b,c,d,e)}
function Kk(a,b,c,d,e){var f,g=Jk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Lk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Dk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Ya("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Fk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Jk(a){return a?a.replace(":","").toLowerCase():""}function Lk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Mk={},Nk=0;
function Ok(a){var b=Mk[a];if(!b){var c=y.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Ya("TAGGING",1),d="/"+d);var e=c.hostname.replace(Dk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Nk<5&&(Mk[a]=b,Nk++)}return b}function Pk(a,b,c){var d=Ok(a);return Gb(b,d,c)}
function Qk(a){var b=Ok(l.location.href),c=Ik(b,"host",!1);if(c&&c.match(Ek)){var d=Ik(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Rk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Sk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Tk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Ok(""+c+b).href}}function Uk(a,b){if(hk()||Ij.O)return Tk(a,b)}
function Vk(){return!!Mj.Oi&&Mj.Oi.split("@@").join("")!=="SGTM_TOKEN"}function Wk(a){for(var b=k([K.m.ld,K.m.uc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function Xk(a,b,c){c=c===void 0?"":c;if(!hk())return a;var d=b?Rk[a]||"":"";d==="/gs"&&(c="");return""+gk()+d+c}function Yk(a){if(!hk())return a;for(var b=k(Sk),c=b.next();!c.done;c=b.next())if(zb(a,""+gk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function Zk(a){var b=String(a[Ze.Ha]||"").replace(/_/g,"");return zb(b,"cvt")?"cvt":b}var $k=l.location.search.indexOf("?gtm_latency=")>=0||l.location.search.indexOf("&gtm_latency=")>=0;var al={jq:Ni(27,Number("0.005000")),Sm:"",Eq:"0.01",Vo:Ni(42,Number("0.010000"))};function bl(){var a=al.jq;return Number(a)}
var cl=Math.random(),dl=$k||cl<bl(),el,fl=bl()===1||(nc==null?void 0:nc.includes("gtm_debug=d"))||$k;el=E(163)?$k||cl>=1-Number(al.Vo):fl||cl>=1-Number(al.Eq);var gl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},hl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var il,jl;a:{for(var kl=["CLOSURE_FLAGS"],ml=za,nl=0;nl<kl.length;nl++)if(ml=ml[kl[nl]],ml==null){jl=null;break a}jl=ml}var ol=jl&&jl[610401301];il=ol!=null?ol:!1;function pl(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var ql,rl=za.navigator;ql=rl?rl.userAgentData||null:null;function sl(a){if(!il||!ql)return!1;for(var b=0;b<ql.brands.length;b++){var c=ql.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function tl(a){return pl().indexOf(a)!=-1};function ul(){return il?!!ql&&ql.brands.length>0:!1}function vl(){return ul()?!1:tl("Opera")}function wl(){return tl("Firefox")||tl("FxiOS")}function xl(){return ul()?sl("Chromium"):(tl("Chrome")||tl("CriOS"))&&!(ul()?0:tl("Edge"))||tl("Silk")};var yl=function(a){yl[" "](a);return a};yl[" "]=function(){};var zl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Al(){return il?!!ql&&!!ql.platform:!1}function Bl(){return tl("iPhone")&&!tl("iPod")&&!tl("iPad")}function Cl(){Bl()||tl("iPad")||tl("iPod")};vl();ul()||tl("Trident")||tl("MSIE");tl("Edge");!tl("Gecko")||pl().toLowerCase().indexOf("webkit")!=-1&&!tl("Edge")||tl("Trident")||tl("MSIE")||tl("Edge");pl().toLowerCase().indexOf("webkit")!=-1&&!tl("Edge")&&tl("Mobile");Al()||tl("Macintosh");Al()||tl("Windows");(Al()?ql.platform==="Linux":tl("Linux"))||Al()||tl("CrOS");Al()||tl("Android");Bl();tl("iPad");tl("iPod");Cl();pl().toLowerCase().indexOf("kaios");var Dl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{yl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},El=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Fl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Gl=function(a){if(l.top==l)return 0;if(a===void 0?0:a){var b=l.location.ancestorOrigins;
if(b)return b[b.length-1]==l.location.origin?1:2}return Dl(l.top)?1:2},Hl=function(a){a=a===void 0?document:a;return a.createElement("img")},Il=function(){for(var a=l,b=a;a&&a!=a.parent;)a=a.parent,Dl(a)&&(b=a);return b};function Jl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Kl(){return Jl("join-ad-interest-group")&&eb(kc.joinAdInterestGroup)}
function Ll(a,b,c){var d=pg[3]===void 0?1:pg[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=y.querySelector(e);g&&(f=[g])}else f=Array.from(y.querySelectorAll(e))}catch(r){}var h;a:{try{h=y.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(pg[2]===void 0?50:pg[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&ub()-q<(pg[1]===void 0?6E4:pg[1])?(Ya("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ml(f[0]);else{if(n)return Ya("TAGGING",10),!1}else f.length>=d?Ml(f[0]):n&&Ml(m[0]);zc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:ub()});return!0}function Ml(a){try{a.parentNode.removeChild(a)}catch(b){}};function Nl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Ol=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};wl();Bl()||tl("iPod");tl("iPad");!tl("Android")||xl()||wl()||vl()||tl("Silk");xl();!tl("Safari")||xl()||(ul()?0:tl("Coast"))||vl()||(ul()?0:tl("Edge"))||(ul()?sl("Microsoft Edge"):tl("Edg/"))||(ul()?sl("Opera"):tl("OPR"))||wl()||tl("Silk")||tl("Android")||Cl();var Pl={},Ql=null,Rl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Ql){Ql={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Pl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Ql[q]===void 0&&(Ql[q]=p)}}}for(var r=Pl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],B=b[v+2],C=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|B>>6],I=r[B&63];t[w++]=""+C+F+G+I}var L=0,V=u;switch(b.length-v){case 2:L=b[v+1],V=r[(L&15)<<2]||u;case 1:var Q=b[v];t[w]=""+r[Q>>2]+r[(Q&3)<<4|L>>4]+V+u}return t.join("")};var Sl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Tl=/#|$/,Ul=function(a,b){var c=a.search(Tl),d=Sl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return zl(a.slice(d,e!==-1?e:0))},Vl=/[?&]($|#)/,Wl=function(a,b,c){for(var d,e=a.search(Tl),f=0,g,h=[];(g=Sl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Vl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Xl(a,b,c,d,e,f){var g=Ul(c,"fmt");if(d){var h=Ul(c,"random"),m=Ul(c,"label")||"";if(!h)return!1;var n=Rl(zl(m)+":"+zl(h));if(!Nl(a,n,d))return!1}g&&Number(g)!==4&&(c=Wl(c,"rfmt",g));var p=Wl(c,"fmt",4);xc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Yl={},Zl=(Yl[1]={},Yl[2]={},Yl[3]={},Yl[4]={},Yl);function $l(a,b,c){var d=am(b,c);if(d){var e=Zl[b][d];e||(e=Zl[b][d]=[]);e.push(Object.assign({},a))}}function bm(a,b){var c=am(a,b);if(c){var d=Zl[a][c];d&&(Zl[a][c]=d.filter(function(e){return!e.Dm}))}}function cm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function am(a,b){var c=b;if(b[0]==="/"){var d;c=((d=l.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function dm(a){var b=ya.apply(1,arguments);el&&($l(a,2,b[0]),$l(a,3,b[0]));Ic.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);el&&$l(a,2,b[0]);return Jc.apply(null,ua(b))}function fm(a){var b=ya.apply(1,arguments);el&&$l(a,3,b[0]);Ac.apply(null,ua(b))}
function gm(a){var b=ya.apply(1,arguments),c=b[0];el&&($l(a,2,c),$l(a,3,c));return Lc.apply(null,ua(b))}function hm(a){var b=ya.apply(1,arguments);el&&$l(a,1,b[0]);xc.apply(null,ua(b))}function im(a){var b=ya.apply(1,arguments);b[0]&&el&&$l(a,4,b[0]);zc.apply(null,ua(b))}function jm(a){var b=ya.apply(1,arguments);el&&$l(a,1,b[2]);return Xl.apply(null,ua(b))}function km(a){var b=ya.apply(1,arguments);el&&$l(a,4,b[0]);Ll.apply(null,ua(b))};var lm=/gtag[.\/]js/,mm=/gtm[.\/]js/,nm=!1;function om(a){if(nm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(lm.test(c))return"3";if(mm.test(c))return"2"}return"0"};function pm(a,b){var c=qm();c.pending||(c.pending=[]);jb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function rm(){var a=l.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=k(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var sm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=rm()};
function qm(){var a=oc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new sm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=rm());return c};var tm={},um=!1,vm=void 0,cg={ctid:Li(5,"GTM-TT9C75WP"),canonicalContainerId:Li(6,"168426611"),xm:Li(10,"GTM-TT9C75WP"),ym:Li(9,"GTM-TT9C75WP")};tm.ie=Ki(7,qb(""));function wm(){return tm.ie&&xm().some(function(a){return a===cg.ctid})}function ym(){var a=zm();return um?a.map(Am):a}function Bm(){var a=xm();return um?a.map(Am):a}
function Cm(){var a=Bm();if(!um)for(var b=k([].concat(ua(a))),c=b.next();!c.done;c=b.next()){var d=Am(c.value),e=qm().destination[d];e&&e.state!==0||a.push(d)}return a}function Dm(){return Em(cg.ctid)}function Fm(){return Em(cg.canonicalContainerId||"_"+cg.ctid)}function zm(){return cg.xm?cg.xm.split("|"):[cg.ctid]}function xm(){return cg.ym?cg.ym.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function Gm(){var a=Hm(Im()),b=a&&a.parent;if(b)return Hm(b)}
function Hm(a){var b=qm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function Em(a){return um?Am(a):a}function Am(a){return"siloed_"+a}function Jm(a){a=String(a);return zb(a,"siloed_")?a.substring(7):a}function Km(){if(Ij.R){var a=qm();if(a.siloed){for(var b=[],c=zm().map(Am),d=xm().map(Am),e={},f=0;f<a.siloed.length;e={yh:void 0},f++)e.yh=a.siloed[f],!um&&jb(e.yh.isDestination?d:c,function(g){return function(h){return h===g.yh.ctid}}(e))?um=!0:b.push(e.yh);a.siloed=b}}}
function Lm(){var a=qm();if(a.pending){for(var b,c=[],d=!1,e=ym(),f=vm?vm:Cm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],jb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Fm())}catch(m){}}}
function Mm(){var a=cg.ctid,b=ym(),c=Cm();vm=c;for(var d=function(n,p){var q={canonicalContainerId:cg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};mc&&(q.scriptElement=mc);nc&&(q.scriptSource=nc);if(Gm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Ij.D,x=Ok(v),z=w?x.pathname:""+x.hostname+x.pathname,B=y.scripts,C="",F=0;F<B.length;++F){var G=B[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}C=String(F)}}if(C){t=C;break b}}t=void 0}var I=t;if(I){nm=!0;r=I;break a}}var L=[].slice.call(y.scripts);r=q.scriptElement?String(L.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=om(q)}var V=p?e.destination:e.container,Q=V[n];Q?(p&&Q.state===0&&N(93),Object.assign(Q,q)):V[n]=q},e=qm(),f=k(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=k(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Fm()]={};Lm()}function Nm(){var a=Fm();return!!qm().canonical[a]}function Om(a){return!!qm().container[a]}function Pm(a){var b=qm().destination[a];return!!b&&!!b.state}function Im(){return{ctid:Dm(),isDestination:tm.ie}}function Qm(a,b,c){b.siloed&&Rm({ctid:a,isDestination:!1});var d=Im();qm().container[a]={state:1,context:b,parent:d};pm({ctid:a,isDestination:!1},c)}
function Rm(a){var b=qm();(b.siloed=b.siloed||[]).push(a)}function Sm(){var a=qm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Tm(){var a={};nb(qm().destination,function(b,c){c.state===0&&(a[Jm(b)]=c)});return a}function Um(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Vm(){for(var a=qm(),b=k(ym()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function Wm(a){var b=qm();return b.destination[a]?1:b.destination[Am(a)]?2:0};var Xm={Ka:{ce:0,he:1,Ki:2}};Xm.Ka[Xm.Ka.ce]="FULL_TRANSMISSION";Xm.Ka[Xm.Ka.he]="LIMITED_TRANSMISSION";Xm.Ka[Xm.Ka.Ki]="NO_TRANSMISSION";var Ym={Z:{Fb:0,Ea:1,Fc:2,Oc:3}};Ym.Z[Ym.Z.Fb]="NO_QUEUE";Ym.Z[Ym.Z.Ea]="ADS";Ym.Z[Ym.Z.Fc]="ANALYTICS";Ym.Z[Ym.Z.Oc]="MONITORING";function Zm(){var a=oc("google_tag_data",{});return a.ics=a.ics||new $m}var $m=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
$m.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Ya("TAGGING",19);b==null?Ya("TAGGING",18):an(this,a,b==="granted",c,d,e,f,g)};$m.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)an(this,a[d],void 0,void 0,"","",b,c)};
var an=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&fb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&l.setTimeout(function(){m[b]===t&&t.quiet&&(Ya("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};ba=$m.prototype;ba.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=k(d),n=m.next();!n.done;n=m.next())bn(this,n.value)}else if(b!==void 0&&h!==b)for(var p=k(d),q=p.next();!q.done;q=p.next())bn(this,q.value)};
ba.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
ba.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&fb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
ba.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
ba.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};ba.addListener=function(a,b){this.D.push({consentTypes:a,te:b})};var bn=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.zm=!0)}};$m.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.zm){d.zm=!1;try{d.te({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var cn=!1,dn=!1,en={},fn={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(en.ad_storage=1,en.analytics_storage=1,en.ad_user_data=1,en.ad_personalization=1,en),usedContainerScopedDefaults:!1};function gn(a){var b=Zm();b.accessedAny=!0;return(fb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,fn)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function hn(a){var b=Zm();b.accessedAny=!0;return b.getConsentState(a,fn)}function jn(a){var b=Zm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function kn(){if(!qg(8))return!1;var a=Zm();a.accessedAny=!0;if(a.active)return!0;if(!fn.usedContainerScopedDefaults)return!1;for(var b=k(Object.keys(fn.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(fn.containerScopedDefaults[c.value]!==1)return!0;return!1}function ln(a,b){Zm().addListener(a,b)}
function mn(a,b){Zm().notifyListeners(a,b)}function nn(a,b){function c(){for(var e=0;e<b.length;e++)if(!jn(b[e]))return!0;return!1}if(c()){var d=!1;ln(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function on(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];gn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=fb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),ln(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):l.setTimeout(function(){m(c())},500)}}))};var pn={},qn=(pn[Ym.Z.Fb]=Xm.Ka.ce,pn[Ym.Z.Ea]=Xm.Ka.ce,pn[Ym.Z.Fc]=Xm.Ka.ce,pn[Ym.Z.Oc]=Xm.Ka.ce,pn),rn=function(a,b){this.D=a;this.consentTypes=b};rn.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return gn(a)});case 1:return this.consentTypes.some(function(a){return gn(a)});default:bc(this.D,"consentsRequired had an unknown type")}};
var sn={},tn=(sn[Ym.Z.Fb]=new rn(0,[]),sn[Ym.Z.Ea]=new rn(0,["ad_storage"]),sn[Ym.Z.Fc]=new rn(0,["analytics_storage"]),sn[Ym.Z.Oc]=new rn(1,["ad_storage","analytics_storage"]),sn);var vn=function(a){var b=this;this.type=a;this.D=[];ln(tn[a].consentTypes,function(){un(b)||b.flush()})};vn.prototype.flush=function(){for(var a=k(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var un=function(a){return qn[a.type]===Xm.Ka.Ki&&!tn[a.type].isConsentGranted()},wn=function(a,b){un(a)?a.D.push(b):b()},xn=new Map;function yn(a){xn.has(a)||xn.set(a,new vn(a));return xn.get(a)};var zn="https://"+Li(21,"www.googletagmanager.com"),An="/td?id="+cg.ctid,Bn="v t pid dl tdp exp".split(" "),Cn=["mcc"],Dn={},En={},Fn=!1,Gn=void 0;function Hn(a,b,c){En[a]=b;(c===void 0||c)&&In(a)}function In(a,b){Dn[a]!==void 0&&(b===void 0||!b)||E(166)&&zb(cg.ctid,"GTM-")&&a==="mcc"||(Dn[a]=!0)}
function Jn(a){a=a===void 0?!1:a;var b=Object.keys(Dn).filter(function(c){return Dn[c]===!0&&En[c]!==void 0&&(a||!Cn.includes(c))}).map(function(c){var d=En[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Xk(zn)+An+(""+b+"&z=0")}function Kn(){Object.keys(Dn).forEach(function(a){Bn.indexOf(a)<0&&(Dn[a]=!1)})}
function Ln(a){a=a===void 0?!1:a;if(Ij.la&&el&&cg.ctid){var b=yn(Ym.Z.Oc);if(un(b))Fn||(Fn=!0,wn(b,Ln));else{var c=Jn(a),d={destinationId:cg.ctid,endpoint:61};a?gm(d,c,void 0,{Ih:!0},void 0,function(){fm(d,c+"&img=1")}):fm(d,c);Kn();Fn=!1}}}var Mn={};function Nn(a){var b=String(a);Mn.hasOwnProperty(b)||(Mn[b]=!0,Hn("csp",Object.keys(Mn).join("~")),In("csp",!0),Gn===void 0&&E(171)&&(Gn=l.setTimeout(function(){var c=Dn.csp;Dn.csp=!0;var d=Jn(!1);Dn.csp=c;xc(d+"&script=1");Gn=void 0},500)))}
function On(){Object.keys(Dn).filter(function(a){return Dn[a]&&!Bn.includes(a)}).length>0&&Ln(!0)}var Pn=kb();function Qn(){Pn=kb()}function Rn(){Hn("v","3");Hn("t","t");Hn("pid",function(){return String(Pn)});Hn("exp",fk());Cc(l,"pagehide",On);l.setInterval(Qn,864E5)};var Sn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Tn=[K.m.ld,K.m.uc,K.m.Xd,K.m.Nb,K.m.sc,K.m.Ta,K.m.Sa,K.m.kb,K.m.qb,K.m.Pb],Un=!1,Vn=!1,Wn={},Xn={};function Yn(){!Vn&&Un&&(Sn.some(function(a){return fn.containerScopedDefaults[a]!==1})||Zn("mbc"));Vn=!0}function Zn(a){el&&(Hn(a,"1"),Ln())}function $n(a,b){if(!Wn[b]&&(Wn[b]=!0,Xn[b]))for(var c=k(Tn),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){Zn("erc");break}}
function ao(a,b){if(!Wn[b]&&(Wn[b]=!0,Xn[b]))for(var c=k(Tn),d=c.next();!d.done;d=c.next())if(O(a,d.value)){Zn("erc");break}};function bo(a){Ya("HEALTH",a)};var co={aa:{Tm:"aw_user_data_cache",wg:"cookie_deprecation_label",bo:"fl_user_data_cache",eo:"ga4_user_data_cache",zf:"ip_geo_data_cache",Ei:"ip_geo_fetch_in_progress",Al:"nb_data",vo:"page_experiment_ids",If:"pt_data",Cl:"pt_listener_set",Jl:"service_worker_endpoint",Pi:"shared_user_id",Qi:"shared_user_id_requested",Kf:"shared_user_id_source"}};var eo=function(a){return Se(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(co.aa);
function fo(a,b){b=b===void 0?!1:b;if(eo(a)){var c,d,e=(d=(c=oc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=k(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function go(a,b){var c=fo(a,!0);c&&c.set(b)}function ho(a){var b;return(b=fo(a))==null?void 0:b.get()}function io(a){var b={},c=fo(a);if(!c){c=fo(a,!0);if(!c)return;c.set(b)}return c.get()}function jo(a,b){if(typeof b==="function"){var c;return(c=fo(a,!0))==null?void 0:c.subscribe(b)}}function ko(a,b){var c=fo(a);return c?c.unsubscribe(b):!1};var lo={pp:Li(22,"eyIwIjoiSVQiLCIxIjoiIiwiMiI6ZmFsc2UsIjMiOiJnb29nbGUuaXQiLCI0IjoicmVnaW9uMSIsIjUiOmZhbHNlLCI2Ijp0cnVlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0")},mo={},no=!1;function oo(){function a(){c!==void 0&&ko(co.aa.zf,c);try{var e=ho(co.aa.zf);mo=JSON.parse(e)}catch(f){N(123),bo(2),mo={}}no=!0;b()}var b=po,c=void 0,d=ho(co.aa.zf);d?a(d):(c=jo(co.aa.zf,a),qo())}
function qo(){function a(c){go(co.aa.zf,c||"{}");go(co.aa.Ei,!1)}if(!ho(co.aa.Ei)){go(co.aa.Ei,!0);var b="";try{l.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function ro(){var a=lo.pp;try{return JSON.parse(Va(a))}catch(b){return N(123),bo(2),{}}}function so(){return mo["0"]||""}function to(){return mo["1"]||""}function uo(){var a=!1;return a}function vo(){return mo["6"]!==!1}function wo(){var a="";return a}
function xo(){var a=!1;return a}function yo(){var a="";return a};var zo={},Ao=Object.freeze((zo[K.m.Fa]=1,zo[K.m.Cg]=1,zo[K.m.Dg]=1,zo[K.m.Lb]=1,zo[K.m.wa]=1,zo[K.m.qb]=1,zo[K.m.rb]=1,zo[K.m.xb]=1,zo[K.m.dd]=1,zo[K.m.Pb]=1,zo[K.m.kb]=1,zo[K.m.Hc]=1,zo[K.m.Ue]=1,zo[K.m.qa]=1,zo[K.m.wk]=1,zo[K.m.Xe]=1,zo[K.m.Og]=1,zo[K.m.Pg]=1,zo[K.m.Xd]=1,zo[K.m.Lk]=1,zo[K.m.qc]=1,zo[K.m.ae]=1,zo[K.m.Nk]=1,zo[K.m.Sg]=1,zo[K.m.ii]=1,zo[K.m.Kc]=1,zo[K.m.Lc]=1,zo[K.m.Sa]=1,zo[K.m.ji]=1,zo[K.m.Sb]=1,zo[K.m.sb]=1,zo[K.m.kd]=1,zo[K.m.ld]=1,zo[K.m.hf]=1,zo[K.m.li]=1,zo[K.m.kf]=1,zo[K.m.uc]=
1,zo[K.m.nd]=1,zo[K.m.eh]=1,zo[K.m.Ub]=1,zo[K.m.rd]=1,zo[K.m.Ni]=1,zo));Object.freeze([K.m.Ba,K.m.Xa,K.m.Db,K.m.yb,K.m.ki,K.m.Ta,K.m.fi,K.m.En]);
var Bo={},Co=Object.freeze((Bo[K.m.jn]=1,Bo[K.m.kn]=1,Bo[K.m.ln]=1,Bo[K.m.mn]=1,Bo[K.m.nn]=1,Bo[K.m.qn]=1,Bo[K.m.rn]=1,Bo[K.m.sn]=1,Bo[K.m.un]=1,Bo[K.m.Rd]=1,Bo)),Do={},Eo=Object.freeze((Do[K.m.mk]=1,Do[K.m.nk]=1,Do[K.m.Nd]=1,Do[K.m.Od]=1,Do[K.m.pk]=1,Do[K.m.Wc]=1,Do[K.m.Pd]=1,Do[K.m.hc]=1,Do[K.m.Gc]=1,Do[K.m.jc]=1,Do[K.m.nb]=1,Do[K.m.Qd]=1,Do[K.m.wb]=1,Do[K.m.qk]=1,Do)),Fo=Object.freeze([K.m.Fa,K.m.Ke,K.m.Lb,K.m.Hc,K.m.Xd,K.m.cf,K.m.sb,K.m.nd]),Go=Object.freeze([].concat(ua(Fo))),Ho=Object.freeze([K.m.rb,
K.m.Pg,K.m.hf,K.m.li,K.m.Kg]),Io=Object.freeze([].concat(ua(Ho))),Jo={},Ko=(Jo[K.m.V]="1",Jo[K.m.ja]="2",Jo[K.m.W]="3",Jo[K.m.Oa]="4",Jo),Lo={},Mo=Object.freeze((Lo.search="s",Lo.youtube="y",Lo.playstore="p",Lo.shopping="h",Lo.ads="a",Lo.maps="m",Lo));function No(a){return typeof a!=="object"||a===null?{}:a}function Oo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Po(a){if(a!==void 0&&a!==null)return Oo(a)}function Qo(a){return typeof a==="number"?a:Po(a)};function Ro(a){return a&&a.indexOf("pending:")===0?So(a.substr(8)):!1}function So(a){if(a==null||a.length===0)return!1;var b=Number(a),c=ub();return b<c+3E5&&b>c-9E5};var To=!1,Uo=!1,Vo=!1,Wo=0,Xo=!1,Yo=[];function Zo(a){if(Wo===0)Xo&&Yo&&(Yo.length>=100&&Yo.shift(),Yo.push(a));else if($o()){var b=Li(41,'google.tagmanager.ta.prodqueue'),c=oc(b,[]);c.length>=50&&c.shift();c.push(a)}}function ap(){bp();Dc(y,"TAProdDebugSignal",ap)}function bp(){if(!Uo){Uo=!0;cp();var a=Yo;Yo=void 0;a==null||a.forEach(function(b){Zo(b)})}}
function cp(){var a=y.documentElement.getAttribute("data-tag-assistant-prod-present");So(a)?Wo=1:!Ro(a)||To||Vo?Wo=2:(Vo=!0,Cc(y,"TAProdDebugSignal",ap,!1),l.setTimeout(function(){bp();To=!0},200))}function $o(){if(!Xo)return!1;switch(Wo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var dp=!1;function ep(a,b){var c=zm(),d=xm();if($o()){var e=fp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Zo(e)}}
function gp(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.eb;e=a.isBatched;var f;if(f=$o()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=fp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Zo(h)}}function hp(a){$o()&&gp(a())}
function fp(a,b){b=b===void 0?{}:b;b.groupId=ip;var c,d=b,e={publicId:jp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'41',messageType:a};c.containerProduct=dp?"OGT":"GTM";c.key.targetRef=kp;return c}var jp="",kp={ctid:"",isDestination:!1},ip;
function lp(a){var b=cg.ctid,c=wm();Wo=0;Xo=!0;cp();ip=a;jp=b;dp=Vj;kp={ctid:b,isDestination:c}};var mp=[K.m.V,K.m.ja,K.m.W,K.m.Oa],np,op;function pp(a){var b=a[K.m.bc];b||(b=[""]);for(var c={Yf:0};c.Yf<b.length;c={Yf:c.Yf},++c.Yf)nb(a,function(d){return function(e,f){if(e!==K.m.bc){var g=Oo(f),h=b[d.Yf],m=so(),n=to();dn=!0;cn&&Ya("TAGGING",20);Zm().declare(e,g,h,m,n)}}}(c))}
function qp(a){Yn();!op&&np&&Zn("crc");op=!0;var b=a[K.m.ug];b&&N(41);var c=a[K.m.bc];c?N(40):c=[""];for(var d={Zf:0};d.Zf<c.length;d={Zf:d.Zf},++d.Zf)nb(a,function(e){return function(f,g){if(f!==K.m.bc&&f!==K.m.ug){var h=Po(g),m=c[e.Zf],n=Number(b),p=so(),q=to();n=n===void 0?0:n;cn=!0;dn&&Ya("TAGGING",20);Zm().default(f,h,m,p,q,n,fn)}}}(d))}
function rp(a){fn.usedContainerScopedDefaults=!0;var b=a[K.m.bc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(to())&&!c.includes(so()))return}nb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}fn.usedContainerScopedDefaults=!0;fn.containerScopedDefaults[d]=e==="granted"?3:2})}
function sp(a,b){Yn();np=!0;nb(a,function(c,d){var e=Oo(d);cn=!0;dn&&Ya("TAGGING",20);Zm().update(c,e,fn)});mn(b.eventId,b.priorityId)}function tp(a){a.hasOwnProperty("all")&&(fn.selectedAllCorePlatformServices=!0,nb(Mo,function(b){fn.corePlatformServices[b]=a.all==="granted";fn.usedCorePlatformServices=!0}));nb(a,function(b,c){b!=="all"&&(fn.corePlatformServices[b]=c==="granted",fn.usedCorePlatformServices=!0)})}function up(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return gn(b)})}
function vp(a,b){ln(a,b)}function wp(a,b){on(a,b)}function xp(a,b){nn(a,b)}function yp(){var a=[K.m.V,K.m.Oa,K.m.W];Zm().waitForUpdate(a,500,fn)}function zp(a){for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;Zm().clearTimeout(d,void 0,fn)}mn()}function Ap(){if(!Xj)for(var a=vo()?ik(Ij.ab):ik(Ij.Gb),b=0;b<mp.length;b++){var c=mp[b],d=c,e=a[c]?"granted":"denied";Zm().implicit(d,e)}};var Bp=!1,Cp=[];function Dp(){if(!Bp){Bp=!0;for(var a=Cp.length-1;a>=0;a--)Cp[a]();Cp=[]}};var Ep=l.google_tag_manager=l.google_tag_manager||{};function Fp(a,b){return Ep[a]=Ep[a]||b()}function Gp(){var a=Dm(),b=Hp;Ep[a]=Ep[a]||b}function Ip(){var a=Ep.sequence||1;Ep.sequence=a+1;return a};function Jp(){if(Ep.pscdl!==void 0)ho(co.aa.wg)===void 0&&go(co.aa.wg,Ep.pscdl);else{var a=function(c){Ep.pscdl=c;go(co.aa.wg,c)},b=function(){a("error")};try{kc.cookieDeprecationLabel?(a("pending"),kc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Kp=0;function Lp(a){el&&a===void 0&&Kp===0&&(Hn("mcc","1"),Kp=1)};var Np={xf:{Xm:"cd",Ym:"ce",Zm:"cf",bn:"cpf",dn:"cu"}};var Op=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Pp=/\s/;
function Qp(a,b){if(fb(a)){a=sb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Op.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Pp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Rp(a,b){for(var c={},d=0;d<a.length;++d){var e=Qp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Sp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=k(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Tp={},Sp=(Tp[0]=0,Tp[1]=1,Tp[2]=2,Tp[3]=0,Tp[4]=1,Tp[5]=0,Tp[6]=0,Tp[7]=0,Tp);var Up=Number('')||500,Vp={},Wp={},Xp={initialized:11,complete:12,interactive:13},Yp={},Zp=Object.freeze((Yp[K.m.sb]=!0,Yp)),$p=void 0;function aq(a,b){if(b.length&&el){var c;(c=Vp)[a]!=null||(c[a]=[]);Wp[a]!=null||(Wp[a]=[]);var d=b.filter(function(e){return!Wp[a].includes(e)});Vp[a].push.apply(Vp[a],ua(d));Wp[a].push.apply(Wp[a],ua(d));!$p&&d.length>0&&(In("tdc",!0),$p=l.setTimeout(function(){Ln();Vp={};$p=void 0},Up))}}
function bq(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function cq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;$c(t)==="object"?u=t[r]:$c(t)==="array"&&(u=t[r]);return u===void 0?Zp[r]:u},f=bq(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=$c(m)==="object"||$c(m)==="array",q=$c(n)==="object"||$c(n)==="array";if(p&&q)cq(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function dq(){Hn("tdc",function(){$p&&(l.clearTimeout($p),$p=void 0);var a=[],b;for(b in Vp)Vp.hasOwnProperty(b)&&a.push(b+"*"+Vp[b].join("."));return a.length?a.join("!"):void 0},!1)};var eq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.R=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},fq=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.R)}return c},O=function(a,b,c,d){for(var e=k(fq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},gq=function(a){for(var b={},c=fq(a,4),d=k(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=k(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
eq.prototype.getMergedValues=function(a,b,c){function d(n){bd(n)&&nb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=fq(this,b);g.reverse();for(var h=k(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var hq=function(a){for(var b=[K.m.Pe,K.m.Le,K.m.Me,K.m.Ne,K.m.Oe,K.m.Qe,K.m.Re],c=fq(a,3),d=k(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=k(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},iq=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.ia={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},jq=function(a,
b){a.J=b;return a},kq=function(a,b){a.T=b;return a},lq=function(a,b){a.D=b;return a},mq=function(a,b){a.O=b;return a},nq=function(a,b){a.ia=b;return a},oq=function(a,b){a.R=b;return a},pq=function(a,b){a.eventMetadata=b||{};return a},qq=function(a,b){a.onSuccess=b;return a},rq=function(a,b){a.onFailure=b;return a},sq=function(a,b){a.isGtmEvent=b;return a},tq=function(a){return new eq(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={C:{Tj:"accept_by_default",sg:"add_tag_timing",Mh:"allow_ad_personalization",Vj:"batch_on_navigation",Xj:"client_id_source",Ce:"consent_event_id",De:"consent_priority_id",Hq:"consent_state",ka:"consent_updated",Vc:"conversion_linker_enabled",ya:"cookie_options",xg:"create_dc_join",yg:"create_fpm_geo_join",zg:"create_fpm_signals_join",Ld:"create_google_join",Md:"em_event",Kq:"endpoint_for_debug",lk:"enhanced_client_id_source",Sh:"enhanced_match_result",od:"euid_mode_enabled",lb:"event_start_timestamp_ms",
nl:"event_usage",gh:"extra_tag_experiment_ids",Rq:"add_parameter",zi:"attribution_reporting_experiment",Ai:"counting_method",hh:"send_as_iframe",Sq:"parameter_order",ih:"parsed_target",co:"ga4_collection_subdomain",ql:"gbraid_cookie_marked",fa:"hit_type",sd:"hit_type_override",jo:"is_config_command",Af:"is_consent_update",Bf:"is_conversion",vl:"is_ecommerce",ud:"is_external_event",Fi:"is_fallback_aw_conversion_ping_allowed",Cf:"is_first_visit",wl:"is_first_visit_conversion",jh:"is_fl_fallback_conversion_flow_allowed",
de:"is_fpm_encryption",kh:"is_fpm_split",ee:"is_gcp_conversion",Gi:"is_google_signals_allowed",vd:"is_merchant_center",mh:"is_new_to_site",nh:"is_server_side_destination",fe:"is_session_start",yl:"is_session_start_conversion",Vq:"is_sgtm_ga_ads_conversion_study_control_group",Wq:"is_sgtm_prehit",zl:"is_sgtm_service_worker",Hi:"is_split_conversion",ko:"is_syn",Df:"join_id",Ii:"join_elapsed",Ef:"join_timer_sec",je:"tunnel_updated",ar:"prehit_for_retry",gr:"promises",hr:"record_aw_latency",wc:"redact_ads_data",
ke:"redact_click_ids",wo:"remarketing_only",Hl:"send_ccm_parallel_ping",qh:"send_fledge_experiment",jr:"send_ccm_parallel_test_ping",Jf:"send_to_destinations",Mi:"send_to_targets",Il:"send_user_data_hit",cb:"source_canonical_id",Ja:"speculative",Ll:"speculative_in_message",Ml:"suppress_script_load",Nl:"syn_or_mod",Ql:"transient_ecsid",Lf:"transmission_type",Ua:"user_data",mr:"user_data_from_automatic",nr:"user_data_from_automatic_getter",ne:"user_data_from_code",th:"user_data_from_manual",Sl:"user_data_mode",
Mf:"user_id_updated"}};var uq={Rm:Number("5"),Ir:Number("")},vq=[],wq=!1;function xq(a){vq.push(a)}var yq="?id="+cg.ctid,zq=void 0,Aq={},Bq=void 0,Cq=new function(){var a=5;uq.Rm>0&&(a=uq.Rm);this.J=a;this.D=0;this.O=[]},Dq=1E3;
function Eq(a,b){var c=zq;if(c===void 0)if(b)c=Ip();else return"";for(var d=[Xk("https://www.googletagmanager.com"),"/a",yq],e=k(vq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Kd:!!a}),m=k(h),n=m.next();!n.done;n=m.next()){var p=k(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Fq(){if(Ij.la&&(Bq&&(l.clearTimeout(Bq),Bq=void 0),zq!==void 0&&Gq)){var a=yn(Ym.Z.Oc);if(un(a))wq||(wq=!0,wn(a,Fq));else{var b;if(!(b=Aq[zq])){var c=Cq;b=c.D<c.J?!1:ub()-c.O[c.D%c.J]<1E3}if(b||Dq--<=0)N(1),Aq[zq]=!0;else{var d=Cq,e=d.D++%d.J;d.O[e]=ub();var f=Eq(!0);fm({destinationId:cg.ctid,endpoint:56,eventId:zq},f);wq=Gq=!1}}}}function Hq(){if(dl&&Ij.la){var a=Eq(!0,!0);fm({destinationId:cg.ctid,endpoint:56,eventId:zq},a)}}var Gq=!1;
function Iq(a){Aq[a]||(a!==zq&&(Fq(),zq=a),Gq=!0,Bq||(Bq=l.setTimeout(Fq,500)),Eq().length>=2022&&Fq())}var Jq=kb();function Kq(){Jq=kb()}function Lq(){return[["v","3"],["t","t"],["pid",String(Jq)]]};var Mq={};function Nq(a,b,c){dl&&a!==void 0&&(Mq[a]=Mq[a]||[],Mq[a].push(c+b),Iq(a))}function Oq(a){var b=a.eventId,c=a.Kd,d=[],e=Mq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Mq[b];return d};function Pq(a,b,c,d){var e=Qp(Em(a),!0);e&&Qq.register(e,b,c,d)}function Rq(a,b,c,d){var e=Qp(c,d.isGtmEvent);e&&(Uj&&(d.deferrable=!0),Qq.push("event",[b,a],e,d))}function Sq(a,b,c,d){var e=Qp(c,d.isGtmEvent);e&&Qq.push("get",[a,b],e,d)}function Tq(a){var b=Qp(Em(a),!0),c;b?c=Uq(Qq,b).D:c={};return c}function Vq(a,b){var c=Qp(Em(a),!0);c&&Wq(Qq,c,b)}
var Xq=function(){this.T={};this.D={};this.J={};this.ia=null;this.R={};this.O=!1;this.status=1},Yq=function(a,b,c,d){this.J=ub();this.D=b;this.args=c;this.messageContext=d;this.type=a},Zq=function(){this.destinations={};this.D={};this.commands=[]},Uq=function(a,b){var c=b.destinationId;um||(c=Jm(c));return a.destinations[c]=a.destinations[c]||new Xq},$q=function(a,b,c,d){if(d.D){var e=Uq(a,d.D),f=e.ia;if(f){var g=d.D.id;um||(g=Jm(g));var h=cd(c,null),m=cd(e.T[g],null),n=cd(e.R,null),p=cd(e.D,null),
q=cd(a.D,null),r={};if(dl)try{r=cd(kk,null)}catch(x){N(72)}var t=d.D.prefix,u=function(x){Nq(d.messageContext.eventId,t,x)},v=tq(sq(rq(qq(pq(nq(mq(oq(lq(kq(jq(new iq(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Nq(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(el&&x==="config"){var B,C=(B=Qp(z))==null?void 0:B.ids;if(!(C&&C.length>1)){var F,G=oc("google_tag_data",{});G.td||(G.td={});F=G.td;var I=cd(v.R);cd(v.D,I);var L=[],V;for(V in F)F.hasOwnProperty(V)&&cq(F[V],I).length&&L.push(V);L.length&&(aq(z,L),Ya("TAGGING",Xp[y.readyState]||14));F[z]=I}}f(d.D.id,b,d.J,v)}catch(Q){Nq(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():wn(e.la,w)}}};
Zq.prototype.register=function(a,b,c,d){var e=Uq(this,a);e.status!==3&&(e.ia=b,e.status=3,e.la=yn(c),Wq(this,a,d||{}),this.flush())};
Zq.prototype.push=function(a,b,c,d){c!==void 0&&(Uq(this,c).status===1&&(Uq(this,c).status=2,this.push("require",[{}],c,{})),Uq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.C.Jf]||(d.eventMetadata[P.C.Jf]=[c.destinationId]),d.eventMetadata[P.C.Mi]||(d.eventMetadata[P.C.Mi]=[c.id]));this.commands.push(new Yq(a,c,b,d));d.deferrable||this.flush()};
Zq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={xc:void 0,zh:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||Uq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Uq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];nb(h,function(u,v){cd(Bb(u,v),b.D)});Gj(h,!0);break;case "config":var m=Uq(this,g);
e.xc={};nb(f.args[0],function(u){return function(v,w){cd(Bb(v,w),u.xc)}}(e));var n=!!e.xc[K.m.nd];delete e.xc[K.m.nd];var p=g.destinationId===g.id;Gj(e.xc,!0);n||(p?m.R={}:m.T[g.id]={});m.O&&n||$q(this,K.m.ra,e.xc,f);m.O=!0;p?cd(e.xc,m.R):(cd(e.xc,m.T[g.id]),N(70));d=!0;E(166)||($n(e.xc,g.id),Un=!0);break;case "event":e.zh={};nb(f.args[0],function(u){return function(v,w){cd(Bb(v,w),u.zh)}}(e));Gj(e.zh);$q(this,f.args[1],e.zh,f);if(!E(166)){var q=void 0;!f.D||((q=f.messageContext.eventMetadata)==null?
0:q[P.C.Md])||(Xn[f.D.id]=!0);Un=!0}break;case "get":var r={},t=(r[K.m.oc]=f.args[0],r[K.m.Ic]=f.args[1],r);$q(this,K.m.Cb,t,f);E(166)||(Un=!0)}this.commands.shift();ar(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var ar=function(a,b){if(b.type!=="require")if(b.D)for(var c=Uq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},Wq=function(a,b,c){var d=cd(c,null);cd(Uq(a,b).D,d);Uq(a,b).D=d},Qq=new Zq;function br(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function cr(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function dr(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Hl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=hc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}cr(e,"load",f);cr(e,"error",f)};br(e,"load",f);br(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function er(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";El(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});fr(c,b)}
function fr(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else dr(c,a,b===void 0?!1:b,d===void 0?!1:d)};var gr=function(){this.ia=this.ia;this.R=this.R};gr.prototype.ia=!1;gr.prototype.dispose=function(){this.ia||(this.ia=!0,this.O())};gr.prototype[Symbol.dispose]=function(){this.dispose()};gr.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};gr.prototype.O=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function hr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var ir=function(a,b){b=b===void 0?{}:b;gr.call(this);this.D=null;this.la={};this.Gb=0;this.T=null;this.J=a;var c;this.ab=(c=b.timeoutMs)!=null?c:500;var d;this.Ca=(d=b.xr)!=null?d:!1};sa(ir,gr);ir.prototype.O=function(){this.la={};this.T&&(cr(this.J,"message",this.T),delete this.T);delete this.la;delete this.J;delete this.D;gr.prototype.O.call(this)};var kr=function(a){return typeof a.J.__tcfapi==="function"||jr(a)!=null};
ir.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ca},d=hl(function(){return a(c)}),e=0;this.ab!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.ab));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=hr(c),c.internalBlockOnErrors=b.Ca,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{lr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};ir.prototype.removeEventListener=function(a){a&&a.listenerId&&lr(this,"removeEventListener",null,a.listenerId)};
var nr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=mr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&mr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?mr(a.purpose.legitimateInterests,
b)&&mr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},mr=function(a,b){return!(!a||!a[b])},lr=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(jr(a)){or(a);var g=++a.Gb;a.la[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},jr=function(a){if(a.D)return a.D;a.D=Fl(a.J,"__tcfapiLocator");return a.D},or=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;br(a.J,"message",b)}},pr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=hr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(er({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var qr={1:0,3:0,4:0,7:3,9:3,10:3};function rr(){return Fp("tcf",function(){return{}})}var sr=function(){return new ir(l,{timeoutMs:-1})};
function tr(){var a=rr(),b=sr();kr(b)&&!ur()&&!vr()&&N(124);if(!a.active&&kr(b)){ur()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Zm().active=!0,a.tcString="tcunavailable");yp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)wr(a),zp([K.m.V,K.m.Oa,K.m.W]),Zm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,vr()&&(a.active=!0),!xr(c)||ur()||vr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in qr)qr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(xr(c)){var g={},h;for(h in qr)if(qr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={op:!0};p=p===void 0?{}:p;m=pr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.op)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?nr(n,"1",0):!0:!1;g["1"]=m}else g[h]=nr(c,h,qr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.V]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(zp([K.m.V,K.m.Oa,K.m.W]),Zm().active=!0):(r[K.m.Oa]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":zp([K.m.W]),sp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:yr()||""}))}}else zp([K.m.V,K.m.Oa,K.m.W])})}catch(c){wr(a),zp([K.m.V,K.m.Oa,K.m.W]),Zm().active=!0}}}
function wr(a){a.type="e";a.tcString="tcunavailable"}function xr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function ur(){return l.gtag_enable_tcf_support===!0}function vr(){return rr().enableAdvertiserConsentMode===!0}function yr(){var a=rr();if(a.active)return a.tcString}function zr(){var a=rr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Ar(a){if(!qr.hasOwnProperty(String(a)))return!0;var b=rr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Br=[K.m.V,K.m.ja,K.m.W,K.m.Oa],Cr={},Dr=(Cr[K.m.V]=1,Cr[K.m.ja]=2,Cr);function Er(a){if(a===void 0)return 0;switch(O(a,K.m.Fa)){case void 0:return 1;case !1:return 3;default:return 2}}function Fr(){return E(182)?(E(183)?Ri.vp:Ri.wp).indexOf(to())!==-1&&kc.globalPrivacyControl===!0:to()==="US-CO"&&kc.globalPrivacyControl===!0}
function Gr(a){if(Fr())return!1;var b=Er(a);if(b===3)return!1;switch(hn(K.m.Oa)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Hr(){return kn()||!gn(K.m.V)||!gn(K.m.ja)}function Ir(){var a={},b;for(b in Dr)Dr.hasOwnProperty(b)&&(a[Dr[b]]=hn(b));return"G1"+Ve(a[1]||0)+Ve(a[2]||0)}var Jr={},Kr=(Jr[K.m.V]=0,Jr[K.m.ja]=1,Jr[K.m.W]=2,Jr[K.m.Oa]=3,Jr);
function Lr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Mr(a){for(var b="1",c=0;c<Br.length;c++){var d=b,e,f=Br[c],g=fn.delegatedConsentTypes[f];e=g===void 0?0:Kr.hasOwnProperty(g)?12|Kr[g]:8;var h=Zm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Lr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Lr(m.declare)<<4|Lr(m.default)<<2|Lr(m.update)])}var n=b,p=(Fr()?1:0)<<3,q=(kn()?1:0)<<2,r=Er(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[fn.containerScopedDefaults.ad_storage<<4|fn.containerScopedDefaults.analytics_storage<<2|fn.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(fn.usedContainerScopedDefaults?1:0)<<2|fn.containerScopedDefaults.ad_personalization]}
function Nr(){if(!gn(K.m.W))return"-";for(var a=Object.keys(Mo),b={},c=k(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=fn.corePlatformServices[e]!==!1}for(var f="",g=k(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Mo[m])}(fn.usedCorePlatformServices?fn.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Or(){return vo()||(ur()||vr())&&zr()==="1"?"1":"0"}function Pr(){return(vo()?!0:!(!ur()&&!vr())&&zr()==="1")||!gn(K.m.W)}
function Qr(){var a="0",b="0",c;var d=rr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=rr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;vo()&&(h|=1);zr()==="1"&&(h|=2);ur()&&(h|=4);var m;var n=rr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Zm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Rr(){return to()==="US-CO"};function Sr(){var a=!1;return a};var Tr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Ur(a){a=a===void 0?{}:a;var b=cg.ctid.split("-")[0].toUpperCase(),c={ctid:cg.ctid,Kj:Oj,Oj:Nj,km:tm.ie?2:1,uq:a.Gm,pe:cg.canonicalContainerId};c.pe!==a.Pa&&(c.Pa=a.Pa);var d=Gm();c.wm=d?d.canonicalContainerId:void 0;Vj?(c.Uc=Tr[b],c.Uc||(c.Uc=0)):c.Uc=Xj?13:10;Ij.D?(c.Sc=0,c.Xl=2):Ij.O?c.Sc=1:Sr()?c.Sc=2:c.Sc=3;var e={};e[6]=um;Ij.J===2?e[7]=!0:Ij.J===1&&(e[2]=!0);if(nc){var f=Ik(Ok(nc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Zl=e;return Ye(c,a.uh)}
function Vr(){if(!E(192))return Ur();if(E(193))return Ye({Kj:Oj,Oj:Nj});var a=cg.ctid.split("-")[0].toUpperCase(),b={ctid:cg.ctid,Kj:Oj,Oj:Nj,km:tm.ie?2:1,pe:cg.canonicalContainerId},c=Gm();b.wm=c?c.canonicalContainerId:void 0;Vj?(b.Uc=Tr[a],b.Uc||(b.Uc=0)):b.Uc=Xj?13:10;Ij.D?(b.Sc=0,b.Xl=2):Ij.O?b.Sc=1:Sr()?b.Sc=2:b.Sc=3;var d={};d[6]=um;Ij.J===2?d[7]=!0:Ij.J===1&&(d[2]=!0);if(nc){var e=Ik(Ok(nc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Zl=d;return Ye(b)};function Wr(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Xr={P:{xo:0,Uj:1,vg:2,bk:3,Oh:4,Yj:5,Zj:6,dk:7,Ph:8,kl:9,jl:10,yi:11,ml:12,fh:13,pl:14,Gf:15,uo:16,me:17,Ti:18,Ui:19,Vi:20,Ol:21,Wi:22,Qh:23,kk:24}};Xr.P[Xr.P.xo]="RESERVED_ZERO";Xr.P[Xr.P.Uj]="ADS_CONVERSION_HIT";Xr.P[Xr.P.vg]="CONTAINER_EXECUTE_START";Xr.P[Xr.P.bk]="CONTAINER_SETUP_END";Xr.P[Xr.P.Oh]="CONTAINER_SETUP_START";Xr.P[Xr.P.Yj]="CONTAINER_BLOCKING_END";Xr.P[Xr.P.Zj]="CONTAINER_EXECUTE_END";Xr.P[Xr.P.dk]="CONTAINER_YIELD_END";Xr.P[Xr.P.Ph]="CONTAINER_YIELD_START";Xr.P[Xr.P.kl]="EVENT_EXECUTE_END";
Xr.P[Xr.P.jl]="EVENT_EVALUATION_END";Xr.P[Xr.P.yi]="EVENT_EVALUATION_START";Xr.P[Xr.P.ml]="EVENT_SETUP_END";Xr.P[Xr.P.fh]="EVENT_SETUP_START";Xr.P[Xr.P.pl]="GA4_CONVERSION_HIT";Xr.P[Xr.P.Gf]="PAGE_LOAD";Xr.P[Xr.P.uo]="PAGEVIEW";Xr.P[Xr.P.me]="SNIPPET_LOAD";Xr.P[Xr.P.Ti]="TAG_CALLBACK_ERROR";Xr.P[Xr.P.Ui]="TAG_CALLBACK_FAILURE";Xr.P[Xr.P.Vi]="TAG_CALLBACK_SUCCESS";Xr.P[Xr.P.Ol]="TAG_EXECUTE_END";Xr.P[Xr.P.Wi]="TAG_EXECUTE_START";Xr.P[Xr.P.Qh]="CUSTOM_PERFORMANCE_START";Xr.P[Xr.P.kk]="CUSTOM_PERFORMANCE_END";var Yr=[],Zr={},$r={};var as=["1"];function bs(a){return a.origin!=="null"};function cs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return qg(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function ds(a,b,c,d){if(!es(d))return[];if(Yr.includes("1")){var e;(e=Rc())==null||e.mark("1-"+Xr.P.Qh+"-"+($r["1"]||0))}var f=cs(a,String(b||fs()),c);if(Yr.includes("1")){var g="1-"+Xr.P.kk+"-"+($r["1"]||0),h={start:"1-"+Xr.P.Qh+"-"+($r["1"]||0),end:g},m;(m=Rc())==null||m.mark(g);var n,p,q=(p=(n=Rc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&($r["1"]=($r["1"]||0)+1,Zr["1"]=q+(Zr["1"]||0))}return f}
function gs(a,b,c,d,e){if(es(e)){var f=hs(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=is(f,function(g){return g.Wo},b);if(f.length===1)return f[0];f=is(f,function(g){return g.Yp},c);return f[0]}}}function js(a,b,c,d){var e=fs(),f=window;bs(f)&&(f.document.cookie=a);var g=fs();return e!==g||c!==void 0&&ds(b,g,!1,d).indexOf(c)>=0}
function ks(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!es(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ls(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Up);g=e(g,"samesite",c.kq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ms(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!ns(u,c.path)&&js(v,a,b,c.Dc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return ns(n,c.path)?1:js(g,a,b,c.Dc)?0:1}function os(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return ks(a,b,c)}
function is(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function hs(a,b,c){for(var d=[],e=ds(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Oo:e[f],Po:g.join("."),Wo:Number(n[0])||1,Yp:Number(n[1])||1})}}}return d}function ls(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ps=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,qs=/(^|\.)doubleclick\.net$/i;function ns(a,b){return a!==void 0&&(qs.test(window.document.location.hostname)||b==="/"&&ps.test(a))}function rs(a){if(!a)return 1;var b=a;qg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ss(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function ts(a,b){var c=""+rs(a),d=ss(b);d>1&&(c+="-"+d);return c}
var fs=function(){return bs(window)?window.document.cookie:""},es=function(a){return a&&qg(8)?(Array.isArray(a)?a:[a]).every(function(b){return jn(b)&&gn(b)}):!0},ms=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;qs.test(e)||ps.test(e)||a.push("none");return a};function us(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Wr(a)&2147483647):String(b)}function vs(a){return[us(a),Math.round(ub()/1E3)].join(".")}function ws(a,b,c,d,e){var f=rs(b),g;return(g=gs(a,f,ss(c),d,e))==null?void 0:g.Po};function xs(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||ub())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var ys=["ad_storage","ad_user_data"];function zs(a,b){if(!a)return Ya("TAGGING",32),10;if(b===null||b===void 0||b==="")return Ya("TAGGING",33),11;var c=As(!1);if(c.error!==0)return Ya("TAGGING",34),c.error;if(!c.value)return Ya("TAGGING",35),2;c.value[a]=b;var d=Bs(c);d!==0&&Ya("TAGGING",36);return d}
function Cs(a){if(!a)return Ya("TAGGING",27),{error:10};var b=As();if(b.error!==0)return Ya("TAGGING",29),b;if(!b.value)return Ya("TAGGING",30),{error:2};if(!(a in b.value))return Ya("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(Ya("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function As(a){a=a===void 0?!0:a;if(!gn(ys))return Ya("TAGGING",43),{error:3};try{if(!l.localStorage)return Ya("TAGGING",44),{error:1}}catch(f){return Ya("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=l.localStorage.getItem("_gcl_ls")}catch(f){return Ya("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return Ya("TAGGING",47),{error:12}}}catch(f){return Ya("TAGGING",48),{error:8}}if(b.schema!=="gcl")return Ya("TAGGING",49),{error:4};
if(b.version!==1)return Ya("TAGGING",50),{error:5};try{var e=Ds(b);a&&e&&Bs({value:b,error:0})}catch(f){return Ya("TAGGING",48),{error:8}}return{value:b,error:0}}
function Ds(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,Ya("TAGGING",54),!0}else{for(var c=!1,d=k(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Ds(a[e.value])||c;return c}return!1}
function Bs(a){if(a.error)return a.error;if(!a.value)return Ya("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return Ya("TAGGING",52),6}try{l.localStorage.setItem("_gcl_ls",c)}catch(d){return Ya("TAGGING",53),7}return 0};function Es(){if(!Fs())return-1;var a=Gs();return a!==-1&&Hs(a+1)?a+1:-1}function Gs(){if(!Fs())return-1;var a=Cs("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Fs(){return gn(["ad_storage","ad_user_data"])?qg(11):!1}
function Hs(a,b){b=b||{};var c=ub();return zs("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(xs(b,c,!0).expires)})===0?!0:!1};var Is;function Js(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ks,d=Ls,e=Ms();if(!e.init){Cc(y,"mousedown",a);Cc(y,"keyup",a);Cc(y,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ns(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ms().decorators.push(f)}
function Os(a,b,c){for(var d=Ms().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==y.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&xb(e,g.callback())}}return e}
function Ms(){var a=oc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ps=/(.*?)\*(.*?)\*(.*)/,Qs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Rs=/^(?:www\.|m\.|amp\.)+/,Ss=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ts(a){var b=Ss.exec(a);if(b)return{Dj:b[1],query:b[2],fragment:b[3]}}function Us(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Vs(a,b){var c=[kc.userAgent,(new Date).getTimezoneOffset(),kc.userLanguage||kc.language,Math.floor(ub()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Is)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Is=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Is[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ws(a){return function(b){var c=Ok(l.location.href),d=c.search.replace("?",""),e=Fk(d,"_gl",!1,!0)||"";b.query=Xs(e)||{};var f=Ik(c,"fragment"),g;var h=-1;if(zb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Xs(g||"")||{};a&&Ys(c,d,f)}}function Zs(a,b){var c=Us(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ys(a,b,c){function d(g,h){var m=Zs("_gl",g);m.length&&(m=h+m);return m}if(jc&&jc.replaceState){var e=Us("_gl");if(e.test(b)||e.test(c)){var f=Ik(a,"path");b=d(b,"?");c=d(c,"#");jc.replaceState({},"",""+f+b+c)}}}function $s(a,b){var c=Ws(!!b),d=Ms();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(xb(e,f.query),a&&xb(e,f.fragment));return e}
var Xs=function(a){try{var b=at(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Va(d[e+1]);c[f]=g}Ya("TAGGING",6);return c}}catch(h){Ya("TAGGING",8)}};function at(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ps.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Vs(h,p)){m=!0;break a}m=!1}if(m)return h;Ya("TAGGING",7)}}}
function bt(a,b,c,d,e){function f(p){p=Zs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ts(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Dj+h+m}
function ct(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Ua(String(x))))}var z=v.join("*");u=["1",Vs(z),z].join("*");d?(qg(3)||qg(1)||!p)&&dt("_gl",u,a,p,q):et("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Os(b,1,d),f=Os(b,2,d),g=Os(b,4,d),h=Os(b,3,d);c(e,!1,!1);c(f,!0,!1);qg(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ft(m,h[m],a)}function ft(a,b,c){c.tagName.toLowerCase()==="a"?et(a,b,c):c.tagName.toLowerCase()==="form"&&dt(a,b,c)}function et(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!qg(5)||d)){var h=l.location.href,m=Ts(c.href),n=Ts(h);g=!(m&&n&&m.Dj===n.Dj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=bt(a,b,c.href,d,e);Zb.test(p)&&(c.href=p)}}
function dt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=bt(a,b,f,d,e);Zb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=y.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ks(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||ct(e,e.hostname)}}catch(g){}}function Ls(a){try{var b=a.getAttribute("action");if(b){var c=Ik(Ok(b),"host");ct(a,c)}}catch(d){}}function gt(a,b,c,d){Js();var e=c==="fragment"?2:1;d=!!d;Ns(a,b,e,d,!1);e===2&&Ya("TAGGING",23);d&&Ya("TAGGING",24)}
function ht(a,b){Js();Ns(a,[Kk(l.location,"host",!0)],b,!0,!0)}function it(){var a=y.location.hostname,b=Qs.exec(y.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Rs,""),m=e.replace(Rs,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function jt(a,b){return a===!1?!1:a||b||it()};var kt=["1"],lt={},mt={};function nt(a,b){b=b===void 0?!0:b;var c=ot(a.prefix);if(lt[c])pt(a);else if(qt(c,a.path,a.domain)){var d=mt[ot(a.prefix)]||{id:void 0,Gh:void 0};b&&rt(a,d.id,d.Gh);pt(a)}else{var e=Qk("auiddc");if(e)Ya("TAGGING",17),lt[c]=e;else if(b){var f=ot(a.prefix),g=vs();st(f,g,a);qt(c,a.path,a.domain);pt(a,!0)}}}
function pt(a,b){if((b===void 0?0:b)&&Fs()){var c=As(!1);c.error!==0?Ya("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Bs(c)!==0&&Ya("TAGGING",41)):Ya("TAGGING",40):Ya("TAGGING",39)}gn(["ad_storage","ad_user_data"])&&qg(10)&&Gs()===-1&&Hs(0,a)}function rt(a,b,c){var d=ot(a.prefix),e=lt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(ub()/1E3)));st(d,h,a,g*1E3)}}}}
function st(a,b,c,d){var e;e=["1",ts(c.domain,c.path),b].join(".");var f=xs(c,d);f.Dc=tt();os(a,e,f)}function qt(a,b,c){var d=ws(a,b,c,kt,tt());if(!d)return!1;ut(a,d);return!0}function ut(a,b){var c=b.split(".");c.length===5?(lt[a]=c.slice(0,2).join("."),mt[a]={id:c.slice(2,4).join("."),Gh:Number(c[4])||0}):c.length===3?mt[a]={id:c.slice(0,2).join("."),Gh:Number(c[2])||0}:lt[a]=b}function ot(a){return(a||"_gcl")+"_au"}
function vt(a){function b(){gn(c)&&a()}var c=tt();nn(function(){b();gn(c)||on(b,c)},c)}function wt(a){var b=$s(!0),c=ot(a.prefix);vt(function(){var d=b[c];if(d){ut(c,d);var e=Number(lt[c].split(".")[1])*1E3;if(e){Ya("TAGGING",16);var f=xs(a,e);f.Dc=tt();var g=["1",ts(a.domain,a.path),d].join(".");os(c,g,f)}}})}function xt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=ws(a,e.path,e.domain,kt,tt());h&&(g[a]=h);return g};vt(function(){gt(f,b,c,d)})}
function tt(){return["ad_storage","ad_user_data"]};function zt(a){for(var b=[],c=y.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Rj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function At(a,b){var c=zt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Rj]||(d[c[e].Rj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Rj].push(g)}}return d};var Bt={},Ct=(Bt.k={da:/^[\w-]+$/},Bt.b={da:/^[\w-]+$/,Lj:!0},Bt.i={da:/^[1-9]\d*$/},Bt.h={da:/^\d+$/},Bt.t={da:/^[1-9]\d*$/},Bt.d={da:/^[A-Za-z0-9_-]+$/},Bt.j={da:/^\d+$/},Bt.u={da:/^[1-9]\d*$/},Bt.l={da:/^[01]$/},Bt.o={da:/^[1-9]\d*$/},Bt.g={da:/^[01]$/},Bt.s={da:/^.+$/},Bt);var Dt={},Ht=(Dt[5]={Lh:{2:Et},wj:"2",wh:["k","i","b","u"]},Dt[4]={Lh:{2:Et,GCL:Ft},wj:"2",wh:["k","i","b"]},Dt[2]={Lh:{GS2:Et,GS1:Gt},wj:"GS2",wh:"sogtjlhd".split("")},Dt);function It(a,b,c){var d=Ht[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Lh[e];if(f)return f(a,b)}}}
function Et(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ht[b];if(f){for(var g=f.wh,h=k(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Ct[p];r&&(r.Lj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Jt(a,b,c){var d=Ht[b];if(d)return[d.wj,c||"1",Kt(a,b)].join(".")}
function Kt(a,b){var c=Ht[b];if(c){for(var d=[],e=k(c.wh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Ct[g];if(h){var m=a[g];if(m!==void 0)if(h.Lj&&Array.isArray(m))for(var n=k(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ft(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Gt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Lt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Mt(a,b,c){if(Ht[b]){for(var d=[],e=ds(a,void 0,void 0,Lt.get(b)),f=k(e),g=f.next();!g.done;g=f.next()){var h=It(g.value,b,c);h&&d.push(Nt(h))}return d}}function Ot(a,b,c,d,e){d=d||{};var f=ts(d.domain,d.path),g=Jt(b,c,f);if(!g)return 1;var h=xs(d,e,void 0,Lt.get(c));return os(a,g,h)}function Pt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Nt(a){for(var b=k(Object.keys(a)),c=b.next(),d={};!c.done;d={Rf:void 0},c=b.next()){var e=c.value,f=a[e];d.Rf=Ct[e];d.Rf?d.Rf.Lj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Pt(h,g.Rf)}}(d)):void 0:typeof f==="string"&&Pt(f,d.Rf)||(a[e]=void 0):a[e]=void 0}return a};var Qt=function(){this.value=0};Qt.prototype.set=function(a){return this.value|=1<<a};var Rt=function(a,b){b<=0||(a.value|=1<<b-1)};Qt.prototype.get=function(){return this.value};Qt.prototype.clear=function(a){this.value&=~(1<<a)};Qt.prototype.clearAll=function(){this.value=0};Qt.prototype.equals=function(a){return this.value===a.value};function St(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Tt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]}
function Ut(a){if(!a||a.length<50||a.length>200)return!1;var b=St(a),c;if(b)a:{if(b&&b.length!==0){var d=0;try{for(;d<b.length;){var e=Tt(b,d);if(e===void 0)break;var f=k(e),g=f.next().value,h=f.next().value,m=g,n=h,p=m&7;if(m>>3===16382){if(p!==0)break;var q=Tt(b,n);if(q===void 0)break;c=k(q).next().value===1;break a}var r;b:{var t=void 0;switch(p){case 0:r=(t=Tt(b,n))==null?void 0:t[1];break b;case 1:r=n+8;break b;case 2:var u=Tt(b,n);if(u===void 0)break;var v=k(u),w=v.next().value;r=v.next().value+
w;break b;case 5:r=n+4;break b}r=void 0}var x=r;if(x===void 0||x>b.length)break;d=x}}catch(z){}}c=!1}else c=!1;return c};function Vt(){var a=String,b=l.location.hostname,c=l.location.pathname,d=b=Hb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Hb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Wr((""+b+e).toLowerCase()))};var Wt={},Xt=(Wt.gclid=!0,Wt.dclid=!0,Wt.gbraid=!0,Wt.wbraid=!0,Wt),Yt=/^\w+$/,Zt=/^[\w-]+$/,$t={},au=($t.aw="_aw",$t.dc="_dc",$t.gf="_gf",$t.gp="_gp",$t.gs="_gs",$t.ha="_ha",$t.ag="_ag",$t.gb="_gb",$t),bu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,cu=/^www\.googleadservices\.com$/;function du(){return["ad_storage","ad_user_data"]}function eu(a){return!qg(8)||gn(a)}function fu(a,b){function c(){var d=eu(b);d&&a();return d}nn(function(){c()||on(c,b)},b)}
function gu(a){return hu(a).map(function(b){return b.gclid})}function iu(a){return ju(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function ju(a){var b=ku(a.prefix),c=lu("gb",b),d=lu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=hu(c).map(e("gb")),g=mu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function nu(a,b,c,d,e,f){var g=jb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Ed=f),g.labels=ou(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Ed:f})}function mu(a){for(var b=Mt(a,5)||[],c=[],d=k(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=pu(f);if(n){var p=void 0;qg(9)&&(p=f.u);nu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function hu(a){for(var b=[],c=ds(a,y.cookie,void 0,du()),d=k(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);if(f!=null){var g=f;nu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ru(b)}function su(a,b){for(var c=[],d=k(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=k(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function tu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=k(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Na&&b.Na&&h.Na.equals(b.Na)&&(e=h)}if(d){var m,n,p=(m=d.Na)!=null?m:new Qt,q=(n=b.Na)!=null?n:new Qt;p.value|=q.value;d.Na=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Ed=b.Ed);d.labels=su(d.labels||[],b.labels||[]);d.Bb=su(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function uu(a){if(!a)return new Qt;var b=new Qt;if(a===1)return Rt(b,2),Rt(b,3),b;Rt(b,a);return b}
function vu(){var a=Cs("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Qt;typeof e==="number"?g=uu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Na:g,Bb:[2]}}catch(h){return null}}
function wu(){var a=Cs("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Zt))return b;var f=new Qt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Na:f,Bb:[2]});return b},[])}catch(b){return null}}
function xu(a){for(var b=[],c=ds(a,y.cookie,void 0,du()),d=k(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);f!=null&&(f.Ed=void 0,f.Na=new Qt,f.Bb=[1],tu(b,f))}var g=vu();g&&(g.Ed=void 0,g.Bb=g.Bb||[2],tu(b,g));if(qg(14)){var h=wu();if(h)for(var m=k(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Ed=void 0;p.Bb=p.Bb||[2];tu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ru(b)}
function ou(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ku(a){return a&&typeof a==="string"&&a.match(Yt)?a:"_gcl"}function yu(a,b){if(a){var c={value:a,Na:new Qt};Rt(c.Na,b);return c}}
function zu(a,b,c,d){var e=Ok(a),f=Ik(e,"query",!1,void 0,"gclsrc"),g=yu(Ik(e,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!g||!f)){var h=e.hash.replace("#","");g||(g=yu(Fk(h,"gclid",!1),3));f||(f=Fk(h,"gclsrc",!1))}var m;if(d&&!Ut((m=g)==null?void 0:m.value)){var n;a:{for(var p=Gk(Ik(e,"query")),q=k(Object.keys(p)),r=q.next();!r.done;r=q.next()){var t=r.value;if(!Xt[t]){var u=p[t][0]||"";if(Ut(u)){n=u;break a}}}n=void 0}var v=n,w;v&&v!==((w=g)==null?void 0:w.value)&&(g=yu(v,7))}return!g||f!==void 0&&
f!=="aw"&&f!=="aw.ds"?[]:[g]}function Au(a,b){var c=Ok(a),d=Ik(c,"query",!1,void 0,"gclid"),e=Ik(c,"query",!1,void 0,"gclsrc"),f=Ik(c,"query",!1,void 0,"wbraid");f=Fb(f);var g=Ik(c,"query",!1,void 0,"gbraid"),h=Ik(c,"query",!1,void 0,"gad_source"),m=Ik(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Fk(n,"gclid",!1);e=e||Fk(n,"gclsrc",!1);f=f||Fk(n,"wbraid",!1);g=g||Fk(n,"gbraid",!1);h=h||Fk(n,"gad_source",!1)}return Bu(d,e,m,f,g,h)}
function Cu(){return Au(l.location.href,!0)}
function Bu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Du(a){for(var b=Cu(),c=!0,d=k(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Au(l.document.referrer,!1),b.gad_source=void 0);Eu(b,!1,a)}
function Fu(a){Du(a);var b=zu(l.location.href,!0,!1,qg(15)?Gu(Hu()):!1);b.length||(b=zu(l.document.referrer,!1,!0,!1));if(b.length){var c=b[0];a=a||{};var d=ub(),e=xs(a,d,!0),f=du(),g=function(){eu(f)&&e.expires!==void 0&&zs("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Na.get()},expires:Number(e.expires)})};nn(function(){g();eu(f)||on(g,f)},f)}}
function Iu(a,b,c){c=c||{};var d=ub(),e=xs(c,d,!0),f=du(),g=function(){if(eu(f)&&e.expires!==void 0){var h=wu()||[];tu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Na:uu(b)},!0);zs("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Na?m.Na.get():0},expires:Number(m.expires)}}))}};nn(function(){eu(f)?g():on(g,f)},f)}
function Eu(a,b,c,d,e){c=c||{};e=e||[];var f=ku(c.prefix),g=d||ub(),h=Math.round(g/1E3),m=du(),n=!1,p=!1,q=function(){if(eu(m)){var r=xs(c,g,!0);r.Dc=m;for(var t=function(L,V){var Q=lu(L,f);Q&&(os(Q,V,r),L!=="gb"&&(n=!0))},u=function(L){var V=["GCL",h,L];e.length>0&&V.push(e.join("."));return V.join(".")},v=k(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],B=lu("gb",f);!b&&hu(B).some(function(L){return L.gclid===z&&L.labels&&
L.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&eu("ad_storage")&&(p=!0,!n)){var C=a.gbraid,F=lu("ag",f);if(b||!mu(F).some(function(L){return L.gclid===C&&L.labels&&L.labels.length>0})){var G={},I=(G.k=C,G.i=""+h,G.b=e,G);Ot(F,I,5,c,g)}}Ju(a,f,g,c)};nn(function(){q();eu(m)||on(q,m)},m)}
function Ju(a,b,c,d){if(a.gad_source!==void 0&&eu("ad_storage")){if(qg(4)){var e=Qc();if(e==="r"||e==="h")return}var f=a.gad_source,g=lu("gs",b);if(g){var h=Math.floor((ub()-(Pc()||0))/1E3),m;if(qg(9)){var n=Vt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ot(g,m,5,d,c)}}}
function Ku(a,b){var c=$s(!0);fu(function(){for(var d=ku(b.prefix),e=0;e<a.length;++e){var f=a[e];if(au[f]!==void 0){var g=lu(f,d),h=c[g];if(h){var m=Math.min(Lu(h),ub()),n;b:{for(var p=m,q=ds(g,y.cookie,void 0,du()),r=0;r<q.length;++r)if(Lu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=xs(b,m,!0);t.Dc=du();os(g,h,t)}}}}Eu(Bu(c.gclid,c.gclsrc),!1,b)},du())}
function Mu(a){var b=["ag"],c=$s(!0),d=ku(a.prefix);fu(function(){for(var e=0;e<b.length;++e){var f=lu(b[e],d);if(f){var g=c[f];if(g){var h=It(g,5);if(h){var m=pu(h);m||(m=ub());var n;a:{for(var p=m,q=Mt(f,5),r=0;r<q.length;++r)if(pu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ot(f,h,5,a,m)}}}}},["ad_storage"])}function lu(a,b){var c=au[a];if(c!==void 0)return b+c}function Lu(a){return Nu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function pu(a){return a?(Number(a.i)||0)*1E3:0}function qu(a){var b=Nu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Nu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Zt.test(a[2])?[]:a}
function Ou(a,b,c,d,e){if(Array.isArray(b)&&bs(l)){var f=ku(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=lu(a[m],f);if(n){var p=ds(n,y.cookie,void 0,du());p.length&&(h[n]=p.sort()[p.length-1])}}return h};fu(function(){gt(g,b,c,d)},du())}}
function Pu(a,b,c,d){if(Array.isArray(a)&&bs(l)){var e=["ag"],f=ku(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=lu(e[m],f);if(!n)return{};var p=Mt(n,5);if(p.length){var q=p.sort(function(r,t){return pu(t)-pu(r)})[0];h[n]=Jt(q,5)}}return h};fu(function(){gt(g,a,b,c)},["ad_storage"])}}function ru(a){return a.filter(function(b){return Zt.test(b.gclid)})}
function Qu(a,b){if(bs(l)){for(var c=ku(b.prefix),d={},e=0;e<a.length;e++)au[a[e]]&&(d[a[e]]=au[a[e]]);fu(function(){nb(d,function(f,g){var h=ds(c+g,y.cookie,void 0,du());h.sort(function(t,u){return Lu(u)-Lu(t)});if(h.length){var m=h[0],n=Lu(m),p=Nu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Nu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Eu(q,!0,b,n,p)}})},du())}}
function Ru(a){var b=["ag"],c=["gbraid"];fu(function(){for(var d=ku(a.prefix),e=0;e<b.length;++e){var f=lu(b[e],d);if(!f)break;var g=Mt(f,5);if(g.length){var h=g.sort(function(q,r){return pu(r)-pu(q)})[0],m=pu(h),n=h.b,p={};p[c[e]]=h.k;Eu(p,!0,a,m,n)}}},["ad_storage"])}function Su(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Tu(a){function b(h,m,n){n&&(h[m]=n)}if(kn()){var c=Cu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:$s(!1)._gs);if(Su(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ht(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ht(function(){return g},1)}}}
function Uu(a){if(!qg(1))return null;var b=$s(!0).gad_source;if(b!=null)return l.location.hash="",b;if(qg(2)){var c=Ok(l.location.href);b=Ik(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=Cu();if(Su(d,a))return"0"}return null}function Vu(a){var b=Uu(a);b!=null&&ht(function(){var c={};return c.gad_source=b,c},4)}
function Wu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Xu(a,b,c,d){var e=[];c=c||{};if(!eu(du()))return e;var f=hu(a),g=Wu(e,f,b);if(g.length&&!d)for(var h=k(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=xs(c,p,!0);r.Dc=du();os(a,q,r)}return e}
function Yu(a,b){var c=[];b=b||{};var d=ju(b),e=Wu(c,d,a);if(e.length)for(var f=k(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ku(b.prefix),n=lu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Ot(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=xs(b,u,!0);B.Dc=du();os(n,z,B)}}return c}
function Zu(a,b){var c=ku(b),d=lu(a,c);if(!d)return 0;var e;e=a==="ag"?mu(d):hu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function $u(a){for(var b=0,c=k(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function av(a){var b=Math.max(Zu("aw",a),$u(eu(du())?At():{})),c=Math.max(Zu("gb",a),$u(eu(du())?At("_gac_gb",!0):{}));c=Math.max(c,Zu("ag",a));return c>b}
function Gu(a){return bu.test(a)||cu.test(a)}function Hu(){return y.referrer?Ik(Ok(y.referrer),"host"):""};function pv(){return Fp("dedupe_gclid",function(){return vs()})};var qv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,rv=/^www.googleadservices.com$/;function sv(a){a||(a=tv());return a.Dq?!1:a.Dp||a.Ep||a.Hp||a.Fp||a.Wf||a.np||a.Gp||a.tp?!0:!1}function tv(){var a={},b=$s(!0);a.Dq=!!b._up;var c=Cu();a.Dp=c.aw!==void 0;a.Ep=c.dc!==void 0;a.Hp=c.wbraid!==void 0;a.Fp=c.gbraid!==void 0;a.Gp=c.gclsrc==="aw.ds";a.Wf=dv().Wf;var d=y.referrer?Ik(Ok(y.referrer),"host"):"";a.tp=qv.test(d);a.np=rv.test(d);return a};function uv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function vv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function wv(){return["ad_storage","ad_user_data"]}function xv(a){if(E(38)&&!ho(co.aa.Al)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{uv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(go(co.aa.Al,function(d){d.gclid&&Iu(d.gclid,5,a)}),vv(c)||N(178))})}catch(c){N(177)}};nn(function(){eu(wv())?b():on(b,wv())},wv())}};var yv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function zv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?go(co.aa.If,{gadSource:a.data.gadSource}):N(173)}
function Av(a,b){if(E(a)){if(ho(co.aa.If))return N(176),co.aa.If;if(ho(co.aa.Cl))return N(170),co.aa.If;var c=Il();if(!c)N(171);else if(c.opener){var d=function(g){if(yv.includes(g.origin)){a===119?zv(g):a===200&&(zv(g),g.data.gclid&&Iu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);cr(c,"message",d)}else N(172)};if(br(c,"message",d)){go(co.aa.Cl,!0);for(var e=k(yv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);N(174);return co.aa.If}N(175)}}}
;var Bv=function(){this.D=this.gppString=void 0};Bv.prototype.reset=function(){this.D=this.gppString=void 0};var Cv=new Bv;var Dv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Ev=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Fv=/^\d+\.fls\.doubleclick\.net$/,Gv=/;gac=([^;?]+)/,Hv=/;gacgb=([^;?]+)/;
function Iv(a,b){if(Fv.test(y.location.host)){var c=y.location.href.match(b);return c&&c.length===2&&c[1].match(Dv)?Hk(c[1])||"":""}for(var d=[],e=k(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Jv(a,b,c){for(var d=eu(du())?At("_gac_gb",!0):{},e=[],f=!1,g=k(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Xu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{mp:f?e.join(";"):"",lp:Iv(d,Hv)}}function Kv(a){var b=y.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Ev)?b[1]:void 0}
function Lv(a){var b=qg(9),c={},d,e,f;Fv.test(y.location.host)&&(d=Kv("gclgs"),e=Kv("gclst"),b&&(f=Kv("gcllp")));if(d&&e&&(!b||f))c.Ah=d,c.Ch=e,c.Bh=f;else{var g=ub(),h=mu((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Ed}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.Ah=m.join("."),c.Ch=n.join("."),b&&p.length>0&&(c.Bh=p.join(".")))}return c}
function Mv(a,b,c,d){d=d===void 0?!1:d;if(Fv.test(y.location.host)){var e=Kv(c);if(e){if(d){var f=new Qt;Rt(f,2);Rt(f,3);return e.split(".").map(function(h){return{gclid:h,Na:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?xu(g):hu(g)}if(b==="wbraid")return hu((a||"_gcl")+"_gb");if(b==="braids")return ju({prefix:a})}return[]}function Nv(a){return Fv.test(y.location.host)?!(Kv("gclaw")||Kv("gac")):av(a)}
function Ov(a,b,c){var d;d=c?Yu(a,b):Xu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Pv(){var a=l.__uspapi;if(eb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function bw(a){var b=O(a.F,K.m.Lc),c=O(a.F,K.m.Kc);b&&!c?(a.eventName!==K.m.ra&&a.eventName!==K.m.Rd&&N(131),a.isAborted=!0):!b&&c&&(N(132),a.isAborted=!0)}function cw(a){var b=up(K.m.V)?Ep.pscdl:"denied";b!=null&&W(a,K.m.Ig,b)}function dw(a){var b=Gl(!0);W(a,K.m.Jc,b)}function ew(a){Rr()&&W(a,K.m.Zd,1)}
function Tv(){var a=y.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Hk(a.substring(0,b))===void 0;)b--;return Hk(a.substring(0,b))||""}function fw(a){gw(a,Np.xf.Ym,O(a.F,K.m.rb))}function gw(a,b,c){Sv(a,K.m.rd)||W(a,K.m.rd,{});Sv(a,K.m.rd)[b]=c}function hw(a){S(a,P.C.Lf,Ym.Z.Ea)}function iw(a){var b=ab("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,K.m.bf,b),Za())}function jw(a){var b=a.F.getMergedValues(K.m.qc);b&&a.mergeHitDataForKey(K.m.qc,b)}
function kw(a,b){b=b===void 0?!1:b;if(E(108)){var c=R(a,P.C.Jf);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,P.C.Tj,!1),b||!lw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,P.C.Tj,!0)}}function mw(a){E(166)&&el&&(Un=!0,a.eventName===K.m.ra?ao(a.F,a.target.id):(R(a,P.C.Md)||(Xn[a.target.id]=!0),Lp(R(a,P.C.cb))))};function ww(a,b,c,d){var e=yc(),f;if(e===1)a:{var g=Zj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=y.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==l.location.protocol?a:b)+c};function Iw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Sv(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Sv(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.F,b)},zb:function(){return a},getHitKeys:function(){return Object.keys(a.D)},getMergedValues:function(b){return a.F.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return bd(c)?a.mergeHitDataForKey(b,c):!1}}};function Nw(a,b){return arguments.length===1?Ow("set",a):Ow("set",a,b)}function Pw(a,b){return arguments.length===1?Ow("config",a):Ow("config",a,b)}function Qw(a,b,c){c=c||{};c[K.m.kd]=a;return Ow("event",b,c)}function Ow(){return arguments};var Sw=function(){this.messages=[];this.D=[]};Sw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};Sw.prototype.listen=function(a){this.D.push(a)};
Sw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Sw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Tw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.C.cb]=cg.canonicalContainerId;Uw().enqueue(a,b,c)}
function Vw(){var a=Ww;Uw().listen(a)}function Uw(){return Fp("mb",function(){return new Sw})};var Xw,Yw=!1;function Zw(){Yw=!0;Xw=Xw||{}}function $w(a){Yw||Zw();return Xw[a]};function ax(){var a=l.screen;return{width:a?a.width:0,height:a?a.height:0}}
function bx(a){if(y.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!l.getComputedStyle)return!0;var c=l.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=l.getComputedStyle(d,null))}return!1}
var dx=function(a){var b=cx(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},cx=function(){var a=y.body,b=y.documentElement||a&&a.parentElement,c,d;if(y.compatMode&&y.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var gx=function(a){if(ex){if(a>=0&&a<fx.length&&fx[a]){var b;(b=fx[a])==null||b.disconnect();fx[a]=void 0}}else l.clearInterval(a)},jx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(ex){var e=!1;A(function(){e||hx(a,b,c)()});return ix(function(f){e=!0;for(var g={cg:0};g.cg<f.length;g={cg:g.cg},g.cg++)A(function(h){return function(){a(f[h.cg])}}(g))},
b,c)}return l.setInterval(hx(a,b,c),1E3)},hx=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:ub()};A(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=dx(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),f[h]++;
else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},ix=function(a,b,c){for(var d=new l.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<fx.length;f++)if(!fx[f])return fx[f]=d,f;return fx.push(d)-1},fx=[],ex=!(!l.IntersectionObserver||!l.IntersectionObserverEntry);var Zf;var oy=Number('')||5,py=Number('')||50,qy=kb();
var sy=function(a,b){a&&(ry("sid",a.targetId,b),ry("cc",a.clientCount,b),ry("tl",a.totalLifeMs,b),ry("hc",a.heartbeatCount,b),ry("cl",a.clientLifeMs,b))},ry=function(a,b,c){b!=null&&c.push(a+"="+b)},ty=function(){var a=y.referrer;if(a){var b;return Ik(Ok(a),"host")===((b=l.location)==null?void 0:b.host)?1:2}return 0},uy="https://"+Li(21,"www.googletagmanager.com")+"/a?",wy=function(){this.T=vy;this.O=0};wy.prototype.J=function(a,b,c,d){var e=ty(),f,
g=[];f=l===l.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&ry("si",a.eg,g);ry("m",0,g);ry("iss",f,g);ry("if",c,g);sy(b,g);d&&ry("fm",encodeURIComponent(d.substring(0,py)),g);this.R(g);};wy.prototype.D=function(a,b,c,d,e){var f=[];ry("m",1,f);ry("s",a,f);ry("po",ty(),f);b&&(ry("st",b.state,f),ry("si",b.eg,f),ry("sm",b.og,f));sy(c,f);ry("c",d,f);e&&ry("fm",encodeURIComponent(e.substring(0,
py)),f);this.R(f);};wy.prototype.R=function(a){a=a===void 0?[]:a;!dl||this.O>=oy||(ry("pid",qy,a),ry("bc",++this.O,a),a.unshift("ctid="+cg.ctid+"&t=s"),this.T(""+uy+a.join("&")))};var xy=Number('')||500,yy=Number('')||5E3,zy=Number('20')||10,Ay=Number('')||5E3;function By(a){return a.performance&&a.performance.now()||Date.now()}
var Cy=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{sm:function(){},tm:function(){},rm:function(){},onFailure:function(){}}:g;this.Do=e;this.D=f;this.O=g;this.ia=this.la=this.heartbeatCount=this.Co=0;this.ph=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.eg=By(this.D);this.og=By(this.D);this.T=10};d.prototype.init=function(){this.R(1);this.Ca()};d.prototype.getState=function(){return{state:this.state,
eg:Math.round(By(this.D)-this.eg),og:Math.round(By(this.D)-this.og)}};d.prototype.R=function(e){this.state!==e&&(this.state=e,this.og=By(this.D))};d.prototype.Rl=function(){return String(this.Co++)};d.prototype.Ca=function(){var e=this;this.heartbeatCount++;this.ab({type:0,clientId:this.id,requestId:this.Rl(),maxDelay:this.rh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ia++,f.isDead||e.ia>zy){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.R(4);e.Ao();var m,n;(n=(m=e.O).rm)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.R(3),e.Tl();else{if(e.heartbeatCount>f.stats.heartbeatCount+zy){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.R(2);if(r!==2)if(e.ph){var t,u;(u=(t=e.O).tm)==null||u.call(t)}else{e.ph=!0;var v,w;(w=(v=e.O).sm)==null||w.call(v)}e.ia=0;e.Eo();e.Tl()}}})};d.prototype.rh=function(){return this.state===2?
yy:xy};d.prototype.Tl=function(){var e=this;this.D.setTimeout(function(){e.Ca()},Math.max(0,this.rh()-(By(this.D)-this.la)))};d.prototype.Ho=function(e,f,g){var h=this;this.ab({type:1,clientId:this.id,requestId:this.Rl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.ab=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.Ff(r,7)},(n=e.maxDelay)!=null?n:Ay),q={request:e,Fm:f,Am:h,Tp:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.la=By(this.D);e.Am=!1;this.Do(e.request)};d.prototype.Eo=function(){for(var e=k(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.Am&&this.sendRequest(g)}};d.prototype.Ao=function(){for(var e=
k(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.Ff(this.J[f.value],this.T)};d.prototype.Ff=function(e,f){this.Gb(e);var g=e.request;g.failure={failureType:f};e.Fm(g)};d.prototype.Gb=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.Tp)};d.prototype.Bp=function(e){this.la=By(this.D);var f=this.J[e.requestId];if(f)this.Gb(f),f.Fm(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,l,b);return c};var Dy;
var Ey=function(){Dy||(Dy=new wy);return Dy},vy=function(a){wn(yn(Ym.Z.Oc),function(){Bc(a)})},Fy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Gy=function(a){var b=a,c=Ij.Ca;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Hy=function(a){var b=ho(co.aa.Jl);return b&&b[a]},Iy=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.R=!1;this.ia=null;this.initTime=c;this.D=15;this.O=this.Ro(a);l.setTimeout(function(){f.initialize()},1E3);A(function(){f.Lp(a,b,e)})};ba=Iy.prototype;ba.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),eg:this.initTime,og:Math.round(ub())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.Ho(a,b,c)};ba.getState=function(){return this.O.getState().state};ba.Lp=function(a,b,c){var d=l.location.origin,e=this,
f=zc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Fy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});zc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.Bp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};ba.Ro=function(a){var b=this,c=Cy(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{sm:function(){b.R=!0;b.J.J(c.getState(),c.stats)},tm:function(){},rm:function(d){b.R?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};ba.initialize=function(){this.T||this.O.init();this.T=!0};function Jy(){var a=bg(Zf.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ky(a,b){var c=Math.round(ub());b=b===void 0?!1:b;var d=l.location.origin;if(!d||!Jy()||E(168))return;hk()&&(a=""+d+gk()+"/_/service_worker");var e=Gy(a);if(e===null||Hy(e.origin))return;if(!lc()){Ey().J(void 0,void 0,6);return}var f=new Iy(e,!!a,c||Math.round(ub()),Ey(),b);io(co.aa.Jl)[e.origin]=f;}
var Ly=function(a,b,c,d){var e;if((e=Hy(a))==null||!e.delegate){var f=lc()?16:6;Ey().D(f,void 0,void 0,b.commandType);d({failureType:f});return}Hy(a).delegate(b,c,d);};
function My(a,b,c,d,e){var f=Gy();if(f===null){d(lc()?16:6);return}var g,h=(g=Hy(f.origin))==null?void 0:g.initTime,m=Math.round(ub()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Ly(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ny(a,b,c,d){var e=Gy(a);if(e===null){d("_is_sw=f"+(lc()?16:6)+"te");return}var f=b?1:0,g=Math.round(ub()),h,m=(h=Hy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Ly(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:l.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Hy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Oy(a){if(E(10)||hk()||Ij.O||Wk(a.F)||E(168))return;Ky(void 0,E(131));};var Py="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Qy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ry(){var a=l.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Sy(){var a,b;return(b=(a=l.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Ty(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Uy(){var a=l;if(!Ty(a))return null;var b=Qy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Py).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Wy=function(a,b){if(a)for(var c=Vy(a),d=k(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}},Vy=function(a){var b={};b[K.m.lf]=a.architecture;b[K.m.nf]=a.bitness;a.fullVersionList&&(b[K.m.pf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.qf]=a.mobile?"1":"0";b[K.m.rf]=a.model;b[K.m.tf]=a.platform;b[K.m.uf]=a.platformVersion;b[K.m.vf]=a.wow64?"1":"0";return b},Xy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=Ry();if(d)c(d);else{var e=Sy();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=l.setTimeout(function(){c.fg||(c.fg=!0,N(106),c(null,Error("Timeout")))},b);e.then(function(g){c.fg||(c.fg=!0,N(104),l.clearTimeout(f),c(g))}).catch(function(g){c.fg||(c.fg=!0,N(105),l.clearTimeout(f),c(null,g))})}else c(null)}},Zy=function(){if(Ty(l)&&(Yy=ub(),!Sy())){var a=Uy();a&&(a.then(function(){N(95)}),a.catch(function(){N(96)}))}},Yy;function $y(a){var b=a.location.href;if(a===a.top)return{url:b,Qp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Qp:c}};function Rz(a,b){var c=!!hk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?gk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(187)?Oz()?Pz():""+gk()+"/ag/g/c":Oz().toLowerCase()==="region1"?""+gk()+"/r1ag/g/c":""+gk()+"/ag/g/c":Pz();case 16:if(c){if(E(187))return Oz()?Qz():
""+gk()+"/ga/g/c";var d=Oz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+gk()+d}return Qz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?gk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?gk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Io+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?gk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return E(180)?c&&b.Cd?gk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion":c?b.Cd?gk()+"/as/d/ccm/conversion":gk()+"/as/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?gk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return E(180)?c&&b.Cd?gk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion":c?b.Cd?gk()+"/g/d/ccm/conversion":gk()+"/g/ccm/conversion":"https://www.google.com/ccm/conversion";case 21:return E(180)?c&&b.Cd?gk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data":c?b.Cd?gk()+"/d/ccm/form-data":gk()+"/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 55:case 27:case 30:case 36:case 54:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:bc(a,"Unknown endpoint")}};function Sz(a){a=a===void 0?[]:a;return Jj(a).join("~")}function Tz(){if(!E(118))return"";var a,b;return(((a=Hm(Im()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Uz(a,b){b&&nb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var bA={};bA.P=Xr.P;var cA={Xq:"L",yo:"S",qr:"Y",Gq:"B",Qq:"E",Uq:"I",lr:"TC",Tq:"HTC"},dA={yo:"S",Pq:"V",Jq:"E",kr:"tag"},eA={},fA=(eA[bA.P.Ui]="6",eA[bA.P.Vi]="5",eA[bA.P.Ti]="7",eA);function gA(){function a(c,d){var e=ab(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var hA=!1;function yA(a){}
function zA(a){}function AA(){}
function BA(a){}function CA(a){}
function DA(a){}
function EA(){}function FA(a,b){}
function GA(a,b,c){}
function HA(){};var IA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function JA(a,b,c,d,e,f,g){var h=Object.assign({},IA);c&&(h.body=c,h.method="POST");Object.assign(h,e);l.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});KA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?em(a,b,c):dm(a,b))})};var LA=function(a){this.R=a;this.D=""},MA=function(a,b){a.J=b;return a},NA=function(a,b){a.O=b;return a},KA=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=k(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}OA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},PA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};OA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},OA=function(a,b){b&&(QA(b.send_pixel,b.options,a.R),QA(b.create_iframe,b.options,a.J),QA(b.fetch,b.options,a.O))};function RA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function QA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=bd(b)?b:{},f=k(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function GB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function HB(a,b,c){c=c===void 0?!1:c;IB().addRestriction(0,a,b,c)}function JB(a,b,c){c=c===void 0?!1:c;IB().addRestriction(1,a,b,c)}function KB(){var a=Fm();return IB().getRestrictions(1,a)}var LB=function(){this.container={};this.D={}},MB=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
LB.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=MB(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
LB.prototype.getRestrictions=function(a,b){var c=MB(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
LB.prototype.getExternalRestrictions=function(a,b){var c=MB(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};LB.prototype.removeExternalRestrictions=function(a){var b=MB(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function IB(){return Fp("r",function(){return new LB})};var NB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),OB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},PB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},QB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function RB(){var a=nk("gtm.allowlist")||nk("gtm.whitelist");a&&N(9);Vj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);NB.test(l.location&&l.location.hostname)&&(Vj?N(116):(N(117),SB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&yb(rb(a),OB),c=nk("gtm.blocklist")||nk("gtm.blacklist");c||(c=nk("tagTypeBlacklist"))&&N(3);c?N(8):c=[];NB.test(l.location&&l.location.hostname)&&(c=rb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
rb(c).indexOf("google")>=0&&N(2);var d=c&&yb(rb(c),PB),e={};return function(f){var g=f&&f[Ze.Ha];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=dk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Vj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){N(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=lb(d,h||[]);t&&N(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Vj&&h.indexOf("cmpPartners")>=0?!TB():b&&b.indexOf("sandboxedScripts")!==-1?0:lb(d,QB))&&(u=!0);return e[g]=u}}function TB(){var a=bg(Zf.D,Dm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var SB=!1;
function UB(){um&&HB(Fm(),function(a){var b=Kf(a.entityId),c;if(Nf(b)){var d=b[Ze.Ha];if(!d)throw Error("Error: No function name given for function call.");var e=Bf[d];c=!!e&&!!e.runInSiloedMode}else c=!!GB(b[Ze.Ha],4);return c})};function VB(a,b,c,d,e){if(!WB()){var f=d.siloed?Am(a):a;if(!Om(f)){d.loadExperiments=Kj();Qm(f,d,e);var g=XB(a),h=function(){qm().container[f]&&(qm().container[f].state=3);YB()},m={destinationId:f,endpoint:0};if(hk())hm(m,gk()+"/"+g,void 0,h);else{var n=zb(a,"GTM-"),p=Vk(),q=c?"/gtag/js":"/gtm.js",r=Uk(b,q+g);if(!r){var t=Mj.Ag+q;p&&nc&&n&&(t=nc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=ww("https://","http://",t+g)}hm(m,r,void 0,h)}}}}
function YB(){Sm()||nb(Tm(),function(a,b){ZB(a,b.transportUrl,b.context);N(92)})}
function ZB(a,b,c,d){if(!WB()){var e=c.siloed?Am(a):a;if(!Pm(e))if(c.loadExperiments||(c.loadExperiments=Kj()),Sm()){var f;(f=qm().destination)[e]!=null||(f[e]={state:0,transportUrl:b,context:c,parent:Im()});qm().destination[e].state=0;pm({ctid:e,isDestination:!0},d);N(91)}else{c.siloed&&Rm({ctid:e,isDestination:!0});var g;(g=qm().destination)[e]!=null||(g[e]={context:c,state:1,parent:Im()});qm().destination[e].state=1;pm({ctid:e,isDestination:!0},d);var h={destinationId:e,endpoint:0};if(hk())hm(h,
gk()+("/gtd"+XB(a,!0)));else{var m="/gtag/destination"+XB(a,!0),n=Uk(b,m);n||(n=ww("https://","http://",Mj.Ag+m));hm(h,n)}}}}function XB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Pj!=="dataLayer"&&(c+="&l="+Pj);if(!zb(a,"GTM-")||b)c=E(130)?c+(hk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Vr();Vk()&&(c+="&sign="+Mj.Oi);var d=Ij.J;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Kj().join("~")&&(c+="&tag_exp="+Kj().join("~"));return c}
function WB(){if(Sr()){return!0}return!1};var $B=function(){this.J=0;this.D={}};$B.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,ac:c};return d};$B.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var bC=function(a,b){var c=[];nb(aC.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.ac===void 0||b.indexOf(e.ac)>=0)&&c.push(e.listener)});return c};function cC(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Dm()}};var eC=function(a,b){this.D=!1;this.R=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;dC(this,a,b)},fC=function(a,b,c,d){if(Rj.hasOwnProperty(b)||b==="__zone")return-1;var e={};bd(d)&&(e=cd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},gC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},hC=function(a){if(!a.D){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.D=!0;a.R.length=0}},dC=function(a,b,c){b!==void 0&&a.Nf(b);c&&l.setTimeout(function(){hC(a)},
Number(c))};eC.prototype.Nf=function(a){var b=this,c=wb(function(){A(function(){a(Dm(),b.eventData)})});this.D?c():this.R.push(c)};var iC=function(a){a.O++;return wb(function(){a.J++;a.T&&a.J>=a.O&&hC(a)})},jC=function(a){a.T=!0;a.J>=a.O&&hC(a)};var kC={};function lC(){return l[mC()]}
function mC(){return l.GoogleAnalyticsObject||"ga"}function pC(){var a=Dm();}
function qC(a,b){return function(){var c=lC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var wC=["es","1"],xC={},yC={};function zC(a,b){if(dl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";xC[a]=[["e",c],["eid",a]];Iq(a)}}function AC(a){var b=a.eventId,c=a.Kd;if(!xC[b])return[];var d=[];yC[b]||d.push(wC);d.push.apply(d,ua(xC[b]));c&&(yC[b]=!0);return d};var BC={},CC={},DC={};function EC(a,b,c,d){dl&&E(120)&&((d===void 0?0:d)?(DC[b]=DC[b]||0,++DC[b]):c!==void 0?(CC[a]=CC[a]||{},CC[a][b]=Math.round(c)):(BC[a]=BC[a]||{},BC[a][b]=(BC[a][b]||0)+1))}function FC(a){var b=a.eventId,c=a.Kd,d=BC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete BC[b];return e.length?[["md",e.join(".")]]:[]}
function GC(a){var b=a.eventId,c=a.Kd,d=CC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete CC[b];return e.length?[["mtd",e.join(".")]]:[]}function HC(){for(var a=[],b=k(Object.keys(DC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+DC[d])}return a.length?[["mec",a.join(".")]]:[]};var IC={},JC={};function KC(a,b,c){if(dl&&b){var d=Zk(b);IC[a]=IC[a]||[];IC[a].push(c+d);var e=(Nf(b)?"1":"2")+d;JC[a]=JC[a]||[];JC[a].push(e);Iq(a)}}function LC(a){var b=a.eventId,c=a.Kd,d=[],e=IC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=JC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete IC[b],delete JC[b]);return d};function MC(a,b,c,d){var e=zf[a],f=NC(a,b,c,d);if(!f)return null;var g=Of(e[Ze.Kl],c,[]);if(g&&g.length){var h=g[0];f=MC(h.index,{onSuccess:f,onFailure:h.hm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function NC(a,b,c,d){function e(){function w(){bo(3);var I=ub()-G;KC(c.id,f,"7");gC(c.Pc,C,"exception",I);E(109)&&GA(c,f,bA.P.Ti);F||(F=!0,h())}if(f[Ze.po])h();else{var x=Mf(f,c,[]),z=x[Ze.Vm];if(z!=null)for(var B=0;B<z.length;B++)if(!up(z[B])){h();return}var C=fC(c.Pc,String(f[Ze.Ha]),Number(f[Ze.sh]),x[Ze.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var I=ub()-G;KC(c.id,zf[a],"5");gC(c.Pc,C,"success",I);E(109)&&GA(c,f,bA.P.Vi);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var I=ub()-
G;KC(c.id,zf[a],"6");gC(c.Pc,C,"failure",I);E(109)&&GA(c,f,bA.P.Ui);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);KC(c.id,f,"1");E(109)&&FA(c,f);var G=ub();try{Pf(x,{event:c,index:a,type:1})}catch(I){w(I)}E(109)&&GA(c,f,bA.P.Ol)}}var f=zf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Of(f[Ze.Pl],c,[]);if(n&&n.length){var p=n[0],q=MC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.hm===
2?m:q}if(f[Ze.Bl]||f[Ze.ro]){var r=f[Ze.Bl]?Af:c.xq,t=g,u=h;if(!r[a]){var v=OC(a,r,wb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function OC(a,b,c){var d=[],e=[];b[a]=PC(d,e,c);return{onSuccess:function(){b[a]=QC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=RC;for(var f=0;f<e.length;f++)e[f]()}}}function PC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function QC(a){a()}function RC(a,b){b()};var UC=function(a,b){for(var c=[],d=0;d<zf.length;d++)if(a[d]){var e=zf[d];var f=iC(b.Pc);try{var g=MC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Ze.Ha];if(!h)throw Error("Error: No function name given for function call.");var m=Bf[h];c.push({Lm:d,priorityOverride:(m?m.priorityOverride||0:0)||GB(e[Ze.Ha],1)||0,execute:g})}else SC(d,b),f()}catch(p){f()}}c.sort(TC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function VC(a,b){if(!aC)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=bC(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=iC(b);try{d[e](a,f)}catch(g){f()}}return!0}function TC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Lm,h=b.Lm;f=g>h?1:g<h?-1:0}return f}
function SC(a,b){if(dl){var c=function(d){var e=b.isBlocked(zf[d])?"3":"4",f=Of(zf[d][Ze.Kl],b,[]);f&&f.length&&c(f[0].index);KC(b.id,zf[d],e);var g=Of(zf[d][Ze.Pl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var WC=!1,aC;function XC(){aC||(aC=new $B);return aC}
function YC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(WC)return!1;WC=!0}var e=!1,f=KB(),g=cd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}zC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:ZC(g,e),xq:[],logMacroError:function(){N(6);bo(0)},cachedModelValues:$C(),Pc:new eC(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&dl&&(n.reportMacroDiscrepancy=EC);E(109)&&CA(n.id);var p=Uf(n);E(109)&&DA(n.id);e&&(p=aD(p));E(109)&&BA(b);var q=UC(p,n),r=VC(a,n.Pc);jC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||pC();return bD(p,q)||r}function $C(){var a={};a.event=sk("event",1);a.ecommerce=sk("ecommerce",1);a.gtm=sk("gtm");a.eventModel=sk("eventModel");return a}
function ZC(a,b){var c=RB();return function(d){if(c(d))return!0;var e=d&&d[Ze.Ha];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Fm();f=IB().getRestrictions(0,g);var h=a;b&&(h=cd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=dk[e]||[],n=k(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function aD(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(zf[c][Ze.Ha]);if(Qj[d]||zf[c][Ze.so]!==void 0||GB(d,2))b[c]=!0}return b}function bD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&zf[c]&&!Rj[String(zf[c][Ze.Ha])])return!0;return!1};function cD(){XC().addListener("gtm.init",function(a,b){Ij.la=!0;Ln();b()})};var dD=!1,eD=0,fD=[];function gD(a){if(!dD){var b=y.createEventObject,c=y.readyState==="complete",d=y.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){dD=!0;for(var e=0;e<fD.length;e++)A(fD[e])}fD.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)A(f[g]);return 0}}}function hD(){if(!dD&&eD<140){eD++;try{var a,b;(b=(a=y.documentElement).doScroll)==null||b.call(a,"left");gD()}catch(c){l.setTimeout(hD,50)}}}
function iD(){dD=!1;eD=0;if(y.readyState==="interactive"&&!y.createEventObject||y.readyState==="complete")gD();else{Cc(y,"DOMContentLoaded",gD);Cc(y,"readystatechange",gD);if(y.createEventObject&&y.documentElement.doScroll){var a=!0;try{a=!l.frameElement}catch(b){}a&&hD()}Cc(l,"load",gD)}}function jD(a){dD?a():fD.push(a)};var kD={},lD={};function mD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fj:void 0,nj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fj=Qp(g,b),e.Fj){var h=vm?vm:Cm();jb(h,function(r){return function(t){return r.Fj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=kD[g]||[];e.nj={};m.forEach(function(r){return function(t){r.nj[t]=!0}}(e));for(var n=ym(),p=0;p<n.length;p++)if(e.nj[n[p]]){c=c.concat(Bm());break}var q=lD[g]||[];q.length&&(c=c.concat(q))}}return{yj:c,Vp:d}}
function nD(a){nb(kD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function oD(a){nb(lD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var pD=!1,qD=!1;function rD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=cd(b,null),b[K.m.Xe]&&(d.eventCallback=b[K.m.Xe]),b[K.m.Pg]&&(d.eventTimeout=b[K.m.Pg]));return d}function sD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Ip()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function tD(a,b){var c=a&&a[K.m.kd];c===void 0&&(c=nk(K.m.kd,2),c===void 0&&(c="default"));if(fb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?fb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=mD(d,b.isGtmEvent),f=e.yj,g=e.Vp;if(g.length)for(var h=uD(a),m=0;m<g.length;m++){var n=Qp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=zb(p,"siloed_"))){var r=n.destinationId,t=qm().destination[r];q=!!t&&t.state===0}q||ZB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{yj:Rp(f,b.isGtmEvent),Jo:Rp(u,b.isGtmEvent)}}}var vD=void 0,wD=void 0;function xD(a,b,c){var d=cd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&N(136);var e=cd(b,null);cd(c,e);Tw(Pw(ym()[0],e),a.eventId,d)}function uD(a){for(var b=k([K.m.ld,K.m.uc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Qq.D[d];if(e)return e}}
var yD={config:function(a,b){var c=sD(a,b);if(!(a.length<2)&&fb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!bd(a[2])||a.length>3)return;d=a[2]}var e=Qp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!tm.ie){var m=Hm(Im());if(Um(m)){var n=m.parent,p=n.isDestination;h={Xp:Hm(n),Sp:p};break a}}h=void 0}var q=h;q&&(f=q.Xp,g=q.Sp);zC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Bm().indexOf(r)===-1:ym().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Lc]){var u=uD(d);if(t)ZB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;vD?xD(b,v,vD):wD||(wD=cd(v,null))}else VB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(N(128),g&&N(130),b.inheritParentConfig)){var w;var x=d;wD?(xD(b,wD,x),w=!1):(!x[K.m.nd]&&Tj&&vD||(vD=cd(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}el&&(Kp===1&&(Dn.mcc=!1),Kp=2);if(Tj&&!t&&!d[K.m.nd]){var z=qD;qD=!0;if(z)return}pD||N(43);if(!b.noTargetGroup)if(t){oD(e.id);
var B=e.id,C=d[K.m.Sg]||"default";C=String(C).split(",");for(var F=0;F<C.length;F++){var G=lD[C[F]]||[];lD[C[F]]=G;G.indexOf(B)<0&&G.push(B)}}else{nD(e.id);var I=e.id,L=d[K.m.Sg]||"default";L=L.toString().split(",");for(var V=0;V<L.length;V++){var Q=kD[L[V]]||[];kD[L[V]]=Q;Q.indexOf(I)<0&&Q.push(I)}}delete d[K.m.Sg];var na=b.eventMetadata||{};na.hasOwnProperty(P.C.ud)||(na[P.C.ud]=!b.fromContainerExecution);b.eventMetadata=na;delete d[K.m.Xe];for(var T=t?[e.id]:Bm(),aa=0;aa<T.length;aa++){var Y=d,
U=T[aa],ka=cd(b,null),ja=Qp(U,ka.isGtmEvent);ja&&Qq.push("config",[Y],ja,ka)}}}}},consent:function(a,b){if(a.length===3){N(39);var c=sD(a,b),d=a[1],e={},f=No(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.ug?Array.isArray(h)?NaN:Number(h):g===K.m.bc?(Array.isArray(h)?h:[h]).map(Oo):Po(h)}b.fromContainerExecution||(e[K.m.W]&&N(139),e[K.m.Oa]&&N(140));d==="default"?qp(e):d==="update"?sp(e,c):d==="declare"&&b.fromContainerExecution&&pp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&fb(c)){var d=void 0;if(a.length>2){if(!bd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=rD(c,d),f=sD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=tD(d,b);if(m){var n=m.yj,p=m.Jo,q,r,t;if(!um&&E(108)){q=p.map(function(I){return I.id});r=p.map(function(I){return I.destinationId});t=n.map(function(I){return I.id});for(var u=k(vm?vm:Cm()),v=u.next();!v.done;v=u.next()){var w=v.value;
!zb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(Am(w))<0&&t.push(w)}}else q=n.map(function(I){return I.id}),r=n.map(function(I){return I.destinationId}),t=q;zC(g,c);for(var x=k(t),z=x.next();!z.done;z=x.next()){var B=z.value,C=cd(b,null),F=cd(d,null);delete F[K.m.Xe];var G=C.eventMetadata||{};G.hasOwnProperty(P.C.ud)||(G[P.C.ud]=!C.fromContainerExecution);G[P.C.Mi]=q.slice();G[P.C.Jf]=r.slice();C.eventMetadata=G;Rq(c,F,B,C);E(166)||Lp(G[P.C.cb])}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.kd]=
q.join(","):delete e.eventModel[K.m.kd];pD||N(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[P.C.Nl]&&(b.noGtmEvent=!0);e.eventModel[K.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){N(53);if(a.length===4&&fb(a[1])&&fb(a[2])&&eb(a[3])){var c=Qp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){pD||N(43);var f=uD();if(jb(Bm(),function(h){return c.destinationId===h})){sD(a,b);var g={};cd((g[K.m.oc]=d,g[K.m.Ic]=e,g),null);Sq(d,function(h){A(function(){e(h)})},c.id,
b)}else ZB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){pD=!0;var c=sD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&fb(a[1])&&eb(a[2])){if($f(a[1],a[2]),N(74),a[1]==="all"){N(75);var b=!1;try{b=a[2](Dm(),"unknown",{})}catch(c){}b||N(76)}}else N(73)},set:function(a,b){var c=void 0;a.length===
2&&bd(a[1])?c=cd(a[1],null):a.length===3&&fb(a[1])&&(c={},bd(a[2])||Array.isArray(a[2])?c[a[1]]=cd(a[2],null):c[a[1]]=a[2]);if(c){var d=sD(a,b),e=d.eventId,f=d.priorityId;cd(c,null);var g=cd(c,null);Qq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},zD={policy:!0};var BD=function(a){if(AD(a))return a;this.value=a};BD.prototype.getUntrustedMessageValue=function(){return this.value};var AD=function(a){return!a||$c(a)!=="object"||bd(a)?!1:"getUntrustedMessageValue"in a};BD.prototype.getUntrustedMessageValue=BD.prototype.getUntrustedMessageValue;var CD=!1,DD=[];function ED(){if(!CD){CD=!0;for(var a=0;a<DD.length;a++)A(DD[a])}}function FD(a){CD?A(a):DD.push(a)};var GD=0,HD={},ID=[],JD=[],KD=!1,LD=!1;function MD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function ND(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return OD(a)}function PD(a,b){if(!gb(b)||b<0)b=0;var c=Ep[Pj],d=0,e=!1,f=void 0;f=l.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(l.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function QD(a,b){var c=a._clear||b.overwriteModelFields;nb(a,function(e,f){e!=="_clear"&&(c&&qk(e),qk(e,f))});ak||(ak=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Ip(),a["gtm.uniqueEventId"]=d,qk("gtm.uniqueEventId",d));return YC(a)}function RD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ob(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function SD(){var a;if(JD.length)a=JD.shift();else if(ID.length)a=ID.shift();else return;var b;var c=a;if(KD||!RD(c.message))b=c;else{KD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Ip(),f=Ip(),c.message["gtm.uniqueEventId"]=Ip());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};ID.unshift(n,c);b=h}return b}
function TD(){for(var a=!1,b;!LD&&(b=SD());){LD=!0;delete kk.eventModel;mk();var c=b,d=c.message,e=c.messageContext;if(d==null)LD=!1;else{e.fromContainerExecution&&rk();try{if(eb(d))try{d.call(ok)}catch(u){}else if(Array.isArray(d)){if(fb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=nk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(ob(d))a:{if(d.length&&fb(d[0])){var p=yD[d[0]];if(p&&(!e.fromContainerExecution||!zD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=QD(n,e)||a)}}finally{e.fromContainerExecution&&mk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=HD[String(q)]||[],t=0;t<r.length;t++)JD.push(UD(r[t]));r.length&&JD.sort(MD);delete HD[String(q)];q>GD&&(GD=q)}LD=!1}}}return!a}
function VD(){if(E(109)){var a=!Ij.R;}var c=TD();if(E(109)){}try{var e=Dm(),f=l[Pj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Ww(a){if(GD<a.notBeforeEventId){var b=String(a.notBeforeEventId);HD[b]=HD[b]||[];HD[b].push(a)}else JD.push(UD(a)),JD.sort(MD),A(function(){LD||TD()})}function UD(a){return{message:a.message,messageContext:a.messageContext}}
function WD(){function a(f){var g={};if(AD(f)){var h=f;f=AD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=oc(Pj,[]),c=Ep[Pj]=Ep[Pj]||{};c.pruned===!0&&N(83);HD=Uw().get();Vw();jD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});FD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Ep.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new BD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});ID.push.apply(ID,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(N(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return TD()&&p};var e=b.slice(0).map(function(f){return a(f)});ID.push.apply(ID,e);if(!Ij.R){if(E(109)){}A(VD)}}var OD=function(a){return l[Pj].push(a)};function XD(a){OD(a)};function YD(){var a,b=Ok(l.location.href);(a=b.hostname+b.pathname)&&Hn("dl",encodeURIComponent(a));var c;var d=cg.ctid;if(d){var e=tm.ie?1:0,f,g=Hm(Im());f=g&&g.context;c=d+";"+cg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Hn("tdp",h);var m=Gl(!0);m!==void 0&&Hn("frm",String(m))};function ZD(){el&&l.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){N(179);var b=cm(a.effectiveDirective);if(b){var c;var d=am(b,a.blockedURI);c=d?Zl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=k(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Dm){p.Dm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if($o()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if($o()){var u=fp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Zo(u)}}}Nn(p.endpoint)}}bm(b,a.blockedURI)}}}}})};function $D(){var a;var b=Gm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Hn("pcid",e)};var aE=/^(https?:)?\/\//;
function bE(){var a;var b=Hm(Im());if(b){for(;b.parent;){var c=Hm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Rc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=k(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(aE,"")===g.replace(aE,""))){e=n;break a}}N(146)}else N(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
Hn("rtg",String(d.canonicalContainerId)),Hn("slo",String(t)),Hn("hlo",d.htmlLoadOrder||"-1"),Hn("lst",String(d.loadScriptType||"0")))}else N(144)};function cE(){var a=[],b=Number('')||0,c=function(){var f=!1;return f}();a.push({Km:195,Jm:195,experimentId:104527906,controlId:104527907,percent:b,active:c,fj:1});var d=Number('')||0,e=function(){var f=!1;
return f}();a.push({Km:196,Jm:196,experimentId:104528500,controlId:104528501,percent:d,active:e,fj:0});return a};var dE={};function eE(a){for(var b=k(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Ij.ia.J.add(Number(c.value))}function fE(){if(E(194))for(var a=k(cE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Km;gi[d]=c;if(c.fj===1){var e=d,f=io(co.aa.vo);ji(f,e);eE(f)}else if(c.fj===0){var g=dE;ji(g,d);eE(g)}}};
function AE(){};var BE=function(){};BE.prototype.toString=function(){return"undefined"};var CE=new BE;function JE(a,b){function c(g){var h=Ok(g),m=Ik(h,"protocol"),n=Ik(h,"host",!0),p=Ik(h,"port"),q=Ik(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function KE(a){return LE(a)?1:0}
function LE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=cd(a,{});cd({arg1:c[d],any_of:void 0},e);if(KE(e))return!0}return!1}switch(a["function"]){case "_cn":return Lg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Gg.length;g++){var h=Gg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Hg(b,c);case "_eq":return Mg(b,c);case "_ge":return Ng(b,c);case "_gt":return Pg(b,c);case "_lc":return Ig(b,c);case "_le":return Og(b,
c);case "_lt":return Qg(b,c);case "_re":return Kg(b,c,a.ignore_case);case "_sw":return Rg(b,c);case "_um":return JE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var ME=function(a,b,c,d){gr.call(this);this.ph=b;this.Ff=c;this.Gb=d;this.ab=new Map;this.rh=0;this.la=new Map;this.Ca=new Map;this.T=void 0;this.J=a};sa(ME,gr);ME.prototype.O=function(){delete this.D;this.ab.clear();this.la.clear();this.Ca.clear();this.T&&(cr(this.J,"message",this.T),delete this.T);delete this.J;delete this.Gb;gr.prototype.O.call(this)};
var NE=function(a){if(a.D)return a.D;a.Ff&&a.Ff(a.J)?a.D=a.J:a.D=Fl(a.J,a.ph);var b;return(b=a.D)!=null?b:null},PE=function(a,b,c){if(NE(a))if(a.D===a.J){var d=a.ab.get(b);d&&d(a.D,c)}else{var e=a.la.get(b);if(e&&e.xj){OE(a);var f=++a.rh;a.Ca.set(f,{Jh:e.Jh,Uo:e.lm(c),persistent:b==="addEventListener"});a.D.postMessage(e.xj(c,f),"*")}}},OE=function(a){a.T||(a.T=function(b){try{var c;c=a.Gb?a.Gb(b):void 0;if(c){var d=c.aq,e=a.Ca.get(d);if(e){e.persistent||a.Ca.delete(d);var f;(f=e.Jh)==null||f.call(e,
e.Uo,c.payload)}}}catch(g){}},br(a.J,"message",a.T))};var QE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},RE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},SE={lm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Jh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},TE={lm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Jh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function UE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,aq:b.__gppReturn.callId}}
var VE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;gr.call(this);this.caller=new ME(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},UE);this.caller.ab.set("addEventListener",QE);this.caller.la.set("addEventListener",SE);this.caller.ab.set("removeEventListener",RE);this.caller.la.set("removeEventListener",TE);this.timeoutMs=c!=null?c:500};sa(VE,gr);VE.prototype.O=function(){this.caller.dispose();gr.prototype.O.call(this)};
VE.prototype.addEventListener=function(a){var b=this,c=hl(function(){a(WE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);PE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(XE,!0);return}a(YE,!0)}}})};
VE.prototype.removeEventListener=function(a){PE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var YE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},WE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},XE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function ZE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Cv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Cv.D=d}}function $E(){try{var a=new VE(l,{timeoutMs:-1});NE(a.caller)&&a.addEventListener(ZE)}catch(b){}};function aF(){var a=[["cv",Mi(1)],["rv",Nj],["tc",zf.filter(function(b){return b}).length]];Oj&&a.push(["x",Oj]);fk()&&a.push(["tag_exp",fk()]);return a};var bF={};function Pi(a){bF[a]=(bF[a]||0)+1}function cF(){for(var a=[],b=k(Object.keys(bF)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+bF[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var dF={},eF={};function fF(a){var b=a.eventId,c=a.Kd,d=[],e=dF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=eF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete dF[b],delete eF[b]);return d};function gF(){return!1}function hF(){var a={};return function(b,c,d){}};function iF(){var a=jF;return function(b,c,d){var e=d&&d.event;kF(c);var f=wh(b)?void 0:1,g=new Oa;nb(c,function(r,t){var u=sd(t,void 0,f);u===void 0&&t!==void 0&&N(44);g.set(r,u)});a.D.D.J=Sf();var h={Wl:gg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Nf:e!==void 0?function(r){e.Pc.Nf(r)}:void 0,Hb:function(){return b},log:function(){},hp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},iq:!!GB(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(gF()){var m=hF(),n,p;h.ub={Qj:[],Of:{},Yb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Hh:Oh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Qe(a,h,[b,g]);a.D.D.J=void 0;q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return rd(q,void 0,f)}}function kF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;eb(b)&&(a.gtmOnSuccess=function(){A(b)});eb(c)&&(a.gtmOnFailure=function(){A(c)})};function lF(a){}lF.N="internal.addAdsClickIds";function mF(a,b){var c=this;}mF.publicName="addConsentListener";var nF=!1;function oF(a){for(var b=0;b<a.length;++b)if(nF)try{a[b]()}catch(c){N(77)}else a[b]()}function pF(a,b,c){var d=this,e;return e}pF.N="internal.addDataLayerEventListener";function qF(a,b,c){}qF.publicName="addDocumentEventListener";function rF(a,b,c,d){}rF.publicName="addElementEventListener";function sF(a){return a.M.D};function tF(a){}tF.publicName="addEventCallback";
var uF=function(a){return typeof a==="string"?a:String(Ip())},xF=function(a,b){vF(a,"init",!1)||(wF(a,"init",!0),b())},vF=function(a,b,c){var d=yF(a);return vb(d,b,c)},zF=function(a,b,c,d){var e=yF(a),f=vb(e,b,d);e[b]=c(f)},wF=function(a,b,c){yF(a)[b]=c},yF=function(a){var b=Fp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},AF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Oc(a,"className"),"gtm.elementId":a.for||Ec(a,"id")||"","gtm.elementTarget":a.formTarget||
Oc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Oc(a,"href")||a.src||a.code||a.codebase||"";return d};
function JF(a){}JF.N="internal.addFormAbandonmentListener";function KF(a,b,c,d){}
KF.N="internal.addFormData";var LF={},MF=[],NF={},OF=0,PF=0;
function WF(a,b){}WF.N="internal.addFormInteractionListener";
function cG(a,b){}cG.N="internal.addFormSubmitListener";
function hG(a){}hG.N="internal.addGaSendListener";function iG(a){if(!a)return{};var b=a.hp;return cC(b.type,b.index,b.name)}function jG(a){return a?{originatingEntity:iG(a)}:{}};
var lG=function(a,b,c){kG().updateZone(a,b,c)},nG=function(a,b,c,d,e,f){var g=kG();c=c&&yb(c,mG);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,Dm(),h)){var p=n,q=a,r=d,t=e,u=f;if(zb(p,"GTM-"))VB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Ow("js",tb());VB(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};E(146)||Tw(v,q,w);Tw(Pw(p,r),q,w)}}}return h},kG=function(){return Fp("zones",function(){return new oG})},
pG={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},mG={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},oG=function(){this.D={};this.J={};this.O=0};ba=oG.prototype;ba.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.D[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.Ej],b))return!1;for(var e=0;e<c.rg.length;e++)if(this.J[c.rg[e]].ve(b))return!0;return!1};ba.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.D[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.rg.length;f++){var g=this.J[c.rg[f]];g.ve(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.Ej],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].O(m,n))return!0;return!1}};ba.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.D[a[b]]};ba.createZone=function(a,b){var c=String(++this.O);this.J[c]=new qG(a,b);return c};ba.updateZone=function(a,
b,c){var d=this.J[a];d&&d.R(b,c)};ba.registerChild=function(a,b,c){var d=this.D[a];if(!d&&Ep[a]||!d&&Om(a)||d&&d.Ej!==b)return!1;if(d)return d.rg.push(c),!1;this.D[a]={Ej:b,rg:[c]};return!0};var qG=function(a,b){this.J=null;this.D=[{eventId:a,ve:!0}];if(b){this.J={};for(var c=0;c<b.length;c++)this.J[b[c]]=!0}};qG.prototype.R=function(a,b){var c=this.D[this.D.length-1];a<=c.eventId||c.ve!==b&&this.D.push({eventId:a,ve:b})};qG.prototype.ve=function(a){for(var b=this.D.length-1;b>=0;b--)if(this.D[b].eventId<=
a)return this.D[b].ve;return!1};qG.prototype.O=function(a,b){b=b||[];if(!this.J||pG[a]||this.J[a])return!0;for(var c=0;c<b.length;++c)if(this.J[b[c]])return!0;return!1};function rG(a){var b=Ep.zones;return b?b.getIsAllowedFn(ym(),a):function(){return!0}}function sG(){var a=Ep.zones;a&&a.unregisterChild(ym())}
function tG(){JB(Fm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Ep.zones;return c?c.isActive(ym(),b):!0});HB(Fm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return rG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var uG=function(a,b){this.tagId=a;this.pe=b};
function vG(a,b){var c=this,d=void 0;if(!hh(a)||!ah(b)&&!ch(b))throw H(this.getName(),["string","Object|undefined"],arguments);var e=rd(b,this.M,1)||{},f=e.firstPartyUrl,g=e.onLoad,h=e.loadByDestination===!0,m=e.isGtmEvent===!0,n=e.siloed===!0;d=n?Am(a):a;oF([function(){J(c,"load_google_tags",a,f)}]);if(h){if(Pm(a))return d}else if(Om(a))return d;var p=6,q=sF(this);m&&(p=7);q.Hb()==="__zone"&&(p=1);var r={source:p,fromContainerExecution:!0,
siloed:n},t=function(u){HB(u,function(v){for(var w=IB().getExternalRestrictions(0,Fm()),x=k(w),z=x.next();!z.done;z=x.next()){var B=z.value;if(!B(v))return!1}return!0},!0);JB(u,function(v){for(var w=IB().getExternalRestrictions(1,Fm()),x=k(w),z=x.next();!z.done;z=x.next()){var B=z.value;if(!B(v))return!1}return!0},!0);g&&g(new uG(a,u))};h?ZB(a,f,r,t):VB(a,f,!zb(a,"GTM-"),r,t);g&&q.Hb()==="__zone"&&nG(Number.MIN_SAFE_INTEGER,[a],null,{},iG(sF(this)));
return d}vG.N="internal.loadGoogleTag";function wG(a){return new jd("",function(b){var c=this.evaluate(b);if(c instanceof jd)return new jd("",function(){var d=ya.apply(0,arguments),e=this,f=cd(sF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Ha(this.M);h.D=f;return c.Jb.apply(c,[h].concat(ua(g)))})})};function xG(a,b,c){var d=this;}xG.N="internal.addGoogleTagRestriction";var yG={},zG=[];
function GG(a,b){}
GG.N="internal.addHistoryChangeListener";function HG(a,b,c){}HG.publicName="addWindowEventListener";function IG(a,b){return!0}IG.publicName="aliasInWindow";function JG(a,b,c){}JG.N="internal.appendRemoteConfigParameter";function KG(a){var b;return b}
KG.publicName="callInWindow";function LG(a){}LG.publicName="callLater";function MG(a){if(!dh(a))throw H(this.getName(),["function"],arguments);J(this,"process_dom_events","document","DOMContentLoaded");J(this,"process_dom_events","document","readystatechange");J(this,"process_dom_events","window","load");jD(rd(a));}MG.N="callOnDomReady";function NG(a){if(!dh(a))throw H(this.getName(),["function"],arguments);J(this,"process_dom_events","window","load");FD(rd(a));}NG.N="callOnWindowLoad";function OG(a,b){var c;return c}OG.N="internal.computeGtmParameter";function PG(a,b){var c=this;}PG.N="internal.consentScheduleFirstTry";function QG(a,b){var c=this;}QG.N="internal.consentScheduleRetry";function RG(a){var b;return b}RG.N="internal.copyFromCrossContainerData";function SG(a,b){var c;if(!hh(a)||!mh(b)&&b!==null&&!ch(b))throw H(this.getName(),["string","number|undefined"],arguments);J(this,"read_data_layer",a);c=(b||2)!==2?nk(a,1):pk(a,[l,y]);var d=sd(c,this.M,wh(sF(this).Hb())?2:1);d===void 0&&c!==void 0&&N(45);return d}SG.publicName="copyFromDataLayer";
function TG(a){var b=void 0;J(this,"read_data_layer",a);a=String(a);var c;a:{for(var d=sF(this).cachedModelValues,e=k(a.split(".")),f=e.next();!f.done;f=e.next()){if(d==null){c=void 0;break a}d=d[f.value]}c=d}b=sd(c,this.M,1);return b}TG.N="internal.copyFromDataLayerCache";function UG(a){var b;return b}UG.publicName="copyFromWindow";function VG(a){var b=void 0;if(!hh(a))throw H(this.getName(),["string"],arguments);J(this,"unsafe_access_globals",a);var c=a.split(".");b=l[c.shift()];for(var d=0;d<c.length;d++)b=b&&b[c[d]];return sd(b,this.M,1)}VG.N="internal.copyKeyFromWindow";var WG=function(a){return a===Ym.Z.Ea&&qn[a]===Xm.Ka.he&&!up(K.m.V)};var XG=function(){return"0"},YG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Pk(a,b,"0")};var ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH={},yH=(xH[K.m.Ta]=(ZG[2]=[WG],ZG),xH[K.m.jf]=($G[2]=[WG],$G),xH[K.m.Ye]=(aH[2]=[WG],aH),xH[K.m.ri]=(bH[2]=[WG],bH),xH[K.m.si]=(cH[2]=[WG],cH),xH[K.m.ui]=(dH[2]=[WG],dH),xH[K.m.wi]=(eH[2]=[WG],eH),xH[K.m.xi]=(fH[2]=[WG],fH),xH[K.m.Tb]=(gH[2]=[WG],gH),xH[K.m.lf]=(hH[2]=[WG],hH),xH[K.m.nf]=(iH[2]=[WG],iH),xH[K.m.pf]=(jH[2]=[WG],jH),xH[K.m.qf]=(kH[2]=
[WG],kH),xH[K.m.rf]=(lH[2]=[WG],lH),xH[K.m.tf]=(mH[2]=[WG],mH),xH[K.m.uf]=(nH[2]=[WG],nH),xH[K.m.vf]=(oH[2]=[WG],oH),xH[K.m.ob]=(pH[1]=[WG],pH),xH[K.m.Yc]=(qH[1]=[WG],qH),xH[K.m.ed]=(rH[1]=[WG],rH),xH[K.m.Vd]=(sH[1]=[WG],sH),xH[K.m.Je]=(tH[1]=[function(a){return E(102)&&WG(a)}],tH),xH[K.m.fd]=(uH[1]=[WG],uH),xH[K.m.Ba]=(vH[1]=[WG],vH),xH[K.m.Xa]=(wH[1]=[WG],wH),xH),zH={},AH=(zH[K.m.ob]=XG,zH[K.m.Yc]=XG,zH[K.m.ed]=XG,zH[K.m.Vd]=XG,zH[K.m.Je]=XG,zH[K.m.fd]=function(a){if(!bd(a))return{};var b=cd(a,
null);delete b.match_id;return b},zH[K.m.Ba]=YG,zH[K.m.Xa]=YG,zH),BH={},CH={},DH=(CH[P.C.Ua]=(BH[2]=[WG],BH),CH),EH={};var FH=function(a,b,c,d){this.D=a;this.O=b;this.R=c;this.T=d};FH.prototype.getValue=function(a){a=a===void 0?Ym.Z.Fb:a;if(!this.O.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.T(this.D):this.D};FH.prototype.J=function(){return $c(this.D)==="array"||bd(this.D)?cd(this.D,null):this.D};
var GH=function(){},HH=function(a,b){this.conditions=a;this.D=b},IH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new FH(c,e,g,a.D[b]||GH)},JH,KH;var LH=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=k(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Sv=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,P.C.Lf))},W=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:(JH!=null||(JH=new HH(yH,AH)),e=IH(JH,b,c));d[b]=e};
LH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.D[a])==null?void 0:(e=d.J)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!bd(c))return!1;W(this,a,Object.assign(c,b));return!0};var MH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.D)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};
LH.prototype.copyToHitData=function(a,b,c){var d=O(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&fb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===P.C.Lf){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,P.C.Lf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(KH!=null||(KH=new HH(DH,EH)),e=IH(KH,b,c));d[b]=e},NH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},lw=function(a,b,c){var d=a.target.destinationId;um||(d=Jm(d));var e=$w(d);return e&&e[b]!==void 0?e[b]:c};function OH(a,b){var c;return c}OH.N="internal.copyPreHit";function PH(a,b){var c=null;if(!hh(a)||!hh(b))throw H(this.getName(),["string","string"],arguments);J(this,"access_globals","readwrite",a);J(this,"access_globals","readwrite",b);var d=[l,y],e=a.split("."),f=Ab(e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return eb(h)?sd(h,this.M,2):null;var m;h=function(){if(!eb(m.push))throw Error("Object at "+b+" in window is not an array.");m.push.call(m,
arguments)};f[g]=h;var n=b.split("."),p=Ab(n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");m=p[q];m===void 0&&(m=[],p[q]=m);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return sd(c,this.M,2)}PH.publicName="createArgumentsQueue";function QH(a){return sd(function(c){var d=lC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
lC(),n=m&&m.getByName&&m.getByName(f);return(new l.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.M,1)}QH.N="internal.createGaCommandQueue";function RH(a){return sd(function(){if(!eb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.M,
wh(sF(this).Hb())?2:1)}RH.publicName="createQueue";function SH(a,b){var c=null;if(!hh(a)||!ih(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new od(new RegExp(a,d))}catch(e){}return c}SH.N="internal.createRegex";function TH(){var a={};return a};function UH(a){}UH.N="internal.declareConsentState";function VH(a){var b="";return b}VH.N="internal.decodeUrlHtmlEntities";function WH(a,b,c){var d;return d}WH.N="internal.decorateUrlWithGaCookies";function XH(){}XH.N="internal.deferCustomEvents";function YH(a){var b;return b}YH.N="internal.detectUserProvidedData";
var aI=function(a){var b=Hc(a,["button","input"],50);if(!b)return null;var c=String(b.tagName).toLowerCase();if(c==="button")return b;if(c==="input"){var d=Ec(b,"type");if(d==="button"||d==="submit"||d==="image"||d==="file"||d==="reset")return b}return null},bI=function(a,b,c){var d=c.target;if(d){var e=vF(a,"individualElementIds",[]);if(e.length>0){var f=AF(d,b,e);OD(f)}var g=!1,h=vF(a,"commonButtonIds",[]);if(h.length>0){var m=aI(d);if(m){var n=AF(m,b,h);OD(n);g=!0}}var p=vF(a,"selectorToTriggerIds",
{}),q;for(q in p)if(p.hasOwnProperty(q)){var r=g?p[q].filter(function(v){return h.indexOf(v)===-1}):p[q];if(r.length!==0){var t=pi(d,q);if(t){var u=AF(t,b,r);OD(u)}}}}};
function cI(a,b){if(!bh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var c=a?rd(a):{},d=qb(c.matchCommonButtons),e=!!c.cssSelector,f=uF(b);J(this,"detect_click_events",c.matchCommonButtons,c.cssSelector);var g=c.useV2EventName?"gtm.click-v2":"gtm.click",h=c.useV2EventName?"ecl":"cl",m=function(p){p.push(f);return p};if(e||d){if(d&&zF(h,"commonButtonIds",m,[]),e){var n=sb(String(c.cssSelector));zF(h,"selectorToTriggerIds",
function(p){p.hasOwnProperty(n)||(p[n]=[]);m(p[n]);return p},{})}}else zF(h,"individualElementIds",m,[]);xF(h,function(){Cc(y,"click",function(p){bI(h,g,p)},!0)});return f}cI.N="internal.enableAutoEventOnClick";var fI=function(a){if(!dI){var b=function(){var c=y.body;if(c)if(eI)(new MutationObserver(function(){for(var e=0;e<dI.length;e++)A(dI[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Cc(c,"DOMNodeInserted",function(){d||(d=!0,A(function(){d=!1;for(var e=0;e<dI.length;e++)A(dI[e])}))})}};dI=[];y.body?b():A(b)}dI.push(a)},eI=!!l.MutationObserver,dI;
function kI(a,b){return p}kI.N="internal.enableAutoEventOnElementVisibility";function lI(){}lI.N="internal.enableAutoEventOnError";var mI={},nI=[],oI={},pI=0,qI=0;
function wI(a,b){var c=this;return d}wI.N="internal.enableAutoEventOnFormInteraction";
function BI(a,b){var c=this;return f}BI.N="internal.enableAutoEventOnFormSubmit";
function GI(){var a=this;}GI.N="internal.enableAutoEventOnGaSend";var HI={},II=[];
function PI(a,b){var c=this;return f}PI.N="internal.enableAutoEventOnHistoryChange";var QI=["http://","https://","javascript:","file://"];
var RI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Oc(b,"href");if(c.indexOf(":")!==-1&&!QI.some(function(h){return zb(c,h)}))return!1;var d=c.indexOf("#"),e=Oc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Lk(Ok(c)),g=Lk(Ok(l.location.href));return f!==g}return!0},SI=function(a,b){for(var c=Ik(Ok((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Oc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},TI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.D||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Hc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=vF("lcl",e?"nv.mwt":"mwt",0),g;g=e?vF("lcl","nv.ids",[]):vF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=vF("lcl","aff.map",{})[n];p&&!SI(p,d)||h.push(n)}if(h.length){var q=RI(c,d),r=AF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Fc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!jb(String(Oc(d,"rel")||"").split(" "),function(x){return x.toLowerCase()==="noreferrer"}),u=l[(Oc(d,"target")||"_self").substring(1)],v=!0,w=PD(function(){var x;if(x=v&&u){var z;a:if(t){var B;try{B=new MouseEvent(c.type,{bubbles:!0})}catch(C){if(!y.createEvent){z=!1;break a}B=y.createEvent("MouseEvents");B.initEvent(c.type,!0,!0)}B.D=!0;c.target.dispatchEvent(B);z=!0}else z=!1;x=!z}x&&(u.location.href=Oc(d,
"href"))},f);if(ND(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else ND(r,function(){},f||2E3);return!0}}}var b=0;Cc(y,"click",a,!1);Cc(y,"auxclick",a,!1)};
function UI(a,b){var c=this;if(!bh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var d=rd(a);oF([function(){J(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=uF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};zF("lcl","mwt",n,0);f||zF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};zF("lcl","ids",p,[]);f||zF("lcl","nv.ids",p,[]);g&&zF("lcl","aff.map",function(q){q[h]=g;return q},{});vF("lcl","init",!1)||(TI(),wF("lcl","init",!0));return h}UI.N="internal.enableAutoEventOnLinkClick";var VI,WI;
var XI=function(a){return vF("sdl",a,{})},YI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];zF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},aJ=function(){function a(){ZI();$I(a,!0)}return a},bJ=function(){function a(){f?e=l.setTimeout(a,c):(e=0,ZI(),$I(b));f=!1}function b(){d&&VI();e?f=!0:(e=l.setTimeout(a,c),wF("sdl","pending",!0))}var c=250,d=!1;y.scrollingElement&&y.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
$I=function(a,b){vF("sdl","init",!1)&&!cJ()&&(b?Dc(l,"scrollend",a):Dc(l,"scroll",a),Dc(l,"resize",a),wF("sdl","init",!1))},ZI=function(){var a=VI(),b=a.depthX,c=a.depthY,d=b/WI.scrollWidth*100,e=c/WI.scrollHeight*100;dJ(b,"horiz.pix","PIXELS","horizontal");dJ(d,"horiz.pct","PERCENT","horizontal");dJ(c,"vert.pix","PIXELS","vertical");dJ(e,"vert.pct","PERCENT","vertical");wF("sdl","pending",!1)},dJ=function(a,b,c,d){var e=XI(b),f={},g;for(g in e)if(f={Ae:f.Ae},f.Ae=g,e.hasOwnProperty(f.Ae)){var h=
Number(f.Ae);if(!(a<h)){var m={};XD((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Ae].join(","),m));zF("sdl",b,function(n){return function(p){delete p[n.Ae];return p}}(f),{})}}},fJ=function(){zF("sdl","scr",function(a){a||(a=y.scrollingElement||y.body&&y.body.parentNode);return WI=a},!1);zF("sdl","depth",function(a){a||(a=eJ());return VI=a},!1)},eJ=function(){var a=0,b=0;return function(){var c=cx(),d=c.height;
a=Math.max(WI.scrollLeft+c.width,a);b=Math.max(WI.scrollTop+d,b);return{depthX:a,depthY:b}}},cJ=function(){return!!(Object.keys(XI("horiz.pix")).length||Object.keys(XI("horiz.pct")).length||Object.keys(XI("vert.pix")).length||Object.keys(XI("vert.pct")).length)};
function gJ(a,b){var c=this;if(!ah(a))throw H(this.getName(),["Object","any"],arguments);oF([function(){J(c,"detect_scroll_events")}]);fJ();if(!WI)return;var d=uF(b),e=rd(a);switch(e.horizontalThresholdUnits){case "PIXELS":YI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":YI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":YI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":YI(e.verticalThresholds,
d,"vert.pct")}vF("sdl","init",!1)?vF("sdl","pending",!1)||A(function(){ZI()}):(wF("sdl","init",!0),wF("sdl","pending",!0),A(function(){ZI();if(cJ()){var f=bJ();"onscrollend"in l?(f=aJ(),Cc(l,"scrollend",f)):Cc(l,"scroll",f);Cc(l,"resize",f)}else wF("sdl","init",!1)}));return d}gJ.N="internal.enableAutoEventOnScroll";function hJ(a){return function(){if(a.limit&&a.Aj>=a.limit)a.Fh&&l.clearInterval(a.Fh);else{a.Aj++;var b=ub();OD({event:a.eventName,"gtm.timerId":a.Fh,"gtm.timerEventNumber":a.Aj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Im,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Im,"gtm.triggers":a.Cq})}}}
function iJ(a,b){
return f}iJ.N="internal.enableAutoEventOnTimer";
var jJ=function(a,b,c){function d(){var g=a();f+=e?(ub()-e)*g.playbackRate/1E3:0;e=ub()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.gj,q=m?Math.round(m):h?Math.round(n.gj*h):Math.round(n.dm),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=y.hidden?!1:dx(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var v=AF(c,"gtm.video",u);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=t;return v},Em:function(){e=ub()},oe:function(){d()}}};var dc=wa(["data-gtm-yt-inspected-"]),kJ=["www.youtube.com","www.youtube-nocookie.com"],lJ,mJ=!1;
var nJ=function(a,b,c){var d=a.map(function(g){return{ib:g,ng:g,lg:void 0}});if(!b.length)return d;var e=b.map(function(g){return{ib:g*c,ng:void 0,lg:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.ib-h.ib});return f},oJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},pJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},qJ=function(a,b){var c,d;function e(){t=jJ(function(){return{url:w,title:x,gj:v,dm:a.getCurrentTime(),playbackRate:z}},b.ac,a.getIframe());v=0;x=w="";z=1;return f}function f(G){switch(G){case 1:v=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var I=a.getVideoData();x=I?I.title:""}z=a.getPlaybackRate();if(b.aj){var L=t.createEvent("start");OD(L)}else t.oe();u=nJ(b.Ij,b.Hj,a.getDuration());return g(G);default:return f}}function g(){B=a.getCurrentTime();C=tb().getTime();
t.Em();r();return h}function h(G){var I;switch(G){case 0:return n(G);case 2:I="pause";case 3:var L=a.getCurrentTime()-B;I=Math.abs((tb().getTime()-C)/1E3*z-L)>1?"seek":I||"buffering";if(a.getCurrentTime())if(b.Zi){var V=t.createEvent(I);OD(V)}else t.oe();q();return m;case -1:return e(G);default:return h}}function m(G){switch(G){case 0:return n(G);case 1:return g(G);case -1:return e(G);default:return m}}function n(){for(;d;){var G=c;l.clearTimeout(d);G()}if(b.Yi){var I=t.createEvent("complete",1);
OD(I)}return e(-1)}function p(){}function q(){d&&(l.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&z!==0){var G=-1,I;do{I=u[0];if(I.ib>a.getDuration())return;G=(I.ib-a.getCurrentTime())/z;if(G<0&&(u.shift(),u.length===0))return}while(G<0);c=function(){d=0;c=p;if(u.length>0&&u[0].ib===I.ib){u.shift();var L=t.createEvent("progress",I.lg,I.ng);OD(L)}r()};d=l.setTimeout(c,G*1E3)}}var t,u=[],v,w,x,z,B,C,F=e(-1);d=0;c=p;return{onStateChange:function(G){F=F(G)},onPlaybackRateChange:function(G){B=a.getCurrentTime();
C=tb().getTime();t.oe();z=G;q();r()}}},sJ=function(a){A(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)rJ(d[f],a)}var c=y;b();fI(b)})},rJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.ac)&&(fc(a,"data-gtm-yt-inspected-"+b.ac),tJ(a,b.Vf))){a.id||(a.id=uJ());var c=l.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=qJ(d,b),f={},g;for(g in e)f={gg:f.gg},f.gg=g,e.hasOwnProperty(f.gg)&&d.addEventListener(f.gg,function(h){return function(m){return e[h.gg](m.data)}}(f))}},
tJ=function(a,b){var c=a.getAttribute("src");if(vJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(lJ||(lJ=y.location.protocol+"//"+y.location.hostname,y.location.port&&(lJ+=":"+y.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(lJ));var f;f=Nb(d);a.src=Ob(f).toString();return!0}}return!1},vJ=function(a,b){if(!a)return!1;for(var c=0;c<kJ.length;c++)if(a.indexOf("//"+kJ[c]+"/"+b)>=0)return!0;
return!1},uJ=function(){var a=""+Math.round(Math.random()*1E9);return y.getElementById(a)?uJ():a};
function wJ(a,b){var c=this;var d=function(){sJ(q)};if(!ah(a))throw H(this.getName(),["Object","any"],arguments);oF([function(){J(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=uF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=pJ(rd(a.get("progressThresholdsPercent"))),n=oJ(rd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={aj:f,Yi:g,Zi:h,Hj:m,Ij:n,Vf:p,ac:e},r=l.YT;if(r)return r.ready&&r.ready(d),e;var t=l.onYouTubeIframeAPIReady;l.onYouTubeIframeAPIReady=function(){t&&t();d()};A(function(){for(var u=y.getElementsByTagName("script"),v=u.length,w=0;w<v;w++){var x=u[w].getAttribute("src");if(vJ(x,"iframe_api")||vJ(x,"player_api"))return e}for(var z=y.getElementsByTagName("iframe"),B=z.length,C=0;C<B;C++)if(!mJ&&tJ(z[C],q.Vf))return xc("https://www.youtube.com/iframe_api"),
mJ=!0,e});return e}wJ.N="internal.enableAutoEventOnYouTubeActivity";mJ=!1;function xJ(a,b){if(!hh(a)||!bh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?rd(b):{},d=a,e=!1;return e}xJ.N="internal.evaluateBooleanExpression";var yJ;function zJ(a){var b=!1;return b}zJ.N="internal.evaluateMatchingRules";function iK(){return Ar(7)&&Ar(9)&&Ar(10)};function dL(a,b,c,d){}dL.N="internal.executeEventProcessor";function eL(a){var b;return sd(b,this.M,1)}eL.N="internal.executeJavascriptString";function fL(a){var b;return b};function gL(a){var b="";return b}gL.N="internal.generateClientId";function hL(a){var b={};return sd(b)}hL.N="internal.getAdsCookieWritingOptions";function iL(a,b){var c=!1;return c}iL.N="internal.getAllowAdPersonalization";function jL(){var a;return a}jL.N="internal.getAndResetEventUsage";function kL(a,b){b=b===void 0?!0:b;var c;return c}kL.N="internal.getAuid";var lL=null;
function mL(){var a=new Oa;J(this,"read_container_data"),E(49)&&lL?a=lL:(a.set("containerId",'GTM-TT9C75WP'),a.set("version",'41'),a.set("environmentName",''),a.set("debugMode",hg),a.set("previewMode",ig.Nm),a.set("environmentMode",ig.bp),a.set("firstPartyServing",hk()||Ij.O),a.set("containerUrl",nc),a.hb(),E(49)&&(lL=a));return a}
mL.publicName="getContainerVersion";function nL(a,b){b=b===void 0?!0:b;var c;return c}nL.publicName="getCookieValues";function oL(){var a="";return a}oL.N="internal.getCorePlatformServicesParam";function pL(){return so()}pL.N="internal.getCountryCode";function qL(){var a=[];return sd(a)}qL.N="internal.getDestinationIds";function rL(a){var b=new Oa;return b}rL.N="internal.getDeveloperIds";function sL(a){var b;return b}sL.N="internal.getEcsidCookieValue";function tL(a,b){var c=null;if(!gh(a)||!hh(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementAttribute requires an HTML Element.");J(this,"get_element_attributes",d,b);c=Ec(d,b);return c}tL.N="internal.getElementAttribute";function uL(a){var b=null;return b}uL.N="internal.getElementById";function vL(a){var b="";if(!gh(a))throw H(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementInnerText requires an HTML Element.");J(this,"read_dom_element_text",c);b=Fc(c);return b}vL.N="internal.getElementInnerText";function wL(a,b){var c=null;if(!gh(a)||!hh(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementProperty requires an HTML element.");J(this,"access_dom_element_properties",d,"read",b);c=d[b];return sd(c)}wL.N="internal.getElementProperty";function xL(a){var b;if(!gh(a))throw H(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementValue requires an HTML Element.");J(this,"access_element_values",c,"read");b=c instanceof HTMLInputElement?c.value:Ec(c,"value")||"";return b}xL.N="internal.getElementValue";function yL(a){var b=0;return b}yL.N="internal.getElementVisibilityRatio";function zL(a){var b=null;return b}zL.N="internal.getElementsByCssSelector";
function AL(a){var b;if(!hh(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=sF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=k(n),B=z.next();!B.done;B=
z.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===h?x+".":x+C}x&&w.push(x);for(var F=k(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=sd(c,this.M,1);return b}AL.N="internal.getEventData";var BL={};BL.enableCcdSendTo=E(41);BL.enableConversionAutoDataAnalysis=E(188);BL.enableDCFledge=E(56);BL.enableDecodeUri=E(92);BL.enableDeferAllEnhancedMeasurement=E(58);BL.enableDv3Gact=E(174);BL.enableGa4OutboundClicksFix=E(96);BL.enableGaAdsConversions=E(122);BL.enableGaAdsConversionsClientId=E(121);BL.enableOverrideAdsCps=E(170);BL.enableUrlDecodeEventUsage=E(139);BL.enableZoneConfigInChildContainers=E(142);BL.useEnableAutoEventOnFormApis=E(156);function CL(){return sd(BL)}CL.N="internal.getFlags";function DL(){var a;return a}DL.N="internal.getGsaExperimentId";function EL(){return new od(CE)}EL.N="internal.getHtmlId";function FL(a){var b;return b}FL.N="internal.getIframingState";function GL(a,b){var c={};return sd(c)}GL.N="internal.getLinkerValueFromLocation";function HL(){var a=new Oa;return a}HL.N="internal.getPrivacyStrings";function IL(a,b){var c;return c}IL.N="internal.getProductSettingsParameter";function JL(a,b){var c;return c}JL.publicName="getQueryParameters";function KL(a,b){var c;return c}KL.publicName="getReferrerQueryParameters";function LL(a){var b="";if(!ih(a))throw H(this.getName(),["string|undefined"],arguments);J(this,"get_referrer",a);b=Kk(Ok(y.referrer),a);return b}LL.publicName="getReferrerUrl";function ML(){return to()}ML.N="internal.getRegionCode";function NL(a,b){var c;return c}NL.N="internal.getRemoteConfigParameter";function OL(){var a=new Oa;a.set("width",0);a.set("height",0);return a}OL.N="internal.getScreenDimensions";function PL(){var a="";return a}PL.N="internal.getTopSameDomainUrl";function QL(){var a="";return a}QL.N="internal.getTopWindowUrl";function RL(a){var b="";if(!ih(a))throw H(this.getName(),["string|undefined"],arguments);J(this,"get_url",a);b=Ik(Ok(l.location.href),a);return b}RL.publicName="getUrl";function SL(){J(this,"get_user_agent");return kc.userAgent}SL.N="internal.getUserAgent";function TL(){var a;return a?sd(Vy(a)):a}TL.N="internal.getUserAgentClientHints";function aM(){return l.gaGlobal=l.gaGlobal||{}}function bM(){var a=aM();a.hid=a.hid||kb();return a.hid}function cM(a,b){var c=aM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function AM(a){(my(a)||hk())&&W(a,K.m.fl,to()||so());!my(a)&&hk()&&W(a,K.m.tl,"::")}function BM(a){if(hk()&&!my(a)&&(E(176)&&W(a,K.m.Qk,!0),E(78))){fw(a);gw(a,Np.xf.bn,Qo(O(a.F,K.m.kb)));var b=Np.xf.dn;var c=O(a.F,K.m.Hc);gw(a,b,c===!0?1:c===!1?0:void 0);gw(a,Np.xf.Zm,Qo(O(a.F,K.m.xb)));gw(a,Np.xf.Xm,ts(Po(O(a.F,K.m.qb)),Po(O(a.F,K.m.Pb))))}};var XM={AW:co.aa.Tm,G:co.aa.eo,DC:co.aa.bo};function YM(a){var b=Xi(a);return""+Wr(b.map(function(c){return c.value}).join("!"))}function ZM(a){var b=Qp(a);return b&&XM[b.prefix]}function $M(a,b){var c=a[b];c&&(c.clearTimerId&&l.clearTimeout(c.clearTimerId),c.clearTimerId=l.setTimeout(function(){delete a[b]},36E5))};var EN=window,FN=document,GN=function(a){var b=EN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||FN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&EN["ga-disable-"+a]===!0)return!0;try{var c=EN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(FN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return FN.getElementById("__gaOptOutExtension")?!0:!1};
function SN(a){nb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Ub]||{};nb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function zO(a,b){}function AO(a,b){var c=function(){};return c}
function BO(a,b,c){};var CO=AO;var DO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function EO(a,b,c){var d=this;if(!hh(a)||!bh(b)||!bh(c))throw H(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?rd(b):{};oF([function(){return J(d,"configure_google_tags",a,e)}]);var f=c?rd(c):{},g=sF(this);f.originatingEntity=iG(g);Tw(Pw(a,e),g.eventId,f);}EO.N="internal.gtagConfig";
function GO(a,b){}
GO.publicName="gtagSet";function HO(){var a={};return a};function IO(a){}IO.N="internal.initializeServiceWorker";function JO(a,b){}JO.publicName="injectHiddenIframe";var KO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function LO(a,b,c,d,e){}LO.N="internal.injectHtml";var PO={};
function RO(a,b,c,d){}var SO={dl:1,id:1},TO={};
function UO(a,b,c,d){}E(160)?UO.publicName="injectScript":RO.publicName="injectScript";UO.N="internal.injectScript";function VO(){return xo()}VO.N="internal.isAutoPiiEligible";function WO(a){var b=!0;return b}WO.publicName="isConsentGranted";function XO(a){var b=!1;return b}XO.N="internal.isDebugMode";function YO(){return vo()}YO.N="internal.isDmaRegion";function ZO(a){var b=!1;return b}ZO.N="internal.isEntityInfrastructure";function $O(a){var b=!1;return b}$O.N="internal.isFeatureEnabled";function aP(){var a=!1;return a}aP.N="internal.isFpfe";function bP(){var a=!1;return a}bP.N="internal.isGcpConversion";function cP(){var a=!1;return a}cP.N="internal.isLandingPage";function dP(){var a;return a}dP.N="internal.isSafariPcmEligibleBrowser";function eP(){var a=Jh(function(b){sF(this).log("error",b)});a.publicName="JSON";return a};function fP(a){var b=void 0;if(!hh(a))throw H(this.getName(),["string"],arguments);b=Ok(a);return sd(b)}fP.N="internal.legacyParseUrl";function gP(){return!1}
var hP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function iP(){try{J(this,"logging")}catch(c){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=rd(a[b],this.M);console.log.apply(console,a);}iP.publicName="logToConsole";function jP(a,b){}jP.N="internal.mergeRemoteConfig";function kP(a,b,c){c=c===void 0?!0:c;var d=[];return sd(d)}kP.N="internal.parseCookieValuesFromString";function lP(a){var b=void 0;if(typeof a!=="string")return;a&&zb(a,"//")&&(a=y.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=sd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Ok(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Hk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=sd(n);
return b}lP.publicName="parseUrl";function mP(a){}mP.N="internal.processAsNewEvent";function nP(a,b,c){var d;return d}nP.N="internal.pushToDataLayer";function oP(a){var b=ya.apply(1,arguments),c=!1;return c}oP.publicName="queryPermission";function pP(a){var b=this;}pP.N="internal.queueAdsTransmission";function qP(a,b){var c=void 0;return c}qP.publicName="readAnalyticsStorage";function rP(){var a="";return a}rP.publicName="readCharacterSet";function sP(){return Pj}sP.N="internal.readDataLayerName";function tP(){var a="";return a}tP.publicName="readTitle";function uP(a,b){var c=this;}uP.N="internal.registerCcdCallback";function vP(a,b){return!0}vP.N="internal.registerDestination";var wP=["config","event","get","set"];function xP(a,b,c){}xP.N="internal.registerGtagCommandListener";function yP(a,b){var c=!1;return c}yP.N="internal.removeDataLayerEventListener";function zP(a,b){}
zP.N="internal.removeFormData";function AP(){}AP.publicName="resetDataLayer";function BP(a,b,c){var d=void 0;return d}BP.N="internal.scrubUrlParams";function CP(a){}CP.N="internal.sendAdsHit";function DP(a,b,c,d){}DP.N="internal.sendGtagEvent";function EP(a,b,c){}EP.publicName="sendPixel";function FP(a,b){}FP.N="internal.setAnchorHref";function GP(a){}GP.N="internal.setContainerConsentDefaults";function HP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}HP.publicName="setCookie";function IP(a){}IP.N="internal.setCorePlatformServices";function JP(a,b){}JP.N="internal.setDataLayerValue";function KP(a){}KP.publicName="setDefaultConsentState";function LP(a,b){}LP.N="internal.setDelegatedConsentType";function MP(a,b){}MP.N="internal.setFormAction";function NP(a,b,c){c=c===void 0?!1:c;}NP.N="internal.setInCrossContainerData";function OP(a,b,c){return!1}OP.publicName="setInWindow";function PP(a,b,c){}PP.N="internal.setProductSettingsParameter";function QP(a,b,c){}QP.N="internal.setRemoteConfigParameter";function RP(a,b){}RP.N="internal.setTransmissionMode";function SP(a,b,c,d){var e=this;}SP.publicName="sha256";function TP(a,b,c){}
TP.N="internal.sortRemoteConfigParameters";function UP(a){}UP.N="internal.storeAdsBraidLabels";function VP(a,b){var c=void 0;return c}VP.N="internal.subscribeToCrossContainerData";var WP={},XP={};WP.getItem=function(a){var b=null;J(this,"access_template_storage");var c=sF(this).Hb();XP[c]&&(b=XP[c].hasOwnProperty("gtm."+a)?XP[c]["gtm."+a]:null);return b};WP.setItem=function(a,b){J(this,"access_template_storage");var c=sF(this).Hb();XP[c]=XP[c]||{};XP[c]["gtm."+a]=b;};
WP.removeItem=function(a){J(this,"access_template_storage");var b=sF(this).Hb();if(!XP[b]||!XP[b].hasOwnProperty("gtm."+a))return;delete XP[b]["gtm."+a];};WP.clear=function(){J(this,"access_template_storage"),delete XP[sF(this).Hb()];};WP.publicName="templateStorage";function YP(a,b){var c=!1;if(!gh(a)||!hh(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}YP.N="internal.testRegex";function ZP(a){var b;return b};function $P(a){var b;return b}$P.N="internal.unsiloId";function aQ(a,b){var c;return c}aQ.N="internal.unsubscribeFromCrossContainerData";function bQ(a){}bQ.publicName="updateConsentState";function cQ(a){var b=!1;return b}cQ.N="internal.userDataNeedsEncryption";var dQ;function eQ(a,b,c){dQ=dQ||new Uh;dQ.add(a,b,c)}function fQ(a,b){var c=dQ=dQ||new Uh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=eb(b)?ph(a,b):qh(a,b)}
function gQ(){return function(a){var b;var c=dQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.M.D;if(e){var f=!1,g=e.Hb();if(g){wh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function hQ(){var a=function(c){return void fQ(c.N,c)},b=function(c){return void eQ(c.publicName,c)};b(mF);b(tF);b(IG);b(KG);b(LG);b(SG);b(UG);b(PH);b(eP());b(RH);b(mL);b(nL);b(JL);b(KL);b(LL);b(RL);b(GO);b(JO);b(WO);b(iP);b(lP);b(oP);b(rP);b(tP);b(EP);b(HP);b(KP);b(OP);b(SP);b(WP);b(bQ);eQ("Math",uh());eQ("Object",Sh);eQ("TestHelper",Wh());eQ("assertApi",rh);eQ("assertThat",sh);eQ("decodeUri",xh);eQ("decodeUriComponent",yh);eQ("encodeUri",zh);eQ("encodeUriComponent",Ah);eQ("fail",Fh);eQ("generateRandom",
Gh);eQ("getTimestamp",Hh);eQ("getTimestampMillis",Hh);eQ("getType",Ih);eQ("makeInteger",Kh);eQ("makeNumber",Lh);eQ("makeString",Mh);eQ("makeTableMap",Nh);eQ("mock",Qh);eQ("mockObject",Rh);eQ("fromBase64",fL,!("atob"in l));eQ("localStorage",hP,!gP());eQ("toBase64",ZP,!("btoa"in l));a(lF);a(pF);a(KF);a(WF);a(cG);a(hG);a(xG);a(GG);a(JG);a(MG);a(NG);a(OG);a(PG);a(QG);a(RG);a(TG);a(VG);a(OH);a(QH);a(SH);a(UH);a(VH);a(WH);a(XH);a(YH);a(cI);a(kI);a(lI);a(wI);a(BI);a(GI);a(PI);a(UI);a(gJ);a(iJ);a(wJ);a(xJ);
a(zJ);a(dL);a(eL);a(gL);a(hL);a(iL);a(jL);a(kL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(CL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(ML);a(NL);a(OL);a(PL);a(QL);a(TL);a(EO);a(IO);a(LO);a(UO);a(VO);a(XO);a(YO);a(ZO);a($O);a(aP);a(bP);a(cP);a(dP);a(fP);a(vG);a(jP);a(kP);a(mP);a(nP);a(pP);a(sP);a(uP);a(vP);a(xP);a(yP);a(zP);a(BP);a(CP);a(DP);a(FP);a(GP);a(IP);a(JP);a(LP);a(MP);a(NP);a(PP);a(QP);a(RP);a(TP);a(UP);a(VP);a(YP);a($P);a(aQ);a(cQ);fQ("internal.CrossContainerSchema",
TH());fQ("internal.IframingStateSchema",HO());E(104)&&a(oL);E(160)?b(UO):b(RO);E(177)&&b(qP);return gQ()};var jF;
function iQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;jF=new Oe;jQ();vf=iF();var e=jF,f=hQ(),g=new kd("require",f);g.hb();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Rf(n,d[m]);try{jF.execute(n),E(120)&&dl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(If=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");dk[q]=
["sandboxedScripts"]}kQ(b)}function jQ(){jF.D.D.O=function(a,b,c){Ep.SANDBOXED_JS_SEMAPHORE=Ep.SANDBOXED_JS_SEMAPHORE||0;Ep.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Ep.SANDBOXED_JS_SEMAPHORE--}}}function kQ(a){a&&nb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");dk[e]=dk[e]||[];dk[e].push(b)}})};function lQ(a){Tw(Nw("developer_id."+a,!0),0,{})};var mQ=Array.isArray;function nQ(a,b){return cd(a,b||null)}function X(a){return window.encodeURIComponent(a)}function oQ(a,b,c){Bc(a,b,c)}function pQ(a,b){if(!a)return!1;var c=Ik(Ok(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function qQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var zQ=l.clearTimeout,AQ=l.setTimeout;function BQ(a,b,c){if(Sr()){b&&A(b)}else return xc(a,b,c,void 0)}function CQ(){return l.location.href}function DQ(a,b){return nk(a,b||2)}function EQ(a,b){l[a]=b}function FQ(a,b,c){b&&(l[a]===void 0||c&&!l[a])&&(l[a]=b);return l[a]}function GQ(a,b){if(Sr()){b&&A(b)}else zc(a,b)}
var HQ={};var Z={securityGroups:{}};

Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},U:function(){return{}}}},Z.__access_template_storage.H="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage.runInSiloedMode=!1;
Z.securityGroups.access_element_values=["google"],function(){function a(b,c,d,e){return{element:c,operation:d,newValue:e}}(function(b){Z.__access_element_values=b;Z.__access_element_values.H="access_element_values";Z.__access_element_values.isVendorTemplate=!0;Z.__access_element_values.priorityOverride=0;Z.__access_element_values.isInfrastructure=!1;Z.__access_element_values.runInSiloedMode=!1})(function(b){var c=b.vtp_allowRead,d=b.vtp_allowWrite,e=b.vtp_createPermissionError;return{assert:function(f,
g,h,m){if(!(g instanceof HTMLElement))throw e(f,{},"Element must be a HTMLElement.");if(h!=="read"&&h!=="write")throw e(f,{},"Unknown operation: "+h+".");if(h=="read"&&!c)throw e(f,{},"Attempting to perform disallowed operation: read.");if(h=="write"){if(!d)throw e(f,{},"Attempting to perform disallowed operation: write.");if(!fb(m))throw e(f,{},"Attempting to write value without valid new value.");}},U:a}})}();

Z.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){Z.__access_globals=b;Z.__access_globals.H="access_globals";Z.__access_globals.isVendorTemplate=!0;Z.__access_globals.priorityOverride=0;Z.__access_globals.isInfrastructure=!1;
Z.__access_globals.runInSiloedMode=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var m=c[h],n=m.key;m.read&&e.push(n);m.write&&f.push(n);m.execute&&g.push(n)}return{assert:function(p,q,r){if(!fb(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},U:a}})}();
Z.securityGroups.access_dom_element_properties=["google"],function(){function a(b,c,d,e){var f={property:e,read:!1,write:!1};switch(d){case "read":f.read=!0;break;case "write":f.write=!0;break;default:throw Error("Invalid "+b+" operation "+d);}return f}(function(b){Z.__access_dom_element_properties=b;Z.__access_dom_element_properties.H="access_dom_element_properties";Z.__access_dom_element_properties.isVendorTemplate=!0;Z.__access_dom_element_properties.priorityOverride=0;Z.__access_dom_element_properties.isInfrastructure=
!1;Z.__access_dom_element_properties.runInSiloedMode=!1})(function(b){for(var c=b.vtp_properties||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],m=h.property;h.read&&e.push(m);h.write&&f.push(m)}return{assert:function(n,p,q,r){if(!fb(r))throw d(n,{},"Property must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else throw d(n,{},'Operation must be either "read" or "write"');throw d(n,{},'"'+q+'" operation is not allowed.');
},U:a}})}();
Z.securityGroups.read_dom_element_text=["google"],function(){function a(b,c){return{element:c}}(function(b){Z.__read_dom_element_text=b;Z.__read_dom_element_text.H="read_dom_element_text";Z.__read_dom_element_text.isVendorTemplate=!0;Z.__read_dom_element_text.priorityOverride=0;Z.__read_dom_element_text.isInfrastructure=!1;Z.__read_dom_element_text.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(!(e instanceof HTMLElement))throw c(d,{},"Wrong element type. Must be HTMLElement.");
},U:a}})}();

Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.H="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&
c.push("extension"),b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!fb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!fb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},
"Prohibited query key: "+h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},U:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!fb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&Fg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},U:a}})}();

Z.securityGroups.process_dom_events=["google"],function(){function a(b,c,d){return{targetType:c,eventName:d}}(function(b){Z.__process_dom_events=b;Z.__process_dom_events.H="process_dom_events";Z.__process_dom_events.isVendorTemplate=!0;Z.__process_dom_events.priorityOverride=0;Z.__process_dom_events.isInfrastructure=!1;Z.__process_dom_events.runInSiloedMode=!1})(function(b){for(var c=b.vtp_targets||[],d=b.vtp_createPermissionError,e={},f=0;f<c.length;f++){var g=c[f];e[g.targetType]=e[g.targetType]||
[];e[g.targetType].push(g.eventName)}return{assert:function(h,m,n){if(!e[m])throw d(h,{},"Prohibited event target "+m+".");if(e[m].indexOf(n)===-1)throw d(h,{},"Prohibited listener registration for DOM event "+n+".");},U:a}})}();
Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.H="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events.runInSiloedMode=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},U:a}})}();
Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.H="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!fb(g))throw e(f,{},"Keys must be strings.");if(c!==
"any"){try{if(Fg(g,d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},U:a}})}();
Z.securityGroups.unsafe_access_globals=["google"],function(){function a(c,d){c("access_globals","readwrite",d)}function b(c,d){return{key:d}}(function(c){Z.__unsafe_access_globals=c;Z.__unsafe_access_globals.H="unsafe_access_globals";Z.__unsafe_access_globals.isVendorTemplate=!0;Z.__unsafe_access_globals.priorityOverride=0;Z.__unsafe_access_globals.isInfrastructure=!1;Z.__unsafe_access_globals.runInSiloedMode=!1})(function(c){var d=c.vtp_createPermissionError;return{assert:function(e,f){if(!fb(f))throw d(e,
{},"Wrong key type. Must be string.");},U:b,Vl:a}})}();
Z.securityGroups.smm=["google"],Z.__smm=function(a){var b=a.vtp_input,c=qQ(a.vtp_map,"key","value")||{};return c.hasOwnProperty(b)?c[b]:a.vtp_defaultValue},Z.__smm.H="smm",Z.__smm.isVendorTemplate=!0,Z.__smm.priorityOverride=0,Z.__smm.isInfrastructure=!0,Z.__smm.runInSiloedMode=!1;



Z.securityGroups.gaawe=["google"],function(){function a(f,g,h){for(var m=0;m<g.length;m++)f.hasOwnProperty(g[m])&&(f[g[m]]=h(f[g[m]]))}function b(f,g,h){var m={},n=function(u,v){m[u]=m[u]||v},p=function(u,v,w){w=w===void 0?!1:w;c.push(6);if(u){m.items=m.items||[];for(var x={},z=0;z<u.length;x={jg:void 0},z++)x.jg={},nb(u[z],function(C){return function(F,G){w&&F==="id"?C.jg.promotion_id=G:w&&F==="name"?C.jg.promotion_name=G:C.jg[F]=G}}(x)),m.items.push(x.jg)}if(v)for(var B in v)d.hasOwnProperty(B)?n(d[B],
v[B]):n(B,v[B])},q;f.vtp_getEcommerceDataFrom==="dataLayer"?(q=f.vtp_gtmCachedValues.eventModel)||(q=f.vtp_gtmCachedValues.ecommerce):(q=f.vtp_ecommerceMacroData,bd(q)&&q.ecommerce&&!q.items&&(q=q.ecommerce));if(bd(q)){var r=!1,t;for(t in q)q.hasOwnProperty(t)&&(r||(c.push(5),r=!0),t==="currencyCode"?n("currency",q.currencyCode):t==="impressions"&&g===K.m.hc?p(q.impressions,null):t==="promoClick"&&g===K.m.Gc?p(q.promoClick.promotions,q.promoClick.actionField,!0):t==="promoView"&&g===K.m.jc?p(q.promoView.promotions,
q.promoView.actionField,!0):e.hasOwnProperty(t)?g===e[t]&&p(q[t].products,q[t].actionField):m[t]=q[t]);nQ(m,h)}}var c=[],d={id:"transaction_id",revenue:"value",list:"item_list_name"},e={click:"select_item",detail:"view_item",add:"add_to_cart",remove:"remove_from_cart",checkout:"begin_checkout",checkout_option:"checkout_option",purchase:"purchase",refund:"refund"};(function(f){Z.__gaawe=f;Z.__gaawe.H="gaawe";Z.__gaawe.isVendorTemplate=!0;Z.__gaawe.priorityOverride=0;Z.__gaawe.isInfrastructure=!1;Z.__gaawe.runInSiloedMode=
!1})(function(f){var g;g=f.vtp_migratedToV2?String(f.vtp_measurementIdOverride):String(f.vtp_measurementIdOverride||f.vtp_measurementId);if(fb(g)&&g.indexOf("G-")===0){var h=String(f.vtp_eventName),m={};c=[];f.vtp_sendEcommerceData&&(Eo.hasOwnProperty(h)||h==="checkout_option")&&b(f,h,m);var n=f.vtp_eventSettingsVariable;if(n)for(var p in n)n.hasOwnProperty(p)&&(m[p]=n[p]);if(f.vtp_eventSettingsTable){var q=qQ(f.vtp_eventSettingsTable,"parameter","parameterValue"),r;for(r in q)m[r]=q[r]}var t=qQ(f.vtp_eventParameters,
"name","value"),u;for(u in t)t.hasOwnProperty(u)&&(m[u]=t[u]);var v=f.vtp_userDataVariable;v&&(m[K.m.Za]=v);if(m.hasOwnProperty(K.m.Ub)||f.vtp_userProperties){var w=m[K.m.Ub]||{};nQ(qQ(f.vtp_userProperties,"name","value"),w);m[K.m.Ub]=w}var x={originatingEntity:cC(1,f.vtp_gtmEntityIndex,f.vtp_gtmEntityName)};if(c.length>0){var z={};x.eventMetadata=(z[P.C.nl]=c,z)}a(m,Fo,function(C){return qb(C)});a(m,Ho,function(C){return Number(C)});var B=f.vtp_gtmEventId;x.noGtmEvent=!0;Tw(Qw(g,h,m),B,x);A(f.vtp_gtmOnSuccess)}else A(f.vtp_gtmOnFailure)})}();



Z.securityGroups.get_element_attributes=["google"],function(){function a(b,c,d){return{element:c,attribute:d}}(function(b){Z.__get_element_attributes=b;Z.__get_element_attributes.H="get_element_attributes";Z.__get_element_attributes.isVendorTemplate=!0;Z.__get_element_attributes.priorityOverride=0;Z.__get_element_attributes.isInfrastructure=!1;Z.__get_element_attributes.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedAttributes||"specific",d=b.vtp_attributes||[],e=b.vtp_createPermissionError;
return{assert:function(f,g,h){if(!fb(h))throw e(f,{},"Attribute must be a string.");if(!(g instanceof HTMLElement))throw e(f,{},"Wrong element type. Must be HTMLElement.");if(h==="value"||c!=="any"&&(c!=="specific"||d.indexOf(h)===-1))throw e(f,{},'Reading attribute "'+h+'" is not allowed.');},U:a}})}();
Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.H="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},U:a}})}();
Z.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){Z.__load_google_tags=b;Z.__load_google_tags.H="load_google_tags";Z.__load_google_tags.isVendorTemplate=!0;Z.__load_google_tags.priorityOverride=0;Z.__load_google_tags.isInfrastructure=!1;Z.__load_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||
[],h=b.vtp_createPermissionError;return{assert:function(m,n,p){(function(q){if(!fb(q))throw h(m,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(m,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!fb(q))throw h(m,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(Xg(Ok(q),f))return}catch(r){throw h(m,{},"Invalid first party URL filter.");}}throw h(m,{},"Prohibited first party URL: "+q);}})(p)},U:a}})}();
Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},U:function(){return{}}}},Z.__read_container_data.H="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data.runInSiloedMode=!1;



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.H="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!fb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!fb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},U:a}})}();



Z.securityGroups.remm=["google"],Z.__remm=function(a){for(var b=a.vtp_input,c=a.vtp_map||[],d=a.vtp_fullMatch,e=a.vtp_ignoreCase?"gi":"g",f=a.vtp_defaultValue,g=0;g<c.length;g++){var h=c[g].key||"";d&&(h="^"+h+"$");var m=new RegExp(h,e);if(m.test(b)){var n=c[g].value;a.vtp_replaceAfterMatch&&(n=String(b).replace(m,n));f=n;break}}return f},Z.__remm.H="remm",Z.__remm.isVendorTemplate=!0,Z.__remm.priorityOverride=0,Z.__remm.isInfrastructure=!0,Z.__remm.runInSiloedMode=!1;

Z.securityGroups.detect_click_events=["google"],function(){function a(b,c,d){return{matchCommonButtons:c,cssSelector:d}}(function(b){Z.__detect_click_events=b;Z.__detect_click_events.H="detect_click_events";Z.__detect_click_events.isVendorTemplate=!0;Z.__detect_click_events.priorityOverride=0;Z.__detect_click_events.isInfrastructure=!1;Z.__detect_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(e!==void 0&&typeof e!=="boolean")throw c(d,
{},"matchCommonButtons must be a boolean.");if(f!==void 0&&typeof f!=="string")throw c(d,{},"cssSelector must be a string.");},U:a}})}();
Z.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){Z.__logging=b;Z.__logging.H="logging";Z.__logging.isVendorTemplate=!0;Z.__logging.priorityOverride=0;Z.__logging.isInfrastructure=!1;Z.__logging.runInSiloedMode=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},U:a}})}();

Z.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){Z.__configure_google_tags=b;Z.__configure_google_tags.H="configure_google_tags";Z.__configure_google_tags.isVendorTemplate=!0;Z.__configure_google_tags.priorityOverride=0;Z.__configure_google_tags.isInfrastructure=!1;Z.__configure_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!fb(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},U:a}})}();

Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.H="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();




var Hp={dataLayer:ok,callback:function(a){ck.hasOwnProperty(a)&&eb(ck[a])&&ck[a]();delete ck[a]},bootstrap:0};
function IQ(){Gp();Mm();YB();xb(dk,Z.securityGroups);var a=Hm(Im()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;ep(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||N(142);Hf={No:Xf}}var JQ=!1;
function po(){try{if(JQ||!Vm()){Lj();Ij.T=Li(18,"");
Ij.Gb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Ij.ab="ad_storage|analytics_storage|ad_user_data";Ij.Ca="5690";Ij.Ca="5690";Km();if(E(109)){}og[8]=!0;var a=Fp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});lp(a);Dp();$E();tr();Jp();if(Nm()){sG();IB().removeExternalRestrictions(Fm());}else{Zy();UB();Ff();Bf=Z;Cf=KE;Zf=new fg;iQ();IQ();no||(mo=ro());
Ap();WD();iD();CD=!1;y.readyState==="complete"?ED():Cc(l,"load",ED);cD();dl&&(xq(Lq),l.setInterval(Kq,864E5),xq(aF),xq(AC),xq(gA),xq(Oq),xq(fF),xq(LC),E(120)&&(xq(FC),xq(GC),xq(HC)),bF={},xq(cF),Oi());el&&(Rn(),dq(),YD(),bE(),$D(),Hn("bt",String(Ij.D?2:Ij.O?1:0)),Hn("ct",String(Ij.D?0:Ij.O?1:Sr()?2:3)),ZD());AE();bo(1);tG();fE();bk=ub();Hp.bootstrap=bk;Ij.R&&VD();E(109)&&AA();E(134)&&(typeof l.name==="string"&&zb(l.name,"web-pixel-sandbox-CUSTOM")&&Sc()?lQ("dMDg0Yz"):l.Shopify&&(lQ("dN2ZkMj"),Sc()&&lQ("dNTU0Yz")))}}}catch(b){bo(4),Hq()}}
(function(a){function b(){n=y.documentElement.getAttribute("data-tag-assistant-present");So(n)&&(m=h.ol)}function c(){m&&nc?g(m):a()}if(!l[Li(37,"__TAGGY_INSTALLED")]){var d=!1;if(y.referrer){var e=Ok(y.referrer);d=Kk(e,"host")===Li(38,"cct.google")}if(!d){var f=ds(Li(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(l[Li(37,"__TAGGY_INSTALLED")]=!0,xc(Li(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";Vj&&(v="OGT",w="GTAG");
var x=Li(23,"google.tagmanager.debugui2.queue"),z=l[x];z||(z=[],l[x]=z,xc("https://"+Mj.Ag+"/debug/bootstrap?id="+cg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Ur()));var B={messageType:"CONTAINER_STARTING",data:{scriptSource:nc,containerProduct:v,debug:!1,id:cg.ctid,targetRef:{ctid:cg.ctid,isDestination:wm()},aliases:zm(),destinations:xm()}};B.data.resume=function(){a()};Mj.Wm&&(B.data.initialPublish=!0);z.push(B)},h={io:1,rl:2,Fl:3,jk:4,ol:5};h[h.io]="GTM_DEBUG_LEGACY_PARAM";h[h.rl]="GTM_DEBUG_PARAM";h[h.Fl]="REFERRER";
h[h.jk]="COOKIE";h[h.ol]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Ik(l.location,"query",!1,void 0,"gtm_debug");So(p)&&(m=h.rl);if(!m&&y.referrer){var q=Ok(y.referrer);Kk(q,"host")===Li(24,"tagassistant.google.com")&&(m=h.Fl)}if(!m){var r=ds("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.jk)}m||b();if(!m&&Ro(n)){var t=!1;Cc(y,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);l.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&JQ&&!ro()["0"]?oo():po()});

})()

