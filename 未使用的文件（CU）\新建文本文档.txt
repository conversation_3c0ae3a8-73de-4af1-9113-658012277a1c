# 定义目标 URL
$url = "https://miui.ink/%E5%B4%A9%E5%9D%8F3-%E5%8D%95%E6%9C%BA%E7%89%88-%E6%9C%AC%E5%9C%B0%E4%B8%80%E9%94%AE%E7%AB%AF/"

# 使用 Invoke-WebRequest 获取网页内容
try {
    $response = Invoke-WebRequest -Uri $url -UseBasicParsing
} catch {
    # 如果请求失败，显示错误信息并退出
    Write-Host "无法获取网页内容。请检查网络连接或 URL 是否正确。" -ForegroundColor Red
    exit 1
}

# 将 HTML 内容转换为字符串
$htmlContent = $response.Content

# 使用正则表达式匹配目标文本
# 注意：这里的正则表达式假设密码文本的格式是固定的。如果网站的 HTML 结构发生变化，可能需要调整正则表达式。
$pattern = "启动器今日密码：(\d+)"
if ($htmlContent -match $pattern) {
    # 提取匹配到的密码
    $password = $Matches[1]

    # 使用 PowerShell 的 MessageBox 显示密码
    Add-Type -AssemblyName System.Windows.Forms
    [System.Windows.Forms.MessageBox]::Show("启动器今日密码：$password", "获取到的密码", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
} else {
    # 如果没有找到密码，显示提示信息
    Write-Host "未找到启动器今日密码。" -ForegroundColor Yellow
}
