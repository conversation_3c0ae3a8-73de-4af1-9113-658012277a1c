.m-checkbox[data-v-a2b23999]{position:relative;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;gap:.5rem;vertical-align:top;outline:2px solid transparent;outline-offset:2px}.m-checkbox .check-icon[data-v-a2b23999]{color:var(--637b9682);--tw-scale-x: 0;--tw-scale-y: 0;-webkit-transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));-webkit-transition-property:-webkit-transform;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;-webkit-transition-duration:.3s;transition-duration:.3s;-webkit-transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(.4,0,.2,1)}.m-checkbox .check-icon[data-v-a2b23999] path{stroke-width:2.5}.m-checkbox>span[data-v-a2b23999]{position:relative;display:-webkit-box;display:-ms-flexbox;display:flex;border-radius:.375rem;border-width:var(--maz-border-width);border-color:var(--maz-border-color);-webkit-transition-property:all;transition-property:all;-webkit-transition-duration:.3s;transition-duration:.3s;-webkit-transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(.4,0,.2,1);-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.m-checkbox>span[data-v-a2b23999]:is([class~=dark] *){border-color:var(--maz-color-bg-lighter)}.m-checkbox>span[data-v-a2b23999]{width:var(--7a7f3c21);height:var(--7a7f3c21)}.m-checkbox input[data-v-a2b23999]{display:none}.m-checkbox input:not(:checked)~span[data-v-a2b23999]{background-color:var(--maz-color-bg)}.m-checkbox input:not(:checked)~span[data-v-a2b23999]:is([class~=dark] *){background-color:var(--maz-color-bg-light)}.m-checkbox input:checked~span[data-v-a2b23999]{border-color:var(--4af0fd68);background-color:var(--4af0fd68)}.m-checkbox input:checked~span .check-icon[data-v-a2b23999]{--tw-scale-x: 1;--tw-scale-y: 1;-webkit-transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.m-checkbox input:disabled~span[data-v-a2b23999]{background-color:var(--maz-color-bg-light)}.m-checkbox input:disabled~span[data-v-a2b23999]:is([class~=dark] *){background-color:var(--maz-color-bg-lighter)}.m-checkbox.--disabled[data-v-a2b23999]{cursor:not-allowed;color:var(--maz-color-muted)}.m-checkbox.--disabled input:checked~span[data-v-a2b23999]{border-color:var(--maz-border-color)}.m-checkbox.--disabled input:checked~span[data-v-a2b23999]:is([class~=dark] *){border-color:var(--maz-color-bg-lighter)}.m-checkbox.--disabled input:checked~span .check-icon[data-v-a2b23999]{color:var(--maz-color-muted)}.m-checkbox[data-v-a2b23999]:not(.--disabled){cursor:pointer}.m-checkbox:not(.--disabled):hover>span[data-v-a2b23999],.m-checkbox:not(.--disabled):focus>span[data-v-a2b23999]{-webkit-transition-property:all;transition-property:all;-webkit-transition-duration:.3s;transition-duration:.3s;-webkit-transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(.4,0,.2,1);-webkit-box-shadow:0 0 0 .125rem var(--27d552aa);box-shadow:0 0 0 .125rem var(--27d552aa)}.m-checkbox__text[data-v-a2b23999]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:0px}.m-checkbox__hint[data-v-a2b23999]{font-size:.875rem;line-height:1.25rem;color:var(--maz-color-muted)}.m-checkbox__hint.--error[data-v-a2b23999]{color:var(--maz-color-danger-600)}.m-checkbox__hint.--success[data-v-a2b23999]{color:var(--maz-color-success-600)}.m-checkbox__hint.--warning[data-v-a2b23999]{color:var(--maz-color-warning-600)}.m-checkbox.--error>span[data-v-a2b23999],.m-checkbox.--warning>span[data-v-a2b23999],.m-checkbox.--success>span[data-v-a2b23999]{-webkit-transition-property:all;transition-property:all;-webkit-transition-duration:.3s;transition-duration:.3s;-webkit-transition-timing-function:cubic-bezier(.4,0,.2,1);transition-timing-function:cubic-bezier(.4,0,.2,1);-webkit-box-shadow:0 0 0 .125rem var(--27d552aa);box-shadow:0 0 0 .125rem var(--27d552aa)}
