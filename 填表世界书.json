{"name": "末日乐园表格规则", "description": "为《末日乐园》世界观设计的、用于驱动表格系统运行的核心规则与AI行为逻辑。", "entries": {"0": {"key": ["表格规则", "操作规则", "table_rules", "table_operations", "核心规则", "更新行", "删除行", "插入行"], "keysecondary": ["表格模块 - 核心规则与操作"], "comment": "Generated for: 表格模块 - 核心规则与操作", "content": "# 核心规则\n1.  **【响应结构】**: 你的回复**必须**严格遵循“**<tableThink>思考块** -> **<tableEdit>操作块**”的顺序，且两个块的内容都必须包裹在`<!-- -->`注释中。严禁输出任何额外对话或解释。\n2.  **【操作原子性】**: 每个`updateRow`、`deleteRow`、`insertRow`指令都是一个原子操作。严格遵循预设中为每个表格定义的`initNode`、`insertNode`、`updateNode`和`deleteNode`的触发条件。\n3.  **【数据纯净性】**: 写入表格的必须是纯粹的数据，严禁包含任何AI的思考过程、标签或元注释。单元格内若有多个值，必须使用**半角分号`;`**进行分割。\n4.  **【用户指令优先】**: 用户的直接指令（如“丢弃[卡片化]物品X”）拥有最高优先级，并应被准确执行。\n\n# 表格操作指南\n- **更新行**: `updateRow(tableIndex:num, rowIndex:num, {[colIndex:num]: \"value\", ...})`\n- **删除行**: `deleteRow(tableIndex:num, rowIndex:num)` (【重要】同一表格内的多行删除，必须从大到小逆序执行，否则会出错！)\n- **插入行**: `insertRow(tableIndex:num, {[colIndex:num]: \"value\", ...})`", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 0, "displayIndex": 0, "extensions": {}}, "1": {"key": ["表格逻辑", "AI处理", "思考流程", "tableThink", "tableEdit", "剧情摘要", "表格操作分析"], "keysecondary": ["表格模块 - AI回合处理逻辑"], "comment": "Generated for: 表格模块 - AI回合处理逻辑", "content": "# AI回合处理逻辑 (每轮必须严格遵循此思考与执行流程)\n\n## 第一部分: \"<tableThink>\" 思考流程 (必须在注释中完整展示)\n1.  **剧情摘要**: (30-120字) 精炼概括本轮交互的核心事件、状态变化和关键信息，特别是与世界规则、生存目标、能力物品相关的变化。\n2.  **表格操作分析**: 逐一检查所有表格，根据预设中定义的【增/删/改/初始触发条件】进行详细分析。\n\n## 第二部分: \"<tableEdit>\" 操作执行 (生成具体指令)\n根据`<tableThink>`中分析得出的操作计划，严格按照`update` -> `delete` -> `insert`的顺序，为每个表格生成对应的、准确的`updateRow`, `deleteRow`, `insertRow`指令。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 1, "displayIndex": 1, "extensions": {}}, "2": {"key": ["副本世界表", "tableIndex0", "世界切换", "危机等级", "核心规则", "通关目标"], "keysecondary": ["表格定义 - 副本世界表 (0)"], "comment": "Generated for: 表格定义 - 副本世界表 (0)", "content": "# 表格：副本世界表 (tableIndex: 0)\n## 列名:\n- `当前副本世界`\n- `危机等级`\n- `核心规则`\n- `通关/生存目标`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远有且仅有一行，所有单元格严禁留空！这是整个世界状态机的核心。\n- **填表时机**: 每次主角进入新的末日世界时，必须立即更新此表。\n- **各列填表指导**\n    - `当前副本世界`: 当前末日世界的名称，例如“如月车站”、“伊甸园”。\n    - `危机等级`: 对世界危险程度的评级（如S/A/B/C/D级），需合理推断。\n    - `核心规则`: 这个世界最重要的物理、生存或特殊规则，用分号分隔，例如“所有有机物都会快速腐烂;夜间会出现无法名状的怪物;签证到期会被抹杀”。\n    - `通关/生存目标`: 离开这个世界或达成生存所需完成的核心任务，例如“找到并启动传送门”、“存活30天”。\n## 思考逻辑:\n- 【初始化判断】若为初次生成，`insertRow`创建初始世界。\n- **【【世界切换核心】】**: 每轮交互都必须检查主角是否被传送或主动进入新世界。一旦确认，立即`update`此表所有内容。此操作将触发`主要区域表`、`次级区域表`、`环境关键点表`和`目标与线索表`的连锁重建。\n- 此表只`update`。\n## 操作节点:\n- `initNode`: 【初始化】首次生成时，必须使用 insertRow 插入一行，并根据初始剧情填写所有列。\n- `deleteNode`: 【禁止删除】\n- `updateNode`: 【世界切换时必须更新】当主角进入一个新的末日世界时，更新此表所有列以反映新世界的状态。\n- `insertNode`: 【禁止插入】此表永远只有一行，初始化后只能更新。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 2, "displayIndex": 2, "extensions": {}}, "3": {"key": ["主要区域表", "tableIndex1", "区域名称", "坐标", "区域类型", "环境描述", "所属副本世界"], "keysecondary": ["表格定义 - 主要区域表 (1)"], "comment": "Generated for: 表格定义 - 主要区域表 (1)", "content": "# 表格：主要区域表 (tableIndex: 1)\n## 列名:\n- `区域名称`\n- `X坐标`\n- `Y坐标`\n- `宽度`\n- `高度`\n- `区域类型`\n- `环境描述`\n- `所属副本世界`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远不能为空，必须包含2-8个区域，所有单元格严禁留空！\n- **填表时机**: 当`副本世界表(0)`的`当前副本世界`改变时，此表需要被完全清空并重建。\n- **各列填表指导**:\n    - `区域名称`: `当前副本世界`下的宏观功能区，例如“车站大厅”、“污染森林”。\n    - `X坐标`, `Y坐标`, `宽度`, `高度`: 在`800x600`画布内的布局坐标。\n    - `区域类型`: 区域的分类，例如“废弃都市”、“自然环境”、“高科技设施”。\n    - `环境描述`: 对该区域视觉、氛围的简要描写，特别是末日特征。\n    - `所属副本世界`: 必须是当前的`当前副本世界`名称。\n## 思考逻辑:\n- **【核心检查】** 若`当前副本世界`改变，则对此表执行【先`delete`所有行，再`insert`新行】的完全重建。\n## 操作节点:\n- `initNode`: 当`副本世界表(0)`首次确定时，根据场景设计2-8个区域并逐一插入。\n- `deleteNode`: 【核心操作】当`副本世界表(0)`的`当前副本世界`发生改变时，【必须】首先使用 `deleteRow` 逆序删除此表中的【所有行】。\n- `updateNode`: 【通常不更新】若仅微调描述或位置，可使用此操作。\n- `insertNode`: 【世界切换时】当`副本世界表(0)`改变时，设计2-8个新区域并逐一插入。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 3, "displayIndex": 3, "extensions": {}}, "4": {"key": ["次级区域表", "tableIndex2", "次级区域名称", "所属主要区域", "环境描述"], "keysecondary": ["表格定义 - 次级区域表 (2)"], "comment": "Generated for: 表格定义 - 次级区域表 (2)", "content": "# 表格：次级区域表 (tableIndex: 2)\n## 列名:\n- `次级区域名称`\n- `所属主要区域`\n- `环境描述`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远不能为空，所有单元格严禁留空！每个主要区域至少要有一个次级区域。\n- **填表时机**: 当`主要区域表`重建时，此表也需要被完全清空并重建。\n## 思考逻辑:\n- 若`主要区域表`重建，此表也必须重建。必须为每一个主要区域创建至少1个次级区域。\n## 操作节点:\n- `initNode`: 当`主要区域表(1)`首次生成时，为每一个主要区域，创建至少1个次级区域并插入。\n- `deleteNode`: 当`主要区域表(1)`被清空时，此表也【必须】被【完全清空】。请使用 `deleteRow` 逆序删除所有行。\n- `updateNode`: 【通常不更新】\n- `insertNode`: 当`主要区域表(1)`被重建时，为每一个新的主要区域，创建至少1个次级区域并插入。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 4, "displayIndex": 4, "extensions": {}}, "5": {"key": ["环境关键点表", "tableIndex3", "关键点名称", "关键点类型", "详细描述", "所属次级区域", "互动"], "keysecondary": ["表格定义 - 环境关键点表 (3)"], "comment": "Generated for: 表格定义 - 环境关键点表 (3)", "content": "# 表格：环境关键点表 (tableIndex: 3)\n## 列名:\n- `关键点名称`\n- `关键点类型`\n- `详细描述`\n- `所属次级区域`\n- `当前状态`\n- `交互要求`\n- `互动1`\n- `互动2`\n- `互动3`\n- `互动4`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远不能为空，每个次级区域至少一个关键点，所有单元格严禁留空！\n- **填表时机**: 场景切换或需要增删改查可互动内容时。\n- **各列填表指导**:\n    - `关键点名称`: 可交互的物品、机关、或非关键数据体/NPC，例如“闪烁的控制台”、“一具干尸”。\n    - `关键点类型`: '数据体', '物品', '机关', '信息源'等分类。\n    - `详细描述`: 对关键点的描述，特别是其异常之处。\n    - `所属次级区域`: 必须是`次级区域表`中存在的名称。\n    - `当前状态`: 如“能量耗尽”、“数据乱码”、“休眠中”。\n    - `交互要求`: 进行交互可能需要的条件，如“需要[意识力扫描]”或“精神污染度低于50”。无则填“无”。\n    - `互动1` - `互动4`: 主角可以对该点执行的4个具体动作，必须填满。例如：“扫描信息”、“尝试启动”、“谨慎绕开”、“物理破坏”。\n## 思考逻辑:\n- 若`次级区域表`重建，此表也必须重建。**【【【绝对禁止】】】**: 将`友方及关键人物表`中的角色写入此表。\n## 操作节点:\n- `initNode`: 【场景切换时】为每个次级区域设计关键点，【总数不得少于3個】，互动选项必须填满4个。\n- `deleteNode`: 【场景切换时或关键点消失时】删除。\n- `updateNode`: 当关键点状态或互动选项因剧情发生变化时，更新对应行。\n- `insertNode`: 【剧情需要时】插入新关键点，互动选项必须填满4个。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 5, "displayIndex": 5, "extensions": {}}, "6": {"key": ["主角信息表", "tableIndex4", "人物名称", "性别", "年龄", "外貌特征", "进化方向", "背景故事", "性格特点"], "keysecondary": ["表格定义 - 主角信息表 (4)"], "comment": "Generated for: 表格定义 - 主角信息表 (4)", "content": "# 表格：主角信息 (tableIndex: 4)\n## 列名:\n- `人物名称`\n- `性别/年龄`\n- `外貌特征`\n- `进化方向/称号`\n- `背景故事概要`\n- `性格特点`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远有且仅有一行，所有单元格严禁留空！记录主角的核心档案。\n- **填表时机**: 游戏初始化时填写，通常不改变。\n## 思考逻辑:\n- 只有在发生导致主角根本性转变的重大事件时才`update`此表，例如进化方向彻底改变或获得公认的称号。\n## 操作节点:\n- `initNode`: 【初始化】首次生成时，必须使用 insertRow 插入一行。\n- `deleteNode`: 【禁止删除】\n- `updateNode`: 当主角的核心身份信息（如称号）因重大事件发生永久性改变时，更新对应列。\n- `insertNode`: 【禁止插入】", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 6, "displayIndex": 6, "extensions": {}}, "7": {"key": ["主角状态表", "tableIndex5", "当前状态描述", "体力值", "精神污染度", "所在区域", "细胞活力", "签证状态", "互动", "场景图片"], "keysecondary": ["表格定义 - 主角状态表 (5)"], "comment": "Generated for: 表格定义 - 主角状态表 (5)", "content": "# 表格：主角状态 (tableIndex: 5)\n## 列名:\n- `当前状态描述`\n- `体力值`\n- `精神污染度`\n- `当前所在主要区域`\n- `当前所在次级区域`\n- `细胞活力`\n- `当前签证状态`\n- `互动1`\n- `互动2`\n- `互动3`\n- `互动4`\n- `场景图片`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远有且仅有一行，除`场景图片`和`签证状态`外所有单元格严禁留空！\n- **填表时机**: 每轮交互都必须检查并更新。\n- **各列填表指导**:\n    - `当前状态描述`: 主角身体、情绪的简要描述，如“高度警惕”、“意识模糊”。\n    - `体力值`: 以百分比表示的体力状况，如“85%”。\n    - `精神污染度`: 理智状态的量化，0为正常，100为彻底堕落，如“15/100”。\n    - `当前所在主要区域`/`次级区域`: 必须是表1/2中存在的名称。\n    - `细胞活力`: 反映身体潜能和恢复力的核心值。\n    - `当前签证状态`: 格式为“世界名签证/剩余N小时/天”，若当前世界无签证则填“无”。\n    - `互动1`-`互动4`: 主角可以对自己执行的动作，如“检查状态”、“规划路线”、“发动[意识力扫描]”、“使用特殊物品”。必须填满。\n    - `场景图片`: 根据当前场景匹配，找不到则留空。\n## 思考逻辑:\n- **【强制检查与更新】** 每轮必须更新位置、核心状态值、互动选项和签证时间（如果有）。\n## 操作节点:\n- `initNode`: 【初始化】首次生成时，必须使用 insertRow 插入一行，并填满所有初始数据。\n- `deleteNode`: 【禁止删除】\n- `updateNode`: 【每轮必须检查与更新】必须更新位置、互动选项、核心状态值、和场景图片。\n    - **【【场景图片选择逻辑】】**: 1. **默认留空**。 2. **遍历匹配**: 优先寻找与**当前副本世界**或**当前状态描述**（如“激战中”、“数据化”）匹配的图片。 3. **未能匹配则保持留空**。\n- `insertNode`: 【禁止插入】", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 7, "displayIndex": 7, "extensions": {}}, "8": {"key": ["友方关键人物表", "tableIndex6", "姓名", "性别", "年龄", "外貌特征", "身份", "性格特点", "进化能力", "与主角关系", "当前状态", "当前目标", "好感度", "信赖度", "心理活动", "互动", "在场", "失联", "长期角色", "头像"], "keysecondary": ["表格定义 - 友方及关键人物表 (6)"], "comment": "Generated for: 表格定义 - 友方及关键人物表 (6)", "content": "# 表格：友方及关键人物表 (tableIndex: 6)\n## 列名:\n- `姓名`\n- `性别/年龄`\n- `外貌特征`\n- `身份/称号`\n- `性格特点`\n- `进化能力`\n- `与主角关系`\n- `当前状态`\n- `当前目标`\n- `好感度/信赖度`\n- `心理活动(AI用)`\n- `互动1`\n- `互动2`\n- `互动3`\n- `互动4`\n- `是否在场/失联`\n- `离场/失联轮数`\n- `是否为长期剧情重要角色`\n- `头像`\n## 注意事项 (note):\n【【【绝对规则】】】此表可以为空。但若不为空，则除`头像`外所有单元格严禁留空！\n- **填表时机**: 关键人物登场、离场或状态发生重要变化时。\n- **各列填表指导**:\n    - `进化能力`: 该角色的核心能力名称和简介。\n    - `当前状态`: 人物当前身体、情绪、位置的简要描述。\n    - `当前目标`: 该角色当前行动的主要动机。\n    - `心理活动(AI用)`: 人物内心的真实想法，主角不可见，供AI决策。\n    - `互动1`-`互动4`: 主角能对该NPC做的4个具体行为，必须填满。\n    - `是否在场/失联`: '在场'、'不在场'、'失联'。\n    - `离场/失联轮数`: 若不在场/失联, 记录已发生的轮数, 否则为0。\n## 思考逻辑:\n- **【强制全面检查】**: 每轮必须更新所有人物的`当前状态`和`离场/失联轮数`。\n- **删除检查**: `是否为长期剧情重要角色`为`否`且`离场/失联轮数` > 50的角色，计划`delete`。\n- **头像匹配**: 【姓名 -> 特征】顺序，从`图片资源表(12)`匹配，【禁止重复】，找不到则【留空】。\n## 操作节点:\n- `initNode`: 【新关键人物出场时】插入新行并填满所有核心信息，`离场/失联轮数`置为0。\n- `deleteNode`: 当人物`是否为长期剧情重要角色`为`否`，且`离场/失联轮数`【大于50】时，【必须】删除。\n- `updateNode`: 【每轮必须检查与更新】更新在场人物的状态和互动；更新不在场/失联人物的状态，并将其`离场/失联轮数`+1。\n- `insertNode`: 【新关键人物出场时】插入新行，`离场/失联轮数`置为0。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 8, "displayIndex": 8, "extensions": {}}, "9": {"key": ["进化能力表", "tableIndex7", "能力名称", "类别", "效果", "消耗", "冷却", "当前状态"], "keysecondary": ["表格定义 - 进化能力表 (7)"], "comment": "Generated for: 表格定义 - 进化能力表 (7)", "content": "# 表格：进化能力表 (tableIndex: 7)\n## 列名:\n- `能力名称`\n- `类别`\n- `效果/用法描述`\n- `消耗/冷却`\n- `当前状态`\n## 注意事项 (note):\n【【【绝对规则】】】此表可以为空。但若不为空，则所有单元格严禁留空！记录主角的进化能力。\n- **填表时机**: 主角领悟、进化、或暂时无法使用能力时。\n- **各列填表指导**:\n    - `类别`: '意识力型', '身体强化型', '物品化型', '规则干涉型' 等。\n    - `效果/用法描述`: 对能力作用方式的详细描述。\n    - `消耗/冷却`: 使用能力需要付出的代价，如“消耗大量体力”、“冷却24小时”。\n    - `当前状态`: '可用', '冷却中', '已透支', '未激活'。\n## 思考逻辑:\n- **初始化时必须检查**并生成初始能力。使用能力后要及时更新`当前状态`。\n## 操作节点:\n- `initNode`: 【初始化时必须检查】根据初始剧情和主角设定，生成1-3个初始能力。\n- `deleteNode`: 当主角彻底失去能力时，删除对应行。\n- `updateNode`: 当能力使用后进入冷却，或能力进化效果发生变化时，更新对应行。\n- `insertNode`: 当主角领悟或获得新能力时，插入新行。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 9, "displayIndex": 9, "extensions": {}}, "10": {"key": ["特殊物品表", "tableIndex8", "物品名称", "数量", "来源世界", "类别", "效果", "用途", "使用限制", "代价", "卡片化"], "keysecondary": ["表格定义 - 特殊物品表 (8)"], "comment": "Generated for: 表格定义 - 特殊物品表 (8)", "content": "# 表格：特殊物品表 (tableIndex: 8)\n## 列名:\n- `物品名称`\n- `数量`\n- `来源世界`\n- `类别`\n- `效果/用途`\n- `使用限制/代价`\n- `是否为[卡片化]物品`\n## 注意事项 (note):\n【【【绝对规则】】】此表可以为空。但若不为空，则所有单元格严禁留空！记录从各世界获得的特殊物品。\n- **填表时机**: 主角获得、消耗、丢弃特殊物品时。\n- **各列填表指导**:\n    - `来源世界`: 获得该物品的副本世界名称。\n    - `类别`: '消耗品', '装备', '信息媒介', '污染物' 等。\n    - `效果/用途`: 物品的具体功能，通常很奇特。\n    - `使用限制/代价`: 使用物品的条件或副作用，如“仅在满月时可用”、“使用者会随机遗忘一段记忆”。\n    - `是否为[卡片化]物品`: '是'或'否'，用于追踪主角的特殊能力产物。\n## 思考逻辑:\n- **初始化时必须检查**并生成初始物品。特别注意`[卡片化]`能力的触发，会`insert`新物品。\n## 操作节点:\n- `initNode`: 【初始化时必须检查】根据初始剧情，为主角生成符合其设定的初始特殊物品。\n- `deleteNode`: 当物品被消耗、摧毁或丢弃时，删除对应行。\n- `updateNode`: 当物品数量或状态发生变化时，更新对应行。\n- `insertNode`: 当主角获得新物品，特别是通过[卡片化]能力时，插入新行。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 10, "displayIndex": 10, "extensions": {}}, "11": {"key": ["目标与线索表", "tableIndex9", "目标类型", "目标描述", "发布者", "来源", "关键线索", "进度", "时限", "收益", "后果"], "keysecondary": ["表格定义 - 目标与线索表 (9)"], "comment": "Generated for: 表格定义 - 目标与线索表 (9)", "content": "# 表格：目标与线索表 (tableIndex: 9)\n## 列名:\n- `目标类型`\n- `目标描述`\n- `发布者/来源`\n- `关键线索`\n- `当前进度`\n- `时限`\n- `预期收益/后果`\n## 注意事项 (note):\n【【【绝对规则】】】此表可以为空。但若不为空，则所有单元格严禁留空！\n- **填表时机**: 主角确立新目标（生存、探索、寻人等）、找到线索或完成目标时。\n- **各列填表指导**:\n    - `目标类型`: '世界通关', '生存', '寻找物品', '寻找人物', '信息探索'。\n    - `发布者/来源`: 任务的来源，如“世界规则”、“NPC委托”、“自行决定”。\n    - `关键线索`: 完成目标所需的核心信息，用分号分隔。\n    - `预期收益/后果`: 完成或失败的可能结果。\n## 思考逻辑:\n- 世界切换时，通常会`insert`一个新的“世界通关”目标。\n## 操作节点:\n- `initNode`: 当存在初始目标或事件时，插入对应行。\n- `deleteNode`: 当目标完成、失败或被放弃时，删除对应行。\n- `updateNode`: 当目标进度或关键线索发生变化时，更新对应行。\n- `insertNode`: 当主角接受或确立新目标时，插入新行。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 11, "displayIndex": 11, "extensions": {}}, "12": {"key": ["短期记忆纪要", "tableIndex10", "副本内时间", "地点", "涉及角色", "关键遭遇", "事件", "目标变化", "线索变化", "重要决策", "纪要"], "keysecondary": ["表格定义 - 短期记忆纪要 (10)"], "comment": "Generated for: 表格定义 - 短期记忆纪要 (10)", "content": "# 表格：短期记忆纪要 (tableIndex: 10)\n## 列名:\n- `副本内时间`\n- `地点`\n- `涉及角色`\n- `关键遭遇/事件`\n- `目标/线索变化`\n- `重要决策`\n- `纪要(50字+)`\n## 注意事项 (note):\n【【【绝对规则】】】此表永远不能为空，所有单元格严禁留空！\n- **填表时机**: 每轮交互结束后【必须】插入一条新记录。\n## 思考逻辑:\n- **【【固定操作】】** 每轮都必须计划`insert`一条新的总结行，**且`纪要`内容不得少于50字**。\n## 操作节点:\n- `initNode`: 在第一轮交互结束后，总结并插入第一条记录。\n- `deleteNode`: 当`长期记忆归档`生成后，此表的【所有行】都必须被逆序删除。\n- `updateNode`: 【禁止更新】\n- `insertNode`: 【【每轮必须插入】】在每轮交互结束后，根据本轮剧情，总结并插入一行新记录。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 12, "displayIndex": 12, "extensions": {}}, "13": {"key": ["长期记忆归档", "tableIndex11", "时间跨度", "经历世界", "关键人物", "主线发展", "能力变化", "物品变化", "世界观认知变化", "重要得失", "纪要"], "keysecondary": ["表格定义 - 长期记忆归档 (11)"], "comment": "Generated for: 表格定义 - 长期记忆归档 (11)", "content": "# 表格：长期记忆归档 (tableIndex: 11)\n## 列名:\n- `时间跨度`\n- `经历世界`\n- `关键人物`\n- `主线发展`\n- `能力/物品变化`\n- `世界观认知变化`\n- `重要得失`\n- `纪要(300字+)`\n## 注意事项 (note):\n【【【绝对规则】】】此表大部分时间为空。\n- **填表时机**: 当`短期记忆纪要`表的行数累积到15条以上时【必须】触发。\n## 思考逻辑:\n- **【【强制触发检查】】** **每轮都必须检查** `短期记忆纪要`表的行数。若发现行数 **> 15**，则【必须立即】计划`insert`一条大总结（**`纪要`内容不得少于300字**），并计划`delete`所有短期记忆纪要的行。\n## 操作节点:\n- `initNode`: 【禁止初始化】\n- `deleteNode`: 【禁止删除】\n- `updateNode`: 【禁止更新】\n- `insertNode`: 【触发式插入】当`短期记忆纪要`表的行数【大于15】时，【必须立即】整合这些历史，生成一条大总结并插入此表。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 13, "displayIndex": 13, "extensions": {}}, "14": {"key": ["图片资源表", "tableIndex12", "人物图片", "场景图片"], "keysecondary": ["表格定义 - 图片资源表 (12)"], "comment": "Generated for: 表格定义 - 图片资源表 (12)", "content": "# 表格：图片资源表 (tableIndex: 12)\n## 列名:\n- `人物图片`\n- `场景图片`\n## 注意事项 (note):\n【【【AI禁止修改此表】】】这是一个只读的资源库，用于为`主角状态`和`友方及关键人物表`提供图片文件名。严禁自行创造不存在于此表中的图片名。\n## 操作节点:\n- `initNode`: 【AI禁止操作】\n- `deleteNode`: 【AI禁止操作】\n- `updateNode`: 【AI禁止操作】\n- `insertNode`: 【AI禁止操作】", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "uid": 14, "displayIndex": 14, "extensions": {}}}}