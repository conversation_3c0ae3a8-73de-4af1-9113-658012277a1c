import os
import re
import sys
import tkinter as tk
from tkinter import ttk, messagebox

class TraeParametersApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Trae Parameters")
        self.root.geometry("430x320")
        self.center_window()  
        self.setup_ui()

    def center_window(self):
        # 获取屏幕宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算窗口位置
        x = (screen_width - 430) // 2
        y = (screen_height - 320) // 2

        # 设置窗口位置
        self.root.geometry(f"430x320+{x}+{y}")

    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        self.parameters = {
            "USER_ID": tk.StringVar(),
            "REFRESH_TOKEN": tk.StringVar(),
            "TOKEN": tk.StringVar(),
            "CLIENT_ID": tk.StringVar(),
            "APP_ID": tk.StringVar()
        }

        for i, (key, var) in enumerate(self.parameters.items()):
            ttk.Label(main_frame, text=key).grid(row=i, column=0, sticky="w", pady=5)
            entry = ttk.Entry(main_frame, textvariable=var, width=40)
            entry.grid(row=i, column=1, sticky="ew", pady=5)
            entry.bind("<Button-1>", lambda e, k=key: self.copy_to_clipboard(k))

        ttk.Button(main_frame, text="获取参数", command=self.get_parameters).grid(row=len(self.parameters), column=0, columnspan=2, pady=10)

        # 添加提示标签
        self.info_label = ttk.Label(main_frame, text="")
        self.info_label.grid(row=len(self.parameters)+1, column=0, columnspan=2, pady=5)

        # 添加复制提示标签
        hint_label = ttk.Label(main_frame, text="点击文本框可复制对应内容", foreground="gray")
        hint_label.grid(row=len(self.parameters)+2, column=0, columnspan=2, pady=5)

    def copy_to_clipboard(self, key):
        content = self.parameters[key].get()
        self.root.clipboard_clear()
        self.root.clipboard_append(content)
        self.info_label.config(text=f"{key} 内容已复制")

    def get_parameters(self):
        try:
            # 检查操作系统
            if not os.name == 'nt':
                raise Exception("This script only supports Windows")

            # 检查Trae安装
            trae_dir = os.path.join(os.getenv('APPDATA'), 'Trae')
            if not os.path.exists(trae_dir):
                raise Exception("Trae installation not found")

            # 找到最新的日志目录
            log_dirs = [d for d in os.listdir(os.path.join(trae_dir, 'logs')) if os.path.isdir(os.path.join(trae_dir, 'logs', d))]
            if not log_dirs:
                raise Exception("No log directories found")

            log_dirs.sort()
            newest_log_dir = os.path.join(trae_dir, 'logs', log_dirs[-1])
            main_log = os.path.join(newest_log_dir, 'main.log')
            if not os.path.exists(main_log):
                raise Exception(f"main.log not found in the newest log directory: {log_dirs[-1]}")

            # 读取main.log并提取ClientID
            with open(main_log, 'r', encoding='utf-8') as file:
                content = file.read()
            
            client_id_match = re.search(r'"userJwt":"{\\"ClientID\\":\\"([^\\"]*)\\"', content)
            if not client_id_match:
                raise Exception(f"Could not find ClientID in main.log in directory: {log_dirs[-1]}")
            
            self.parameters["CLIENT_ID"].set(client_id_match.group(1))

            # 寻找ai_1_stdout.log并提取AppID
            modulars_path = os.path.join(newest_log_dir, 'Modular')
            app_log = os.path.join(modulars_path, 'ai_1_stdout.log')
            if not os.path.exists(app_log):
                raise Exception(f"ai_1_stdout.log not found in the latest log directory: {log_dirs[-1]}")

            with open(app_log, 'r', encoding='utf-8') as file:
                app_content = file.read()

            app_id_match = re.search(r'"x-app-id": "([^"]*)"', app_content)
            if not app_id_match:
                raise Exception("Could not find APP_ID in ai_1_stdout.log")
            
            self.parameters["APP_ID"].set(app_id_match.group(1))

            # 提取认证信息
            storage_file = os.path.join(os.getenv('APPDATA'), 'Trae', 'User', 'globalStorage', 'storage.json')
            if not os.path.exists(storage_file):
                raise Exception("storage.json file not found")

            with open(storage_file, 'r', encoding='utf-8') as file:
                storage_content = file.read()

            auth_match = re.search(r'"iCubeAuthInfo://icube\.cloudide": "({.*?})"', storage_content)
            if not auth_match:
                raise Exception("Could not find authentication information in storage.json")

            json_str = auth_match.group(1).replace('\\"', '"')
            refresh_token_match = re.search(r'refreshToken":"(.*?)",".*?userId":"(\d+)"', json_str)
            token_match = re.search(r'token":"(.*?)"', json_str)

            if not (refresh_token_match and token_match):
                raise Exception("Could not extract refresh token, user ID, and token from storage.json")

            self.parameters["USER_ID"].set(refresh_token_match.group(2))
            self.parameters["REFRESH_TOKEN"].set(refresh_token_match.group(1))
            self.parameters["TOKEN"].set(token_match.group(1))

        except Exception as e:
            messagebox.showerror("错误", str(e))

if __name__ == "__main__":
    root = tk.Tk()
    app = TraeParametersApp(root)
    root.mainloop()