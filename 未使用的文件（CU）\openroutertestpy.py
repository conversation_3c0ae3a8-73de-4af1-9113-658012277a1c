# -*- coding: utf-8 -*-
import os
import requests
import uvicorn
import threading
from fastapi import FastAPI, Request, HTTPException, Security
from fastapi.security import APIKeyHeader
from fastapi.responses import StreamingResponse, Response
import logging
import time # 引入 time 模块

# --- 配置 ---
KEY_FILE = "keys.txt"
LISTEN_HOST = "0.0.0.0"
LISTEN_PORT = 3323
OPENROUTER_API_BASE = "https://openrouter.ai/api/v1"
PROXY_API_KEY = "sk-mariahlamb" # !! 请务必修改 !!
# --- ---

# --- 全局变量 ---
api_keys = []
current_key_index = 0
lock = threading.Lock() # 锁仍然是必要的
api_key_header = APIKeyHeader(name="Authorization", auto_error=False)
# --- ---

# --- 日志配置 ---
# 修改日志格式，增加线程ID，方便追踪并发问题
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(thread)d] - %(message)s')
logger = logging.getLogger(__name__)
# --- ---

# --- 加载 OpenRouter API Keys ---
def load_keys():
    global api_keys, current_key_index
    logger.info(f"尝试从 '{KEY_FILE}' 加载 OpenRouter Keys...")
    loaded_keys = []
    try:
        if os.path.exists(KEY_FILE):
            with open(KEY_FILE, 'r', encoding='utf-8') as f: # 指定 utf-8 编码
                for i, line in enumerate(f):
                    key = line.strip()
                    if key and not key.startswith('#'):
                        if key.startswith("sk-or-v1-"): # 做一个简单的格式检查
                           loaded_keys.append(key)
                           logger.info(f"  加载 Key #{len(loaded_keys)}: ...{key[-4:]}")
                        else:
                           logger.warning(f"  忽略第 {i+1} 行无效格式的 Key: '{key[:10]}...' (应以 sk-or-v1- 开头)")
                    # else:
                        # logger.debug(f"  忽略第 {i+1} 行空行或注释行") # 可以取消注释以查看详情

            if not loaded_keys:
                logger.error(f"错误：'{KEY_FILE}' 文件为空、仅包含注释/空行或未包含有效的 Keys (需以 sk-or-v1- 开头)。")
                api_keys = [] # 确保列表为空
                current_key_index = 0
                return False
            else:
                # 使用 with lock 确保线程安全地更新全局列表和索引
                with lock:
                    api_keys = loaded_keys
                    current_key_index = 0 # 每次重新加载都重置索引
                logger.info(f"成功加载 {len(api_keys)} 个有效的 OpenRouter API Keys。轮询将从第一个 Key 开始。")
                return True
        else:
            # ... (文件不存在时的处理逻辑保持不变) ...
            logger.error(f"错误：未找到 OpenRouter API Key 文件 '{KEY_FILE}'。")
            logger.info(f"请在该程序同目录下创建一个 '{KEY_FILE}' 文件，每行写入一个 OpenRouter API Key (sk-or-...)。")
            try:
                with open(KEY_FILE, 'w', encoding='utf-8') as f:
                    f.write("# 请将下面替换为你的真实 OpenRouter API Keys，每行一个 (以 sk-or-v1- 开头)\n")
                    f.write("sk-or-v1-abc...xyz\n")
                    f.write("sk-or-v1-123...789\n")
                logger.info(f"已创建示例 '{KEY_FILE}' 文件，请修改后再运行。")
            except Exception as e_create:
                 logger.error(f"创建示例 '{KEY_FILE}' 文件失败: {e_create}")
            api_keys = []
            current_key_index = 0
            return False
    except Exception as e:
        logger.exception(f"加载 OpenRouter API Keys 时发生严重错误: {e}")
        api_keys = []
        current_key_index = 0
        return False

# --- 获取下一个 OpenRouter API Key (轮询策略) ---
def get_next_openrouter_key():
    global current_key_index # 明确再次声明 global (虽然在函数外已有)
    # 使用锁确保对 api_keys 列表和 current_key_index 的访问和修改是原子性的
    with lock:
        if not api_keys:
            logger.error("请求 Key 时发现 api_keys 列表为空。")
            return None

        # 获取当前索引对应的 Key
        key_index_to_use = current_key_index
        selected_key = api_keys[key_index_to_use]

        # 计算下一个索引，并确保它回绕
        current_key_index = (key_index_to_use + 1) % len(api_keys)

        # 增加日志，记录选择了哪个 key 和下一个索引将是什么
        logger.info(f"轮询: 使用索引 {key_index_to_use} 的 Key (...{selected_key[-4:]})。下一个索引将是 {current_key_index}。")

        return selected_key

# --- 验证客户端提供的 API Key ---
# (verify_api_key 函数保持不变)
async def verify_api_key(auth_header: str = Security(api_key_header)):
    # ... (之前的代码) ...
    if PROXY_API_KEY == "change-me-to-your-secret-proxy-key":
         logger.warning("安全警告：你正在使用默认的代理 API Key 'change-me-to-your-secret-proxy-key'。请修改 proxy_v1.py 文件中的 PROXY_API_KEY 变量以确保安全！")
         # raise HTTPException(status_code=500, detail="代理配置不安全，请管理员修改默认的 PROXY_API_KEY")

    if auth_header is None:
        logger.warning("拒绝访问：缺少 Authorization header")
        raise HTTPException(status_code=401, detail="缺少 Authorization header")

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        logger.warning("拒绝访问：Authorization header 格式错误 (应为 'Bearer YOUR_KEY')")
        raise HTTPException(status_code=401, detail="Authorization header 格式错误 (应为 'Bearer YOUR_KEY')")

    client_key = parts[1]
    if client_key != PROXY_API_KEY:
        # 隐藏大部分客户端 Key，只显示最后几位用于调试
        client_key_suffix = "..." + client_key[-4:] if len(client_key) > 4 else client_key
        logger.warning(f"拒绝访问：提供的客户端 API Key 不正确 (收到: {client_key_suffix})")
        raise HTTPException(status_code=401, detail="提供的 API Key 不正确")

    # logger.info("客户端 API Key 验证通过")
    return True

# --- 创建 FastAPI 应用 ---
app = FastAPI(title="OpenRouter Load Balancer Proxy (OpenAI Style)")

@app.on_event("startup")
async def startup_event():
    logger.info("--- OpenRouter API Key 负载均衡代理 (OpenAI 兼容模式) v1.1 ---")
    load_keys() # 启动时加载 Keys
    if not api_keys:
        logger.warning("警告：OpenRouter API Keys 未能成功加载或列表为空，代理将无法转发请求到 OpenRouter。")
    if PROXY_API_KEY == "change-me-to-your-secret-proxy-key":
         logger.warning("!!! 安全警告：请务必修改脚本中的 'PROXY_API_KEY' 为你自己的密钥 !!!")


# --- 定义代理路由 /v1/{full_path:path} ---
@app.api_route("/v1/{full_path:path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH", "TRACE"])
async def forward_request_v1(
    request: Request,
    full_path: str,
    is_authorized: bool = Security(verify_api_key) # 客户端 Key 验证
):
    start_time = time.time()
    logger.info(f"收到请求: {request.method} {request.url.path}")

    # 尝试获取下一个 OpenRouter Key
    selected_openrouter_key = get_next_openrouter_key()

    if not selected_openrouter_key:
        logger.error("无法获取 OpenRouter API Key (列表为空或加载失败)。请检查 keys.txt 文件和启动日志。")
        return Response(content="错误：代理配置错误，无法获取 OpenRouter API Key", status_code=503) # Service Unavailable

    or_key_suffix = selected_openrouter_key[-4:]
    target_url = f"{OPENROUTER_API_BASE}/{full_path}"
    logger.info(f"准备转发到: {target_url} (使用 OR Key: ...{or_key_suffix})")

    # ... (准备 headers, request_body, 判断 is_streaming 的逻辑保持不变) ...
    headers = dict(request.headers)
    headers["Authorization"] = f"Bearer {selected_openrouter_key}"
    headers.pop("host", None)
    headers.pop("content-length", None)
    headers["HTTP-Referer"] = headers.get("HTTP-Referer", f"http://localhost:{LISTEN_PORT}") # 伪造来源
    headers["X-Title"] = headers.get("X-Title", "OpenAI-Style-Proxy/1.1") # 伪造标题
    request_body = await request.body()
    is_streaming = False
    content_type = request.headers.get("content-type", "")
    if "application/json" in content_type:
        try:
            body_str = request_body.decode('utf-8')
            if body_str:
                import json
                body_json = json.loads(body_str)
                if isinstance(body_json, dict) and body_json.get("stream") is True:
                    is_streaming = True
                    logger.info("检测到流式请求 (stream=true)")
        except Exception as e:
            logger.warning(f"解析 JSON 请求体以判断 stream 时出错: {e}")


    try:
        response = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            data=request_body,
            stream=is_streaming,
            timeout=600
        )
        # 增加响应状态码的日志
        logger.info(f"收到来自 OpenRouter 的响应: Status {response.status_code} (Key: ...{or_key_suffix})")

        # 检查是否是速率限制错误
        if response.status_code == 429:
             logger.warning(f"!!! OpenRouter Key ...{or_key_suffix} 触发速率限制 (429 Too Many Requests) !!!")
             # 可以在这里添加逻辑：暂时禁用这个 Key，或者只是记录下来

        # --- 处理响应 ---
        response_headers = dict(response.headers)
        response_headers.pop('content-encoding', None)
        response_headers.pop('transfer-encoding', None)
        response_headers.pop('content-length', None)

        if is_streaming:
            # logger.info("开始流式响应转发...") # 可以取消注释来调试流开始
            async def stream_content():
                stream_start_time = time.time()
                chunk_count = 0
                try:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                           chunk_count += 1
                           # logger.debug(f"Streaming chunk #{chunk_count}...") # 调试流块
                           yield chunk
                except requests.exceptions.ChunkedEncodingError as e:
                     logger.error(f"流式传输中发生 ChunkedEncodingError (Key: ...{or_key_suffix}): {e}")
                     yield f"Proxy Stream Error: {e}".encode()
                except Exception as e:
                    logger.exception(f"流式传输中发生未知错误 (Key: ...{or_key_suffix}): {e}")
                    yield f"Proxy Unknown Stream Error: {e}".encode()
                finally:
                    response.close()
                    stream_duration = time.time() - stream_start_time
                    logger.info(f"完成流式响应转发: {request.method} {request.url.path} (Key: ...{or_key_suffix}, Chunks: {chunk_count}, Duration: {stream_duration:.2f}s)")

            return StreamingResponse(
                stream_content(),
                status_code=response.status_code,
                headers=response_headers,
                media_type=response.headers.get("content-type", "text/event-stream")
            )
        else:
             # 非流式响应处理
             resp_content = response.content
             # 如果是非流式但仍然遇到速率限制，记录响应体（可能包含有用信息）
             if response.status_code == 429:
                 try:
                     error_detail = resp_content.decode('utf-8', errors='ignore')
                     logger.warning(f"Rate limit error detail (Key: ...{or_key_suffix}): {error_detail[:500]}") # 记录部分错误信息
                 except Exception:
                     logger.warning(f"Rate limit error for Key ...{or_key_suffix}, unable to decode response body.")

             total_time = time.time() - start_time
             logger.info(f"完成非流式响应转发: {request.method} {request.url.path} (Key: ...{or_key_suffix}, Status: {response.status_code}, Duration: {total_time:.2f}s)")
             return Response(
                content=resp_content,
                status_code=response.status_code,
                headers=response_headers,
                media_type=response.headers.get("content-type")
            )

    except requests.exceptions.Timeout:
         total_time = time.time() - start_time
         logger.error(f"转发请求超时 (Key: ...{or_key_suffix}): {target_url} (Duration: {total_time:.2f}s)")
         return Response(content="代理转发请求超时", status_code=504)
    except requests.exceptions.RequestException as e:
        total_time = time.time() - start_time
        logger.exception(f"转发请求时出错 (Key: ...{or_key_suffix}): {e} (Duration: {total_time:.2f}s)")
        return Response(content=f"代理转发错误: {e}", status_code=502)

# --- 主程序入口 ---
if __name__ == "__main__":
    # 检查 PROXY_API_KEY 是否已修改
    if PROXY_API_KEY == "change-me-to-your-secret-proxy-key":
        logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        logger.critical("!!! 严重安全警告：PROXY_API_KEY 未修改，请立即修改脚本并重启 !!!")
        logger.critical("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        # 可以考虑在这里退出程序，强制用户修改
        # import sys
        # sys.exit("错误：PROXY_API_KEY 未设置，为了安全已停止运行。")

    # 运行服务器
    logger.info(f"代理服务器将在 http://{LISTEN_HOST}:{LISTEN_PORT} 上启动")
    logger.info(f"客户端需配置 API Base 为: http://<你的IP>:{LISTEN_PORT}/v1")
    logger.info(f"客户端需在 Authorization Header 中提供 Key: Bearer YOUR_PROXY_API_KEY ({PROXY_API_KEY[:4]}...{PROXY_API_KEY[-4:]})")

    # 使用 uvicorn.run 运行，默认通常是单进程
    # 如果需要多worker，需要考虑状态共享问题，目前的全局变量方式不适用于多进程
    uvicorn.run(app, host=LISTEN_HOST, port=LISTEN_PORT)