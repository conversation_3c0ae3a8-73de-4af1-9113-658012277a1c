gapi.loaded_1(function(_){var window=this;
_.jo=function(a){this.T=a};_.g=_.jo.prototype;_.g.cL=function(a){this.T.anchor=a;return this};_.g.Zi=function(){return this.T.anchor};_.g.dL=function(a){this.T.anchorPosition=a};_.g.Sd=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Me=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.setZIndex=function(a){this.T.zIndex=a;return this};_.g.getZIndex=function(){return this.T.zIndex};
_.ko=function(a){a.T.connectWithQueryParams=!0;return a};
_.t("gapi.iframes.create",_.xm);
_.t("gapi.iframes.registerStyle",_.Un);_.t("gapi.iframes.registerBeforeOpenStyle",_.Xn);_.t("gapi.iframes.getStyle",_.Vn);_.t("gapi.iframes.getBeforeOpenStyle",_.Yn);_.t("gapi.iframes.registerIframesApi",_.hn);_.t("gapi.iframes.registerIframesApiHandler",_.jn);_.t("gapi.iframes.getContext",_.ln);_.t("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",_.cn);_.t("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.dn);_.t("gapi.iframes.makeWhiteListIframesFilter",_.en);_.t("gapi.iframes.Context",_.Pn);
_.t("gapi.iframes.Context.prototype.isDisposed",_.Pn.prototype.isDisposed);_.t("gapi.iframes.Context.prototype.getWindow",_.Pn.prototype.getWindow);_.t("gapi.iframes.Context.prototype.getFrameName",_.Pn.prototype.getFrameName);_.t("gapi.iframes.Context.prototype.getGlobalParam",_.Pn.prototype.getGlobalParam);_.t("gapi.iframes.Context.prototype.setGlobalParam",_.Pn.prototype.setGlobalParam);_.t("gapi.iframes.Context.prototype.open",_.Pn.prototype.open);
_.t("gapi.iframes.Context.prototype.openChild",_.Pn.prototype.openChild);_.t("gapi.iframes.Context.prototype.getParentIframe",_.Pn.prototype.getParentIframe);_.t("gapi.iframes.Context.prototype.closeSelf",_.Pn.prototype.closeSelf);_.t("gapi.iframes.Context.prototype.restyleSelf",_.Pn.prototype.restyleSelf);_.t("gapi.iframes.Context.prototype.setCloseSelfFilter",_.Pn.prototype.setCloseSelfFilter);_.t("gapi.iframes.Context.prototype.setRestyleSelfFilter",_.Pn.prototype.setRestyleSelfFilter);
_.t("gapi.iframes.Context.prototype.addOnConnectHandler",_.Pn.prototype.addOnConnectHandler);_.t("gapi.iframes.Context.prototype.removeOnConnectHandler",_.Pn.prototype.removeOnConnectHandler);_.t("gapi.iframes.Context.prototype.addOnOpenerHandler",_.Pn.prototype.addOnOpenerHandler);_.t("gapi.iframes.Context.prototype.connectIframes",_.Pn.prototype.connectIframes);_.t("gapi.iframes.Iframe",_.Ln);_.t("gapi.iframes.Iframe.prototype.isDisposed",_.Ln.prototype.isDisposed);
_.t("gapi.iframes.Iframe.prototype.getContext",_.Ln.prototype.getContext);_.t("gapi.iframes.Iframe.prototype.getFrameName",_.Ln.prototype.getFrameName);_.t("gapi.iframes.Iframe.prototype.getId",_.Ln.prototype.getId);_.t("gapi.iframes.Iframe.prototype.register",_.Ln.prototype.register);_.t("gapi.iframes.Iframe.prototype.unregister",_.Ln.prototype.unregister);_.t("gapi.iframes.Iframe.prototype.send",_.Ln.prototype.send);_.t("gapi.iframes.Iframe.prototype.applyIframesApi",_.Ln.prototype.applyIframesApi);
_.t("gapi.iframes.Iframe.prototype.getIframeEl",_.Ln.prototype.getIframeEl);_.t("gapi.iframes.Iframe.prototype.getSiteEl",_.Ln.prototype.getSiteEl);_.t("gapi.iframes.Iframe.prototype.setSiteEl",_.Ln.prototype.setSiteEl);_.t("gapi.iframes.Iframe.prototype.getWindow",_.Ln.prototype.getWindow);_.t("gapi.iframes.Iframe.prototype.getOrigin",_.Ln.prototype.getOrigin);_.t("gapi.iframes.Iframe.prototype.close",_.Ln.prototype.close);_.t("gapi.iframes.Iframe.prototype.restyle",_.Ln.prototype.restyle);
_.t("gapi.iframes.Iframe.prototype.restyleDone",_.Ln.prototype.Or);_.t("gapi.iframes.Iframe.prototype.registerWasRestyled",_.Ln.prototype.registerWasRestyled);_.t("gapi.iframes.Iframe.prototype.registerWasClosed",_.Ln.prototype.registerWasClosed);_.t("gapi.iframes.Iframe.prototype.getParam",_.Ln.prototype.getParam);_.t("gapi.iframes.Iframe.prototype.setParam",_.Ln.prototype.setParam);_.t("gapi.iframes.Iframe.prototype.ping",_.Ln.prototype.ping);_.t("gapi.iframes.Iframe.prototype.getOpenParams",_.Ln.prototype.wc);
});
// Google Inc.
