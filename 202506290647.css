/*
 * 🌸 CherryStudio 多主题合集 🌸
 *
 * 设计师: 一位精通UI/UX与CSS的专家
 * 日期: 2024-08-01
 * 版本: 1.0
 *
 * --- 设计理念 ---
 * 本合集包含了五款为CherryStudio量身打造的独特主题 <sup data-citation='{&quot;id&quot;:1,&quot;url&quot;:&quot;D:\\PortableApps\\memoryable (1)\\参考资料\\cherry-studio-main\\温柔渐变触感主题-安装说明.md&quot;,&quot;title&quot;:&quot;D:\\PortableApps\\memoryable (1)\\参考资料\\cherry-studio-main\\温柔渐变触感主题-安装说明.md&quot;,&quot;content&quot;:&quot;享受您的新主题！ 🌸✨ 如果您喜欢这个主题，请考虑分享给其他CherryStudio用户。&quot;}'>1</sup>。
 * 每款主题都支持浅色与深色模式，并拥有不同程度的透明磨砂效果，
 * 旨在增强应用的视觉层次感，同时与您的桌面背景和谐共融 [<sup data-citation='{&quot;id&quot;:5,&quot;url&quot;:&quot;https://docs.cherry-ai.com&quot;,&quot;title&quot;:&quot;https://docs.cherry-ai.com&quot;,&quot;content&quot;:&quot;Cherry Studio 文档 开发文档 kai-fa-wen-dang 简体中文 Cherry Studio 项目简介 客户端下载 cherry-studiodownload 项目规划 cherry-studioplanning 功能介绍 cherry-studiopreview 对话界面 cherry-studiopreviewchat 智能体 cherry-studiopreviewage&quot;}'>5</sup>](https://docs.cherry-ai.com)。
 *
 * --- 主题列表 ---
 * 1. theme-ocean-breeze: 海洋微风 - 清新、简约、宁静。
 * 2. theme-forest-canopy: 森林秘境 - 自然、质朴、沉稳。
 * 3. theme-sunset-glow:   日落光辉 - 温暖、绚丽、梦幻。
 * 4. theme-minimalist-slate: 极简石板 - 现代、锋利、专注。
 * 5. theme-cyberpunk-neon:  赛博霓虹 - 未来、炫酷、前卫。
 *
 * --- 使用方法 ---
 * 1. 复制所有代码并粘贴至 CherryStudio 的自定义CSS设置中 [7]。
 * 2. 使用开发者工具将 <body> 的 class 属性更改为上述任一主题名称。
 *
 * --- 版权与许可 ---
 * 本代码基于 MIT 许可证开源，您可以自由使用与修改 <sup data-citation='{&quot;id&quot;:4,&quot;url&quot;:&quot;/*\n * 🌸 CherryStudio 清透...attachment: fixed;\n */\n&quot;,&quot;title&quot;:&quot;/*\n * 🌸 CherryStudio 清透...attachment: fixed;\n */\n&quot;,&quot;content&quot;:&quot;🌸 CherryStudio 清透温柔触感主题 🌸 设计理念：清透透明 + 温柔色调 + 轻盈质感 + 精致细腻 基于 CherryYoucss 透明度设计优化 特色功能： - 极致透明背景，最大化壁纸可见性 - 轻盈毛玻璃效果替代厚重渐变 - 细线边框定义元素边界 - 温柔色调的深色浅色主题自适应 - 防溢出的右键菜单和框架设计 安装方法： 1 复制此CSS代码 2 打开CherryStud&quot;}'>4</sup>。
 */

/* ================================================================== */
/* ========================= 字体导入 (可选) ========================= */
/* ================================================================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');

/* ================================================================== */
/* ========================= 基础变量与结构 ========================= */
/* ================================================================== */
:root {
  /* 基础结构与过渡效果，所有主题通用 */
  --cs-font-primary: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --cs-font-mono: 'JetBrains Mono', 'Maple Mono NF CN', monospace;
  --cs-transition-fast: 0.2s ease;
  --cs-transition-normal: 0.3s ease;
  --cs-right-click-menu-protection: none; /* 确保不影响右键菜单保护 */
}

/* ================================================================== */
/* ========================== 主题变量定义 ========================== */
/* ================================================================== */

/*
 * --------------------------------------------------
 * 1. 主题: Ocean Breeze (海洋微风)
 * --------------------------------------------------
 */
body.theme-ocean-breeze {
  --cs-radius-sm: 8px;
  --cs-radius-md: 12px;
  --cs-radius-lg: 16px;

  /* 浅色模式变量 */
  --cs-bg-color: rgba(240, 248, 255, 0.7); /* AliceBlue */
  --cs-panel-bg: rgba(255, 255, 255, 0.65);
  --cs-text-primary: #002D62; /* Dark Slate Blue */
  --cs-text-secondary: #005A9C;
  --cs-accent-primary: #318CE7; /* Bleu de France */
  --cs-accent-secondary: #89CFF0; /* Baby Blue */
  --cs-border-color: rgba(49, 140, 231, 0.3);
  --cs-shadow-color: rgba(0, 90, 156, 0.1);
  --cs-header-bg: linear-gradient(135deg, rgba(173, 216, 230, 0.7), rgba(135, 206, 250, 0.8)); /* LightBlue to LightSkyBlue */
  --cs-backdrop-blur: 12px;
  background-image: url('https://w.wallhaven.cc/full/m9/wallhaven-m99exk.jpg');
}

body.theme-ocean-breeze[theme-mode="dark"] {
  --cs-bg-color: rgba(0, 21, 41, 0.8); /* Dark Imperial Blue */
  --cs-panel-bg: rgba(1, 29, 56, 0.75);
  --cs-text-primary: #E0FFFF; /* Light Cyan */
  --cs-text-secondary: #B0E0E6; /* Powder Blue */
  --cs-accent-primary: #87CEFA; /* Light Sky Blue */
  --cs-accent-secondary: #4682B4; /* Steel Blue */
  --cs-border-color: rgba(70, 130, 180, 0.4);
  --cs-shadow-color: rgba(135, 206, 250, 0.1);
  --cs-header-bg: linear-gradient(135deg, rgba(25, 25, 112, 0.8), rgba(0, 0, 139, 0.9)); /* MidnightBlue to DarkBlue */
  background-image: url('https://w.wallhaven.cc/full/pk/wallhaven-pkgk9p.jpg');
}


/*
 * --------------------------------------------------
 * 2. 主题: Forest Canopy (森林秘境)
 * --------------------------------------------------
 */
body.theme-forest-canopy {
  --cs-radius-sm: 4px;
  --cs-radius-md: 6px;
  --cs-radius-lg: 8px;

  /* 浅色模式变量 */
  --cs-bg-color: rgba(240, 244, 240, 0.8);
  --cs-panel-bg: rgba(232, 239, 232, 0.75);
  --cs-text-primary: #3B2F2F; /* Dark Brown */
  --cs-text-secondary: #5a4a4a;
  --cs-accent-primary: #556B2F; /* Dark Olive Green */
  --cs-accent-secondary: #8B4513; /* Saddle Brown */
  --cs-border-color: rgba(139, 69, 19, 0.3);
  --cs-shadow-color: rgba(47, 40, 40, 0.1);
  --cs-header-bg: rgba(232, 239, 232, 0.75);
  --cs-backdrop-blur: 18px;
  background-image: url('https://w.wallhaven.cc/full/zy/wallhaven-zyxvje.jpg');
}

body.theme-forest-canopy[theme-mode="dark"] {
  --cs-bg-color: rgba(20, 31, 23, 0.85);
  --cs-panel-bg: rgba(28, 44, 32, 0.8);
  --cs-text-primary: #F5DEB3; /* Wheat */
  --cs-text-secondary: #D2B48C; /* Tan */
  --cs-accent-primary: #90EE90; /* Light Green */
  --cs-accent-secondary: #6B8E23; /* Olive Drab */
  --cs-border-color: rgba(107, 142, 35, 0.4);
  --cs-shadow-color: rgba(245, 222, 179, 0.1);
  --cs-header-bg: rgba(28, 44, 32, 0.8);
  background-image: url('https://w.wallhaven.cc/full/m9/wallhaven-m99l18.jpg');
}

/* 森林主题的特殊木纹效果 */
body.theme-forest-canopy .cs-wood-panel {
    background-image: linear-gradient(rgba(0,0,0,0.05) 2px, transparent 2px), linear-gradient(90deg, rgba(0,0,0,0.05) 2px, transparent 2px), linear-gradient(rgba(0,0,0,0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.03) 1px, transparent 1px);
    background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
    background-position: -2px -2px, -2px -2px, -1px -1px, -1px -1px;
}


/*
 * --------------------------------------------------
 * 3. 主题: Sunset Glow (日落光辉)
 * --------------------------------------------------
 */
body.theme-sunset-glow {
  --cs-radius-sm: 6px;
  --cs-radius-md: 10px;
  --cs-radius-lg: 20px;

  /* 浅色模式变量 */
  --cs-bg-color: linear-gradient(135deg, rgba(255, 228, 181, 0.7), rgba(255, 192, 203, 0.8)); /* Moccasin to Pink */
  --cs-panel-bg: rgba(255, 250, 240, 0.6); /* FloralWhite */
  --cs-text-primary: #483D8B; /* Dark Slate Blue */
  --cs-text-secondary: #8A2BE2; /* Blue Violet */
  --cs-accent-primary: #FFD700; /* Gold */
  --cs-accent-secondary: #FF69B4; /* Hot Pink */
  --cs-border-color: rgba(255, 105, 180, 0.4);
  --cs-shadow-color: rgba(72, 61, 139, 0.1);
  --cs-header-bg: var(--cs-bg-color);
  --cs-backdrop-blur: 8px;
  background-image: url('https://w.wallhaven.cc/full/l8/wallhaven-l83v3l.png');
}

body.theme-sunset-glow[theme-mode="dark"] {
  --cs-bg-color: linear-gradient(135deg, rgba(75, 0, 130, 0.85), rgba(43, 8, 8, 0.9)); /* Indigo to dark red */
  --cs-panel-bg: rgba(25, 0, 51, 0.7);
  --cs-text-primary: #FFFFFF;
  --cs-text-secondary: #FFDAB9; /* PeachPuff */
  --cs-accent-primary: #FFD700; /* Gold */
  --cs-accent-secondary: #FFA07A; /* Light Salmon */
  --cs-border-color: rgba(255, 215, 0, 0.4);
  --cs-shadow-color: rgba(255, 215, 0, 0.15);
  --cs-header-bg: var(--cs-bg-color);
  background-image: url('https://w.wallhaven.cc/full/m9/wallhaven-m996g8.png');
}


/*
 * --------------------------------------------------
 * 4. 主题: Minimalist Slate (极简石板)
 * --------------------------------------------------
 */
body.theme-minimalist-slate {
  --cs-radius-sm: 0;
  --cs-radius-md: 0;
  --cs-radius-lg: 0;

  /* 浅色模式变量 */
  --cs-bg-color: rgba(245, 245, 245, 0.2);
  --cs-panel-bg: rgba(250, 250, 250, 0.1);
  --cs-text-primary: #000000;
  --cs-text-secondary: #555555;
  --cs-accent-primary: #007AFF; /* Electric Blue */
  --cs-accent-secondary: #E53935; /* Muted Red */
  --cs-border-color: rgba(0, 0, 0, 0.2);
  --cs-shadow-color: rgba(0, 0, 0, 0.05);
  --cs-header-bg: rgba(250, 250, 250, 0.1);
  --cs-backdrop-blur: 24px;
  background-image: url('https://w.wallhaven.cc/full/g8/wallhaven-g83ve7.jpg');
}

body.theme-minimalist-slate:not(:hover) .cs-reveal-on-hover,
body.theme-minimalist-slate .cs-reveal-on-hover:not(:hover) {
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
}

body.theme-minimalist-slate[theme-mode="dark"] {
  --cs-bg-color: rgba(21, 21, 21, 0.3);
  --cs-panel-bg: rgba(33, 33, 33, 0.2);
  --cs-text-primary: #FFFFFF;
  --cs-text-secondary: #AAAAAA;
  --cs-accent-primary: #00AFFF; /* Electric Blue */
  --cs-accent-secondary: #F44336; /* Muted Red */
  --cs-border-color: rgba(255, 255, 255, 0.2);
  --cs-shadow-color: rgba(0, 0, 0, 0.2);
  --cs-header-bg: rgba(33, 33, 33, 0.2);
  background-image: url('https://w.wallhaven.cc/full/1p/wallhaven-1p39v9.jpg');
}


/*
 * --------------------------------------------------
 * 5. 主题: Cyberpunk Neon (赛博霓虹)
 * --------------------------------------------------
 */
body.theme-cyberpunk-neon {
  --cs-radius-sm: 0;
  --cs-radius-md: 4px;
  --cs-radius-lg: 8px;

  /* 浅色模式变量 */
  --cs-bg-color: rgba(230, 230, 255, 0.8);
  --cs-panel-bg: rgba(240, 240, 255, 0.75);
  --cs-text-primary: #000000;
  --cs-text-secondary: #333333;
  --cs-accent-primary: #FF00FF; /* Magenta */
  --cs-accent-secondary: #00FFFF; /* Cyan */
  --cs-border-color: rgba(255, 0, 255, 0.5);
  --cs-shadow-color: rgba(0, 255, 255, 0.3);
  --cs-header-bg: var(--cs-panel-bg);
  --cs-backdrop-blur: 10px;
  background-image: url('https://w.wallhaven.cc/full/zy/wallhaven-zygeko.jpg');
}

body.theme-cyberpunk-neon[theme-mode="dark"] {
  --cs-bg-color: rgba(10, 10, 25, 0.85);
  --cs-panel-bg: rgba(20, 20, 40, 0.8);
  --cs-text-primary: #FFFFFF;
  --cs-text-secondary: #DDDDDD;
  --cs-accent-primary: #F72585; /* Neon Pink */
  --cs-accent-secondary: #3A86FF; /* Neon Blue */
  --cs-border-color: rgba(58, 134, 255, 0.6);
  --cs-shadow-color: rgba(247, 37, 133, 0.5);
  --cs-header-bg: var(--cs-panel-bg);
  background-image: url('https://w.wallhaven.cc/full/l8/wallhaven-l851zl.jpg');
}

/* 赛博朋克主题的霓虹辉光和扫描线效果 */
@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

body.theme-cyberpunk-neon .cs-neon-glow {
    box-shadow: 0 0 5px var(--cs-accent-primary), 0 0 10px var(--cs-accent-primary), 0 0 15px var(--cs-shadow-color), inset 0 0 5px var(--cs-accent-secondary);
    border-color: var(--cs-accent-secondary) !important;
    animation: flicker 3s infinite;
}
body.theme-cyberpunk-neon .cs-neon-text {
    text-shadow: 0 0 3px var(--cs-accent-primary), 0 0 5px var(--cs-accent-primary);
}
body.theme-cyberpunk-neon::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: repeating-linear-gradient(0deg, rgba(255,255,255,0.02), rgba(255,255,255,0.02) 1px, transparent 1px, transparent 2px);
    pointer-events: none;
    z-index: 1000;
}


/* =================================================================== */
/* ==================== 通用组件样式应用 (核心) ==================== */
/* =================================================================== */

/* 整体背景与字体 */
body {
  font-family: var(--cs-font-primary);
  background-color: var(--cs-bg-color);
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  transition: background-image var(--cs-transition-normal);
}

/* 带有背景模糊效果的面板 */
.inputbar-container,
.ant-popover-inner,
div[class^="InputContainer-"],
div[class^="OutputContainer-"],
div[class^="HistoryContainner-"],
.ant-notification-notice,
.ant-message-notice-content,
.ant-drawer-content,
.ant-modal .ant-modal-content,
div[class^="AgentCardContainer-"],
.ant-table-wrapper,
.ant-collapse,
.bubble .message-content-container,
div[class^="SettingGroup-"] .ant-segmented,
div[class^="SettingContainer-"] div[class^="SettingGroup-"] {
  background-color: var(--cs-panel-bg) !important;
  backdrop-filter: blur(var(--cs-backdrop-blur));
  -webkit-backdrop-filter: blur(var(--cs-backdrop-blur));
  border: 1px solid var(--cs-border-color) !important;
  border-radius: var(--cs-radius-md) !important;
  box-shadow: 0 4px 12px var(--cs-shadow-color) !important;
  transition: background-color var(--cs-transition-normal), border-color var(--cs-transition-normal), box-shadow var(--cs-transition-normal);
  /* 添加class用于特定主题效果 */
  &.cs-wood-panel {}
  &.cs-reveal-on-hover {}
  &.cs-neon-glow {}
}

/* 文本颜色 */
body, .ant-modal-title, .ant-collapse-header,
.markdown, .inputbar-container textarea, .ant-table-tbody > tr > td {
  color: var(--cs-text-primary) !important;
}

.ant-dropdown-trigger, div[class^="SettingGroup-"] label,
.markdown code:not(pre > code), .markdown blockquote,
li[class^="MenuItem-"] {
  color: var(--cs-text-secondary) !important;
}

/* 重点色应用 */
a, .ant-switch-checked, .ant-checkbox-checked .ant-checkbox-inner, .ant-radio-inner::after,
li[class^="MenuItem-"].active, #content-container [class^="ListItemContainer-"].active,
.markdown h2 {
  color: var(--cs-accent-primary) !important;
  border-color: var(--cs-accent-primary) !important;
  background-color: transparent; /* 重置 */
}
.ant-switch-checked, .ant-checkbox-checked .ant-checkbox-inner, .ant-radio-inner::after {
  background-color: var(--cs-accent-primary) !important;
}
.inputbar-container .ant-btn:hover {
  border-color: var(--cs-accent-primary) !important;
  color: var(--cs-accent-primary) !important;
}

/* 次要重点色应用 */
.markdown h3 {
  color: var(--cs-accent-secondary) !important;
  border-color: var(--cs-accent-secondary) !important;
}
body.theme-cyberpunk-neon .cs-neon-glow {
  border-color: var(--cs-accent-secondary) !important;
}


/* 特定组件微调 */
.inputbar-container textarea {
  background-color: transparent !important;
  resize: none;
}
.inputbar-container textarea:focus {
  border: none !important;
  box-shadow: 0 0 0 2px var(--cs-accent-primary) !important;
  border-radius: var(--cs-radius-sm) !important;
}

.ant-modal-header, .markdown pre [class^="CodeHeader-"] {
  background: var(--cs-header-bg) !important;
  border-bottom: 1px solid var(--cs-border-color) !important;
  color: var(--cs-text-primary) !important;
  padding: 12px 20px !important;
}
.ant-modal-content {
    overflow: hidden; /* 确保子元素圆角生效 */
}

/* 列表项和菜单 */
li[class^="MenuItem-"], #content-container [class^="ListItemContainer-"] {
    border-radius: var(--cs-radius-sm) !important;
}
li[class^="MenuItem-"].active, #content-container [class^="ListItemContainer-"].active {
    background-color: rgba(var(--cs-accent-primary-rgb, 0, 0, 0), 0.1) !important; /* fallback */
    background-color: color-mix(in srgb, var(--cs-accent-primary) 15%, transparent) !important;
    border-left: 3px solid var(--cs-accent-primary) !important;
}

/* 代码块 */
code, pre {
  font-family: var(--cs-font-mono) !important;
}
.markdown pre [class^="CodeBlockWrapper-"], .markdown table {
    border-radius: var(--cs-radius-md) !important;
    border: 1px solid var(--cs-border-color) !important;
    background-color: color-mix(in srgb, var(--cs-panel-bg) 80%, black 20%) !important;
    overflow: hidden;
}
.markdown pre [class^="CodeContent-"] {
    padding: 1rem;
}
/* 隐藏自带的复制图标，因为它可能与主题风格不符 */
.markdown pre [class^="CodeHeader-"] .iconfont.icon-copy.copy {
    display: none !important;
}
