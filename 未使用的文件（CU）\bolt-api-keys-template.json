{"_comment": "Fill in your API keys for each provider. Keys will be stored with the provider name (e.g., 'OpenAI'). The application also supports the older format with keys like 'OpenAI_API_KEY' for backward compatibility.", "Anthropic": "", "OpenAI": "", "Google": "", "Groq": "", "HuggingFace": "", "OpenRouter": "", "Deepseek": "", "Mistral": "", "OpenAILike": "", "Together": "", "xAI": "", "Perplexity": "", "Cohere": "", "AzureOpenAI": ""}