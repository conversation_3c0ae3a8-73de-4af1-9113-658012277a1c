# >>=====================================<<
# ||                                     ||
# ||      ██████╗  ██████╗  ██████╗      ||
# ||     ██╔════╝ ██╔════╝ ██╔════╝      ||
# ||     ███████╗ ███████╗ ███████╗      ||
# ||     ██╔═══██╗██╔═══██╗██╔═══██╗     ||
# ||     ╚██████╔╝╚██████╔╝╚██████╔╝     ||
# ||      ╚═════╝  ╚═════╝  ╚═════╝      ||
# ||                                     ||
# >>=====================================<<
# 名称: OneSmartPro 一键智能代理
# 地址: https://github.com/666OS/YYDS
# 版本: v1.0.1
# 作者: YYDS666
# 更新: 2025-07-18
# 频道: https://t.me/Pinched666
# 描述: 基于Mihomo的智能代理配置，提供四层架构智能路由和全场景规则集
#       集成Smart算法自动选择最优节点，支持故障转移和负载均衡
#       覆盖AI、流媒体、电报等应用场景，提供多种监听协议支持

# ==================== 锚点配置 ====================
# 代理提供者模板 - 订阅源基础配置
BaseProvider: &BaseProvider { type: http, interval: 86400, proxy: DIRECT, health-check: { enable: true, url: 'https://www.gstatic.com/generate_204', interval: 300 }, filter: '^(?!.*(群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author))' }

# 策略组类型模板 - 定义不同的策略组基础配置
BaseFB: &BaseFB { type: fallback, interval: 200, lazy: true, url: 'https://www.gstatic.com/generate_204' }
BaseLB: &BaseLB { type: load-balance, interval: 200, lazy: true, url: 'https://www.gstatic.com/generate_204' }
BaseSmart: &BaseSmart { type: smart, interval: 200, lazy: true, url: 'https://www.gstatic.com/generate_204', hidden: true, uselightgbm: true }

# 节点筛选正则表达式 - 基于地理位置和关键词过滤
FilterHK: &FilterHK '^(?=.*(?i)(港|🇭🇰|HK|Hong|HKG))(?!.*(排除1|排除2|5x)).*$'
FilterSG: &FilterSG '^(?=.*(?i)(坡|🇸🇬|SG|Sing|SIN|XSP))(?!.*(排除1|排除2|5x)).*$'
FilterJP: &FilterJP '^(?=.*(?i)(日|🇯🇵|JP|Japan|NRT|HND|KIX|CTS|FUK))(?!.*(排除1|排除2|5x)).*$'
FilterKR: &FilterKR '^(?=.*(?i)(韩|🇰🇷|韓|首尔|南朝鲜|KR|KOR|Korea|South))(?!.*(排除1|排除2|5x)).*$'
FilterUS: &FilterUS '^(?=.*(?i)(美|🇺🇸|US|USA|JFK|LAX|ORD|ATL|DFW|SFO|MIA|SEA|IAD))(?!.*(排除1|排除2|5x)).*$'
FilterTW: &FilterTW '^(?=.*(?i)(台|🇼🇸|🇹🇼|TW|tai|TPE|TSA|KHH))(?!.*(排除1|排除2|5x)).*$'
FilterEU: &FilterEU '^(?=.*(?i)(奥|比|保|克罗地亚|塞|捷|丹|爱沙|芬|法|德|希|匈|爱尔|意|拉|立|卢|马其它|荷|波|葡|罗|斯洛伐|斯洛文|西|瑞|英|🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|🇬🇧|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU))(?!.*(排除1|排除2|5x)).*$'
FilterOT: &FilterOT '^(?!.*(DIRECT|美|港|坡|台|新|日|韩|奥|比|保|克罗地亚|塞|捷|丹|爱沙|芬|法|德|希|匈|爱尔|意|拉|立|卢|马其它|荷|波|葡|罗|斯洛伐|斯洛文|西|瑞|英|🇭🇰|🇼🇸|🇹🇼|🇸🇬|🇯🇵|🇰🇷|🇺🇸|🇬🇧|🇦🇹|🇧🇪|🇨🇿|🇩🇰|🇫🇮|🇫🇷|🇩🇪|🇮🇪|🇮🇹|🇱🇹|🇱🇺|🇳🇱|🇵🇱|🇸🇪|HK|TW|SG|JP|KR|US|GB|CDG|FRA|AMS|MAD|BCN|FCO|MUC|BRU|HKG|TPE|TSA|KHH|SIN|XSP|NRT|HND|KIX|CTS|FUK|JFK|LAX|ORD|ATL|DFW|SFO|MIA|SEA|IAD|LHR|LGW))'
FilterAL: &FilterAL '^(?!.*(DIRECT|群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author))'

# 策略组代理列表模板 - 预定义的代理节点优先级排序
SelectAL: &SelectAL { type: select, proxies: [ 高质量线路, 低延迟线路, 大带宽线路, 低倍率线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 美国智能, 欧洲智能, 直接连接 ] }
OneSmart: &OneSmart { type: select, proxies: [ 一键智能, 高质量线路, 低延迟线路, 大带宽线路, 低倍率线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 美国智能, 欧洲智能 ] }
SelectDC: &SelectDC { type: select, proxies: [ 直接连接, 高质量线路, 大带宽线路, 低倍率线路, 低延迟线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 美国智能, 欧洲智能 ] }
SelectHQ: &SelectHQ { type: select, proxies: [ 高质量线路, 低延迟线路, 大带宽线路, 低倍率线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 欧洲智能, 一键智能, 直接连接 ] }
SelectLD: &SelectLD { type: select, proxies: [ 低延迟线路, 高质量线路, 大带宽线路, 低倍率线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 欧洲智能, 一键智能, 直接连接 ] }
SelectBW: &SelectBW { type: select, proxies: [ 大带宽线路, 高质量线路, 低延迟线路, 低倍率线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 欧洲智能, 一键智能, 直接连接 ] }
SelectLR: &SelectLR { type: select, proxies: [ 低倍率线路, 高质量线路, 低延迟线路, 大带宽线路, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 欧洲智能, 一键智能, 直接连接 ] }

# ==================== 代理提供者 ====================
proxy-providers:
  # 优质订阅源 - 优质节点集合，使用时请修改
  节点: { <<: *BaseProvider, url: 'https://gist.githubusercontent.com/nariahlamb/7ffac9233f813625f405f74c50040be9/raw/ipsub', override: { additional-prefix: '[点] ' } }
  # 备用订阅源 - 次优节点集合，使用时请修改
  机场: { <<: *BaseProvider, url: 'https://gist.githubusercontent.com/nariahlamb/7ffac9233f813625f405f74c50040be9/raw/agg', override: { additional-prefix: '[机] ' } }

# ==================== 监听器 ====================
listeners:
# Shadowsocks监听器 - 代理节点 玩法：远程连接家庭网络，端口和密码，使用时请修改
- { name: SS-IN, type: shadowsocks, listen: '::', port: 10000, udp: true, password: Xf3#Lp9WqZ, cipher: aes-256-gcm }
# Mixed监听器 - 分地区专用端口 玩法：本地浏览器插件或手机APP配置代理，实现分地区访问
- { name: MIXED-SG, type: mixed, port: 50000, proxy: 美国智能 }
- { name: MIXED-US, type: mixed, port: 50001, proxy: 狮城智能 }
- { name: MIXED-TW, type: mixed, port: 50002, proxy: 台湾智能 }
- { name: MIXED-HK, type: mixed, port: 50003, proxy: 香港智能 }
- { name: MIXED-JP, type: mixed, port: 50004, proxy: 日本智能 }
- { name: MIXED-KR, type: mixed, port: 50005, proxy: 韩国智能 }
- { name: MIXED-EU, type: mixed, port: 50006, proxy: 欧洲智能 }
- { name: MIXED-AL, type: mixed, port: 50007, proxy: 一键智能 }

# ==================== 核心配置 ====================
# 基础配置
mode: rule
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7895
ipv6: true
allow-lan: true
unified-delay: true
tcp-concurrent: true
log-level: warning
bind-address: '*'
find-process-mode: 'off'
global-client-fingerprint: chrome
keep-alive-interval: 15
keep-alive-idle: 600

# 认证配置
authentication:
- mihomo:yyds666
skip-auth-prefixes:
- ***********/24
- ************/24
- *************/24
- 127.0.0.1/8

# 实验性功能
experimental:
  quic-go-disable-gso: true

# 管理面板配置
external-ui-url: https://github.com/Zephyruso/zashboard/releases/latest/download/dist.zip
external-ui-name: zashboard
external-ui: ui
external-controller: 0.0.0.0:9090
secret:

# 配置存储
profile:
  store-selected: true
  store-fake-ip: true

# 流量嗅探
sniffer:
  enable: false
  override-destination: false
  force-dns-mapping: true
  parse-pure-ip: true

# TUN模式配置
tun:
  enable: false
  stack: mixed
  dns-hijack: [ any:53 ]

dns:
  enable: true
  prefer-h3: false
  use-hosts: true
  use-system-hosts: true
  respect-rules: false
  listen: 0.0.0.0:1053
  ipv6: false
  enhanced-mode: redir-host
  fake-ip-range: **********/15
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
  - '*'
  - '+.lan'
  - "geosite:googlefcm"
  - "geosite:cn"
  default-nameserver:
  - https://*********/dns-query#DIRECT&h3=true

  nameserver:
  - https://*********/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  proxy-server-nameserver:
  - https://*********/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true
  - https://*******/dns-query#DIRECT&h3=true

# ==================== 代理策略组 ====================
proxies:
- { name: 直接连接, type: direct, udp: true }

proxy-groups:
# 应用策略层 - Select手动选择，支持多场景应用优化，使用时默认，也可修改
- { name: 一键智能, <<: *SelectAL, icon: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/image/mihomo.png }
- { name: 人工智能, <<: *SelectHQ, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/AI.png }
- { name: 网速测试, <<: *OneSmart, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Speedtest.png, include-all: true }
- { name: 电报消息, <<: *SelectBW, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Telegram_X.png }
- { name: 油管视频, <<: *SelectLR, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/YouTube.png }
- { name: 国际媒体, <<: *SelectBW, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/DomesticMedia.png }
- { name: 谷歌服务, <<: *SelectHQ, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Google_Search.png }
- { name: 微软服务, <<: *SelectHQ, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Microsoft.png }
- { name: 国外流量, <<: *SelectHQ, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Global.png }
- { name: 国内流量, <<: *SelectDC, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/China.png }
- { name: 兜底流量, <<: *SelectHQ, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Final.png }

# 线路特性层 - Fallback故障转移，按特性优化线路选择，建议修改位置顺序发挥线路优势
- { name: 高质量线路, <<: *BaseFB, proxies: [ 美国智能, 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 欧洲智能, 直接连接 ], icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Round_Robin_1.png }
- { name: 低延迟线路, <<: *BaseFB, proxies: [ 香港智能, 台湾智能, 日本智能, 狮城智能, 韩国智能, 美国智能, 欧洲智能, 直接连接 ], icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Round_Robin_1.png }
- { name: 大带宽线路, <<: *BaseFB, proxies: [ 狮城智能, 香港智能, 台湾智能, 日本智能, 韩国智能, 美国智能, 欧洲智能, 直接连接 ], icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Round_Robin_1.png }
- { name: 低倍率线路, <<: *BaseFB, proxies: [ 日本智能, 香港智能, 台湾智能, 狮城智能, 韩国智能, 美国智能, 欧洲智能, 直接连接 ], icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Round_Robin_1.png }

# 地理策略层 - Smart智能选择，政策权重数值越大优先级越高，修改为关键词节点，比如 优:2;中:1;备:0.5，默认为1，优>中>备，使用时请修改
- { name: 香港智能, <<: *BaseSmart, filter: *FilterHK, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Hong_Kong.png }
- { name: 台湾智能, <<: *BaseSmart, filter: *FilterTW, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Taiwan.png }
- { name: 日本智能, <<: *BaseSmart, filter: *FilterJP, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Japan.png }
- { name: 狮城智能, <<: *BaseSmart, filter: *FilterSG, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Singapore.png }
- { name: 韩国智能, <<: *BaseSmart, filter: *FilterKR, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Korea.png }
- { name: 美国智能, <<: *BaseSmart, filter: *FilterUS, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/United_States.png }
- { name: 欧洲智能, <<: *BaseSmart, filter: *FilterEU, include-all: true, policy-priority: '优:2;中:1;备:0.5', icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/European_Union.png }

# 中转服务层 - LoadBalance负载均衡，提供中转服务提升连通性，选用一个或多个服务商做中转，比如 优质服务商，可通过正则表达式，过滤掉不需要的节点，使用时请修改
- { name: 中转服务, <<: *BaseLB, use: [ 优质服务商 ], filter: 'HK|TW', hidden: true, icon: https://git.imee.me/https://github.com/Koolson/Qure/raw/master/IconSet/Color/Round_Robin.png }

# ==================== 规则路由 ====================
rules:
# 修正规则 - 特定服务直连
- DOMAIN-SUFFIX,ghproxy.net,直接连接
- DST-PORT,9876,直接连接 #httpmeta
- PROCESS-NAME,http-meta,直接连接 #httpmeta
- DOMAIN-SUFFIX,551543.xyz,直接连接
- DOMAIN-SUFFIX,imee.me,直接连接
- RULE-SET,fix-direct,直接连接
- RULE-SET,XPTV,直接连接

# 修正规则 - 特定服务代理
- DOMAIN-KEYWORD,behance,美国智能
- DOMAIN-KEYWORD,diabrowser,人工智能
- DOMAIN-SUFFIX,linux.do,国外流量
- DOMAIN-SUFFIX,423down.com,国外流量

# 应用规则 - 按场景分流
- RULE-SET,category-ai-!cn,人工智能
- RULE-SET,telegram,电报消息
- RULE-SET,youtube,油管视频
- RULE-SET,spotify,国际媒体
- RULE-SET,netflix,国际媒体
- RULE-SET,disney,国际媒体
- RULE-SET,hbo,国际媒体
- RULE-SET,apple-cn,国内流量
- RULE-SET,google,谷歌服务
- RULE-SET,github,微软服务
- RULE-SET,onedrive,微软服务
- RULE-SET,microsoft,微软服务
- RULE-SET,download,国内流量
- RULE-SET,cn_domain,国内流量
- RULE-SET,geolocation-!cn,国外流量
# IP规则 - 基于IP地址分流
- RULE-SET,private_ip,直接连接,no-resolve
- RULE-SET,google_ip,谷歌服务,no-resolve
- RULE-SET,netflix_ip,国际媒体,no-resolve
- RULE-SET,telegram_ip,电报消息,no-resolve
- RULE-SET,cn_ip,国内流量,no-resolve

# 兜底规则 - 未匹配流量
- MATCH,兜底流量

# 规则集行为模板 - 定义不同类型的规则行为
BehaviorCL: &BehaviorCL { type: http, behavior: classical, interval: 86400 }
BehaviorDN: &BehaviorDN { type: http, behavior: domain, interval: 86400 }
BehaviorIP: &BehaviorIP { type: http, behavior: ipcidr, interval: 86400 }

# 规则提供者
rule-providers:
  # Classical规则集 - 复合规则
  download: { <<: *BehaviorCL, format: yaml, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/download.yaml }
  fix-direct: { <<: *BehaviorCL, format: yaml, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/fix-direct.yaml }
  XPTV: { <<: *BehaviorCL, format: yaml, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/XPTV.yaml }

  # Domain规则集 - 域名规则
  telegram: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram.mrs }
  category-ai-!cn: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/category-ai-!cn.mrs }
  youtube: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/youtube.mrs }
  spotify: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/spotify.mrs }
  netflix: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix.mrs }
  disney: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/disney.mrs }
  hbo: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/hbo.mrs }
  apple-cn: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/apple-cn.mrs }
  apple: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/apple.mrs }
  facebook: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/facebook.mrs }
  google: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/google.mrs }
  github: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/github.mrs }
  onedrive: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/onedrive.mrs }
  microsoft: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/microsoft.mrs }
  connectivity-check: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/connectivity-check.mrs }
  private_domain: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/private.mrs }
  geolocation-!cn: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/geolocation-!cn.mrs }
  cn_domain: { <<: *BehaviorDN, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn.mrs }

  # IPCIDR规则集 - IP地址规则
  private_ip: { <<: *BehaviorIP, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/private_ip.mrs }
  netflix_ip: { <<: *BehaviorIP, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/netflix_ip.mrs }
  google_ip: { <<: *BehaviorIP, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/google_ip.mrs }
  telegram_ip: { <<: *BehaviorIP, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/telegram_ip.mrs }
  cn_ip: { <<: *BehaviorIP, format: mrs, url: https://git.imee.me/https://github.com/666OS/YYDS/raw/main/mihomo/rules/cn_ip.mrs }

# ==================== EOF ====================
