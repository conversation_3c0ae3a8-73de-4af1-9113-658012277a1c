(function(sttc){'use strict';var aa=Object.defineProperty,ba=globalThis,ca=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",da={},ea={};function fa(a,b,c){if(!c||a!=null){c=ea[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ha(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in da?f=da:f=ba;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ca&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(da,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ea[d]===void 0&&(a=Math.random()*1E9>>>0,ea[d]=ca?ba.Symbol(d):"$jscp$"+a+"$"+d),aa(f,ea[d],{configurable:!0,writable:!0,value:b})))}}function ia(a){return a} 
ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var q=this||self;function ja(a){a=a.split(".");for(var b=q,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function ka(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function la(a){return Object.prototype.hasOwnProperty.call(a,ma)&&a[ma]||(a[ma]=++na)}var ma="closure_uid_"+(Math.random()*1E9>>>0),na=0;function oa(a,b,c){return a.call.apply(a.bind,arguments)} 
function pa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function qa(a,b,c){qa=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?oa:pa;return qa.apply(null,arguments)} 
function ra(a,b,c){a=a.split(".");c=c||q;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function sa(a){q.setTimeout(()=>{throw a;},0)};function ta(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]} 
function ua(a,b){let c=0;a=ta(String(a)).split(".");b=ta(String(b)).split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=va(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||va(e[2].length==0,f[2].length==0)||va(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c}function va(a,b){return a<b?-1:a>b?1:0};var wa,xa=ja("CLOSURE_FLAGS"),ya=xa&&xa[610401301];wa=ya!=null?ya:!1;function za(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}var Aa;const Ba=q.navigator;Aa=Ba?Ba.userAgentData||null:null;function Ca(a){if(!wa||!Aa)return!1;for(let b=0;b<Aa.brands.length;b++){const {brand:c}=Aa.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function r(a){return za().indexOf(a)!=-1};function Da(){return wa?!!Aa&&Aa.brands.length>0:!1}function Ea(){return Da()?!1:r("Trident")||r("MSIE")}function Fa(){return Da()?Ca("Chromium"):(r("Chrome")||r("CriOS"))&&!(Da()?0:r("Edge"))||r("Silk")}function Ga(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""} 
function Ha(){var a=za();if(Ea()){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])a=b[1];else{b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];a=b}return a}c=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");b=[];let d;for(;d=c.exec(a);)b.push([d[1],d[2],d[3]||void 0]);a=Ga(b);return(Da()?0:r("Opera"))?a(["Version", 
"Opera"]):(Da()?0:r("Edge"))?a(["Edge"]):(Da()?Ca("Microsoft Edge"):r("Edg/"))?a(["Edg"]):r("Silk")?a(["Silk"]):Fa()?a(["Chrome","CriOS","HeadlessChrome"]):(a=b[2])&&a[1]||""};function Ia(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ja(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Ka(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d} 
function La(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Ma(a,b){a:{var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;c--)if(c in d&&b.call(void 0,d[c],c,a)){b=c;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function Na(a,b){return Ia(a,b)>=0}function Oa(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};function Pa(a){Pa[" "](a);return a}Pa[" "]=function(){};var Qa=null;function Ra(a){const b=[];Sa(a,function(c){b.push(c)});return b}function Sa(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Qa[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Ta();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Ta(){if(!Qa){Qa={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));for(let e=0;e<d.length;e++){const f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}}};function Ua(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Va=void 0,Wa;function Xa(a){if(Wa)throw Error("");Wa=b=>{q.setTimeout(()=>{a(b)},0)}}function Ya(a){if(Wa)try{Wa(a)}catch(b){throw b.cause=a,b;}}function Za(a){a=Error(a);Ua(a,"warning");Ya(a);return a};function $a(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var ab=$a(),bb=$a(),db=$a(),eb=$a("m_m",!0);const u=$a("jas",!0);var fb;const gb=[];gb[u]=55;fb=Object.freeze(gb);function hb(a,b){a[u]|=b}function ib(a){if(4&a)return 2048&a?2048:4096&a?4096:0}function jb(a){hb(a,32);return a};var lb={};function mb(a,b){return b===void 0?a.g!==nb&&!!(2&(a.A[u]|0)):!!(2&b)&&a.g!==nb}const nb={};var ob=Object.freeze({});function pb(a,b){const c=qb;if(!b(a))throw b=(typeof c==="function"?c():c)?.concat("\n")??"",Error(b+String(a));}function rb(a){a.uc=!0;return a}let qb=void 0;const sb=rb(a=>a!==null&&a!==void 0);var tb=rb(a=>typeof a==="number"),ub=rb(a=>typeof a==="string"),vb=rb(a=>a===void 0),wb=rb(a=>Array.isArray(a));function xb(){return rb(a=>wb(a)?a.every(b=>tb(b)):!1)};function yb(a){if(ub(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(tb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var Bb=rb(a=>a>=zb&&a<=Ab);const zb=BigInt(Number.MIN_SAFE_INTEGER),Ab=BigInt(Number.MAX_SAFE_INTEGER);let Cb=0,Db=0;function Eb(a){const b=a>>>0;Cb=b;Db=(a-b)/4294967296>>>0}function Fb(a){if(a<0){Eb(-a);a=Cb;var b=Db;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];Cb=c>>>0;Db=d>>>0}else Eb(a)}function Gb(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Hb(){var a=Cb,b=Db,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Gb(a,b);return c};function Ib(a,b=`unexpected value ${a}!`){throw Error(b);};const Jb=typeof BigInt==="function"?BigInt.asIntN:void 0,Kb=Number.isSafeInteger,Lb=Number.isFinite,Mb=Math.trunc;function Nb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Ob(a){if(a!=null&&typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a}const Pb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/; 
function Qb(a){switch(typeof a){case "bigint":return!0;case "number":return Lb(a);case "string":return Pb.test(a);default:return!1}}function Rb(a){if(!Lb(a))throw Za("enum");return a|0}function Sb(a){return a==null?a:Lb(a)?a|0:void 0}function Tb(a){if(typeof a!=="number")throw Za("int32");if(!Lb(a))throw Za("int32");return a|0}function Ub(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Lb(a)?a|0:void 0} 
function Vb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Lb(a)?a>>>0:void 0}function Wb(a){if(!Qb(a))throw Za("int64");switch(typeof a){case "string":return Xb(a);case "bigint":return yb(Jb(64,a));default:return Yb(a)}}function Zb(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337} 
function Yb(a){a=Mb(a);if(!Kb(a)){Fb(a);var b=Cb,c=Db;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Gb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function $b(a){a=Mb(a);if(Kb(a))a=String(a);else{{const b=String(a);Zb(b)?a=b:(Fb(a),a=Hb())}}return a} 
function Xb(a){var b=Mb(Number(a));if(Kb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));Zb(a)||(a.length<16?Fb(Number(a)):(a=BigInt(a),Cb=Number(a&BigInt(4294967295))>>>0,Db=Number(a>>BigInt(32)&BigInt(4294967295))),a=Hb());return a}function ac(a){if(typeof a!=="string")throw Error();return a}function bc(a){if(a!=null&&typeof a!=="string")throw Error();return a}function cc(a){return a==null||typeof a==="string"?a:void 0} 
function dc(a,b,c,d){if(a!=null&&typeof a==="object"&&a[eb]===lb)return a;if(!Array.isArray(a))return c?d&2?((a=b[ab])||(a=new b,hb(a.A,34),a=b[ab]=a),b=a):b=new b:b=void 0,b;c=a[u]|0;d=c|d&32|d&2;d!==c&&(a[u]=d);return new b(a)};function ec(a){return a};function fc(a,b,c,d,e){d=d?!!(b&32):void 0;const f=[];var g=a.length;let h,l,k,m=!1;b&64?(b&256?(g--,h=a[g],l=g):(l=4294967295,h=void 0),e||b&512||(m=!0,k=(hc??ec)(h?l- -1:b>>16&1023||536870912,-1,a,h),l=k+-1)):(l=4294967295,b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,l=g,k=0):h=void 0));let n=void 0;for(let p=0;p<g;p++){let v=a[p];v!=null&&(v=c(v,d))!=null&&(p>=l?(n??(n={}))[p- -1]=v:f[p]=v)}if(h)for(let p in h)Object.prototype.hasOwnProperty.call(h,p)&&(a=h[p],a!= 
null&&(a=c(a,d))!=null&&(g=+p,g<k?f[g+-1]=a:(n??(n={}))[p]=a));n&&(m?f.push(n):f[l]=n);e&&(f[u]=b&67043905|(n!=null?290:34));return f}function ic(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Bb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[u]|0;return a.length===0&&b&1?void 0:fc(a,b,ic,!1,!1)}if(a[eb]===lb)return w(a);return}return a} 
var jc=typeof structuredClone!="undefined"?structuredClone:a=>fc(a,0,ic,void 0,!1);let hc;function w(a){a=a.A;return fc(a,a[u]|0,ic,void 0,!1)};function kc(){if(db!=null){var a=Va??(Va={});var b=a[db]||0;b>=5||(a[db]=b+1,a=Error(),Ua(a,"incident"),Wa?Ya(a):sa(a))}};function lc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){const d=a[u]|0;if(a.length===0&&d&1)return;if(d&2)return a;var c;if(c=b)c=(!!(32&d)||!(1&d))&&!(1&d&&!(16&d));return c?(hb(a,34),d&4&&Object.freeze(a),a):fc(a,d,lc,b!==void 0,!0)}if(a[eb]===lb)return b=a.A,c=b[u]|0,mb(a,c)?a:fc(b,c,lc,!0,!0)}function mc(a){var b=a.A;const c=b[u]|0;if(!mb(a,c))return a;a=new a.constructor(fc(b,c,lc,!0,!0));b=a.A;b[u]&=-3;return a} 
function nc(a){if(a.g!==nb)return!1;let b=a.A;b=fc(b,b[u]|0,lc,!0,!0);b[u]&=-3;a.A=b;a.g=void 0;return!0}function oc(a){if(!nc(a)&&mb(a,a.A[u]|0))throw Error();};const pc=yb(0);function x(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function qc(a,b,c){oc(a);const d=a.A;z(d,d[u]|0,b,c);return a} 
function z(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&&e>=f){const g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&&(f=(b??(b=a[u]|0))>>16&1023||536870912,c>=f?d!=null&&(a[f+-1]={[c]:d},b|=256,a[u]=b):a[e]=d);return b}function rc(a,b,c){a=a.A;return sc(a,a[u]|0,b,c)!==void 0}function A(a){return a===ob?2:4} 
function tc(a,b,c,d,e){let f=a.A,g=f[u]|0;d=mb(a,g)?1:d;e=!!e||d===3;d===2&&nc(a)&&(f=a.A,g=f[u]|0);a=uc(f,b);let h=a[u]|0;if(!(4&h)){4&h&&(a=[...a],h=vc(h,g),g=z(f,g,b,a));let l=0,k=0;for(;l<a.length;l++){const m=c(a[l]);m!=null&&(a[k++]=m)}k<l&&(a.length=k);h=wc(h,g);c=(h|20)&-2049;h=c&=-4097;a[u]=h;2&h&&Object.freeze(a)}d===1||d===4&&32&h?xc(h)||(e=h,h|=2,h!==e&&(a[u]=h),Object.freeze(a)):(d===2&&xc(h)&&(a=[...a],h=vc(h,g),h=yc(h,g,e),a[u]=h,g=z(f,g,b,a)),xc(h)||(b=h,h=yc(h,g,e),h!==b&&(a[u]=h))); 
return a}function uc(a,b){a=x(a,b);return Array.isArray(a)?a:fb}function wc(a,b){a===0&&(a=vc(a,b),a|=16);return a|1}function xc(a){return!!(2&a)&&!!(4&a)||!!(1024&a)} 
function zc(a,b,c,d){oc(a);const e=a.A;let f=e[u]|0;if(c==null)return z(e,f,b),a;let g=c[u]|0,h=g;var l=xc(g);let k=l||Object.isFrozen(c);l||(g=0);k||(c=[...c],h=0,g=vc(g,f),g=yc(g,f,!0),k=!1);g|=21;l=ib(g)??0;for(let m=0;m<c.length;m++){const n=c[m],p=d(n,l);Object.is(n,p)||(k&&(c=[...c],h=0,g=vc(g,f),g=yc(g,f,!0),k=!1),c[m]=p)}g!==h&&(k&&(c=[...c],g=vc(g,f),g=yc(g,f,!0)),c[u]=g);z(e,f,b,c);return a} 
function Ac(a,b,c,d){oc(a);const e=a.A;z(e,e[u]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Bc(a,b,c,d){oc(a);const e=a.A;var f=e[u]|0;if(d==null){var g=Cc(e);if(Dc(g,e,f,c)===b)g.set(c,0);else return a}else{g=Cc(e);const h=Dc(g,e,f,c);h!==b&&(h&&(f=z(e,f,h)),g.set(c,b))}z(e,f,b,d);return a}function Ec(a,b,c){return Fc(a,b)===c?c:-1}function Fc(a,b){a=a.A;return Dc(Cc(a),a,void 0,b)}function Cc(a){return a[bb]??(a[bb]=new Map)} 
function Dc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];x(b,g)!=null&&(e!==0&&(c=z(b,c,e)),e=g)}a.set(d,e);return e}function Gc(a,b,c){oc(a);a=a.A;let d=a[u]|0;const e=x(a,c);b=mc(dc(e,b,!0,d));e!==b&&z(a,d,c,b);return b}function sc(a,b,c,d){a=x(a,d,void 0,e=>dc(e,c,!1,b));if(a!=null)return a}function Hc(a,b,c){a=a.A;(c=sc(a,a[u]|0,b,c))||(c=b[ab])||(c=new b,hb(c.A,34),c=b[ab]=c);return c} 
function B(a,b,c){let d=a.A,e=d[u]|0;b=sc(d,e,b,c);if(b==null)return b;e=d[u]|0;if(!mb(a,e)){const f=mc(b);f!==b&&(nc(a)&&(d=a.A,e=d[u]|0),b=f,z(d,e,c,b))}return b} 
function C(a,b,c,d){var e=a.A,f=e;e=e[u]|0;var g=mb(a,e);const h=g?1:d;d=h===3;var l=!g;(h===2||l)&&nc(a)&&(f=a.A,e=f[u]|0);a=uc(f,c);var k=a[u]|0;g=!!(4&k);if(!g){k=wc(k,e);var m=a,n=e;const p=!!(2&k);p&&(n|=2);let v=!p,t=!0,y=0,J=0;for(;y<m.length;y++){const K=dc(m[y],b,!1,n);if(K instanceof b){if(!p){const kb=mb(K);v&&(v=!kb);t&&(t=kb)}m[J++]=K}}J<y&&(m.length=J);k|=4;k=t?k|16:k&-17;k=v?k|8:k&-9;m[u]=k;p&&Object.freeze(m)}if(l&&!(8&k||!a.length&&(h===1||h===4&&32&k))){xc(k)&&(a=[...a],k=vc(k,e), 
e=z(f,e,c,a));b=a;l=k;for(m=0;m<b.length;m++)k=b[m],n=mc(k),k!==n&&(b[m]=n);l|=8;l=b.length?l&-17:l|16;k=b[u]=l}h===1||h===4&&32&k?xc(k)||(c=k,k|=!a.length||16&k&&(!g||32&k)?2:1024,k!==c&&(a[u]=k),Object.freeze(a)):(h===2&&xc(k)&&(a=[...a],k=vc(k,e),k=yc(k,e,d),a[u]=k,e=z(f,e,c,a)),xc(k)||(c=k,k=yc(k,e,d),k!==c&&(a[u]=k)));return a}function Ic(a){a==null&&(a=void 0);return a}function Jc(a,b,c){c=Ic(c);qc(a,b,c);return a}function Kc(a,b,c,d){d=Ic(d);Bc(a,b,c,d);return a} 
function Lc(a,b,c){oc(a);const d=a.A;let e=d[u]|0;if(c==null)return z(d,e,b),a;let f=c[u]|0,g=f;const h=xc(f),l=h||Object.isFrozen(c);let k=!0,m=!0;for(let p=0;p<c.length;p++){var n=c[p];h||(n=mb(n),k&&(k=!n),m&&(m=n))}h||(f=k?13:5,f=m?f|16:f&-17);l&&f===g||(c=[...c],g=0,f=vc(f,e),f=yc(f,e,!0));f!==g&&(c[u]=f);z(d,e,b,c);return a}function vc(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}function yc(a,b,c){32&b&&c||(a&=-33);return a} 
function Mc(a,b){oc(a);a=tc(a,4,cc,2,!0);const c=ib(a[u]|0)??0;if(Array.isArray(b)){var d=b.length;for(let e=0;e<d;e++)a.push(ac(b[e],c))}else for(d of b)a.push(ac(d,c))}function Nc(a,b){a=x(a.A,b);b=typeof a;a!=null&&(b==="bigint"?a=yb(Jb(64,a)):Qb(a)?b==="string"?(b=Mb(Number(a)),Kb(b)?a=yb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=yb(Jb(64,BigInt(a))))):a=Kb(a)?yb(Yb(a)):yb($b(a)):a=void 0);return a} 
function Oc(a,b){a=x(a.A,b);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0}function Pc(a,b){return Ub(x(a.A,b))}function D(a,b){return cc(x(a.A,b))}function E(a,b){return Sb(x(a.A,b))}function F(a,b){return Oc(a,b)??!1}function G(a,b){return Pc(a,b)??0}function Qc(a,b){return x(a.A,b,void 0,Nb)??0}function H(a,b){return D(a,b)??""}function I(a,b){return E(a,b)??0}function Rc(a,b,c){return I(a,Ec(a,c,b))}function Sc(a,b,c,d){return B(a,b,Ec(a,d,c))} 
function Tc(a,b,c){return qc(a,b,c==null?c:Tb(c))}function Uc(a,b,c){return Ac(a,b,c==null?c:Tb(c),0)}function Vc(a,b,c){return Ac(a,b,c==null?c:Wb(c),"0")}function Wc(a,b){var c=performance.now();if(c!=null&&typeof c!=="number")throw Error(`Value of float/double field must be a number, found ${typeof c}: ${c}`);Ac(a,b,c,0)}function Xc(a,b,c){return qc(a,b,bc(c))}function Yc(a,b,c){return Ac(a,b,bc(c),"")}function $c(a,b,c){return qc(a,b,c==null?c:Rb(c))} 
function ad(a,b,c){return Ac(a,b,c==null?c:Rb(c),0)}function bd(a,b,c,d){return Bc(a,b,c,d==null?d:Rb(d))};function cd(a){const b=a.A,c=b[u]|0;return mb(a,c)?a:new a.constructor(fc(b,c,lc,!0,!0))} 
var L=class{constructor(a){a:{if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[u]|0;16384&b&&!(2&b)&&kc();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[u]=b|16384);var c=a;break a}var d=a;b&=-257;var e=d.length;if(e){var f=e-1;e=d[f];if(e!=null&&typeof e==="object"&&e.constructor===Object){b|=256;const g=b&512?0:-1;f-=g;if(f>=1024)throw Error("pvtlmt");for(c in e){if(!Object.prototype.hasOwnProperty.call(e,c))continue;const h=+c;if(h<f)d[h+g]=e[c],delete e[c];else break}b= 
b&-67043329|(f&1023)<<16}}}a[u]=b|16448;c=a}this.A=c}toJSON(){return w(this)}};L.prototype[eb]=lb;function dd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error("must be an array");if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error("arrays passed to jspb constructors must be mutable");hb(b,128);return new a(jb(b))};function ed(a){return()=>{var b;(b=a[ab])||(b=new a,hb(b.A,34),b=a[ab]=b);return b}}function fd(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(jb(b))}return b}};var gd=class extends L{};var hd=class extends L{};function id(a){return function(){return!a.apply(this,arguments)}}function jd(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function kd(a){let b=a;return function(){if(b){const c=b;b=null;c()}}};function ld(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)}function md(a,b,c){return a.removeEventListener?(a.removeEventListener(b,c,!1),!0):!1};let nd,od=64;function pd(){try{return nd??(nd=new Uint32Array(64)),od>=64&&(crypto.getRandomValues(nd),od=0),nd[od++]}catch(a){return Math.floor(Math.random()*2**32)}};function qd(a,b){if(!tb(a.goog_pvsid))try{const c=pd()+(pd()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(c){b.ka({methodName:784,ua:c})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.ka({methodName:784,ua:Error(`Invalid correlator, ${a}`)});return a||-1};function rd(){return wa&&Aa?Aa.mobile:!sd()&&(r("iPod")||r("iPhone")||r("Android")||r("IEMobile"))}function sd(){return wa&&Aa?!Aa.mobile&&(r("iPad")||r("Android")||r("Silk")):r("iPad")||r("Android")&&!r("Mobile")||r("Silk")};function td(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function ud(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function vd(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function wd(a){const b={};for(const c in a)b[c]=a[c];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let xd=globalThis.trustedTypes,yd;function zd(){let a=null;if(!xd)return a;try{const b=c=>c;a=xd.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var Ad=class{constructor(a){this.g=a}toString(){return this.g+""}};function Bd(a){var b;yd===void 0&&(yd=zd());a=(b=yd)?b.createScriptURL(a):a;return new Ad(a)}function Cd(a){if(a instanceof Ad)return a.g;throw Error("");};var Dd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Ed(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};const Fd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Gd(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};var Hd=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Id=/#|$/;function Jd(a,b){const c=a.search(Id);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Kd(a,...b){if(b.length===0)return Bd(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Bd(c)}function Ld(a,b){a=Cd(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Md(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Md(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(l=>e(l,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return Bd(a+b+c)};function Nd(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Pa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Od(a){return Nd(a.top)?a.top:null}function Pd(a,b){const c=Qd("SCRIPT",a);c.src=Cd(b);(b=Ed(c.ownerDocument))&&c.setAttribute("nonce",b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function Rd(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function Sd(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Td(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Ud(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c}var Vd=/^([0-9.]+)px$/,Wd=/^(-?[0-9.]{1,30})$/; 
function Xd(a){if(!Wd.test(a))return null;a=Number(a);return isNaN(a)?null:a}function Yd(a){return(a=Vd.exec(a))?+a[1]:null}var Zd=jd(()=>rd()?2:sd()?1:0),$d=a=>{Td({display:"none"},(b,c)=>{a.style.setProperty(c,b,"important")})};let ae=[];const be=()=>{const a=ae;ae=[];for(const b of a)try{b()}catch{}};function ce(){var a=M(de).B(ee.g,ee.defaultValue),b=N.document;if(a.length&&b.head)for(const c of a)c&&b.head&&(a=Qd("META"),b.head.appendChild(a),a.httpEquiv="origin-trial",a.content=c)} 
var fe=a=>qd(a,{ka:()=>{}}),he=a=>{var b=ge;b.readyState==="complete"||b.readyState==="interactive"?(ae.push(a),ae.length==1&&(window.Promise?Promise.resolve().then(be):window.setImmediate?setImmediate(be):setTimeout(be,0))):b.addEventListener("DOMContentLoaded",a)};function Qd(a,b=document){return b.createElement(String(a).toLowerCase())};function ie(a,b,c=null,d=!1,e=!1){je(a,b,c,d,e)}function je(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Qd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const l=Ia(h,f);l>=0&&Array.prototype.splice.call(h,l,1)}md(f,"load",g);md(f,"error",g)};ld(f,"load",g);ld(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function ke(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;Td(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});le(c)}function le(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):ie(b,a,void 0,!1,!1)};var ge=document,N=window;let me=null;var ne=(a,b=[])=>{let c=!1;q.google_logging_queue||(c=!0,q.google_logging_queue=[]);q.google_logging_queue.push([a,b]);if(a=c){if(me==null){me=!1;try{const d=Od(q);d&&d.location.hash.indexOf("google_logging")!==-1&&(me=!0)}catch(d){}}a=me}a&&Pd(q.document,Kd`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function oe(a,b){this.width=a;this.height=b}oe.prototype.aspectRatio=function(){return this.width/this.height};oe.prototype.isEmpty=function(){return!(this.width*this.height)};oe.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};oe.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};oe.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
oe.prototype.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function pe(a=q){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function qe(a=pe()){return a?Nd(a.master)?a.master:null:null};function re(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function se(a){this.g=a||q.document||document}se.prototype.contains=function(a,b){return a&&b?a==b||a.contains(b):!1};var te=a=>{a=qe(pe(a))||a;a.google_unique_id=(a.google_unique_id||0)+1;return a.google_unique_id},ue=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},ve=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};function we(a){return!!(a.error&&a.meta&&a.id)}var xe=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function ze(a){return new xe(a,{message:Ae(a)})}function Ae(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const Be=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Ce=class{constructor(a,b){this.g=a;this.i=b}},De=class{constructor(a,b,c){this.url=a;this.l=b;this.g=!!c;this.depth=null}};let Ee=null;function Fe(){var a=window;if(Ee===null){Ee="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);Ee=c?c[1]:""}}catch(b){}}return Ee};function Ge(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function He(){const a=q.performance;return a&&a.now?a.now():null};var Ie=class{constructor(a,b){var c=He()||Ge();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Je=q.performance,Ke=!!(Je&&Je.mark&&Je.measure&&Je.clearMarks),Le=jd(()=>{var a;if(a=Ke)a=Fe(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function Me(a){a&&Je&&Le()&&(Je.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Je.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function Ne(a){a.g=!1;if(a.i!==a.j.google_js_reporting_queue){if(Le()){var b=a.i;const c=b.length;b=typeof b==="string"?b.split(""):b;for(let d=0;d<c;d++)d in b&&Me.call(void 0,b[d])}a.i.length=0}} 
var Oe=class{constructor(a){this.i=[];this.j=a||q;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Le()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Ie(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Je&&Le()&&Je.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(He()||Ge())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Je&&Le()&&Je.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Pe(a,b){const c={};c[a]=b;return[c]}function Qe(a,b,c,d,e){const f=[];Td(a,(g,h)=>{(g=Re(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Re(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Re(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Qe(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Se(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function Te(a,b,c){b="https://"+b+c;let d=Se(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let l=0;l<h.length;l++){if(!d){c=c==null?g:c;break}let k=Qe(h[l],a.j,",$");if(k){k=e+k;if(d>=k.length){d-=k.length;b+=k;e=a.j;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var Ue=class{constructor(){this.j="&";this.i={};this.u=0;this.g=[]}};var Xe=class{constructor(a=null){this.G=Ve;this.j=a;this.i=null;this.C=!1;this.D=this.I}J(a){this.D=a}B(a){this.i=a}Y(a){this.C=a}g(a,b,c){let d,e;try{this.j&&this.j.g?(e=this.j.start(a.toString(),3),d=b(),this.j.end(e)):d=b()}catch(f){b=!0;try{Me(e),b=this.D(a,ze(f),void 0,c)}catch(g){this.I(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}I(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const cb=new Ue;var g=cb;g.g.push(1);g.i[1]=Pe("context", 
a);we(b)||(b=ze(b));g=b;if(g.msg){b=cb;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Pe("msg",h)}var l=g.meta||{};h=l;if(this.i)try{this.i(h)}catch(Y){}if(d)try{d(h)}catch(Y){}d=cb;l=[l];d.g.push(3);d.i[3]=l;var k;if(!(k=p)){d=q;l=[];h=null;do{var m=d;if(Nd(m)){var n=m.location.href;h=m.document&&m.document.referrer||null}else n=h,h=null;l.push(new De(n||"",m));try{d=m.parent}catch(Y){d=null}}while(d&&m!==d);for(let Y=0,Kg=l.length-1;Y<=Kg;++Y)l[Y].depth=Kg-Y;m=q;if(m.location&&m.location.ancestorOrigins&& 
m.location.ancestorOrigins.length===l.length-1)for(n=1;n<l.length;++n){const Y=l[n];Y.url||(Y.url=m.location.ancestorOrigins[n-1]||"",Y.g=!0)}k=l}var p=k;let Zc=new De(q.location.href,q,!1);k=null;const ye=p.length-1;for(m=ye;m>=0;--m){var v=p[m];!k&&Be.test(v.url)&&(k=v);if(v.url&&!v.g){Zc=v;break}}v=null;const Fk=p.length&&p[ye].url;Zc.depth!==0&&Fk&&(v=p[ye]);f=new Ce(Zc,v);if(f.i){p=cb;var t=f.i.url||"";p.g.push(4);p.i[4]=Pe("top",t)}var y={url:f.g.url||""};if(f.g.url){const Y=f.g.url.match(Hd); 
var J=Y[1],K=Y[3],kb=Y[4];t="";J&&(t+=J+":");K&&(t+="//",t+=K,kb&&(t+=":"+kb));var gc=t}else gc="";J=cb;y=[y,{url:gc}];J.g.push(5);J.i[5]=y;We(this.G,e,cb,this.C,c)}catch(cb){try{We(this.G,e,{context:"ecmserr",rctx:a,msg:Ae(cb),url:f?.g.url??""},this.C,c)}catch(Zc){}}return!0}na(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,this.i||void 0)})}};var Ye=class extends L{},Ze=[2,3,4];var $e=class extends L{},af=[3,4,5],bf=[6,7];var cf=class extends L{},df=[4,5];function ef(a,b){var c=C(a,$e,2,A());if(!c.length)return ff(a,b);a=I(a,1);if(a===1)return c=ef(c[0],b),c.success?{success:!0,value:!c.value}:c;c=Ka(c,d=>ef(d,b));switch(a){case 2:return c.find(d=>d.success&&!d.value)??c.find(d=>!d.success)??{success:!0,value:!0};case 3:return c.find(d=>d.success&&d.value)??c.find(d=>!d.success)??{success:!0,value:!1};default:return{success:!1,O:3}}} 
function ff(a,b){var c=Fc(a,af);a:{switch(c){case 3:var d=Rc(a,3,af);break a;case 4:d=Rc(a,4,af);break a;case 5:d=Rc(a,5,af);break a}d=void 0}if(!d)return{success:!1,O:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,da:c,O:1};let e;try{var f=tc(a,8,cc,A());e=b(...f)}catch(g){return{success:!1,property:d,da:c,O:2}}f=I(a,1);if(f===4)return{success:!0,value:!!e};if(f===5)return{success:!0,value:e!=null};if(f===12)a=H(a,Ec(a,bf,7));else a:{switch(c){case 4:a=Qc(a,Ec(a,bf,6));break a;case 5:a=H(a, 
Ec(a,bf,7));break a}a=void 0}if(a==null)return{success:!1,property:d,da:c,O:3};if(f===6)return{success:!0,value:e===a};if(f===9)return{success:!0,value:e!=null&&ua(String(e),a)===0};if(e==null)return{success:!1,property:d,da:c,O:4};switch(f){case 7:c=e<a;break;case 8:c=e>a;break;case 12:c=ub(a)&&ub(e)&&(new RegExp(a)).test(e);break;case 10:c=e!=null&&ua(String(e),a)===-1;break;case 11:c=e!=null&&ua(String(e),a)===1;break;default:return{success:!1,O:3}}return{success:!0,value:c}} 
function gf(a,b){return a?b?ef(a,b):{success:!1,O:1}:{success:!0,value:!0}};function hf(a){return tc(a,4,cc,A())}var jf=class extends L{};var kf=class extends L{getValue(){return B(this,jf,2)}};var lf=class extends L{},mf=fd(lf),nf=[1,2,3,6,7,8];var of=class extends L{};function pf(a,b){try{const c=d=>[{[d.Ca]:d.Aa}];return JSON.stringify([a.filter(d=>d.la).map(c),w(b),a.filter(d=>!d.la).map(c)])}catch(c){return qf(c,b),""}}function qf(a,b){try{ke({m:Ae(a instanceof Error?a:Error(String(a))),b:I(b,1)||null,v:H(b,2)||null},"rcs_internal")}catch(c){}}var rf=class{constructor(a,b){var c=new of;a=ad(c,1,a);b=Yc(a,2,b);this.j=cd(b)}};var sf=class extends L{getWidth(){return G(this,3)}getHeight(){return G(this,4)}};var tf=class extends L{};function uf(a,b){return qc(a,1,b==null?b:Wb(b))}function vf(a,b){return qc(a,2,b==null?b:Wb(b))}var wf=class extends L{getWidth(){return Nc(this,1)??pc}getHeight(){return Nc(this,2)??pc}};var xf=class extends L{};var yf=class extends L{};var zf=class extends L{getValue(){return I(this,1)}};var Af=class extends L{getContentUrl(){return H(this,4)}};var Bf=class extends L{};function Cf(a){return Gc(a,Bf,3)}var Df=class extends L{};var Ef=class extends L{getContentUrl(){return H(this,1)}};var Ff=class extends L{};function Gf(a){var b=new Hf;return ad(b,1,a)}var Hf=class extends L{};var If=class extends L{},Jf=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Kf=class extends L{};function Lf(a,b){return ad(a,1,b)}function Mf(a,b){return ad(a,2,b)}var Nf=class extends L{};var Of=class extends L{},Pf=[1,2];function Qf(a,b){return Jc(a,1,b)}function Rf(a,b){return Lc(a,2,b)}function Sf(a,b){return zc(a,4,b,Tb)}function Tf(a,b){return Lc(a,5,b)}function Uf(a,b){return ad(a,6,b)}var Vf=class extends L{};var Wf=class extends L{},Xf=[1,2,3,4,6];var Yf=class extends L{};function Zf(a){var b=new $f;return Kc(b,4,ag,a)}var $f=class extends L{getTagSessionCorrelator(){return Nc(this,2)??pc}},ag=[4,5,7,8,9];var bg=class extends L{};function cg(){var a=dg();a=mc(a);return Yc(a,1,eg())}var fg=class extends L{};var gg=class extends L{};var hg=class extends L{getTagSessionCorrelator(){return Nc(this,1)??pc}};var ig=class extends L{},jg=[1,7],kg=[4,6,8];class lg extends rf{constructor(){super(...arguments)}}function mg(a,...b){ng(a,...b.map(c=>({la:!0,Ca:3,Aa:w(c)})))}function og(a,...b){ng(a,...b.map(c=>({la:!0,Ca:4,Aa:w(c)})))}function pg(a,...b){ng(a,...b.map(c=>({la:!0,Ca:7,Aa:w(c)})))}var qg=class extends lg{};function rg(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function ng(a,...b){try{a.D&&pf(a.g.concat(b),a.j).length>=65536&&sg(a),a.u&&!a.B&&(a.B=!0,tg(a.u,()=>{sg(a)})),a.g.push(...b),a.g.length>=a.C&&sg(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{sg(a)},a.J))}catch(c){qf(c,a.j)}}function sg(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=pf(a.g,a.j);a.G("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var ug=class extends qg{constructor(a,b,c,d,e,f){super(a,b);this.G=rg;this.J=c;this.C=d;this.D=e;this.u=f;this.g=[];this.i=null;this.B=!1}},vg=class extends ug{constructor(a,b,c=1E3,d=100,e=!1,f){super(a,b,c,d,e&&!0,f)}};function wg(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=Vc(b,1,c);c=fe(window);b=Vc(b,2,c);return Vc(b,6,a.B)}function xg(a,b,c,d,e,f){if(a.j){var g=Mf(Lf(new Nf,b),c);b=Uf(Rf(Qf(Tf(Sf(new Vf,d),e),g),a.g.slice()),f);b=Zf(b);og(a.i,wg(a,b));if(f===1||f===3||f===4&&!a.g.some(h=>I(h,1)===I(g,1)&&I(h,2)===c))a.g.push(g),a.g.length>100&&a.g.shift()}}function yg(a,b,c,d){if(a.j){var e=new Kf;b=Tc(e,1,b);c=Tc(b,2,c);d=$c(c,3,d);c=new $f;d=Kc(c,8,ag,d);og(a.i,wg(a,d))}} 
function zg(a,b,c,d,e){if(a.j){var f=new cf;b=Jc(f,1,b);c=$c(b,2,c);d=Tc(c,3,d);if(e.da===void 0)bd(d,4,df,e.O);else switch(e.da){case 3:c=new Ye;c=bd(c,2,Ze,e.property);e=$c(c,1,e.O);Kc(d,5,df,e);break;case 4:c=new Ye;c=bd(c,3,Ze,e.property);e=$c(c,1,e.O);Kc(d,5,df,e);break;case 5:c=new Ye,c=bd(c,4,Ze,e.property),e=$c(c,1,e.O),Kc(d,5,df,e)}e=new $f;e=Kc(e,9,ag,d);og(a.i,wg(a,e))}}var Ag=class{constructor(a,b,c,d=new vg(6,"unknown",b)){this.B=a;this.u=c;this.i=d;this.g=[];this.j=a>0&&Sd()<1/a}};var M=a=>{var b="za";if(a.za&&a.hasOwnProperty(b))return a.za;b=new a;return a.za=b};var Bg=class{constructor(){this.N={[3]:{},[4]:{},[5]:{}}}};var Cg=/^true$/.test("false");function Dg(a,b){switch(b){case 1:return Rc(a,1,nf);case 2:return Rc(a,2,nf);case 3:return Rc(a,3,nf);case 6:return Rc(a,6,nf);case 8:return Rc(a,8,nf);default:return null}}function Eg(a,b){if(!a)return null;switch(b){case 1:return F(a,1);case 7:return H(a,3);case 2:return Qc(a,2);case 3:return H(a,3);case 6:return hf(a);case 8:return hf(a);default:return null}}const Fg=jd(()=>{if(!Cg)return{};try{var a=window;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch{b=null}if(b)return JSON.parse(b)}catch{}return{}}); 
function Gg(a,b,c,d=0){M(Hg).j[d]=M(Hg).j[d]?.add(b)??(new Set).add(b);const e=Fg();if(e[b]!=null)return e[b];b=Ig(d)[b];if(!b)return c;b=mf(JSON.stringify(b));b=Jg(b);a=Eg(b,a);return a!=null?a:c}function Jg(a){const b=M(Bg).N;if(b&&Fc(a,nf)!==8){const c=Ma(C(a,kf,5,A()),d=>{d=gf(B(d,$e,1),b);return d.success&&d.value});if(c)return c.getValue()??null}return B(a,jf,4)??null}class Hg{constructor(){this.i={};this.u=[];this.j={};this.g=new Map}}function Lg(a,b=!1,c){return!!Gg(1,a,b,c)} 
function Mg(a,b=0,c){a=Number(Gg(2,a,b,c));return isNaN(a)?b:a}function Ng(a,b="",c){a=Gg(3,a,b,c);return typeof a==="string"?a:b}function Og(a,b=[],c){a=Gg(6,a,b,c);return Array.isArray(a)?a:b}function Pg(a,b=[],c){a=Gg(8,a,b,c);return Array.isArray(a)?a:b}function Ig(a){return M(Hg).i[a]||(M(Hg).i[a]={})} 
function Qg(a,b){const c=Ig(b);Td(a,(d,e)=>{if(c[e]){const g=mf(JSON.stringify(d));if(E(g,Ec(g,nf,8))!=null){var f=mf(JSON.stringify(c[e]));d=Gc(g,jf,4);f=hf(Hc(f,jf,4));Mc(d,f)}c[e]=w(g)}else c[e]=d})} 
function Rg(a,b,c,d,e=!1){var f=[],g=[];for(const n of b){b=Ig(n);for(const p of a){var h=Fc(p,nf);const v=Dg(p,h);if(v){a:{var l=v;var k=h,m=M(Hg).g.get(n)?.get(v)?.slice(0)??[];const t=new Wf;switch(k){case 1:bd(t,1,Xf,l);break;case 2:bd(t,2,Xf,l);break;case 3:bd(t,3,Xf,l);break;case 6:bd(t,4,Xf,l);break;case 8:bd(t,6,Xf,l);break;default:l=void 0;break a}zc(t,5,m,Tb);l=t}l&&M(Hg).j[n]?.has(v)&&f.push(l);h===8&&b[v]?(l=mf(JSON.stringify(b[v])),h=Gc(p,jf,4),l=hf(Hc(l,jf,4)),Mc(h,l)):l&&M(Hg).g.get(n)?.has(v)&& 
g.push(l);e||(h=v,l=n,k=d,m=M(Hg),m.g.has(l)||m.g.set(l,new Map),m.g.get(l).has(h)||m.g.get(l).set(h,[]),k&&m.g.get(l).get(h).push(k));b[v]=w(p)}}}if(f.length||g.length)a=d??void 0,c.j&&c.u&&(d=new Yf,f=Lc(d,2,f),g=Lc(f,3,g),a&&Uc(g,1,a),f=new $f,g=Kc(f,7,ag,g),og(c.i,wg(c,g)))}function Sg(a,b){b=Ig(b);for(const c of a){a=mf(JSON.stringify(c));const d=Fc(a,nf);(a=Dg(a,d))&&(b[a]||(b[a]=c))}}function Tg(){return Object.keys(M(Hg).i).map(a=>Number(a))} 
function Ug(a){M(Hg).u.includes(a)||Qg(Ig(4),a)};function O(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Vg(a,b,c){return b[a]||c}function Wg(a){O(5,Lg,a);O(6,Mg,a);O(7,Ng,a);O(8,Og,a);O(17,Pg,a);O(13,Sg,a);O(15,Ug,a)}function Xg(a){O(4,b=>{M(Bg).N=b},a);O(9,(b,c)=>{var d=M(Bg);d.N[3][b]==null&&(d.N[3][b]=c)},a);O(10,(b,c)=>{var d=M(Bg);d.N[4][b]==null&&(d.N[4][b]=c)},a);O(11,(b,c)=>{var d=M(Bg);d.N[5][b]==null&&(d.N[5][b]=c)},a);O(14,b=>{var c=M(Bg);for(const d of[3,4,5])Object.assign(c.N[d],b[d])},a)} 
function Yg(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};function Zg(a,b,c){a.j=Vg(1,b,()=>{});a.u=(d,e)=>Vg(2,b,()=>[])(d,c,e);a.g=()=>Vg(3,b,()=>[])(c);a.i=d=>{Vg(16,b,()=>{})(d,c)}}class $g{j(){}i(){}u(){return[]}g(){return[]}};function We(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Ue?f=c:(f=new Ue,Td(c,(h,l)=>{var k=f;const m=k.u++;h=Pe(l,h);k.g.push(m);k.i[m]=h}));const g=Te(f,a.domain,a.path+b+"&");g&&ie(q,g)}catch(f){}}function ah(a,b){b>=0&&b<=1&&(a.g=b)}var bh=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Ve,ch;const dh=new Oe(window);(function(a){Ve=a??new bh;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());ah(Ve,window.google_srt);ch=new Xe(dh);ch.B(()=>{});ch.Y(!0);window.document.readyState==="complete"?window.google_measure_js_timing||Ne(dh):dh.g&&ld(window,"load",()=>{window.google_measure_js_timing||Ne(dh)})})();let eh=(new Date).getTime();var fh={Yb:0,Xb:1,Ub:2,Pb:3,Vb:4,Qb:5,Wb:6,Sb:7,Tb:8,Ob:9,Rb:10,Zb:11};var gh={bc:0,dc:1,ac:2};function hh(a){if(a.g!=0)throw Error("Already resolved/rejected.");}var kh=class{constructor(){this.i=new ih(this);this.g=0}resolve(a){hh(this);this.g=1;this.u=a;jh(this.i)}reject(a){hh(this);this.g=2;this.j=a;jh(this.i)}};function jh(a){switch(a.g.g){case 0:break;case 1:a.i&&a.i(a.g.u);break;case 2:a.j&&a.j(a.g.j);break;default:throw Error("Unhandled deferred state.");}}var ih=class{constructor(a){this.g=a}then(a,b){if(this.i)throw Error("Then functions already set.");this.i=a;this.j=b;jh(this)}};var lh=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new lh(Ja(this.g,a))}apply(a){return new lh(a(this.g.slice(0)))}sort(a){return new lh(this.g.slice(0).sort(a))}get(a){return this.g[a]}add(a){const b=this.g.slice(0);b.push(a);return new lh(b)}};function mh(a,b){const c=[],d=a.length;for(let e=0;e<d;e++)c.push(a[e]);c.forEach(b,void 0)};var oh=class{constructor(){this.g={};this.i={}}set(a,b){const c=nh(a);this.g[c]=b;this.i[c]=a}get(a,b){a=nh(a);return this.g[a]!==void 0?this.g[a]:b}clear(){this.g={};this.i={}}};function nh(a){return a instanceof Object?String(la(a)):a+""};function ph(a){return new qh({value:a},null)}function rh(a){return new qh(null,a)}function sh(a){try{return ph(a())}catch(b){return rh(b)}}function th(a){return a.g!=null?a.getValue():null}function uh(a,b){a.g!=null&&b(a.getValue());return a}function vh(a,b){a.g!=null||b(a.i);return a}var qh=class{constructor(a,b){this.g=a;this.i=b}getValue(){return this.g.value}map(a){return this.g!=null?(a=a(this.getValue()),a instanceof qh?a:ph(a)):this}};var wh=class{constructor(a){this.g=new oh;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return this.g.g[nh(a)]!==void 0}};var xh=class{constructor(){this.g=new oh}set(a,b){let c=this.g.get(a);c||(c=new wh,this.g.set(a,c));c.add(b)}};var yh=class extends L{getId(){return D(this,3)}};var zh=class{constructor({lb:a,fc:b,tc:c,Fb:d}){this.g=b;this.u=new lh(a||[]);this.j=d;this.i=c}};const Bh=a=>{const b=[],c=a.u;c&&c.g.length&&b.push({ba:"a",ca:Ah(c)});a.g!=null&&b.push({ba:"as",ca:a.g});a.i!=null&&b.push({ba:"i",ca:String(a.i)});a.j!=null&&b.push({ba:"rp",ca:String(a.j)});b.sort(function(d,e){return d.ba.localeCompare(e.ba)});b.unshift({ba:"t",ca:"aa"});return b},Ah=a=>{a=a.g.slice(0).map(Ch);a=JSON.stringify(a);return Ud(a)},Ch=a=>{const b={};D(a,7)!=null&&(b.q=D(a,7));Pc(a,2)!=null&&(b.o=Pc(a,2));Pc(a,5)!=null&&(b.p=Pc(a,5));return b};function Dh(a){return E(a,2)}var Eh=class extends L{setLocation(a){return $c(this,1,a)}};function Fh(a){const b=[].slice.call(arguments).filter(id(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Ta||[]);d=Object.assign(d,e.ab)});return new Gh(c,d)}function Hh(a){switch(a){case 1:return new Gh(null,{google_ad_semantic_area:"mc"});case 2:return new Gh(null,{google_ad_semantic_area:"h"});case 3:return new Gh(null,{google_ad_semantic_area:"f"});case 4:return new Gh(null,{google_ad_semantic_area:"s"});default:return null}} 
function Ih(a){if(a==null)var b=null;else{b=Gh;var c=Bh(a);a=[];for(let d of c)c=String(d.ca),a.push(d.ba+"."+(c.length<=20?c:c.slice(0,19)+"_"));b=new b(null,{google_placement_id:a.join("~")})}return b}var Gh=class{constructor(a,b){this.Ta=a;this.ab=b}};var Jh=new Gh(["google-auto-placed"],{google_reactive_ad_format:40,google_tag_origin:"qs"});var Kh=fd(class extends L{});function Lh(a){return B(a,yh,1)}function Mh(a){return E(a,2)}var Nh=class extends L{};var Oh=class extends L{};var Ph=class extends L{};function Qh(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b};function Rh(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){let d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Qh(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")};var P=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},Q=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},Sh=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var Th=new P(1333),Uh=new Q(1359),Vh=new Q(1358),Wh=new P(1360),Xh=new Q(1357),Yh=new P(1345),Zh=new Q(1130,100),$h=new Q(1340,.2),ai=new Q(1338,.3),bi=new Q(1336,1.2),ci=new Q(1339,.3),di=new P(1337),ei=new class{constructor(a,b=""){this.g=a;this.defaultValue=b}}(14),fi=new P(1342),gi=new P(1344),hi=new Q(1343,300),ii=new P(316),ji=new P(313),ki=new P(369),li=new P(1318,!0),mi=new P(626390500),ni=new Q(717888911),oi=new Sh(635821288,["29_18","30_19"]),pi=new Sh(683929765),qi=new P(506914611),ri= 
new Q(717888910,.7),si=new Q(643258048,.15),ti=new Q(643258049,.33938),ui=new Q(717888912,.7),vi=new P(711741274),wi=new P(45650663),xi=new Q(684147711,-1),yi=new Q(684147712,-1),zi=new P(*********),Ai=new Q(1079,5),Bi=new P(10013),ee=new class{constructor(a,b=[]){this.g=a;this.defaultValue=b}}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==", 
"Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9", 
"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]),Ci=new P(84);var de=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.i=(b,c)=>a[b]!=null?a[b]:c;this.B=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.C=()=>{}}};function R(a){return M(de).j(a.g,a.defaultValue)}function S(a){return M(de).u(a.g,a.defaultValue)};function Di(a,b){const c=e=>{e=Ei(e);return e==null?!1:0<e},d=e=>{e=Ei(e);return e==null?!1:0>e};switch(b){case 0:return{init:Fi(a.previousSibling,c),ga:e=>Fi(e.previousSibling,c),ma:0};case 2:return{init:Fi(a.lastChild,c),ga:e=>Fi(e.previousSibling,c),ma:0};case 3:return{init:Fi(a.nextSibling,d),ga:e=>Fi(e.nextSibling,d),ma:3};case 1:return{init:Fi(a.firstChild,d),ga:e=>Fi(e.nextSibling,d),ma:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Ei(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Fi(a,b){return a&&b(a)?a:null};var Gi={rectangle:1,horizontal:2,vertical:4};var Hi={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7};function Ii(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}}function T(a){return Ii(a).clientWidth??void 0};function Ji(a,b){do{const c=Rd(a,b);if(c&&c.position=="fixed")return!1}while(a=a.parentElement);return!0};function Ki(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=Yd(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function Li(a,b){return!((Wd.test(b.google_ad_width)||Vd.test(a.style.width))&&(Wd.test(b.google_ad_height)||Vd.test(a.style.height)))}function Mi(a,b){return(a=Ni(a,b))?a.y:0} 
function Ni(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}} 
function Oi(a,b,c,d,e){if(a!==a.top)return Od(a)?3:16;if(!(T(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=T(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=T(a);c;c=c.parentElement)if((d=Rd(c,a))&&(e=Yd(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function Pi(a,b,c,d){const e=Oi(b,c,a,S(ci),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Ji(c,b)?(b=T(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function Qi(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function Ri(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=Rd(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function Si(a,b,c){a=Ni(b,a);return c==="rtl"?-a.x:a.x} 
function Ti(a,b){var c;c=(c=b.parentElement)?(c=Rd(c,a))?c.direction:"":"";if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";Qi(b,c,"0px");d.width=`${T(a)}px`;if(Si(a,b,c)!==0){Qi(b,c,"0px");var e=Si(a,b,c);Qi(b,c,`${-1*e}px`);a=Si(a,b,c);a!==0&&a!==e&&Qi(b,c,`${e/(a-e)*e}px`)}d.zIndex="30"}};function Ui(a,b,c){let d;return a.style&&!!a.style[c]&&Yd(a.style[c])||(d=Rd(a,b))&&!!d[c]&&Yd(d[c])||null}function Vi(a,b){const c=ue(a)===0;return b&&c?Math.max(250,2*Ii(a).clientHeight/3):250}function Wi(a,b){let c;return a.style&&a.style.zIndex||(c=Rd(a,b))&&c.zIndex||null}function Xi(a){return b=>b.g<=a}function Yi(a,b,c,d){const e=a&&Zi(c,b),f=Vi(b,d);return g=>!(e&&g.height()>=f)}function $i(a){return b=>b.height()<=a}function Zi(a,b){return Mi(a,b)<Ii(b).clientHeight-100} 
function aj(a,b){var c=Ui(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=Ui(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&Yd(b.style.height))&&(c=Math.min(c,d)),(d=Ui(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};var bj={google_ad_channel:!0,google_ad_client:!0,google_ad_host:!0,google_ad_host_channel:!0,google_adtest:!0,google_tag_for_child_directed_treatment:!0,google_tag_for_under_age_of_consent:!0,google_tag_partner:!0,google_restrict_data_processing:!0,google_page_url:!0,google_debug_params:!0,google_adbreak_test:!0,google_ad_frequency_hint:!0,google_admob_interstitial_slot:!0,google_admob_rewarded_slot:!0,google_admob_ads_only:!0,google_ad_start_delay_hint:!0,google_max_ad_content_rating:!0,google_traffic_source:!0, 
google_overlays:!0,google_privacy_treatments:!0,google_special_category_data:!0,google_ad_intent_query:!0,google_ad_intent_qetid:!0,google_ad_intent_eids:!0,google_ad_intents_format:!0};const cj=RegExp("(^| )adsbygoogle($| )");function dj(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Gd(d.property);a[e]=d.value}};var ej=class extends L{i(){return Oc(this,23)}};var fj=class extends L{i(){return Nc(this,1)??void 0}};var gj=class extends L{};var hj=class extends L{};var ij=class extends L{};var jj=class extends L{};var kj=class extends L{getName(){return D(this,4)}},lj=[1,2,3];var mj=class extends L{};var nj=class extends L{};var pj=class extends L{i(){return Sc(this,nj,2,oj)}},oj=[1,2];var qj=class extends L{i(){return B(this,pj,3)}};var rj=class extends L{},sj=fd(rj);function tj(a){const b=[];mh(a.getElementsByTagName("p"),function(c){uj(c)>=100&&b.push(c)});return b}function uj(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;mh(a.childNodes,function(c){b+=uj(c)});return b}function vj(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function wj(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function xj(a,b){var c=[];try{c=b.querySelectorAll(a.u)}catch(d){}if(!c.length)return[];b=Oa(c);b=wj(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=tj(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var yj=class{constructor(a,b,c,d){this.u=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.u,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var zj=class{constructor(){this.i=Kd`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}I(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;we(b)||(b=new xe(b,{context:a,id:d}));q.google_js_errors=q.google_js_errors||[];q.google_js_errors.push(b);q.error_rep_loaded||(Pd(q.document,this.i),q.error_rep_loaded=!0);return!1}g(a,b){try{return b()}catch(c){if(!this.I(a,c,.01,"jserror"))throw c;}}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}na(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.I(a,c instanceof Error?c:Error(c),void 0)})}};function Aj(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Bj(a,b,c,d,e=!1){const f=d||window,g=typeof queueMicrotask!=="undefined";return function(...h){e&&g&&queueMicrotask(()=>{f.google_rum_task_id_counter=f.google_rum_task_id_counter||1;f.google_rum_task_id_counter+=1});const l=He();let k,m=3;try{k=b.apply(this,h)}catch(n){m=13;if(!c)throw n;c(a,n)}finally{f.google_measure_js_timing&&l&&Aj({label:a.toString(),value:l,duration:(He()||0)-l,type:m,...(e&&g&&{taskId:f.google_rum_task_id_counter=f.google_rum_task_id_counter||1})},f)}return k}} 
function Cj(a,b){return Bj(a,b,(c,d)=>{(new zj).I(c,d)},void 0,!1)};function Dj(a,b,c){return Bj(a,b,void 0,c,!0).apply()}function Ej(a){if(!a)return null;var b=D(a,7);if(D(a,1)||a.getId()||tc(a,4,cc,A()).length>0){var c=a.getId(),d=D(a,1),e=tc(a,4,cc,A());b=Pc(a,2);var f=Pc(a,5);a=Fj(E(a,6));let g="";d&&(g+=d);c&&(g+="#"+vj(c));if(e)for(c=0;c<e.length;c++)g+="."+vj(e[c]);b=(e=g)?new yj(e,b,f,a):null}else b=b?new yj(b,Pc(a,2),Pc(a,5),Fj(E(a,6))):null;return b}const Gj={1:1,2:2,3:3,0:0};function Fj(a){return a==null?a:Gj[a]}const Hj={1:0,2:1,3:2,4:3}; 
function Ij(a){return a.google_ama_state=a.google_ama_state||{}}function Jj(a){a=Ij(a);return a.optimization=a.optimization||{}};var Kj=a=>{switch(E(a,8)){case 1:case 2:if(a==null)var b=null;else b=B(a,yh,1),b==null?b=null:(a=E(a,2),b=a==null?null:new zh({lb:[b],Fb:a}));return b!=null?ph(b):rh(Error("Missing dimension when creating placement id"));case 3:return rh(Error("Missing dimension when creating placement id"));default:return b="Invalid type: "+E(a,8),rh(Error(b))}};var Lj=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function Mj(a,b){const c=new xh,d=new wh;b.forEach(e=>{if(Sc(e,ij,1,lj)){e=Sc(e,ij,1,lj);if(B(e,Nh,1)&&Lh(B(e,Nh,1))&&B(e,Nh,2)&&Lh(B(e,Nh,2))){const g=Nj(a,Lh(B(e,Nh,1))),h=Nj(a,Lh(B(e,Nh,2)));if(g&&h)for(var f of Lj({anchor:g,position:Mh(B(e,Nh,1))},{anchor:h,position:Mh(B(e,Nh,2))}))c.set(la(f.anchor),f.position)}B(e,Nh,3)&&Lh(B(e,Nh,3))&&(f=Nj(a,Lh(B(e,Nh,3))))&&c.set(la(f),Mh(B(e,Nh,3)))}else Sc(e,jj,2,lj)?Oj(a,Sc(e,jj,2,lj),c):Sc(e,hj,3,lj)&&Pj(a,Sc(e,hj,3,lj),d)});return new Qj(c,d)} 
var Qj=class{constructor(a,b){this.i=a;this.g=b}};const Oj=(a,b,c)=>{B(b,Nh,2)?(b=B(b,Nh,2),(a=Nj(a,Lh(b)))&&c.set(la(a),Mh(b))):B(b,yh,1)&&(a=Rj(a,B(b,yh,1)))&&a.forEach(d=>{d=la(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},Pj=(a,b,c)=>{B(b,yh,1)&&(a=Rj(a,B(b,yh,1)))&&a.forEach(d=>{c.add(la(d))})},Nj=(a,b)=>(a=Rj(a,b))&&a.length>0?a[0]:null,Rj=(a,b)=>(b=Ej(b))?xj(b,a):null;function eg(){return"m202504170101"};var Sj=ed(bg);var dg=ed(fg);function Tj(a,b){return b(a)?a:void 0} 
function Uj(a,b,c,d,e){c=c instanceof xe?c.error:c;var f=new ig;const g=new hg;try{var h=fe(window);Vc(g,1,h)}catch(p){}try{var l=M($g).g();zc(g,2,l,Tb)}catch(p){}try{Yc(g,3,window.document.URL)}catch(p){}h=Jc(f,2,g);l=new gg;b=ad(l,1,b);try{var k=ub(c?.name)?c.name:"Unknown error";Yc(b,2,k)}catch(p){}try{var m=ub(c?.message)?c.message:`Caught ${c}`;Yc(b,3,m)}catch(p){}try{var n=ub(c?.stack)?c.stack:Error().stack;n&&zc(b,4,n.split(/\n\s*/),ac)}catch(p){}k=Kc(h,1,jg,b);if(e){m=0;switch(e.errSrc){case "LCC":m= 
1;break;case "PVC":m=2}n=cg();b=Tj(e.shv,ub);n=Yc(n,2,b);m=ad(n,6,m);n=Sj();n=mc(n);b=Tj(e.es,xb());n=zc(n,1,b,Tb);n=cd(n);m=Jc(m,4,n);n=Tj(e.client,ub);m=Xc(m,3,n);n=Tj(e.slotname,ub);m=Yc(m,7,n);e=Tj(e.tag_origin,ub);e=Yc(m,8,e);e=cd(e)}else e=cd(cg());e=Kc(k,6,kg,e);d=Vc(e,5,d??1);mg(a,d)};var Wj=class{constructor(){this.g=Vj}};function Vj(){return{Cb:pd()+(pd()&2**21-1)*2**32,rb:Number.MAX_SAFE_INTEGER}};var Zj=class{constructor(a=!1){var b=Xj;this.D=Yj;this.C=a;this.G=b;this.i=null;this.j=this.I}J(a){this.j=a}B(a){this.i=a}Y(){}g(a,b,c){let d;try{d=b()}catch(e){b=this.C;try{b=this.j(a,ze(e),void 0,c)}catch(f){this.I(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}na(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,void 0)})}I(a,b,c,d){try{const g=c===void 0?1/this.G:c===0?0:1/c;var e=(new Wj).g(); 
if(g>0&&e.Cb*g<=e.rb){var f=this.D;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}Uj(f,a,b,g,c)}}catch(g){}return this.C}};var U=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,U):this.stack=Error().stack||""}};let Yj,ak,bk,ck,Xj;const dk=new Oe(q);function ek(a){a!=null&&(q.google_measure_js_timing=a);q.google_measure_js_timing||Ne(dk)}(function(a,b,c=!0){({Eb:Xj,ub:bk}=fk());ak=a||new bh;ah(ak,bk);Yj=b||new vg(2,eg(),1E3);ck=new Zj(c);q.document.readyState==="complete"?ek():dk.g&&ld(q,"load",()=>{ek()})})();function gk(a,b,c){return ck.g(a,b,c)}function hk(a,b){return ck.u(a,b)}function ik(a,b){ck.na(a,b)}function jk(a,b,c=.01){const d=M($g).g();!b.eid&&d.length&&(b.eid=d.toString());We(ak,a,b,!0,c)} 
function kk(a,b,c=Xj,d,e){return ck.I(a,b,c,d,e)}function lk(a,b,c=Xj,d,e){return(we(b)?b.msg||Ae(b.error):Ae(b)).indexOf("TagError")===0?((we(b)?b.error:b).pbr=!0,!1):kk(a,b,c,d,e)}function fk(){let a,b;typeof q.google_srt==="number"?(b=q.google_srt,a=q.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Eb:a,ub:b}};var mk=class{constructor(){var a=Math.random;this.g=Math.floor(a()*2**52);this.i=0}};function nk(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(ok(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function pk(a){a=qk(a);return a.has("all")||a.has("after")}function rk(a){a=qk(a);return a.has("all")||a.has("before")}function qk(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function ok(a){const b=qk(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var sk=class{constructor(){this.g=new Set;this.i=new mk}};function tk(a,b){if(!a)return!1;a=Rd(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function uk(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null}function vk(a){return!!a.nextSibling||!!a.parentNode&&vk(a.parentNode)};function wk(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function xk(a){return{ec:yk(a),hc:V(a,"body ins.adsbygoogle"),ib:zk(a),jb:V(a,".google-auto-placed"),kb:Ak(a),sb:Bk(a),lc:Ck(a),vc:Dk(a),Bb:Ek(a),kc:V(a,"div.googlepublisherpluginad"),Mb:V(a,"html > ins.adsbygoogle")}}function Ck(a){return Gk(a)||V(a,"div[id^=div-gpt-ad]")}function Gk(a){const b=wk(a);return b?Ja(Ka(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function V(a,b){return Oa(a.document.querySelectorAll(b))} 
function Ak(a){return V(a,"ins.adsbygoogle[data-anchor-status]")}function zk(a){return V(a,"iframe[id^=aswift_],iframe[id^=google_ads_frame]")}function Dk(a){return V(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")}function Bk(a){return Ck(a).concat(V(a,"iframe[id^=google_ads_iframe]"))} 
function Ek(a){return V(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function yk(a){return V(a,"ins.adsbygoogle-ablated-ad-slot")}function Hk(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};function Ik(a,b){if(a.u)return!0;a.u=!0;const c=C(a.j,Ph,1,A());a.i=0;const d=Jk(a.G);var e=a.g;var f;try{var g=(f=e.localStorage.getItem("google_ama_settings"))?Kh(f):null}catch(v){g=null}f=g!==null&&F(g,2);g=Ij(e);f&&(g.eatf=!0,ne(7,[!0,0,!1]));b:{var h={wb:!1,xb:!1},l=V(e,".google-auto-placed"),k=Ak(e),m=Dk(e),n=Bk(e);const v=Ek(e),t=yk(e),y=V(e,"div.googlepublisherpluginad"),J=V(e,"html > ins.adsbygoogle");let K=[].concat(...zk(e),...V(e,"body ins.adsbygoogle"));f=[];for(const [kb,gc]of[[h.nc, 
l],[h.wb,k],[h.rc,m],[h.oc,n],[h.sc,v],[h.mc,t],[h.qc,y],[h.xb,J]])kb===!1?f=f.concat(gc):K=K.concat(gc);h=Hk(K);f=Hk(f);h=h.slice(0);for(p of f)for(f=0;f<h.length;f++)(p.contains(h[f])||h[f].contains(p))&&h.splice(f,1);var p=h;e=Ii(e).clientHeight;for(f=0;f<p.length;f++)if(!(p[f].getBoundingClientRect().top>e)){e=!0;break b}e=!1}e=e?g.eatfAbg=!0:!1;if(e)return!0;e=new wh([2]);for(g=0;g<c.length;g++){p=a;h=c[g];f=g;l=b;(k=!B(h,Eh,4))||(k=e,m=k.contains,n=B(h,Eh,4),n=E(n,1),k=!m.call(k,n));if(k||E(h, 
8)!==1||!Kk(h,d))p=null;else{p.i++;if(l=Lk(p,h,l,d))k=Ij(p.g),k.numAutoAdsPlaced||(k.numAutoAdsPlaced=0),(m=!B(h,yh,1))||(h=B(h,yh,1),m=(Pc(h,5)??void 0)==null),m||(k.numPostPlacementsPlaced?k.numPostPlacementsPlaced++:k.numPostPlacementsPlaced=1),k.placed==null&&(k.placed=[]),k.numAutoAdsPlaced++,k.placed.push({index:f,element:l.ea}),ne(7,[!1,p.i,!0]);p=l}if(p)return!0}ne(7,[!1,a.i,!1]);return!1} 
function Lk(a,b,c,d){if(!Kk(b,d)||(E(b,8)??void 0)!=1)return null;d=B(b,yh,1);if(!d)return null;d=Ej(d);if(!d)return null;d=xj(d,a.g.document);if(d.length==0)return null;d=d[0];var e=E(b,2);e=Hj[e];e=e===void 0?null:e;var f;if(!(f=e==null)){a:{f=a.g;switch(e){case 0:f=tk(uk(d),f);break a;case 3:f=tk(d,f);break a;case 2:var g=d.lastChild;f=tk(g?g.nodeType==1?g:uk(g):null,f);break a}f=!1}if(c=!f&&!(!c&&e==2&&!vk(d)))c=e==1||e==2?d:d.parentNode,c=!(c&&!Qh(c)&&c.offsetWidth<=0);f=!c}if(!(c=f)){c=a.C; 
f=E(b,2);g=c.i;var h=la(d);g=g.g.get(h);if(!(g=g?g.contains(f):!1))a:{if(c.g.contains(la(d)))switch(f){case 2:case 3:g=!0;break a;default:g=!1;break a}for(f=d.parentElement;f;){if(c.g.contains(la(f))){g=!0;break a}f=f.parentElement}g=!1}c=g}if(!c){c=a.D;g=E(b,2);a:switch(g){case 1:f=pk(d.previousElementSibling)||rk(d);break a;case 4:f=pk(d)||rk(d.nextElementSibling);break a;case 2:f=rk(d.firstElementChild);break a;case 3:f=pk(d.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+ 
g);}g=nk(c,d,g);c=c.i;jk("ama_exclusion_zone",{typ:f?g?"siuex":"siex":g?"suex":"noex",cor:c.g,num:c.i++,dvc:Zd()},.1);c=f||g}if(c)return null;f=B(b,Oh,3);c={};f&&(c.fb=D(f,1),c.Ra=D(f,2),c.pb=!!Oc(f,3));f=B(b,Eh,4)&&Dh(B(b,Eh,4))?Dh(B(b,Eh,4)):null;f=Hh(f);g=Pc(b,12)!=null?Pc(b,12):null;g=g==null?null:new Gh(null,{google_ml_rank:g});b=Mk(a,b);b=Fh(a.B,f,g,b);f=a.g;a=a.J;h=f.document;var l=c.pb||!1;g=re((new se(h)).g,"DIV");const k=g.style;k.width="100%";k.height="auto";k.clear=l?"both":"none";l=g.style; 
l.textAlign="center";c.Db&&dj(l,c.Db);h=re((new se(h)).g,"INS");l=h.style;l.display="block";l.margin="auto";l.backgroundColor="transparent";c.fb&&(l.marginTop=c.fb);c.Ra&&(l.marginBottom=c.Ra);c.hb&&dj(l,c.hb);g.appendChild(h);c={xa:g,ea:h};c.ea.setAttribute("data-ad-format","auto");g=[];if(h=b&&b.Ta)c.xa.className=h.join(" ");h=c.ea;h.className="adsbygoogle";h.setAttribute("data-ad-client",a);g.length&&h.setAttribute("data-ad-channel",g.join("+"));a:{try{var m=c.xa;if(R(ji)){{const y=Di(d,e);if(y.init){var n= 
y.init;for(d=n;d=y.ga(d);)n=d;var p={anchor:n,position:y.ma}}else p={anchor:d,position:e}}m["google-ama-order-assurance"]=0;Rh(m,p.anchor,p.position)}else Rh(m,d,e);b:{var v=c.ea;v.dataset.adsbygoogleStatus="reserved";v.className+=" adsbygoogle-noablate";m={element:v};var t=b&&b.ab;if(v.hasAttribute("data-pub-vars")){try{t=JSON.parse(v.getAttribute("data-pub-vars"))}catch(y){break b}v.removeAttribute("data-pub-vars")}t&&(m.params=t);(f.adsbygoogle=f.adsbygoogle||[]).push(m)}}catch(y){(v=c.xa)&&v.parentNode&& 
(t=v.parentNode,t.removeChild(v),Qh(t)&&(t.style.display=t.getAttribute("data-init-display")||"none"));v=!1;break a}v=!0}return v?c:null}function Mk(a,b){return th(vh(Kj(b).map(Ih),c=>{Ij(a.g).exception=c}))}var Nk=class{constructor(a,b,c,d,e){this.g=a;this.J=b;this.j=c;this.B=e||null;(this.G=d)?(a=a.document,d=C(d,kj,5,A()),d=Mj(a,d)):d=Mj(a.document,[]);this.C=d;this.D=new sk;this.i=0;this.u=!1}};function Jk(a){const b={};a&&tc(a,6,Sb,A()).forEach(c=>{b[c]=!0});return b} 
function Kk(a,b){return a&&rc(a,Eh,4)&&b[Dh(B(a,Eh,4))]?!1:!0};var Ok=fd(class extends L{});function Pk(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?sh(()=>Ok(c)):ph(null)};function Qk(){if(Rk)return Rk;var a=qe()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?Rk=b:a.google_persistent_state_async=Rk=new Sk}function Tk(a){return Uk[a]||`google_ps_${a}`}function Vk(a,b,c){b=Tk(b);a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function Wk(a,b,c){return Vk(a,b,()=>c)}var Sk=class{constructor(){this.S={}}},Rk=null;const Uk={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function Xk(a){this.g=a||{cookie:""}} 
Xk.prototype.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.wc,g=c.xc||!1,f=c.domain||void 0,e=c.path||void 0,d=c.zb);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
Xk.prototype.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=ta(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};Xk.prototype.isEmpty=function(){return!this.g.cookie}; 
Xk.prototype.clear=function(){var a=(this.g.cookie||"").split(";");const b=[];var c=[];let d,e;for(let f=0;f<a.length;f++)e=ta(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(c=b.length-1;c>=0;c--)a=b[c],this.get(a),this.set(a,"",{zb:0,path:void 0,domain:void 0})};function Yk(a,b=window){if(F(a,5))try{return b.localStorage}catch{}return null};function Zk(a){var b=new $k;return qc(b,5,Ob(a))}var $k=class extends L{};function al(){this.B=this.B;this.i=this.i}al.prototype.B=!1;al.prototype.dispose=function(){this.B||(this.B=!0,this.D())};al.prototype[fa(Symbol,"dispose")]=function(){this.dispose()};function bl(a,b){a.B?b():(a.i||(a.i=[]),a.i.push(b))}al.prototype.D=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function cl(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function dl(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=cl(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(ke({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function el(a){if(a.g)return a.g;a:{let d=a.j;for(let e=0;e<50;++e){try{var b=!(!d.frames||!d.frames.__tcfapiLocator)}catch{b=!1}if(b){b=d;break a}b:{try{const f=d.parent;if(f&&f!=d){var c=f;break b}}catch{}c=null}if(!(d=c))break}b=null}a.g=b;return a.g}function fl(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):el(a)?(gl(a),e=++a.Y,a.C[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function gl(a){if(!a.u){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.C[d.callId](d.returnValue,d.success)}catch(e){}};a.u=b;ld(a.j,"message",b)}} 
var hl=class extends al{constructor(a){var b={};super();this.g=null;this.C={};this.Y=0;this.u=null;this.j=a;this.J=b.eb??500;this.G=b.ic??!1}D(){this.C={};this.u&&(md(this.j,"message",this.u),delete this.u);delete this.C;delete this.j;delete this.g;super.D()}addEventListener(a){let b={internalBlockOnErrors:this.G};const c=kd(()=>a(b));let d=0;this.J!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.J));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
cl(b),b.internalBlockOnErrors=this.G,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{fl(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&fl(this,"removeEventListener",null,a.listenerId)}};var ml=({l:a,aa:b,eb:c,ob:d,ha:e=!1,ia:f=!1})=>{b=il({l:a,aa:b,ha:e,ia:f});b.g!=null||b.i.message!="tcunav"?d(b):jl(a,c).then(g=>g.map(kl)).then(g=>g.map(h=>ll(a,h))).then(d)},il=({l:a,aa:b,ha:c=!1,ia:d=!1})=>{if(!nl({l:a,aa:b,ha:c,ia:d}))return ll(a,Zk(!0));b=Qk();return(b=Wk(b,24))?ll(a,kl(b)):rh(Error("tcunav"))}; 
function nl({l:a,aa:b,ha:c,ia:d}){if(d=!d)d=new hl(a),d=typeof d.j.__tcfapi==="function"||el(d)!=null;if(!d){if(c=!c){if(b){a=Pk(a);if(a.g!=null)if((a=a.getValue())&&E(a,1)!=null)b:switch(a=I(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else kk(806,a.i),a=!1;b=!a}c=b}d=c}return d?!0:!1}function jl(a,b){return Promise.race([ol(),pl(a,b)])} 
function ol(){return(new Promise(a=>{var b=Qk();a={resolve:a};const c=Wk(b,25,[]);c.push(a);b.S[Tk(25)]=c})).then(ql)}function pl(a,b){return new Promise(c=>{a.setTimeout(c,b,rh(Error("tcto")))})}function ql(a){return a?ph(a):rh(Error("tcnull"))} 
function kl(a){var b={};if(dl(a))if(a.gdprApplies===!1)a=!0;else if(a.tcString==="tcunavailable")a=!b.Va;else if((b.Va||a.gdprApplies!==void 0||b.jc)&&(b.Va||typeof a.tcString==="string"&&a.tcString.length)){b:{if(a.publisher&&a.publisher.restrictions&&(b=a.publisher.restrictions["1"],b!==void 0)){b=b["755"];break b}b=void 0}b===0?a=!1:a.purpose&&a.vendor?(b=a.vendor.consents,(b=!(!b||!b["755"]))&&a.purposeOneTreatment&&a.publisherCC==="CH"?a=!0:(b&&(a=a.purpose.consents,b=!(!a||!a["1"])),a=b)):a= 
!0}else a=!0;else a=!1;return Zk(a)}function ll(a,b){return(a=Yk(b,a))?ph(a):rh(Error("unav"))};var rl=class extends L{};var sl=class extends L{};var tl=class{constructor(a){this.exception=a}};function ul(a,b){try{var c=a.i,d=c.resolve,e=a.g;Ij(e.g);C(e.j,Ph,1,A());d.call(c,new tl(b))}catch(f){a.i.reject(f)}}var vl=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}start(){this.u()}u(){try{switch(this.j.document.readyState){case "complete":case "interactive":Ik(this.g,!0);ul(this);break;default:Ik(this.g,!1)?ul(this):this.j.setTimeout(qa(this.u,this),100)}}catch(a){ul(this,a)}}};var wl=class extends L{getVersion(){return G(this,2)}};function xl(a){return Ra(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function yl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function zl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function Al(a){var b=xl(a),c=yl(b.slice(0,6));a=yl(b.slice(6,12));var d=new wl;c=Uc(d,1,c);a=Uc(c,2,a);b=b.slice(12);c=yl(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let l=0;l<c;l++){if(e.length===0)throw Error(`Found ${l} of ${c} sections [${d}] but reached end of input [${b}]`);var f=yl(e[0])===0;e=e.slice(1);var g=Bl(e,b),h=d.length===0?0:d[d.length-1];h=zl(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=Bl(e,b);g=zl(f);for(let k=0;k<=g;k++)d.push(h+k);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return zc(a,3,d,Tb)}function Bl(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var Cl="a".charCodeAt(),Dl=vd(fh),El=vd(gh);function Fl(){var a=new Gl;return Vc(a,1,0)}function Hl(a){var b=Number;{var c=x(a.A,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Jb(64,c)):Qb(c)?d==="string"?Xb(c):$b(c):void 0}b=b(c??"0");a=G(a,2);return new Date(b*1E3+a/1E6)}var Gl=class extends L{};function Il(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function Jl(a){let b=Il(a,12);const c=[];for(;b--;){var d=!!Il(a,1)===!0,e=Il(a,16);if(d)for(d=Il(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c}function Kl(a,b,c){const d=[];for(let e=0;e<b;e++)if(Il(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d} 
function Ll(a){const b=Il(a,16);return!!Il(a,1)===!0?(a=Jl(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):Kl(a,b)}var Ml=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var Ol=(a,b)=>{try{var c=Ra(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new Ml(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=Il(d,12);c.cmpVersion=Il(d,12);d.skip(30);c.tcfPolicyVersion=Il(d,6);c.isServiceSpecific=!!Il(d,1);c.useNonStandardStacks=!!Il(d,1);c.specialFeatureOptins=Nl(Kl(d,12,El),El);c.purpose={consents:Nl(Kl(d,24,Dl),Dl),legitimateInterests:Nl(Kl(d,24,Dl),Dl)};c.purposeOneTreatment=!!Il(d,1);c.publisherCC=String.fromCharCode(Cl+Il(d,6))+String.fromCharCode(Cl+ 
Il(d,6));c.vendor={consents:Nl(Ll(d),null),legitimateInterests:Nl(Ll(d),null)};return c}catch(d){return null}};const Nl=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var Pl=class extends L{i(){return D(this,2)!=null}};var Ql=class extends L{i(){return D(this,2)!=null}};var Rl=class extends L{};var Sl=fd(class extends L{});function Tl(a){a=Ul(a);try{var b=a?Sl(a):null}catch(c){b=null}return b?B(b,Rl,4)||null:null}function Ul(a){a=(new Xk(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};vd(fh).map(a=>Number(a));vd(gh).map(a=>Number(a));function Vl(a){a.__tcfapiPostMessageReady||Wl(new Xl(a))} 
function Wl(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.l.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.l.addEventListener("message",a.g);a.l.__tcfapiPostMessageReady=!0}var Xl=class{constructor(a){this.l=a}};function Yl(a){a.__uspapiPostMessageReady||Zl(new $l(a))} 
function Zl(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.l.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.l.addEventListener("message",a.g);a.l.__uspapiPostMessageReady=!0} 
var $l=class{constructor(a){this.l=a;this.g=null}};var am=class extends L{};var bm=fd(class extends L{i(){return D(this,1)!=null}});function cm(a,b){function c(n){if(n.length<10)return null;var p=h(n.slice(0,4));p=l(p);n=h(n.slice(6,10));n=k(n);return"1"+p+n+"N"}function d(n){if(n.length<10)return null;var p=h(n.slice(0,6));p=l(p);n=h(n.slice(6,10));n=k(n);return"1"+p+n+"N"}function e(n){if(n.length<12)return null;var p=h(n.slice(0,6));p=l(p);n=h(n.slice(8,12));n=k(n);return"1"+p+n+"N"}function f(n){if(n.length<18)return null;var p=h(n.slice(0,8));p=l(p);n=h(n.slice(12,18));n=k(n);return"1"+p+n+"N"}function g(n){if(n.length<10)return null; 
var p=h(n.slice(0,6));p=l(p);n=h(n.slice(6,10));n=k(n);return"1"+p+n+"N"}function h(n){const p=[];let v=0;for(let t=0;t<n.length/2;t++)p.push(yl(n.slice(v,v+2))),v+=2;return p}function l(n){return n.every(p=>p===1)?"Y":"N"}function k(n){return n.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=xl(a[0]);const m=yl(a.slice(0,6));a=a.slice(6);if(m!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function dm(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Qd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function em(a){if(a!=null)return fm(a)}function fm(a){return Bb(a)?Number(a):String(a)};function gm(a){var b=R(wi);N!==N.top||N.__uspapi||N.frames.__uspapiLocator||(a=new hm(a,b),im(a),jm(a))}function im(a){!a.u||a.l.__uspapi||a.l.frames.__uspapiLocator||(a.l.__uspapiManager="fc",dm(a.l,"__uspapiLocator"),ra("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!F(a.j,3),d({version:1,uspString:b?"1---":a.u},!0))},a.l),Yl(a.l))} 
function jm(a){!a.tcString||a.l.__tcfapi||a.l.frames.__tcfapiLocator||(a.l.__tcfapiManager="fc",dm(a.l,"__tcfapiLocator"),a.l.__tcfapiEventListeners=a.l.__tcfapiEventListeners||[],ra("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.l.__tcfapiEventListeners;c=a.i&&!F(a.j,1);switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=Ol(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.l),Vl(a.l))} 
function km(a){if(!a?.i()||H(a,1).length===0||C(a,am,2,A()).length===0)return null;const b=H(a,1);let c;try{var d=Al(b.split("~")[0]);c=b.includes("~")?b.split("~").slice(1):[]}catch(e){return null}a=C(a,am,2,A()).reduce((e,f)=>{var g=lm(e);g=Nc(g,1)??pc;g=fm(g);var h=lm(f);h=Nc(h,1)??pc;return g>fm(h)?e:f});d=tc(d,3,Ub,A()).indexOf(G(a,1));return d===-1||d>=c.length?null:{uspString:cm(c[d],G(a,1)),wa:Hl(lm(a))}} 
function mm(a){a=a.find(b=>b&&I(b,1)===13);if(a?.i())try{return bm(H(a,2))}catch(b){}return null}function lm(a){return rc(a,Gl,2)?B(a,Gl,2):Fl()} 
var hm=class{constructor(a,b){var c=N;this.l=c;this.j=a;this.i=b;a=Ul(this.l.document);try{var d=a?Sl(a):null}catch(e){d=null}(a=d)?(d=B(a,Ql,5)||null,a=C(a,Pl,7,A()),a=mm(a??[]),d={Sa:d,Ua:a}):d={Sa:null,Ua:null};a=d;d=km(a.Ua);a=a.Sa;a?.i()&&H(a,2).length!==0?(b=rc(a,Gl,1)?B(a,Gl,1):Fl(),a={uspString:H(a,2),wa:Hl(b)}):a=null;this.u=a&&d?d.wa>a.wa?d.uspString:a.uspString:a?a.uspString:d?d.uspString:null;this.tcString=(d=Tl(c.document))&&D(d,1)!=null?H(d,1):null;this.g=(c=Tl(c.document))&&D(c,2)!= 
null?H(c,2):null}};const nm={google_ad_channel:!0,google_ad_host:!0};function om(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));jk("ama",b,.01)}function pm(a){const b={};Td(nm,(c,d)=>{d in a&&(b[d]=a[d])});return b};function qm(a){return a.replace(/(^\/)|(\/$)/g,"")}function rm(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function sm(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function tm(a){a=tc(a,2,Sb,A());if(!a)return!1;for(let b=0;b<a.length;b++)if(a[b]==1)return!0;return!1}function um(a,b){a=qm(sm(rm(a.location.pathname)));const c=Ud(a),d=vm(a);return b.find(e=>{if(rc(e,gj,7)){var f=B(e,gj,7);f=Vb(x(f.A,1))}else f=Vb(x(e.A,1));rc(e,gj,7)?(e=B(e,gj,7),e=E(e,2)):e=2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null}function vm(a){const b={};for(;;){b[Ud(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function W(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function wm(a){a=W(a);const b=a.space_collapsing||"none";return a.remove_ads_by_default?{Qa:!0,Kb:b,ta:a.ablation_viewport_offset}:null}function xm(a){a=W(a);a.had_ads_ablation=!0;a.remove_ads_by_default=!0;a.space_collapsing="slot";a.ablation_viewport_offset=1}function ym(a){W(N).allow_second_reactive_tag=a}function zm(){const a=W(window);a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function Am(a){return W(a)?.head_tag_slot_vars?.google_ad_host??Bm(a)}function Bm(a){return a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};const Cm=[2,7,1];function Dm(a,b,c,d="",e=null){return b===1&&e&&(Em(a,d,e)?.j()??!1)?!0:Fm(a,d,f=>La(C(f,gd,2,A()),g=>E(g,1)===b),c)}function Gm(a,b){const c=Od(N)||N;return Hm(c,a)?!0:Fm(N,"",d=>La(tc(d,3,Sb,A()),e=>e===a),b)}function Hm(a,b){a=(a=(a=a.location&&a.location.hash)&&a.match(/forced_clientside_labs=([\d,]+)/))&&a[1];return!!a&&Na(a.split(","),b.toString())} 
function Fm(a,b,c,d){a=Od(a)||a;const e=Im(a,d);b&&(b=ve(String(b)));return ud(e,(f,g)=>Object.prototype.hasOwnProperty.call(e,g)&&(!b||b===g)&&c(f))}function Im(a,b){a=Jm(a,b);const c={};Td(a,(d,e)=>{try{const f=dd(hd,jc(d));c[e]=f}catch(f){}});return c}function Jm(a,b){a=il({l:a,aa:b});return a.g!=null?Km(a.getValue()):{}} 
function Km(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:td(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}}function Lm(a,b){const c=[];a=Am(q)?Cm:(a=Em(q,a,b)?.C())?[...tc(a,3,Sb,A())]:Cm;a.includes(1)||c.push(1);a.includes(2)||c.push(2);a.includes(7)||c.push(7);return c} 
function Em(a,b,c){if(!b)return null;const d=Mm(c)?.D();a=Mm(c)?.i()?.i()===b&&a.location.host&&H(c,17)===a.location.host;return d===b||a?Mm(c):null};function Nm(a,b,c,d){Om(new Pm(a,b,c,d))}function Om(a){vh(uh(il({l:a.l,aa:F(a.g,6)}),b=>{Qm(a,b,!0)}),()=>{Rm(a)})}function Qm(a,b,c){vh(uh(Sm(b),d=>{Tm("ok");a.i(d,{fromLocalStorage:!0})}),()=>{var d=a.l;try{b.removeItem("google_ama_config")}catch(e){om(d,{lserr:1})}c?Rm(a):a.i(null,null)})}function Rm(a){vh(uh(Um(a),b=>{a.i(b,{fromPABGSettings:!0})}),()=>{Vm(a)})} 
function Sm(a){if(R(ii))var b=null;else try{b=a.getItem("google_ama_config")}catch(d){b=null}try{var c=b?sj(b):null}catch(d){c=null}return(a=(a=c)?(em(B(a,fj,3)?.i())??0)>Date.now()?a:null:null)?ph(a):rh(Error("invlocst"))}function Um(a){if(Am(a.l)&&!F(a.g,22))return rh(Error("invtag"));if(a=(a=Em(a.l,a.j,a.g)?.B())&&C(a,Ph,1,A()).length>0?a:null){var b=new rj;var c=C(a,Ph,1,A());b=Lc(b,1,c);a=C(a,mj,2,A());a=Lc(b,7,a);a=ph(a)}else a=rh(Error("invtag"));return a} 
function Vm(a){ml({l:a.l,aa:F(a.g,6),eb:50,ob:b=>{Wm(a,b)}})}function Wm(a,b){vh(uh(b,c=>{Qm(a,c,!1)}),c=>{Tm(c.message);a.i(null,null)})}function Tm(a){jk("abg::amalserr",{status:a,guarding:"true",timeout:50,rate:.01},.01)}class Pm{constructor(a,b,c,d){this.l=a;this.g=b;this.j=c;this.i=d}};function Xm(a,b,c,d){var e=Ym;try{const f=um(a,C(c,mj,7,A()));if(f&&tm(f)){if(D(f,4)??void 0){const h=new Gh(null,{google_package:D(f,4)??void 0});d=Fh(d,h)}const g=e(a,b,c,f,d);Dj(1E3,()=>{const h=new kh;(new vl(a,g,h)).start();return h.i},a).then(()=>{om(a,{atf:1})},h=>{(a.google_ama_state=a.google_ama_state||{}).exception=h;om(a,{atf:0})})}}catch(f){om(a,{atf:-1})}}function Ym(a,b,c,d,e){return new Nk(a,b,c,d,e)};function Zm(a){return a.length?a.join("~"):void 0};function $m(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=an(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function an(a){let b="";Td(a.split("_"),c=>{b+=c.substr(0,2)});return b};var bn=class extends L{};var cn=class extends L{i(){return H(this,3)}j(){return Oc(this,4)!=null}};var dn=class extends L{i(){return Hc(this,cn,1)}};function en(a){const b=new dn;var c=new cn;var d=G(a,1);c=Tc(c,1,d);d=G(a,18);c=Tc(c,2,d);d=H(a,2);c=Xc(c,3,d);d=F(a,6);c=qc(c,4,Ob(d));d=F(a,20);c=qc(c,5,Ob(d));d=F(a,9);c=qc(c,6,Ob(d));d=F(a,25);c=qc(c,7,Ob(d));d=H(a,8);c=Xc(c,8,d);d=H(a,3);c=Xc(c,9,d);a=B(a,bn,26);a=Jc(c,10,a);Jc(b,1,a);return b};function fn(){const a={};M(de).i(ei.g,ei.defaultValue)&&(a.bust=M(de).i(ei.g,ei.defaultValue));return a};class gn{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function hn(){const {promise:a,resolve:b}=new gn;return{promise:a,resolve:b}};function jn(a=()=>{}){q.google_llp||(q.google_llp={});const b=q.google_llp;let c=b[7];if(c)return c;c=hn();b[7]=c;a();return c}function kn(a){return jn(()=>{Pd(q.document,a)}).promise};Array.from({length:11},(a,b)=>b/10);function ln(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new mn;return a.google_reactive_ads_global_state} 
var mn=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new nn;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},nn=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};var on=a=>{if(q.google_apltlad||a.google_ad_intent_query)return null;var b=a.google_loader_used!=="sd"&&R(li)&&(q.top==q?0:Nd(q.top)?1:2)===1;if(q!==q.top&&!b||!a.google_ad_client)return null;q.google_apltlad=!0;b={enable_page_level_ads:{pltais:!0},google_ad_client:a.google_ad_client};const c=b.enable_page_level_ads;Td(a,(d,e)=>{bj[e]&&e!=="google_ad_client"&&(c[e]=d)});c.google_pgb_reactive=7;c.asro=R(qi);c.aihb=R(mi);c.aifxl=Zm(M(de).g(oi.g,oi.defaultValue));S(si)&&(c.aiapm=S(si));S(ti)&&(c.aiapmi= 
S(ti));S(ri)&&(c.aiact=S(ri));S(ni)&&(c.aicct=S(ni));S(ui)&&(c.ailct=S(ui));c.aiof=Zm(M(de).g(pi.g,pi.defaultValue));if("google_ad_section"in a||"google_ad_region"in a)c.google_ad_section=a.google_ad_section||a.google_ad_region;return b};function pn(a,b){W(N).ama_ran_on_page||Dj(1001,()=>{qn(new rn(a,b))},q)}function qn(a){Nm(a.l,a.i,a.g.google_ad_client||"",(b,c)=>{var d=a.l,e=a.g;W(N).ama_ran_on_page||b&&sn(d,e,b,c)})}class rn{constructor(a,b){this.l=q;this.g=a;this.i=b}} 
function sn(a,b,c,d){d&&(Ij(a).configSourceInAbg=d);rc(c,qj,24)&&(d=Jj(a),d.availableAbg=!0,d.ablationFromStorage=!!B(c,qj,24)?.i()?.i());if(ka(b.enable_page_level_ads)&&b.enable_page_level_ads.google_pgb_reactive===7){if(!um(a,C(c,mj,7,A()))){jk("amaait",{value:"true"});return}jk("amaait",{value:"false"})}W(N).ama_ran_on_page=!0;B(c,ej,15)?.i()&&(W(a).enable_overlap_observer=!0);B(c,qj,24)?.i()?.i()&&(Jj(a).ablatingThisPageview=!0,xm(a));ne(3,[w(c)]);const e=b.google_ad_client||"";b=pm(ka(b.enable_page_level_ads)? 
b.enable_page_level_ads:{});const f=Fh(Jh,new Gh(null,b));gk(782,()=>{Xm(a,e,c,f)})};function tn(a,b){a=a.document;for(var c=void 0,d=0;!c||a.getElementById(c+"_host");)c="aswift_"+d++;a=c;c=Number(b.google_ad_width||0);b=Number(b.google_ad_height||0);d=document.createElement("div");d.id=a+"_host";const e=d.style;e.border="none";e.height=`${b}px`;e.width=`${c}px`;e.margin="0px";e.padding="0px";e.position="relative";e.visibility="visible";e.backgroundColor="transparent";e.display="inline-block";return{vb:a,Nb:d}};function un({va:a,Ba:b}){return a||(b==="dev"?"dev":"")};function vn(a){return a.google_ad_client?String(a.google_ad_client):W(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??""};var wn={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function xn(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};var yn=class extends L{getVersion(){return H(this,2)}};function zn(a,b){return Xc(a,2,b)}function An(a,b){return Xc(a,3,b)}function Bn(a,b){return Xc(a,4,b)}function Cn(a,b){return Xc(a,5,b)}function Dn(a,b){return Xc(a,9,b)}function En(a,b){return Lc(a,10,b)}function Fn(a,b){return qc(a,11,Ob(b))}function Gn(a,b){return Xc(a,1,b)}function Hn(a,b){return qc(a,7,Ob(b))}var In=class extends L{};const Jn="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Kn(){var a=N;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Jn).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function Ln(a){return Fn(En(Cn(zn(Gn(Bn(Hn(Dn(An(new In,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new yn;c=Xc(c,1,b.brand);return Xc(c,2,b.version)})||[]),a.wow64||!1)}function Mn(){return Kn()?.then(a=>Ln(a))??null};function Nn(a,b){b.google_ad_host||(a=Bm(a))&&(b.google_ad_host=a)}function On(a,b,c=""){N.google_sa_queue||(N.google_sa_queue=[],N.google_process_slots=hk(215,()=>{Pn(N.google_sa_queue)}),a=Qn(c,a,b),Pd(N.document,a))}function Pn(a){const b=a.shift();typeof b==="function"&&gk(216,b);a.length&&q.setTimeout(hk(215,()=>{Pn(a)}),0)}function Rn(a,b){a.google_sa_queue=a.google_sa_queue||[];a.google_sa_impl?b():a.google_sa_queue.push(b)} 
function Qn(a,b,c){var d=N;b=F(c,4)?b.Gb:b.Hb;a:{if(F(c,4)){if(a=a||vn(d)){b:{try{for(;d;){if(d.location?.hostname){var e=d.location.hostname;break b}d=d.parent}}catch(f){}e=""}e={client:ve(a),plah:e};break a}throw Error("PublisherCodeNotFoundForAma");}e={}}e={...e,...fn()};d=S(xi);!F(c,4)&&[0,1].includes(d)&&(e.osttc=`${d}`);return Ld(b,new Map(Object.entries(e)))} 
function Sn(a,b,c,d){const {vb:e,Nb:f}=tn(a,b);c.appendChild(f);Tn(a,c,b);c=b.google_start_time??eh;const g=(new Date).getTime();b.google_lrv=un({va:eg(),Ba:H(d,2)});b.google_async_iframe_id=e;b.google_start_time=c;b.google_bpp=g>c?g-c:1;a.google_sv_map=a.google_sv_map||{};a.google_sv_map[e]=b;Rn(a,()=>{var h=f;if(!h||!h.isConnected)if(h=a.document.getElementById(String(b.google_async_iframe_id)+"_host"),h==null)throw Error("no_div");(h=a.google_sa_impl({pubWin:a,vars:b,innerInsElement:h}))&&ik(911, 
h)})} 
function Tn(a,b,c){var d=c.google_ad_output,e=c.google_ad_format,f=c.google_ad_width||0,g=c.google_ad_height||0;e||d!=="html"&&d!=null||(e=`${f}x${g}`);R(Bi)&&(c.google_reactive_ad_format===10?e="interstitial":c.google_reactive_ad_format===11&&(e="rewarded"));d=!c.google_ad_slot||c.google_override_format||!wn[c.google_ad_width+"x"+c.google_ad_height]&&c.google_loader_used==="aa";e=e&&d?e.toLowerCase():"";c.google_ad_format=e;if(typeof c.google_reactive_sra_index!=="number"||!c.google_ad_unit_key){e=[c.google_ad_slot, 
c.google_orig_ad_format||c.google_ad_format,c.google_ad_type,c.google_orig_ad_width||c.google_ad_width,c.google_orig_ad_height||c.google_ad_height];d=[];f=0;for(g=b;g&&f<25;g=g.parentNode,++f)g.nodeType===9?d.push(""):d.push(g.id);(d=d.join())&&e.push(d);c.google_ad_unit_key=Ud(e.join(":")).toString();e=[];for(d=0;b&&d<25;++d){f=(f=b.nodeType!==9&&b.id)?"/"+f:"";a:{if(b&&b.nodeName&&b.parentElement){g=b.nodeName.toString().toLowerCase();const h=b.parentElement.childNodes;let l=0;for(let k=0;k<h.length;++k){const m= 
h[k];if(m.nodeName&&m.nodeName.toString().toLowerCase()===g){if(b===m){g="."+l;break a}++l}}}g=""}e.push((b.nodeName&&b.nodeName.toString().toLowerCase())+f+g);b=b.parentElement}b=e.join()+":";e=[];if(a)try{let h=a.parent;for(d=0;h&&h!==a&&d<25;++d){const l=h.frames;for(f=0;f<l.length;++f)if(a===l[f]){e.push(f);break}a=h;h=a.parent}}catch(h){}c.google_ad_dom_fingerprint=Ud(b+e.join()).toString()}} 
function Un(){var a=Od(q);a&&(a=ln(a),a.tagSpecificState[1]||(a.tagSpecificState[1]={debugCard:null,debugCardRequested:!1}))}function Vn(){const a=Mn();a!=null&&a.then(b=>{N.google_user_agent_client_hint=JSON.stringify(w(b))});ce()};var Wn=class{constructor(a,b){this.g=a;this.u=b}height(){return this.u}i(a){return a>S(hi)&&this.u>300?this.g:Math.min(1200,Math.round(a))}j(){}};function Xn(a){return b=>!!(b.Z()&a)}var X=class extends Wn{constructor(a,b,c,d=!1){super(a,b);this.C=c;this.B=d}Z(){return this.C}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const Yn={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},Zn={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},$n={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function ao(a){var b=0;a.R&&b++;a.K&&b++;a.L&&b++;if(b<3)return{X:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.R.split(",");const c=a.L.split(",");a=a.K.split(",");if(b.length!==c.length||b.length!==a.length)return{X:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{X:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{L:d,K:e,Xa:b}} 
function bo(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function co(a,b,c,d){b=Math.floor(((a-8*b-8)/b*Yn[d]+Zn[d])*c+8*c+8);return a>1500?{width:0,height:0,Ib:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,Ib:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function eo(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*Yn.mobile_banner_image_sidebyside+Zn.mobile_banner_image_sidebyside)*b+8*b+8)}};const fo=Pa("script");var go=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,l=null,k=null,m=null,n=null){this.D=a;this.V=b;this.Z=c;this.g=d;this.G=e;this.F=f;this.P=g;this.u=h;this.B=l;this.i=k;this.j=m;this.C=n}size(){return this.V}};const ho=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var io=class extends Wn{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function jo(a,b){ko(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new go(9,new io(a,Math.floor(a*2.189)));if(R(Wh)){var c=rd();var d=S(Xh);var e=S(Vh),f=S(Uh);a<468?c?(a=eo(a,d),d={W:a.width,U:a.height,K:1,L:d,R:"mobile_banner_image_sidebyside"}):(a=co(a,1,d,"image_sidebyside"),d={W:a.width,U:a.height,K:1,L:d,R:"image_sidebyside"}):(d=bo(a),e===1&&(d.height=Math.floor(d.height*.5)),d={W:d.width,U:d.height,K:f,L:e,R:"image_stacked"})}else d=rd(),a<468?d?(d=eo(a,12),d={W:d.width, 
U:d.height,K:1,L:12,R:"mobile_banner_image_sidebyside"}):(d=bo(a),d={W:d.width,U:d.height,K:1,L:13,R:"image_sidebyside"}):(d=bo(a),d={W:d.width,U:d.height,K:4,L:2,R:"image_stacked"});lo(b,d);return new go(9,new io(d.W,d.U))} 
function mo(a,b){ko(a,b);{const f=ao({L:b.google_content_recommendation_rows_num,K:b.google_content_recommendation_columns_num,R:b.google_content_recommendation_ui_type});if(f.X)a={W:0,U:0,K:0,L:0,R:"image_stacked",X:f.X};else{var c=f.Xa.length===2&&a>=468?1:0;var d=f.Xa[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=$n[d];let g=f.K[c];for(;a/g<e&&g>1;)g--;e=g;c=f.L[c];a=co(a,e,c,d);a={W:a.width,U:a.height,K:e,L:c,R:d}}}if(a.X)throw new U(a.X);lo(b,a);return new go(9,new io(a.W,a.U))} 
function ko(a,b){if(a<=0)throw new U(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function lo(a,b){a.google_content_recommendation_ui_type=b.R;a.google_content_recommendation_columns_num=b.K;a.google_content_recommendation_rows_num=b.L};var no=class extends Wn{i(){return this.g}j(a,b,c){Ti(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const oo={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var po=class extends Wn{i(){return Math.min(1200,this.g)}}; 
function qo(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=Oi(b,c,g,S($h),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=T(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const l=g.parentElement.childNodes;for(let k=0;k<l.length;++k){const m=l[k];if(m!==g&&Ri(b,m))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}Ti(b,c)}else a=g;else a= 
g}if(a<250)throw new U("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new U("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new go(11,new Wn(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new U(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new U(`Invalid height: height=${f}`);if(f<50)throw new U("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new U("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new go(11,new Wn(a,f))}d=oo[f];if(!d)throw new U("Invalid data-ad-layout value: "+f);c=Zi(c,b);b=T(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25);return new go(11, 
f==="in-article"?new po(a,b):new Wn(a,b))};function ro(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function so(a,b){var c=to.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var Z=[new X(970,90,2),new X(728,90,2),new X(468,60,2),new X(336,280,1),new X(320,100,2),new X(320,50,2),new X(300,600,4),new X(300,250,1),new X(250,250,1),new X(234,60,2),new X(200,200,1),new X(180,150,1),new X(160,600,4),new X(125,125,1),new X(120,600,4),new X(120,240,4),new X(120,120,1,!0)],to=[Z[6],Z[12],Z[3],Z[0],Z[7],Z[14],Z[1],Z[8],Z[10],Z[4],Z[15],Z[2],Z[11],Z[5],Z[13],Z[9],Z[16]];function uo(a,b,c,d,e){e.google_full_width_responsive==="false"?c={H:a,F:1}:b==="autorelaxed"&&e.google_full_width_responsive||vo(b)||e.google_ad_resize?(b=Pi(a,c,d,e),c=b!==!0?{H:a,F:b}:{H:T(c)||a,F:!0}):c={H:a,F:2};const {H:f,F:g}=c;return g!==!0?{H:a,F:g}:d.parentElement?{H:f,F:g}:{H:a,F:g}} 
function wo(a,b,c,d,e){const {H:f,F:g}=gk(247,()=>uo(a,b,c,d,e));var h=g===!0;const l=Yd(d.style.width),k=Yd(d.style.height),{V:m,P:n,Z:p,Wa:v}=xo(f,b,c,d,e,h);h=yo(b,p);var t;const y=(t=Ui(d,c,"marginLeft"))?`${t}px`:"",J=(t=Ui(d,c,"marginRight"))?`${t}px`:"";t=Wi(d,c)||"";return new go(h,m,p,null,v,g,n,y,J,k,l,t)}function vo(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function xo(a,b,c,d,e,f){b=zo(c,a,b);let g;var h=!1;let l=!1;var k=T(c)<488;if(k){g=Ji(d,c);var m=Zi(d,c);h=!m&&g;l=m&&g}m=[Xi(a),Xn(b)];R(fi)||m.push(Yi(k,c,d,l));e.google_max_responsive_height!=null&&m.push($i(e.google_max_responsive_height));k=[t=>!t.B];if(h||l)h=aj(c,d),k.push($i(h));const n=so(ro(m),ro(k));if(!n)throw new U(`No slot size for availableWidth=${a}`);const {V:p,P:v}=gk(248,()=>{var t;a:if(f){if(e.gfwrnh&&(t=Yd(e.gfwrnh))){t={V:new no(a,t),P:!0};break a}t=S(bi);t=t>0?a/t:a/1.2;if(e.google_resizing_allowed|| 
e.google_full_width_responsive==="true")var y=Infinity;else{y=d;let K=Infinity;do{var J=Ui(y,c,"height");J&&(K=Math.min(K,J));(J=Ui(y,c,"maxHeight"))&&(K=Math.min(K,J))}while(y.parentElement&&(y=y.parentElement)&&y.tagName!=="HTML");y=K}!(R(di)&&y<=t*2)&&(y=Math.min(t,y),y<t*.5||y<100)&&(y=t);t={V:new no(a,Math.floor(y)),P:y<t?102:!0}}else t={V:n,P:100};return t});return e.google_ad_layout==="in-article"&&Ao(c)?{V:Bo(a,c,d,p,e),P:!1,Z:b,Wa:g}:{V:p,P:v,Z:b,Wa:g}} 
function yo(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function zo(a,b,c){if(c==="auto")c=Math.min(1200,T(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Gi)c.indexOf(d)!==-1&&(b|=Gi[d])}return b}function Bo(a,b,c,d,e){const f=e.google_ad_height||Ui(c,b,"height");b=qo(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new X(b.g,b.height(),1):d} 
function Ao(a){return R(Th)||a.location&&a.location.hash==="#hffwroe2etoq"};function Co(a,b,c,d,e){var f;(f=T(b))?T(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,Ti(b,c),f={H:f,F:!0}):f={H:a,F:5}:f={H:a,F:4}:f={H:a,F:10};const {H:g,F:h}=f;if(h!==!0||a===g)return new go(12,new Wn(a,d),null,null,!0,h,100);const {V:l,P:k,Z:m}=xo(g,"auto",b,c,e,!0);return new go(1,l,m,2,!0,h,k)};function Do(a){const b=a.google_ad_format;if(b==="autorelaxed"){a:{if(a.google_content_recommendation_ui_type!=="pedestal")for(const c of ho)if(a[c]!=null){a=!0;break a}a=!1}return a?9:5}if(vo(b))return 1;if(b==="link")return 4;if(b==="fluid")return a.google_ad_layout==="in-article"?(Eo(a),1):8;if(a.google_reactive_ad_format===27)return Eo(a),1} 
function Fo(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&Ui(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=Go(a,f,b,c,d))?e:wo(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.Z!=null&&(c.google_responsive_formats=e.Z);e.G!=null&&(c.google_safe_for_responsive_override=e.G);e.F!=null&&(e.F===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.F));e.P!=null&&e.P!==!0&&(c.gfwrnher=e.P);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.D;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.F===!0&&(c.gfwrnh=`${e.size().height()}px`);e.u!=null&&(c.gfwroml=e.u);e.B!=null&&(c.gfwromr=e.B);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.C!=null&&(c.gfwroz=e.C);f=Od(window)||window;$m(f.location,"google_responsive_dummy_ad")&&(Na([1,2,3,4,5,6,7,8],e.D)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${fo}>window.top.postMessage('${f}', '*'); 
          </${fo}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function Go(a,b,c,d,e){const f=d.google_ad_height||Ui(c,e,"height")||0;switch(a){case 5:const {H:g,F:h}=gk(247,()=>uo(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&Ti(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return jo(g,d);case 9:return mo(b,d);case 8:return qo(b,e,c,f,d);case 10:return Co(b,e,c,f,d)}}function Eo(a){a.google_ad_format="auto";a.armr=3};let Ho=void 0;function Io(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};function Jo(a,b){var c=Od(b);if(c){c=T(c);const d=Rd(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function Ko(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}} 
function Lo(a,b){if(a.getAttribute("src")){var c=a.getAttribute("src")||"";const d=Jd(c,"client");d&&(b.google_ad_client=Ko("google_ad_client",d));(c=Jd(c,"host"))&&(b.google_ad_host=Ko("google_ad_host",c))}for(const d of a.attributes)/data-/.test(d.name)&&(a=ta(d.name.replace("data-matched-content","google_content_recommendation").replace("data","google").replace(/-/g,"_")),b.hasOwnProperty(a)||(c=Ko(a,d.value),c!==null&&(b[a]=c)))} 
function Mo(a,b){if(a=pe(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?17:12} 
function No(a,b,c,d){Lo(a,b);if(c.document&&c.document.body&&!Do(b)&&!b.google_reactive_ad_format&&!b.google_ad_intent_query){var e=parseInt(a.style.width,10),f=Jo(a,c);if(f>0&&e>f){var g=parseInt(a.style.height,10);e=!!wn[e+"x"+g];var h=f;if(e){const l=xn(f,g);if(l)h=l,b.google_ad_format=l+"x"+g+"_0ads_al";else throw new U("No slot size for availableWidth="+f);}b.google_ad_resize=!0;b.google_ad_width=h;e||(b.google_ad_format=null,b.google_override_format=!0);f=h;a.style.width=`${f}px`;Io(b,4)}}if(R(Yh)|| 
T(c)<488){f=Od(c)||c;g=a.offsetWidth||Ui(a,c,"width")||b.google_ad_width||0;e=b.google_ad_client;(h=$m(f.location,"google_responsive_slot_preview"))||(pb(Ho,sb),h=Dm(f,1,Ho,e,d));if(d=h)b:if(b.google_reactive_ad_format||b.google_ad_resize||Do(b)||Li(a,b))d=!1;else{for(d=a;d;d=d.parentElement){f=Rd(d,c);if(!f){b.gfwrnwer=18;d=!1;break b}if(!Na(["static","relative"],f.position)){b.gfwrnwer=17;d=!1;break b}}if(!R(gi)&&(d=S(ai),d=Oi(c,a,g,d,b),d!==!0)){b.gfwrnwer=d;d=!1;break b}d=c===c.top?!0:!1}d?(Io(b, 
1),d=!0):d=!1}else d=!1;if(g=Do(b))Fo(g,a,b,c,d);else{if(Li(a,b)){if(d=Rd(a,c))a.style.width=d.width,a.style.height=d.height,Ki(d,b);b.google_ad_width||(b.google_ad_width=a.offsetWidth);b.google_ad_height||(b.google_ad_height=a.offsetHeight);b.google_loader_features_used=256;b.google_responsive_auto_format=Mo(c,b)}else Ki(a.style,b);c.location&&c.location.hash==="#gfwmrp"||b.google_responsive_auto_format===12&&b.google_full_width_responsive==="true"?Fo(10,a,b,c,!1):Math.random()<.01&&b.google_responsive_auto_format=== 
12&&(a=Pi(a.offsetWidth||parseInt(a.style.width,10)||b.google_ad_width,c,a,b),a!==!0?(b.efwr=!1,b.gfwrnwer=a):b.efwr=!0)}};function Oo(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&Nd(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function Po(a,b,c){for(const f of b)a:{b=a;var d=f,e=c;for(let g=0;g<b.g.length;g++){if(b.g[g].element.contains(d)){b.g[g].labels.add(e);break a}if(d.contains(b.g[g].element)){b.g[g].element=d;b.g[g].labels.add(e);break a}}b.g.push({element:d,labels:new Set([e])})}}class Qo{constructor(){this.g=[]}getSlots(){return this.g}} 
function Ro(a){const b=xk(a),c=new Qo;Po(c,b.ib,1);Po(c,b.jb,2);Po(c,b.sb,3);Po(c,b.Mb,4);Po(c,b.kb,5);Po(c,b.Bb,6);return c.getSlots().map(d=>{var e=new tf;var f=[...d.labels];e=zc(e,1,f,Rb);d=d.element.getBoundingClientRect();f=new sf;f=Tc(f,1,d.left+a.scrollX);f=Tc(f,2,d.top+a.scrollY);f=Tc(f,3,d.width);d=Tc(f,4,d.height);d=cd(d);e=Jc(e,2,d);return cd(e)}).sort((d,e)=>{d=B(d,sf,2);d=G(d,2);e=B(e,sf,2);e=G(e,2);return d-e})};function tg(a,b,c=0){a.g.size>0||So(a);c=Math.min(Math.max(0,c),9);const d=a.g.get(c);d?d.push(b):a.g.set(c,[b])}function To(a,b,c,d){ld(b,c,d);bl(a,()=>md(b,c,d))}function Uo(a,b){a.j!==1&&(a.j=1,a.g.size>0&&Vo(a,b))} 
function So(a){a.l.document.visibilityState?To(a,a.l.document,"visibilitychange",b=>{a.l.document.visibilityState==="hidden"&&Uo(a,b);a.l.document.visibilityState==="visible"&&(a.j=0)}):"onpagehide"in a.l?(To(a,a.l,"pagehide",b=>{Uo(a,b)}),To(a,a.l,"pageshow",()=>{a.j=0})):To(a,a.l,"beforeunload",b=>{Uo(a,b)})}function Vo(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var Wo=class extends al{constructor(a){super();this.l=a;this.j=0;this.g=new Map}};async function Xo(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function Yo(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=fe(a.l)}function Zo(a){const b=a.g.wpc;return b!==null&&b!==""?b:a.g.wpc=vn(a.l)}function $o(a,b){var c=new If;var d=Yo(a);c=Vc(c,1,d);d=Zo(a);c=Yc(c,2,d);c=Vc(c,3,a.g.sd);return Vc(c,7,Math.round(b||a.l.performance.now()))}async function ap(a){ia(await ia(Xo(a.l,()=>!(!Yo(a)||!Zo(a)))))}function bp(a){var b=M(cp);if(b.j){var c=b.B;a(c);b.g.psi=w(c)}} 
function dp(a){tg(a.u,()=>{var b=$o(a);b=Kc(b,12,Jf,a.C);a.j&&!a.g.le.includes(3)&&(a.g.le.push(3),pg(a.i,b))},9)}function ep(a){const b=new Ef;tg(a.u,()=>{Jc(b,2,a.B);Vc(b,3,a.g.tar);var c=a.l;var d=new xf;var e=Ro(c);d=Lc(d,1,e);e=cd(vf(uf(new wf,T(c)),Ii(c).clientHeight));d=Jc(d,2,e);c=cd(vf(uf(new wf,Ii(c).scrollWidth),Ii(c).scrollHeight));c=Jc(d,3,c);c=cd(c);Jc(b,4,c);c=a.i;d=$o(a);d=Kc(d,8,Jf,b);pg(c,d)},9)} 
async function fp(a){var b=M(cp);if(b.j&&!b.g.le.includes(1)){b.g.le.push(1);var c=b.l.performance.now();ia(await ia(ap(b)));var d=new Af;a=Ac(d,5,Ob(a),!1);d=vf(uf(new wf,Ii(b.l).scrollWidth),Ii(b.l).scrollHeight);a=Jc(a,2,d);d=vf(uf(new wf,T(b.l)),Ii(b.l).clientHeight);a=Jc(a,1,d);for(var e=d=b.l;d&&d!=d.parent;)d=d.parent,Nd(d)&&(e=d);a=Yc(a,4,e.location.href);d=Oo(b.l);d!==0&&(e=new zf,d=$c(e,1,d),Jc(a,3,d));d=b.i;c=$o(b,c);c=Kc(c,4,Jf,a);pg(d,c);dp(b);ep(b)}} 
async function gp(a,b,c){if(a.j&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.l.performance.now();ia(await ia(ap(a)));var e=a.i;a=$o(a,d);d=new yf;b=ad(d,1,b);c=zc(b,2,c,Tb);c=Kc(a,9,Jf,c);pg(e,c)}}async function hp(a,b){ia(await ia(ap(a)));var c=a.i;a=$o(a);a=Vc(a,3,1);b=Kc(a,10,Jf,b);pg(c,b)} 
var cp=class{constructor(a,b){this.l=qe()||window;this.u=b??new Wo(this.l);this.i=a??new vg(2,eg(),100,100,!0,this.u);this.g=Vk(Qk(),33,()=>{const c=S(Zh);return{sd:c,ssp:c>0&&Sd()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get j(){return this.g.ssp}get B(){return gk(1178,()=>dd(Df,jc(this.g.psi||[])))||new Df}get C(){return gk(1227,()=>dd(Ff,jc(this.g.cc||[])))||new Ff}};function ip(){var a=window;return q.google_adtest==="on"||q.google_adbreak_test==="on"||a.location.host.endsWith("h5games.usercontent.goog")||a.location.host==="gamesnacks.com"?a.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(b=>Math.floor(Number(b))).filter(b=>!isNaN(b)&&b>0)||[]:[]};function jp(a,b){return a instanceof HTMLScriptElement&&b.test(a.src)?0:1}function kp(a){var b=N.document;if(b.currentScript)return jp(b.currentScript,a);for(const c of b.scripts)if(jp(c,a)===0)return 0;return 1};function lp(a,b){return{[3]:{[55]:()=>a===0,[23]:c=>Dm(N,Number(c),F(b,6)),[24]:c=>Gm(Number(c),F(b,6)),[61]:()=>F(b,6),[63]:()=>F(b,6)||H(b,8)===".google.ch"},[4]:{},[5]:{[6]:()=>H(b,15)}}};function mp(a=q){return a.ggeac||(a.ggeac={})};function np(a,b=document){return!!b.featurePolicy?.features().includes(a)}function op(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function pp(a,b=navigator){try{return!!b.protectedAudience?.queryFeatureSupport?.(a)}catch(c){return!1}};function qp(a,b){try{const d=a.split(".");a=q;let e=0,f;for(;a!=null&&e<d.length;e++)f=a,a=a[d[e]],typeof a==="function"&&(a=f[d[e]]());var c=a;if(typeof c===b)return c}catch{}} 
var rp={[3]:{[8]:a=>{try{return ja(a)!=null}catch{}},[9]:a=>{try{var b=ja(a)}catch{return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},[10]:()=>window===window.top,[6]:a=>Na(M($g).g(),Number(a)),[27]:a=>{a=qp(a,"boolean");return a!==void 0?a:void 0},[60]:a=>{try{return!!q.document.querySelector(a)}catch{}},[80]:a=>{try{return!!q.matchMedia(a).matches}catch{}},[69]:a=>np(a,q.document),[70]:a=>op(a,q.document),[79]:a=>pp(a, 
q.navigator)},[4]:{[3]:()=>Zd(),[6]:a=>{a=qp(a,"number");return a!==void 0?a:void 0}},[5]:{[2]:()=>window.location.href,[3]:()=>{try{return window.top.location.hash}catch{return""}},[4]:a=>{a=qp(a,"string");return a!==void 0?a:void 0},[12]:a=>{try{const b=qp(a,"string");if(b!==void 0)return atob(b)}catch(b){}}}};var sp=class extends L{getId(){return G(this,1)}};function tp(a){return C(a,sp,2,A())}var up=class extends L{};var vp=class extends L{};var wp=class extends L{i(){return Nc(this,2)??pc}j(){return Nc(this,4)??pc}u(){return F(this,3)}};var xp=class extends L{};function yp(a){return zp({[0]:new Map,[1]:new Map,[2]:new Map},a)} 
function zp(a,b){const c=new Map;for(const [f,g]of a[1].entries()){var d=f,e=g;const {bb:h,Ya:l,Za:k}=e[e.length-1];c.set(d,h+l*k)}for(const f of b)for(const g of C(f,up,2,A()))if(tp(g).length!==0){b=Vb(x(g.A,8))??0;!I(g,4)||I(g,13)||I(g,14)||(b=c.get(I(g,4))??0,d=(Vb(x(g.A,1))??0)*tp(g).length,c.set(I(g,4),b+d));d=[];for(e=0;e<tp(g).length;e++){const h={bb:b,Ya:Vb(x(g.A,1))??0,Za:tp(g).length,Ab:e,fa:I(f,1),oa:g,T:tp(g)[e]};d.push(h)}Ap(a[2],I(g,10),d)||Ap(a[1],I(g,4),d)||Ap(a[0],tp(g)[0].getId(), 
d)}return a}function Ap(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a.get(b).push(...c);return!0};function Bp(a=Sd()){return b=>Ud(`${b} + ${a}`)%1E3};const Cp=[12,13,20];function Dp(a,b){var c=M(Bg).N;const d=gf(B(b.oa,$e,3),c);if(!d.success)return zg(a.M,B(b.oa,$e,3),b.fa,b.T.getId(),d),!1;if(!d.value)return!1;c=gf(B(b.T,$e,3),c);return c.success?c.value?!0:!1:(zg(a.M,B(b.T,$e,3),b.fa,b.T.getId(),c),!1)}function Ep(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];a.includes(b)||a.push(b)} 
function Fp(a,b,c,d){const e=[];var f;if(f=b!==9)a.u[b]?f=!0:(a.u[b]=!0,f=!1);if(f)return xg(a.M,b,c,e,[],4),e;f=Cp.includes(b);const g=[],h=[];for(const n of[0,1,2])for(const [p,v]of a.ja[n].entries()){var l=p,k=v;const t=new Of;var m=k.filter(y=>y.fa===b&&a.i[y.T.getId()]&&Dp(a,y));if(m.length)for(const y of m)h.push(y.T);else if(!a.ya&&(n===2?(m=d[1],bd(t,2,Pf,l)):m=d[0],l=m?.(String(l))??(n===2&&I(k[0].oa,11)===1?void 0:d[0](String(l))),l!==void 0)){for(const y of k){if(y.fa!==b)continue;k=l- 
y.bb;m=y.Ya;const J=y.Za,K=y.Ab;k<0||k>=m*J||k%J!==K||!Dp(a,y)||(k=I(y.oa,13),k!==0&&k!==void 0&&(m=a.j[String(k)],m!==void 0&&m!==y.T.getId()?yg(a.M,a.j[String(k)],y.T.getId(),k):a.j[String(k)]=y.T.getId()),h.push(y.T))}Fc(t,Pf)!==0&&(Uc(t,3,l),g.push(t))}}for(const n of h)d=n.getId(),e.push(d),Ep(a,d,f?4:c),Rg(C(n,lf,2,A()),f?Tg():[c],a.M,d);xg(a.M,b,c,e,g,1);return e}function Gp(a,b){b=b.map(c=>new vp(c)).filter(c=>!Cp.includes(I(c,1)));a.ja=zp(a.ja,b)} 
function Hp(a,b){O(1,c=>{a.i[c]=!0},b);O(2,(c,d,e)=>Fp(a,c,d,e),b);O(3,c=>(a.g[c]||[]).concat(a.g[4]),b);O(12,c=>void Gp(a,c),b);O(16,(c,d)=>void Ep(a,c,d),b)}var Ip=class{constructor(a,b,c,{ya:d=!1,yc:e=[]}={}){this.ja=a;this.M=c;this.u={};this.ya=d;this.g={[b]:[],[4]:[]};this.i={};this.j={};if(a=Fe()){a=a.split(",")||[];for(const f of a)(a=Number(f))&&(this.i[a]=!0)}for(const f of e)this.i[f]=!0}};function Jp(a,b){a.g=Vg(14,b,()=>{})}class Kp{constructor(){this.g=()=>{}}}function Lp(a){M(Kp).g(a)};function Mp({tb:a,N:b,config:c,mb:d=mp(),nb:e=0,M:f=new Ag(em(B(a,wp,5)?.i())??0,em(B(a,wp,5)?.j())??0,B(a,wp,5)?.u()??!1),ja:g=yp(C(a,vp,2,A(ob)))}){d.hasOwnProperty("init-done")?(Vg(12,d,()=>{})(C(a,vp,2,A()).map(h=>w(h))),Vg(13,d,()=>{})(C(a,lf,1,A()).map(h=>w(h)),e),b&&Vg(14,d,()=>{})(b),Np(e,d)):(Hp(new Ip(g,e,f,c),d),Wg(d),Xg(d),Yg(d),Np(e,d),Rg(C(a,lf,1,A(ob)),[e],f,void 0,!0),Cg=Cg||!(!c||!c.yb),Lp(rp),b&&Lp(b))}function Np(a,b=mp()){Zg(M($g),b,a);Op(b,a);Jp(M(Kp),b);M(de).C()} 
function Op(a,b){const c=M(de);c.j=(d,e)=>Vg(5,a,()=>!1)(d,e,b);c.u=(d,e)=>Vg(6,a,()=>0)(d,e,b);c.i=(d,e)=>Vg(7,a,()=>"")(d,e,b);c.B=(d,e)=>Vg(8,a,()=>[])(d,e,b);c.g=(d,e)=>Vg(17,a,()=>[])(d,e,b);c.C=()=>{Vg(15,a,()=>{})(b)}};function Pp(a,b){b={[0]:Bp(fe(b).toString())};b=M($g).u(a,b);a=gp(M(cp),a,b);ch.na(1085,a)}function Qp(a,b,c){var d=W(a);if(d.plle)Np(1,mp(a));else{d.plle=!0;d=B(b,xp,12);var e=F(b,9);Mp({tb:d,N:lp(c,b),config:{ya:e&&!!a.google_disable_experiments,yb:e},mb:mp(a),nb:1});if(c=H(b,15))c=Number(c),M($g).j(c);for(const f of tc(b,19,Ub,A()))M($g).i(f);Pp(12,a);Pp(10,a);a=Od(a)||a;$m(a.location,"google_mc_lab")&&M($g).i(44738307)}};function Rp(a){ck.B(b=>{b.shv=String(a);b.mjsv=un({va:eg(),Ba:a});const c=M($g).g(),d=ip();b.eid=c.concat(d).join(",")})}function Sp(a,b){const c=b?.i();b=c?.i()||H(a,2);a=c?.j()?F(c,4):F(a,6);Rp(b);pb(Ho,vb);Ho=a};var Tp={google_pause_ad_requests:!0,google_user_agent_client_hint:!0};var Up=class extends L{i(){return H(this,1)}j(){return I(this,2)}};var Vp=class extends L{D(){return H(this,1)}i(){return B(this,Up,2)}j(){return F(this,3)}u(){return F(this,4)}B(){return B(this,rl,5)}C(){return B(this,sl,6)}};function Mm(a){return Sc(a,Vp,27,Wp)}var Xp=class extends L{},Wp=[27,28];function Yp(a){var b=ck;try{if(pb(a,ub),a.length>0)return new Xp(JSON.parse(a))}catch(c){b.I(838,c instanceof Error?c:Error(String(c)))}return new Xp};function Zp(a,b){if(F(b,22))return 7;if(F(b,16))return 6;const c=Mm(b)?.i()?.i();b=Mm(b)?.i()?.j()??0;a=c===a;switch(b){case 1:return a?9:8;case 2:return a?11:10;case 3:return a?13:12}return 1}function $p(a,b,c){b.google_loader_used!=="sd"&&(b.abgtt=Zp(a,c))};function aq(a,b){var c=new bq;try{const f=a.createElement("link");if(f.relList?.supports("compression-dictionary")&&Fa()){var d=f;if(b instanceof Ad)d.href=Cd(b).toString(),d.rel="compression-dictionary";else{if(Fd.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var e=Dd.test(b)?b:void 0;e!==void 0&&(d.href=e,d.rel="compression-dictionary")}a.head.appendChild(f)}}catch(f){c.ka({methodName:1296,ua:f})}} 
function cq(a){return Kd`https://googleads.g.doubleclick.net/pagead/managed/dict/${a}/adsbygoogle`};var bq=class{constructor(){this.g=ck}ka(a){const b=a.ua;this.g.I(a.methodName??0,b instanceof Error?b:Error(String(b)))}};function dq(a){ld(window,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="sc-cnf"||a(c,b)})};function eq(a,b){return b==null?`&${a}=null`:`&${a}=${Math.floor(b)}`}function fq(a,b){return`&${a}=${b.toFixed(3)}`}function gq(){const a=new Set,b=wk();try{if(!b)return a;const c=b.pubads();for(const d of c.getSlots())a.add(d.getSlotId().getDomId())}catch{}return a}function hq(a){a=a.id;return a!=null&&(gq().has(a)||a.startsWith("google_ads_iframe_")||a.startsWith("aswift"))} 
function iq(a,b,c){if(!a.sources)return!1;switch(jq(a)){case 2:const d=kq(a);if(d)return c.some(f=>lq(d,f));break;case 1:const e=mq(a);if(e)return b.some(f=>lq(e,f))}return!1}function jq(a){if(!a.sources)return 0;a=a.sources.filter(b=>b.previousRect&&b.currentRect);if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function mq(a){return nq(a,b=>b.currentRect)}function kq(a){return nq(a,b=>b.previousRect)} 
function nq(a,b){return a.sources.reduce((c,d)=>{d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function lq(a,b){const c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50} 
function oq(){const a=Array.from(document.getElementsByTagName("iframe")).filter(hq),b=[...gq()].map(c=>document.getElementById(c)).filter(c=>c!==null);pq=window.scrollX;qq=window.scrollY;return rq=[...a,...b].map(c=>c.getBoundingClientRect())} 
function sq(){var a=new tq;if(R(zi)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.gb.qb&&b.push("event");for(const c of b)b={type:c,buffered:!0},c==="event"&&(b.durationThreshold=40),uq(a).observe(b);vq(a)}}} 
function wq(a,b){const c=pq!==window.scrollX||qq!==window.scrollY?[]:rq,d=oq();for(const e of b.getEntries())switch(b=e.entryType,b){case "layout-shift":xq(a,e,c,d);break;case "largest-contentful-paint":b=e;a.Ia=Math.floor(b.renderTime||b.loadTime);a.Ha=b.size;break;case "first-input":b=e;a.Ea=Number((b.processingStart-b.startTime).toFixed(3));a.Fa=!0;a.g.some(f=>f.entries.some(g=>e.duration===g.duration&&e.startTime===g.startTime))||yq(a,e);break;case "longtask":b=Math.max(0,e.duration-50);a.C+= 
b;a.J=Math.max(a.J,b);a.ra+=1;break;case "event":yq(a,e);break;default:Ib(b,void 0)}}function uq(a){a.M||(a.M=new PerformanceObserver(Cj(640,b=>{wq(a,b)})));return a.M} 
function vq(a){const b=Cj(641,()=>{var d=document;(d.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""]??0)===2&&zq(a)}),c=Cj(641,()=>void zq(a));document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.Da=()=>{document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);uq(a).disconnect()}} 
function zq(a){if(!a.La){a.La=!0;uq(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=fq("cls",a.D),b+=fq("mls",a.Y),b+=eq("nls",a.qa),window.LayoutShiftAttribution&&(b+=fq("cas",a.B),b+=eq("nas",a.Ka),b+=fq("was",a.Pa)),b+=fq("wls",a.sa),b+=fq("tls",a.Oa));window.LargestContentfulPaint&&(b+=eq("lcp",a.Ia),b+=eq("lcps",a.Ha));window.PerformanceEventTiming&&a.Fa&&(b+=eq("fid",a.Ea));window.PerformanceLongTaskTiming&&(b+=eq("cbt",a.C), 
b+=eq("mbt",a.J),b+=eq("nlt",a.ra));let d=0;for(var c of document.getElementsByTagName("iframe"))hq(c)&&d++;b+=eq("nif",d);b+=eq("ifi",ue(window));c=M($g).g();b+=`&${"eid"}=${encodeURIComponent(c.join())}`;b+=`&${"top"}=${q===q.top?1:0}`;b+=a.Na?`&${"qqid"}=${encodeURIComponent(a.Na)}`:eq("pvsid",fe(q));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.M?a.Ga:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=eq("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include", 
redirect:"follow",method:"get",mode:"no-cors"});a.Da()}}function xq(a,b,c,d){if(!b.hadRecentInput){a.D+=Number(b.value);Number(b.value)>a.Y&&(a.Y=Number(b.value));a.qa+=1;if(c=iq(b,c,d))a.B+=b.value,a.Ka++;if(b.startTime-a.Ja>5E3||b.startTime-a.Ma>1E3)a.Ja=b.startTime,a.i=0,a.j=0;a.Ma=b.startTime;a.i+=b.value;c&&(a.j+=b.value);a.i>a.sa&&(a.sa=a.i,a.Pa=a.j,a.Oa=b.startTime+b.duration)}} 
function yq(a,b){Aq(a,b);const c=a.g[a.g.length-1],d=a.G[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(d.entries.push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.G[b.id]=b,a.g.push(b)),a.g.sort((e,f)=>f.latency-e.latency),a.g.splice(10).forEach(e=>{delete a.G[e.id]})}function Aq(a,b){b.interactionId&&(a.pa=Math.min(a.pa,b.interactionId),a.u=Math.max(a.u,b.interactionId),a.Ga=a.u?(a.u-a.pa)/7+1:0)} 
var tq=class{constructor(){this.j=this.i=this.qa=this.Y=this.D=0;this.Ma=this.Ja=Number.NEGATIVE_INFINITY;this.g=[];this.G={};this.Ga=0;this.pa=Infinity;this.Ea=this.Ha=this.Ia=this.Ka=this.Pa=this.B=this.Oa=this.sa=this.u=0;this.Fa=!1;this.ra=this.J=this.C=0;this.M=null;this.La=!1;this.Da=()=>{};const a=document.querySelector("[data-google-query-id]");this.Na=a?a.getAttribute("data-google-query-id"):null;this.gb={qb:!0}}},pq,qq,rq=[];let Bq=null;const Cq=[],Dq=new Map;let Eq=-1;function Fq(a){return cj.test(a.className)&&a.dataset.adsbygoogleStatus!=="done"}function Gq(a,b,c){a.dataset.adsbygoogleStatus="done";Hq(a,b,c)} 
function Hq(a,b,c){var d=window,e=b.google_reactive_ads_config;e||No(a,b,d,c);Nn(d,b);if(!Iq(a,b,d)){if(e){e=e.page_level_pubvars||{};if(W(N).page_contains_reactive_tag&&!W(N).allow_second_reactive_tag){if(e.pltais){ym(!1);return}throw new U("Only one 'enable_page_level_ads' allowed per page.");}W(N).page_contains_reactive_tag=!0;ym(e.google_pgb_reactive===7)}b.google_unique_id=te(d);Td(Tp,(f,g)=>{b[g]=b[g]||d[g]});b.google_loader_used!=="sd"&&(b.google_loader_used="aa");b.google_reactive_tag_first= 
(W(N).first_tag_on_page||0)===1;gk(164,()=>{Sn(d,b,a,c)})}} 
function Iq(a,b,c){var d=b.google_reactive_ads_config,e=typeof a.className==="string"&&RegExp("(\\W|^)adsbygoogle-noablate(\\W|$)").test(a.className),f=wm(c);if(f&&f.Qa&&b.google_adtest!=="on"&&!e){e=Mi(a,c);const g=Ii(c).clientHeight;e=g===0?null:e/g;if(!f.ta||f.ta&&(e||0)>=f.ta)return a.className+=" adsbygoogle-ablated-ad-slot",c=c.google_sv_map=c.google_sv_map||{},d=la(a),b.google_element_uid=d,c[b.google_element_uid]=b,a.setAttribute("google_element_uid",String(d)),f.Kb==="slot"&&(Xd(a.getAttribute("width"))!== 
null&&a.setAttribute("width","0"),Xd(a.getAttribute("height"))!==null&&a.setAttribute("height","0"),a.style.width="0px",a.style.height="0px"),!0}if((f=Rd(a,c))&&f.display==="none"&&!(b.google_adtest==="on"||b.google_reactive_ad_format>0||d))return c.document.createComment&&a.appendChild(c.document.createComment("No ad requested because of display:none on the adsbygoogle tag")),!0;a=b.google_pgb_reactive==null||b.google_pgb_reactive===3;return b.google_reactive_ad_format!==1&&b.google_reactive_ad_format!== 
8||!a?!1:(q.console&&q.console.warn("Adsbygoogle tag with data-reactive-ad-format="+String(b.google_reactive_ad_format)+" is deprecated. Check out page-level ads at https://www.google.com/adsense"),!0)}function Jq(a){var b=document.getElementsByTagName("INS");for(let d=0,e=b[d];d<b.length;e=b[++d]){var c=e;if(Fq(c)&&c.dataset.adsbygoogleStatus!=="reserved"&&(!a||e.id===a))return e}return null} 
function Kq(a,b,c){if(a&&"shift"in a){bp(e=>{var f=Cf(e);Qc(f,2)||(e=Cf(e),Wc(e,2))});for(var d=20;a.length>0&&d>0;){try{Lq(a.shift(),b,c)}catch(e){setTimeout(()=>{throw e;})}--d}}}function Mq(){const a=Qd("INS");a.className="adsbygoogle";a.className+=" adsbygoogle-noablate";$d(a);return a} 
function Nq(a,b){const c={},d=Lm(a.google_ad_client,b);Td(Hi,(g,h)=>{a.enable_page_level_ads===!1?c[h]=!1:a.hasOwnProperty(h)?c[h]=a[h]:d.includes(g)&&(c[h]=!1)});ka(a.enable_page_level_ads)&&(c.page_level_pubvars=a.enable_page_level_ads);const e=Mq();ge.body.appendChild(e);const f={google_reactive_ads_config:c,google_ad_client:a.google_ad_client};f.google_pause_ad_requests=!!W(N).pause_ad_requests;$p(Oq(a)||vn(N),f,b);Gq(e,f,b);bp(g=>{var h=Cf(g);Qc(h,6)||(g=Cf(g),Wc(g,6))})} 
function Pq(a,b){ln(q).wasPlaTagProcessed=!0;const c=()=>{Nq(a,b)},d=q.document;if(d.body||d.readyState==="complete"||d.readyState==="interactive")Nq(a,b);else{const e=kd(hk(191,c));ld(d,"DOMContentLoaded",e);q.MutationObserver!=null&&(new q.MutationObserver((f,g)=>{d.body&&(e(),g.disconnect())})).observe(d,{childList:!0,subtree:!0})}} 
function Lq(a,b,c){const d={};gk(165,()=>{Qq(a,d,b,c)},e=>{e.client=e.client||d.google_ad_client||a.google_ad_client;e.slotname=e.slotname||d.google_ad_slot;e.tag_origin=e.tag_origin||d.google_tag_origin})}function Rq(a){delete a.google_checked_head;Td(a,(b,c)=>{bj[c]||(delete a[c],b=c.replace("google","data").replace(/_/g,"-"),q.console.warn(`AdSense head tag doesn't support ${b} attribute.`))})} 
function Sq(a,b){var c=N.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||N.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||N.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||N.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])');if(c){c.setAttribute("data-checked-head", 
"true");var d=W(window);if(d.head_tag_slot_vars)Tq(c);else{bp(g=>{g=Cf(g);Ac(g,7,Ob(!0),!1)});var e={};Lo(c,e);Rq(e);e.google_ad_intent_query&&(e.google_responsive_auto_format=17,e.google_reactive_ad_format=42);var f=wd(e);d.head_tag_slot_vars=f;c={google_ad_client:e.google_ad_client,enable_page_level_ads:e};e.google_ad_intent_query&&(c.enable_ad_intent_display_ads=!0);e.google_overlays==="bottom"&&(c.overlays={bottom:!0});delete e.google_overlays;N.adsbygoogle||(N.adsbygoogle=[]);d=N.adsbygoogle; 
d.loaded?d.push(c):d.splice&&d.splice(0,0,c);b=Mm(b)?.u();e.google_adbreak_test||b?Uq(f,a):dq(()=>{Uq(f,a)})}}}function Tq(a){const b=W(window).head_tag_slot_vars,c=a.getAttribute("src")||"";if((a=Jd(c,"client")||a.getAttribute("data-ad-client")||"")&&a!==b.google_ad_client)throw new U("Warning: Do not add multiple property codes with AdSense tag to avoid seeing unexpected behavior. These codes were found on the page "+a+", "+b.google_ad_client);} 
function Vq(a){if(typeof a==="object"&&a!=null){if(typeof a.type==="string")return 2;if(typeof a.sound==="string"||typeof a.preloadAdBreaks==="string"||typeof a.h5AdsConfig==="object")return 3}return 0} 
function Qq(a,b,c,d){if(a==null)throw new U("push() called with no parameters.");bp(f=>{var g=Cf(f);Qc(g,3)||(f=Cf(f),Wc(f,3))});var e=Vq(a);if(e!==0)if(d=zm(),d.first_slotcar_request_processing_time||(d.first_slotcar_request_processing_time=Date.now(),d.adsbygoogle_execution_start_time=eh),Bq==null)Wq(a),Cq.push(a);else if(e===3){const f=Bq;gk(787,()=>{f.handleAdConfig(a)})}else ik(730,Bq.handleAdBreak(a));else{eh=(new Date).getTime();On(c,d,Oq(a));Xq();a:{if(!a.enable_ad_intent_display_ads&&a.enable_page_level_ads!= 
void 0){if(typeof a.google_ad_client==="string"){e=!0;break a}throw new U("'google_ad_client' is missing from the tag config.");}e=!1}if(e)bp(f=>{var g=Cf(f);Qc(g,4)||(f=Cf(f),Wc(f,4))}),Yq(a,d);else if((e=a.params)&&Td(e,(f,g)=>{b[g]=f}),b.google_ad_output==="js")console.warn("Ads with google_ad_output='js' have been deprecated and no longer work. Contact your AdSense account manager or switch to standard AdSense ads.");else{$p(Oq(a)||vn(N),b,d);e=Zq(b,a);Lo(e,b);c=W(q).head_tag_slot_vars||{};Td(c, 
(f,g)=>{b.hasOwnProperty(g)||(b[g]=f)});if(e.hasAttribute("data-require-head")&&!W(q).head_tag_slot_vars)throw new U("AdSense head tag is missing. AdSense body tags don't work without the head tag. You can copy the head tag from your account on https://adsense.com.");if(!b.google_ad_client)throw new U("Ad client is missing from the slot.");if(c=(W(N).first_tag_on_page||0)===0&&on(b))bp(f=>{var g=Cf(f);Qc(g,5)||(f=Cf(f),Wc(f,5))}),$q(c);(W(N).first_tag_on_page||0)===0&&(W(N).first_tag_on_page=2);b.google_pause_ad_requests= 
!!W(N).pause_ad_requests;Gq(e,b,d)}}}function Oq(a){return a.google_ad_client?a.google_ad_client:(a=a.params)&&a.google_ad_client?a.google_ad_client:""}function Xq(){if(R(ki)){const a=wm(N);a&&a.Qa||xm(N)}}function $q(a){he(()=>{ln(q).wasPlaTagProcessed||q.adsbygoogle&&q.adsbygoogle.push(a)})}function Yq(a,b){(W(N).first_tag_on_page||0)===0&&(W(N).first_tag_on_page=1);if(a.tag_partner){var c=a.tag_partner;const d=W(q);d.tag_partners=d.tag_partners||[];d.tag_partners.push(c)}pn(a,b);Pq(a,b)} 
function Zq(a,b){if(a.google_ad_format==="rewarded"){if(a.google_ad_slot==null)throw new U("Rewarded format does not have valid ad slot");if(a.google_ad_loaded_callback==null)throw new U("Rewarded format does not have ad loaded callback");a.google_reactive_ad_format=11;a.google_wrap_fullscreen_ad=!0;a.google_video_play_muted=!1;a.google_acr=a.google_ad_loaded_callback;delete a.google_ad_loaded_callback;delete a.google_ad_format}var c=!!a.google_wrap_fullscreen_ad;if(c)b=Mq(),b.dataset.adsbygoogleStatus= 
"reserved",ge.documentElement.appendChild(b);else if(b=b.element){if(!Fq(b)&&(b.id?b=Jq(b.id):b=null,!b))throw new U("'element' has already been filled.");if(!("innerHTML"in b))throw new U("'element' is not a good DOM element.");}else if(b=Jq(),!b)throw new U("All 'ins' elements in the DOM with class=adsbygoogle already have ads in them.");if(c){c=N;try{const e=(c||window).document,f=e.compatMode=="CSS1Compat"?e.documentElement:e.body;var d=(new oe(f.clientWidth,f.clientHeight)).round()}catch(e){d= 
new oe(-12245933,-12245933)}a.google_ad_height=d.height;a.google_ad_width=d.width;a.fsapi=!0}return b}function ar(a){Qk().S[Tk(26)]=!!Number(a)} 
function br(a){Number(a)?W(N).pause_ad_requests=!0:(W(N).pause_ad_requests=!1,a=()=>{if(!W(N).pause_ad_requests){var b={};let c;typeof window.CustomEvent==="function"?c=new CustomEvent("adsbygoogle-pub-unpause-ad-requests-event",b):(c=document.createEvent("CustomEvent"),c.initCustomEvent("adsbygoogle-pub-unpause-ad-requests-event",!!b.bubbles,!!b.cancelable,b.detail));N.dispatchEvent(c)}},q.setTimeout(a,0),q.setTimeout(a,1E3))} 
function cr(a){a&&a.call&&typeof a==="function"&&window.setTimeout(a,0)}function Uq(a,b){const c={...fn()},d=S(yi);[0,1].includes(d)&&(c.osttc=`${d}`);b=kn(Ld(b.Jb,new Map(Object.entries(c)))).then(e=>{Bq==null&&(e.init(a),Bq=e,dr(e))});ik(723,b);b.finally(()=>{Cq.length=0;jk("slotcar",{event:"api_ld",time:Date.now()-eh,time_pr:Date.now()-Eq});hp(M(cp),Gf(23))})} 
function dr(a){for(const [c,d]of Dq){var b=c;const e=d;e!==-1&&(q.clearTimeout(e),Dq.delete(b))}for(b=0;b<Cq.length;b++){if(Dq.has(b))continue;const c=Cq[b],d=Vq(c);gk(723,()=>{d===3?a.handleAdConfig(c):d===2&&ik(730,a.handleAdBreakBeforeReady(c))})}} 
function Wq(a){var b=Cq.length;if(Vq(a)===2&&a.type==="preroll"&&a.adBreakDone!=null){var c=a.adBreakDone;Eq===-1&&(Eq=Date.now());var d=q.setTimeout(()=>{try{c({breakType:"preroll",breakName:a.name,breakFormat:"preroll",breakStatus:"timeout"}),Dq.set(b,-1),jk("slotcar",{event:"pr_to",source:"adsbygoogle"}),hp(M(cp),Gf(22))}catch(e){console.error("[Ad Placement API] adBreakDone callback threw an error:",e instanceof Error?e:Error(String(e)))}},S(Ai)*1E3);Dq.set(b,d)}};(function(a,b,c,d=()=>{}){ck.J(lk);gk(166,()=>{const e=new vg(2,a);try{Xa(m=>{Uj(e,1191,m)})}catch(m){}const f=Yp(b);var g=en(f);Sp(f,g);d();ne(16,[1,w(f)]);var h=qe(pe(N))||N,l=un({va:a,Ba:H(f,2)});const k=c(l,f);l=N.document.currentScript===null?1:kp(k.Lb);Qp(h,f,l);R(vi)&&H(f,29)&&aq(h.document,cq(H(f,29)));bp(m=>{var n=G(m,1)+1;Uc(m,1,n);N.top===N&&(n=G(m,2)+1,Uc(m,2,n));n=Cf(m);Qc(n,1)||(m=Cf(m),Wc(m,1))});ik(1086,fp(l===0));if(!Ea()||ua(Ha(),11)>=0){ek(R(Ci));Vn();gm(Gc(f,bn,26));try{sq()}catch{}Un(); 
Sq(k,f);h=window;l=h.adsbygoogle;if(!l||!l.loaded){g={push:m=>{Lq(m,k,f)},loaded:!0,pageState:JSON.stringify(w(g))};try{Object.defineProperty(g,"requestNonPersonalizedAds",{set:ar}),Object.defineProperty(g,"pauseAdRequests",{set:br}),Object.defineProperty(g,"onload",{set:cr})}catch{}if(l)for(const m of["requestNonPersonalizedAds","pauseAdRequests"])l[m]!==void 0&&(g[m]=l[m]);Kq(l,k,f);h.adsbygoogle=g;l&&(g.onload=l.onload)}}})})(eg(),typeof sttc==="undefined"?void 0:sttc,function(a,b){b=G(b,1)>2012? 
`_fy${G(b,1)}`:"";Kd`data:text/javascript,//show_ads_impl_preview.js`;return{Jb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${b}.js`,Hb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl${b}.js`,Gb:Kd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl_with_ama${b}.js`,Lb:/^(?:https?:)?\/\/(?:pagead2\.googlesyndication\.com|securepubads\.g\.doubleclick\.net)\/pagead\/(?:js\/)?(?:show_ads|adsbygoogle(_direct)?)\.js(?:[?#].*)?$/}}); 
}).call(this,"[2021,\"r20250421\",\"r20190131\",null,null,null,null,null,null,null,null,[[[698926295,null,null,[1]],[null,619278254,null,[null,10]],[676894296,null,null,[1]],[1332,null,null,[1]],[682658313,null,null,[1]],[null,1130,null,[null,100]],[null,1340,null,[null,0.2]],[null,1338,null,[null,0.3]],[null,1336,null,[null,1.2]],[null,1339,null,[null,0.3]],[null,1032,null,[null,200],[[[12,null,null,null,4,null,\"Android\",[\"navigator.userAgent\"]],[null,500]]]],[null,728201648,null,[null,100]],[null,1224,null,[null,0.01]],[null,1346,null,[null,6]],[null,1347,null,[null,3]],[null,1343,null,[null,300]],[1384,null,null,[]],[null,1263,null,[null,-1]],[null,1323,null,[null,-1]],[null,1265,null,[null,-1]],[null,1264,null,[null,-1]],[1267,null,null,[1]],[null,66,null,[null,-1]],[null,65,null,[null,-1]],[1241,null,null,[1]],[1300,null,null,[1]],[null,null,null,[null,null,null,[\"en\",\"de\",\"fr\",\"es\",\"ja\"]],null,1273],[null,null,null,[null,null,null,[\"44786015\",\"44786016\"]],null,1261],[1318,null,null,[1]],[1372,null,null,[1]],[45682913,null,null,[1]],[622128248,null,null,[]],[null,null,589752731,[null,null,\"#FFFFFF\"]],[null,null,589752730,[null,null,\"#1A73E8\"]],[null,null,null,[null,null,null,[\"29_18\",\"30_19\"]],null,null,null,635821288],[null,null,716722045,[null,null,\"600px\"]],[636570127,null,null,[1]],[null,626062006,null,[null,670]],[null,666400580,null,[null,22]],[null,null,null,[null,null,null,[\"\",\"ar\",\"bn\",\"en\",\"es\",\"fr\",\"hi\",\"id\",\"ja\",\"ko\",\"mr\",\"pt\",\"ru\",\"sr\",\"te\",\"th\",\"tr\",\"uk\",\"vi\",\"zh\"]],null,712458671],[null,null,null,[],null,null,null,683929765],[713244099,null,null,[1]],[655991266,null,null,[1]],[741488656,null,null,[1]],[null,717888910,null,[null,0.7]],[null,643258048,null,[null,0.15]],[null,643258049,null,[null,0.33938]],[null,618163195,null,[null,15000]],[null,624950166,null,[null,15000]],[null,623405755,null,[null,300]],[null,508040914,null,[null,500]],[null,547455356,null,[null,49]],[null,717888912,null,[null,0.7]],[null,727864505,null,[null,3]],[null,652486359,null,[null,3]],[null,688905693,null,[null,2]],[null,650548030,null,[null,2]],[null,650548032,null,[null,300]],[null,650548031,null,[null,1]],[null,655966487,null,[null,300]],[null,655966486,null,[null,250]],[null,687270738,null,[null,500]],[null,469675170,null,[null,60000]],[675298507,null,null,[]],[711741274,null,null,[]],[null,684147713,null,[null,-1]],[null,684147711,null,[null,-1]],[null,684147712,null,[null,-1]],[45675667,null,null,[1]],[570863962,null,null,[1]],[null,null,570879859,[null,null,\"control_1\\\\.\\\\d\"]],[null,570863961,null,[null,50]],[570879858,null,null,[1]],[743173402,null,null,[1]],[10024,null,null,[1]],[10020,null,null,[1]],[null,1085,null,[null,5]],[null,63,null,[null,30]],[null,1080,null,[null,5]],[null,10019,null,[null,5]],[null,1027,null,[null,10]],[null,57,null,[null,120]],[null,1079,null,[null,5]],[null,1050,null,[null,30]],[null,58,null,[null,120]],[null,10021,null,[null,2]],[715572365,null,null,[1]],[715572366,null,null,[1]],[555237685,null,null,[1]],[45460956,null,null,[]],[45414947,null,null,[1]],[null,472785970,null,[null,500]],[null,732217386,null,[null,10000]],[null,732217387,null,[null,500]],[null,733329086,null,[null,30000]],[null,629808663,null,[null,100]],[null,736623795,null,[null,250]],[null,745376892,null,[null,1]],[null,745376893,null,[null,2]],[null,550718588,null,[null,250]],[716210739,null,null,[1]],[null,624290870,null,[null,50]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[10,[[50,[[31067422],[31067423,[[null,1032,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31083552],[44776368]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31084127],[31084128]]],[1,[[31089421],[31089422,[[676460084,null,null,[1]]]]],null,139,null,null,null,998,null,null,null,null,null,8],[1,[[31089423],[31089424]],[4,null,61],139,null,null,null,998,null,null,null,null,null,8],[1,[[31089628],[31089629,[[710737579,null,null,[1]]]]]],[50,[[31091333],[31091334,[[736254284,null,null,[1]]]]]],[1000,[[31091901,[[null,null,14,[null,null,\"31091901\"]]],[6,null,null,null,6,null,\"31091901\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31091902,[[null,null,14,[null,null,\"31091902\"]]],[6,null,null,null,6,null,\"31091902\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[10,[[31091911],[31091912,[[748263873,null,null,[1]]]]]],[1,[[42531513],[42531514,[[316,null,null,[1]]]]]],[1,[[42531644],[42531645,[[368,null,null,[1]]]],[42531646,[[369,null,null,[1]],[368,null,null,[1]]]]]],[50,[[42531705],[42531706]]],[1,[[42532242],[42532243,[[1256,null,null,[1]],[290,null,null,[1]]]]]],[50,[[42532523],[42532524,[[1300,null,null,[]]]]]],[null,[[42532525],[42532526]]],[1,[[44719338],[44719339,[[334,null,null,[1]],[null,54,null,[null,100]],[null,66,null,[null,10]],[null,65,null,[null,1000]]]]]],[1,[[44801778],[44801779,[[506914611,null,null,[1]]]]],[4,null,55],143],[50,[[95330276],[95330278,[[null,1336,null,[null,1]]]]]],[50,[[95331832],[95331833,[[1342,null,null,[1]]]]]],[10,[[95332584],[95332585,[[null,1343,null,[null,600]]]],[95332586,[[null,1343,null,[null,900]]]],[95332587,[[null,1343,null,[null,1200]]]]]],[10,[[95332589],[95332590,[[1344,null,null,[1]]]]]],[10,[[95332923],[95332924,[[null,1338,null,[null,0.8]]]],[95332925,[[null,1339,null,[null,0.8]]]],[95332926,[[null,1340,null,[null,0.8]]]],[95332927,[[null,1340,null,[null,0.8]],[null,1338,null,[null,0.8]],[null,1339,null,[null,0.8]]]]]],[10,[[95333409],[95333410,[[null,1346,null,[null,-1]],[null,1347,null,[null,-1]]]],[95333411,[[null,1346,null,[null,3]],[null,1347,null,[null,1]]]],[95333412,[[null,1346,null,[null,8]],[null,1347,null,[null,5]]]]]],[360,[[95334516,[[null,null,null,[null,null,null,[\"95334518\"]],null,null,null,630330362]]],[95334517,[[626390500,null,null,[1]],[null,null,null,[null,null,null,[\"95334519\"]],null,null,null,630330362]]]],[2,[[4,null,55],[12,null,null,null,2,null,\"buzzfun\\\\.me\/|diggfun\\\\.co\/|indiaimagine\\\\.com\/\"]]],143],[50,[[95344787,[[null,null,null,[null,null,null,[\"95344792\"]],null,null,null,630330362]]],[95344788,[[566279275,null,null,[1]],[622128248,null,null,[1]],[null,null,null,[null,null,null,[\"95344793\"]],null,null,null,630330362]]],[95344789,[[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344794\"]],null,null,null,630330362]]],[95344790,[[566279275,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344795\"]],null,null,null,630330362]]],[95344791,[[566279275,null,null,[1]],[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344796\"]],null,null,null,630330362]]]],[4,null,55],143],[1,[[95345037],[95345038,[[1377,null,null,[1]]]]],[4,null,55]],[null,[[95348881],[95348882,[[45650867,null,null,[1]]]]],null,130,null,null,null,null,null,null,null,null,null,7],[10,[[95352051,[[null,null,null,[null,null,null,[\"95352054\"]],null,null,null,630330362]]],[95352052,[[null,643258048,null,[]],[null,null,null,[null,null,null,[\"95352055\"]],null,null,null,630330362]]],[95352053,[[null,643258048,null,[]],[null,643258049,null,[]],[null,null,null,[null,null,null,[\"95352056\"]],null,null,null,630330362]]]],[4,null,55],144],[50,[[95353386],[95353387,[[675298507,null,null,[1]]]]]],[100,[[95353420],[95355501,[[10018,null,null,[1]],[null,10021,null,[null,1.5]]]]]],[50,[[95353450],[95353451,[[10017,null,null,[1]]]]]],[100,[[95354562],[95354563,[[1382,null,null,[1]]]]],[4,null,55]],[100,[[95354564],[95354565]],[4,null,55]],[50,[[95355310],[95355311,[[1134,null,null,[1]]]]]],[200,[[95357460,[[null,null,null,[null,null,null,[\"95357462\"]],null,null,null,630330362]]],[95357461,[[45683445,null,null,[1]],[null,null,null,[null,null,null,[\"95357463\"]],null,null,null,630330362]]]],[4,null,55]],[485,[[95357877,[[null,null,null,[null,null,null,[\"95357879\"]],null,null,null,630330362]]],[95357878,[[null,643258049,null,[null,0.16]],[null,null,null,[null,null,null,[\"95357880\"]],null,null,null,630330362]]]],[4,null,55],144]]],[17,[[10,[[31084487],[31084488]],null,null,null,null,32,null,null,142,1],[10,[[31089209],[31089210]],null,null,null,null,39,null,null,189,1],[96,[[31090357]],[2,[[4,null,55],[7,null,null,15,null,20250512]]],null,null,null,null,null,null,194,1],[896,[[31090358,null,[4,null,71,null,null,null,null,[\"194\",\"14\"]]]],[2,[[4,null,55],[7,null,null,15,null,20250512]]],null,null,null,null,96,null,194,1],[10,[[31090956],[31090957,[[733329085,null,null,[1]]]]],null,null,null,null,null,500,null,198,1],[50,[[31091194],[31091198,[[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,200,null,200,1],[10,[[31091205],[31091206,[[732217385,null,null,[1]]]],[31091638,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]]]],[31091871,[[732217385,null,null,[1]],[745631622,null,null,[1]]]],[31091872,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]],[745631622,null,null,[1]]]]],null,null,null,null,null,700,null,198,1],[10,[[31091243],[31091244,[[736623794,null,null,[1]]]],[31091873,[[736623794,null,null,[1]],[null,745376892,null,[null,2]],[null,745376893,null,[null,4]]]]],null,null,null,null,null,800,null,198,1],[1,[[95356409],[95356410,[[737425145,null,null,[1]]]]],[2,[[6,null,null,3,null,2]]],null,null,null,null,null,null,209,1],[50,[[95356661,[[null,null,null,[null,null,null,[\"95356661\",\"95356663\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356663\"]],null,null,null,630330362]]],[95356662,[[655991266,null,null,[]],[null,null,null,[null,null,null,[\"95356662\",\"95356664\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356664\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,203],[50,[[95356797,[[null,652486359,null,[null,9]],[null,null,null,[null,null,null,[\"95356799\"]],null,null,null,630330362]]],[95356798,[[null,652486359,null,[null,11]],[null,null,null,[null,null,null,[\"95356800\"]],null,null,null,630330362]]],[95356807,[[null,null,null,[null,null,null,[\"95356810\"]],null,null,null,630330362]]],[95356808,[[null,652486359,null,[null,5]],[null,null,null,[null,null,null,[\"95356811\"]],null,null,null,630330362]]],[95356809,[[null,652486359,null,[null,7]],[null,null,null,[null,null,null,[\"95356812\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,204,1],[500,[[95357715,[[null,null,null,[null,null,null,[\"95357715\",\"95357717\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95357717\"]],null,null,null,630330362]]],[95357716,[[742688665,null,null,[1]],[null,null,null,[null,null,null,[\"95357716\",\"95357718\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95357718\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,208],[10,[[95358121,[[null,null,null,[null,null,null,[\"95358121\",\"95358123\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95358123\"]],null,null,null,630330362]]],[95358122,[[null,null,716722045,[null,null,\"max(100px, calc(\\u003cDH\\u003e - 102px))\"]],[741481545,null,null,[1]],[null,null,null,[null,null,null,[\"95358122\",\"95358124\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95358124\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,210]]],[11,[[50,[[31091503],[31091504]],null,122,null,null,null,null,null,null,null,null,null,4]]]],null,null,[null,1000,1,1000]],null,null,null,null,\"fauxid.com\",1202648213,[95357427],null,null,null,null,null,null,[0,0,0],[null,[\"ca-pub-0583047843012866\",1],1,null,[[[[null,0,null,null,null,null,\"DIV.main-content\\u003eDIV.core-msg.spacer.font-red\"],1,[\"32px\",\"32px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV.main-content\"],4,[\"10px\",\"128px\",1],[2],null,null,null,1]],[[null,[1,3,2],null,\"6246676918\",null,null,[0,2],null,null,[0.5,null,1]]]],[null,null,[1,2,7]]],null,\"m202504170101\"]");
