gapi.loaded_2(function(_){var window=this;
_.Ps=function(a){return"rtl"==_.Bs(a,"direction")};_.Qs=function(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d};_.g=_.Qs.prototype;_.g.clone=function(){return new _.Qs(this.left,this.top,this.width,this.height)};_.g.intersects=function(a){return this.left<=a.left+a.width&&a.left<=this.left+this.width&&this.top<=a.top+a.height&&a.top<=this.top+this.height};
_.g.contains=function(a){return a instanceof _.os?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};_.g.distance=function(a){var b=a.x<this.left?this.left-a.x:Math.max(a.x-(this.left+this.width),0);a=a.y<this.top?this.top-a.y:Math.max(a.y-(this.top+this.height),0);return Math.sqrt(b*b+a*a)};_.g.getSize=function(){return new _.rd(this.width,this.height)};
_.g.getCenter=function(){return new _.os(this.left+this.width/2,this.top+this.height/2)};_.g.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.g.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.g.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.top+=a.y):(this.left+=a,typeof b==="number"&&(this.top+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};_.Rs=function(a){return _.Bs(a,"position")};
_.Ss=function(a,b,c){if(b instanceof _.os){var d=b.x;b=b.y}else d=b,b=c;a.style.left=_.Is(d,!1);a.style.top=_.Is(b,!1)};_.Ts=function(a,b){a=a.style;"opacity"in a?a.opacity=b:"MozOpacity"in a?a.MozOpacity=b:"filter"in a&&(a.filter=b===""?"":"alpha(opacity="+Number(b)*100+")")};_.Us=function(){if(_.Fd){var a=/Windows NT ([0-9.]+)/;return(a=a.exec(_.Jc()))?a[1]:"0"}return _.Ed?(a=/1[0|1][_.][0-9_.]+/,(a=a.exec(_.Jc()))?a[0].replace(/_/g,"."):"10"):_.Id?(a=/Android\s+([^\);]+)(\)|;)/,(a=a.exec(_.Jc()))?a[1]:""):_.Jd||_.Kd||_.Ld?(a=/(?:iPhone|CPU)\s+OS\s+(\S+)/,(a=a.exec(_.Jc()))?a[1].replace(/_/g,"."):""):""}();var Vs;Vs=function(a){return(a=a.exec(_.Jc()))?a[1]:""};_.Ws=function(){if(_.yh)return Vs(/Firefox\/([0-9.]+)/);if(_.yd||_.zd||_.xd)return _.Vd;if(_.Ch){if(_.bd()||_.cd()){var a=Vs(/CriOS\/([0-9.]+)/);if(a)return a}return Vs(/Chrome\/([0-9.]+)/)}if(_.Dh&&!_.bd())return Vs(/Version\/([0-9.]+)/);if(_.zh||_.Ah){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(_.Jc()))return a[1]+"."+a[2]}else if(_.Bh)return(a=Vs(/Android\s+([0-9.]+)/))?a:Vs(/Version\/([0-9.]+)/);return""}();
var Dt,Et,Ht;Dt=_.gd(["about:blank"]);Et=_.gd(["javascript:undefined"]);_.Ft=_.vc(Dt);_.Gt=_.kc(_.Ft).toString();Ht=_.vc(Et);_.kc(Ht);
var WC,XC,YC;_.VC=function(a){var b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return _.VC.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};WC=function(a){return a.replace(/&([^;]+);/g,function(b,c){switch(c){case "amp":return"&";case "lt":return"<";case "gt":return">";case "quot":return'"';default:return c.charAt(0)!="#"||(c=Number("0"+c.slice(1)),isNaN(c))?b:String.fromCharCode(c)}})};XC=/&([^;\s<&]+);?/g;
YC=function(a){var b={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};var c=_.Xa.document.createElement("div");return a.replace(XC,function(d,e){var f=b[d];if(f)return f;e.charAt(0)=="#"&&(e=Number("0"+e.slice(1)),isNaN(e)||(f=String.fromCharCode(e)));f||(_.Hc(c,_.ec(d+" ")),f=c.firstChild.nodeValue.slice(0,-1));return b[d]=f})};_.ZC=function(a){return _.yc(a,"&")?"document"in _.Xa?YC(a):WC(a):a};var $C;_.VC("A AREA BUTTON HEAD INPUT LINK MENU META OPTGROUP OPTION PROGRESS STYLE SELECT SOURCE TEXTAREA TITLE TRACK".split(" "));_.aD=function(a,b){b?a.setAttribute("role",b):a.removeAttribute("role")};
_.bD=function(a,b,c){Array.isArray(c)&&(c=c.join(" "));var d="aria-"+b;c===""||c==void 0?($C||(c={},$C=(c.atomic=!1,c.autocomplete="none",c.dropeffect="none",c.haspopup=!1,c.live="off",c.multiline=!1,c.multiselectable=!1,c.orientation="vertical",c.readonly=!1,c.relevant="additions text",c.required=!1,c.sort="none",c.busy=!1,c.disabled=!1,c.hidden=!1,c.invalid="false",c)),c=$C,b in c?a.setAttribute(d,c[b]):a.removeAttribute(d)):a.setAttribute(d,c)};
_.cD=function(a,b){a=a.getAttribute("aria-"+b);return a==null||a==void 0?"":String(a)};_.dD=function(a,b){var c="";b&&(c=b.id);_.bD(a,"activedescendant",c)};
var eD,fD;eD=function(a){return typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||""};fD=function(a){return a.classList?a.classList:eD(a).match(/\S+/g)||[]};_.gD=function(a,b){typeof a.className=="string"?a.className=b:a.setAttribute&&a.setAttribute("class",b)};_.hD=function(a,b){return a.classList?a.classList.contains(b):_.tb(fD(a),b)};_.iD=function(a,b){if(a.classList)a.classList.add(b);else if(!_.hD(a,b)){var c=eD(a);_.gD(a,c+(c.length>0?" "+b:b))}};
_.jD=function(a,b){if(a.classList)Array.prototype.forEach.call(b,function(e){_.iD(a,e)});else{var c={};Array.prototype.forEach.call(fD(a),function(e){c[e]=!0});Array.prototype.forEach.call(b,function(e){c[e]=!0});b="";for(var d in c)b+=b.length>0?" "+d:d;_.gD(a,b)}};_.kD=function(a,b){a.classList?a.classList.remove(b):_.hD(a,b)&&_.gD(a,Array.prototype.filter.call(fD(a),function(c){return c!=b}).join(" "))};
_.lD=function(a,b){a.classList?Array.prototype.forEach.call(b,function(c){_.kD(a,c)}):_.gD(a,Array.prototype.filter.call(fD(a),function(c){return!_.tb(b,c)}).join(" "))};_.mD=function(a,b,c){c?_.iD(a,b):_.kD(a,b)};
_.nD=function(a){a.Bo=void 0;a.Ia=function(){return a.Bo?a.Bo:a.Bo=new a}};_.oD=function(a,b){b?a.tabIndex=0:(a.tabIndex=-1,a.removeAttribute("tabIndex"))};_.pD=function(){};_.nD(_.pD);_.pD.prototype.zda=0;_.qD=function(a){return":"+(a.zda++).toString(36)};_.sD=function(a){_.Oj.call(this);this.Eb=a||_.ae();this.wC=rD;this.Da=null;this.ob=!1;this.ma=null;this.jm=void 0;this.Jh=this.Wc=this.Gb=this.ur=null};_.eb(_.sD,_.Oj);_.sD.prototype.Lba=_.pD.Ia();var rD=null;_.g=_.sD.prototype;_.g.getId=function(){return this.Da||(this.Da=_.qD(this.Lba))};_.g.Le=function(a){if(this.Gb&&this.Gb.Jh){var b=this.Gb.Jh,c=this.Da;c in b&&delete b[c];_.et(this.Gb.Jh,a,this)}this.Da=a};_.g.O=function(){return this.ma};
_.g.wb=function(){this.jm||(this.jm=new _.jt(this));return this.jm};_.g.Nb=function(a){if(this==a)throw Error("Da");if(a&&this.Gb&&this.Da&&this.Gb.tu(this.Da)&&this.Gb!=a)throw Error("Da");this.Gb=a;_.sD.N.eD.call(this,a)};_.g.getParent=function(){return this.Gb};_.g.eD=function(a){if(this.Gb&&this.Gb!=a)throw Error("Ea");_.sD.N.eD.call(this,a)};_.g.Ha=function(){return this.Eb};_.g.wa=function(){this.ma=this.Eb.createElement("DIV")};_.g.va=function(a){this.zi(a)};
_.g.zi=function(a,b){if(this.ob)throw Error("Fa");this.ma||this.wa();a?a.insertBefore(this.ma,b||null):this.Eb.ub().body.appendChild(this.ma);this.Gb&&!this.Gb.ob||this.vc()};_.g.vc=function(){this.ob=!0;_.tD(this,function(a){!a.ob&&a.O()&&a.vc()})};_.g.Yd=function(){_.tD(this,function(a){a.ob&&a.Yd()});this.jm&&this.jm.removeAll();this.ob=!1};
_.g.ua=function(){this.ob&&this.Yd();this.jm&&(this.jm.dispose(),delete this.jm);_.tD(this,function(a){a.dispose()});this.ma&&_.re(this.ma);this.Gb=this.ur=this.ma=this.Jh=this.Wc=null;_.sD.N.ua.call(this)};_.g.Yl=_.jb(19);_.g.ds=function(a){this.ur=a};_.g.mn=_.jb(20);_.g.Xj=_.jb(21);_.g.Ua=function(){return this.ma};_.g.yp=_.jb(22);_.g.tu=function(a){if(this.Jh&&a){var b=this.Jh;a=(b!==null&&a in b?b[a]:void 0)||null}else a=null;return a};_.uD=function(a,b){return a.Wc?a.Wc[b]||null:null};
_.tD=function(a,b,c){a.Wc&&a.Wc.forEach(b,c)};_.sD.prototype.removeChild=function(a,b){if(a){var c=typeof a==="string"?a:a.getId();a=this.tu(c);if(c&&a){var d=this.Jh;c in d&&delete d[c];_.gj(this.Wc,a);b&&(a.Yd(),a.ma&&_.re(a.ma));a.Nb(null)}}if(!a)throw Error("Ha");return a};_.vD=function(a,b,c){return a.removeChild(_.uD(a,b),c)};_.sD.prototype.ke=function(a){for(var b=[];this.Wc&&this.Wc.length!=0;)b.push(_.vD(this,0,a));return b};
var GH,IH;_.FH=function(a,b){var c={},d;for(d in a)c[d]=b.call(void 0,a[d],d,a);return c};GH={};_.HH=function(a){if(GH[a])return GH[a];a=String(a);if(!GH[a]){var b=/function\s+([^\(]+)/m.exec(a);GH[a]=b?b[1]:"[Anonymous]"}return GH[a]};
IH=function(a,b){var c=[];if(_.tb(b,a))c.push("[...circular reference...]");else if(a&&b.length<50){c.push(_.HH(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=_.HH(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(IH(a.caller,
b))}catch(h){c.push("[exception trying to get caller]\n")}}else a?c.push("[...long stack...]"):c.push("[end]");return c.join("")};_.JH=function(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||_.JH),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=IH(a||arguments.callee.caller,[]));return b};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
_.KH=function(a,b){this.PC=[];this.dY=a;this.kR=b||null;this.Pu=this.In=!1;this.nf=void 0;this.uN=this.H7=this.ZE=!1;this.ND=0;this.Gb=null;this.bF=0};_.KH.prototype.cancel=function(a){if(this.In)this.nf instanceof _.KH&&this.nf.cancel();else{if(this.Gb){var b=this.Gb;delete this.Gb;a?b.cancel(a):(b.bF--,b.bF<=0&&b.cancel())}this.dY?this.dY.call(this.kR,this):this.uN=!0;this.In||this.Nl(new _.LH(this))}};_.KH.prototype.TQ=function(a,b){this.ZE=!1;MH(this,a,b)};
var MH=function(a,b,c){a.In=!0;a.nf=c;a.Pu=!b;NH(a)},PH=function(a){if(a.In){if(!a.uN)throw new OH(a);a.uN=!1}};_.KH.prototype.callback=function(a){PH(this);MH(this,!0,a)};_.KH.prototype.Nl=function(a){PH(this);MH(this,!1,a)};_.KH.prototype.xe=function(a,b){return _.QH(this,a,null,b)};_.KH.prototype.finally=function(a){var b=this;return new Promise(function(c,d){_.QH(b,function(e){a();c(e)},function(e){a();d(e)})})};
_.QH=function(a,b,c,d){var e=a.In;e||(b===c?b=c=(0,_.ok)(b):(b=(0,_.ok)(b),c=(0,_.ok)(c)));a.PC.push([b,c,d]);e&&NH(a);return a};_.KH.prototype.then=function(a,b,c){var d,e,f=new _.xk(function(h,k){e=h;d=k});_.QH(this,e,function(h){h instanceof _.LH?f.cancel():d(h);return RH},this);return f.then(a,b,c)};_.mk(_.KH);
var SH=function(a){return _.Jb(a.PC,function(b){return typeof b[1]==="function"})},RH={},NH=function(a){if(a.ND&&a.In&&SH(a)){var b=a.ND,c=TH[b];c&&(_.Xa.clearTimeout(c.Da),delete TH[b]);a.ND=0}a.Gb&&(a.Gb.bF--,delete a.Gb);b=a.nf;for(var d=c=!1;a.PC.length&&!a.ZE;){var e=a.PC.shift(),f=e[0],h=e[1];e=e[2];if(f=a.Pu?h:f)try{var k=f.call(e||a.kR,b);k===RH&&(k=void 0);k!==void 0&&(a.Pu=a.Pu&&(k==b||k instanceof Error),a.nf=b=k);if(_.nk(b)||typeof _.Xa.Promise==="function"&&b instanceof _.Xa.Promise)d=
!0,a.ZE=!0}catch(l){b=l,a.Pu=!0,SH(a)||(c=!0)}}a.nf=b;d&&(k=(0,_.z)(a.TQ,a,!0),d=(0,_.z)(a.TQ,a,!1),b instanceof _.KH?(_.QH(b,k,d),b.H7=!0):b.then(k,d));c&&(b=new UH(b),TH[b.Da]=b,a.ND=b.Da)},OH=function(a){_.lb.call(this);this.lR=a};_.eb(OH,_.lb);OH.prototype.message="Deferred has already fired";OH.prototype.name="AlreadyCalledError";_.LH=function(a){_.lb.call(this);this.lR=a};_.eb(_.LH,_.lb);_.LH.prototype.message="Deferred was canceled";_.LH.prototype.name="CanceledError";
var UH=function(a){this.Da=_.Xa.setTimeout((0,_.z)(this.xha,this),0);this.pz=a};UH.prototype.xha=function(){delete TH[this.Da];throw this.pz;};var TH={};
_.VH=function(a){var b="Bo";if(a.Bo&&a.hasOwnProperty(b))return a.Bo;b=new a;return a.Bo=b};
_.WH=function(a){a.Dra=!0;return a};_.XH=_.WH(function(a){return typeof a==="number"});_.YH=_.WH(function(a){return typeof a==="string"});_.ZH=_.WH(function(a){return typeof a==="boolean"});
var YJ,wK,zK,CK,HK,KK,XK,eL,iL,QK,$J;_.WJ=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};YJ=function(a){return XJ[a]||""};_.bK=function(a){if(!_.ZJ)return $J(a);a=aK.test(a)?a.replace(aK,YJ):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b};_.dK=function(a){return cK&&a!=null&&a instanceof Uint8Array};
_.eK=function(a,b){var c=a.length;if(c!==b.length)return!1;for(var d=0;d<c;d++)if(a[d]!==b[d])return!1;return!0};_.fK=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b};_.kK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]|=b};_.lK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]=b};_.mK=function(a,b){a[_.hK]&=~b};_.nK=function(a){if(4&a)return 512&a?512:1024&a?1024:0};
_.oK=function(){return typeof BigInt==="function"};_.rK=function(a){return a[pK]===qK};_.tK=function(a,b){return b===void 0?a.BF!==sK&&!!(2&(a.V[_.hK]|0)):!!(2&b)&&a.BF!==sK};_.uK=function(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();};_.vK=function(a,b){if(typeof b!=="number"||b<0||b>a.length)throw Error();};wK=function(a){return a};
_.yK=function(a){var b=a;if((0,_.YH)(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if((0,_.XH)(b)&&!Number.isSafeInteger(b))throw Error(String(b));return xK?BigInt(a):a=(0,_.ZH)(a)?a?"1":"0":(0,_.YH)(a)?a.trim()||"0":String(a)};zK=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};CK=function(a){var b=a>>>0;_.AK=b;_.BK=(a-b)/4294967296>>>0};
_.EK=function(a){if(a<0){CK(-a);var b=_.Aa(_.DK(_.AK,_.BK));a=b.next().value;b=b.next().value;_.AK=a>>>0;_.BK=b>>>0}else CK(a)};_.GK=function(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:_.FK(a,b)};
_.FK=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else _.oK()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+HK(c)+HK(a));return c};HK=function(a){a=String(a);return"0000000".slice(a.length)+a};
_.IK=function(a){if(a.length<16)_.EK(Number(a));else if(_.oK())a=BigInt(a),_.AK=Number(a&BigInt(4294967295))>>>0,_.BK=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");_.BK=_.AK=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),_.BK*=1E6,_.AK=_.AK*1E6+d,_.AK>=4294967296&&(_.BK+=Math.trunc(_.AK/4294967296),_.BK>>>=0,_.AK>>>=0);b&&(b=_.Aa(_.DK(_.AK,_.BK)),a=b.next().value,b=b.next().value,_.AK=a,_.BK=b)}};_.DK=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
KK=function(a,b){if(a!=null){var c;var d=(c=JK)!=null?c:JK={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),_.WJ(a,"incident"),_.sh(a))}};_.LK=function(a){return Array.prototype.slice.call(a)};_.MK=function(a){if(a!=null&&typeof a!=="boolean")throw Error("ob`"+_.jd(a)+"`"+a);return a};_.NK=function(a){return a==null||typeof a==="string"?a:void 0};
_.PK=function(a,b,c,d){if(a!=null&&typeof a==="object"&&_.rK(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[_.OK])||(a=new b,_.kK(a.V,34),a=b[_.OK]=a),b=a):b=new b:b=void 0,b;c=a[_.hK]|0;d=c|d&32|d&2;d!==c&&_.lK(a,d);return new b(a)};_.SK=function(a){var b=QK(RK);return b?a[b]:void 0};_.UK=function(a,b){var c=QK(RK),d;_.gK&&c&&((d=a[c])==null?void 0:d[b])!=null&&KK(TK,3)};
XK=function(a,b){var c=c===void 0?!1:c;if(QK(VK)&&QK(RK)&&void 0===VK){var d=a.V,e=d[RK];if(!e)return;if(e=e.a_)try{e(d,b,WK);return}catch(f){_.sh(f)}}c&&(a=a.V,(c=QK(RK))&&c in a&&(a=a[c])&&delete a[b])};
_.$K=function(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f=[],h=a.length,k=4294967295,l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,k=h):p=void 0;if(m&&!(b&128)&&!e){l=!0;var q;k=((q=YK)!=null?q:wK)(k-n,n,a,p)+n}}q=void 0;for(var r=0;r<h;r++){var w=a[r];if(w!=null&&(w=c(w,d))!=null)if(m&&r>=k){var u=r-n,x=void 0;((x=q)!=null?x:q={})[u]=w}else f[r]=w}if(p)for(var A in p)h=p[A],h!=null&&(h=c(h,d))!=null&&(r=+A,w=void 0,
m&&!Number.isNaN(r)&&(w=r+n)<k?f[w]=h:(r=void 0,((r=q)!=null?r:q={})[A]=h));q&&(l?f.push(q):f[k]=q);e&&(_.lK(f,b&16761025|34),QK(RK)&&(a=_.SK(a))&&"function"==typeof _.ZK&&a instanceof _.ZK&&(f[RK]=a.h8()));return f};
_.bL=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.aL)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[_.hK]|0;return a.length===0&&b&1?void 0:_.$K(a,b,_.bL)}if(_.rK(a))return _.cL(a);if("function"==typeof _.dL&&a instanceof _.dL)return a.RE();return}return a};_.cL=function(a){a=a.V;return _.$K(a,a[_.hK]|0,_.bL)};
_.fL=function(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-16760833|(b&1023)<<14)}else{if(!Array.isArray(a))throw Error("pb");e=a[_.hK]|0;4096&e&&!(2&e)&&eL();if(e&256)throw Error("rb");if(e&64)return d!==0||e&4096||_.lK(a,e|4096),a;if(c&&(e|=128,c!==a[0]))throw Error("sb");a:{c=a;e|=64;var f=c.length;if(f){var h=f-1,k=c[h];if(k!=null&&typeof k==="object"&&k.constructor===Object){b=e&128?0:-1;h-=b;if(h>=1024)throw Error("ub");for(var l in k)f=+l,f<h&&(c[f+b]=k[l],
delete k[l]);e=e&-16760833|(h&1023)<<14;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("vb");e=e&-16760833|(l&1023)<<14}}}e|=64;d===0&&(e|=4096);_.lK(a,e);return a};eL=function(){KK(gL,5)};
iL=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.hK]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=_.hL(a,c,b&&!(c&16)):(_.kK(a,34),c&4&&Object.freeze(a)));return a}if(_.rK(a))return b=a.V,c=b[_.hK]|0,_.tK(a,c)?a:_.hL(b,c);if("function"==typeof _.dL&&a instanceof _.dL)return a};_.hL=function(a,b,c){c!=null||(c=!!(34&b));return _.$K(a,b,iL,c,!0)};_.jL=function(a){var b=a.V,c=b[_.hK]|0;if(!_.tK(a,c))return a;a=new a.constructor(_.hL(b,c));_.mK(a.V,2);return a};
_.kL=function(a){if(a.BF!==sK)return!1;var b=a.V;b=_.hL(b,b[_.hK]|0);_.mK(b,2);a.V=b;a.BF=void 0;return!0};_.lL=function(a){if(!_.kL(a)&&_.tK(a,a.V[_.hK]|0))throw Error();};_.mL=function(a,b,c,d,e){var f=c+(e?0:-1),h=a.length-1;if(h>=1+(e?0:-1)&&f>=h){var k=a[h];if(k!=null&&typeof k==="object"&&k.constructor===Object)return k[c]=d,b}if(f<=h)return a[f]=d,b;if(d!==void 0){var l;h=((l=b)!=null?l:b=a[_.hK]|0)>>14&1023||536870912;c>=h?d!=null&&(f={},a[h+(e?0:-1)]=(f[c]=d,f)):a[f]=d}return b};
_.pL=function(a,b,c,d,e,f,h,k,l){var m=b;h===1||(h!==4?0:2&b||!(16&b)&&32&d)?_.nL(b)||(b|=!a.length||k&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==m&&_.lK(a,b),Object.freeze(a)):(h===2&&_.nL(b)&&(a=_.LK(a),m=0,b=_.oL(b,d),_.mL(c,d,e,a,f)),_.nL(b)||(l||(b|=16),b!==m&&_.lK(a,b)));return a};_.sL=function(a,b,c){a=_.qL(a,b,c);return Array.isArray(a)?a:_.rL};_.tL=function(a,b){2&b&&(a|=2);return a|1};_.nL=function(a){return!!(2&a)&&!!(4&a)||!!(256&a)};
_.uL=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a};_.vL=function(a,b,c,d,e){a=_.qL(a,d,e,function(f){return _.PK(f,c,!1,b)});if(a!=null)return a};
_.wL=function(a,b,c,d,e,f,h,k,l){var m=_.tK(a,c);f=m?1:f;k=!!k||f===3;m=l&&!m;(f===2||m)&&_.kL(a)&&(b=a.V,c=b[_.hK]|0);a=_.sL(b,e,h);var n=a===_.rL?7:a[_.hK]|0,p=_.tL(n,c);if(l=!(4&p)){var q=a,r=c,w=!!(2&p);w&&(r|=2);for(var u=!w,x=!0,A=0,D=0;A<q.length;A++){var E=_.PK(q[A],d,!1,r);if(E instanceof d){if(!w){var N=_.tK(E);u&&(u=!N);x&&(x=N)}q[D++]=E}}D<A&&(q.length=D);p|=4;p=x?p&-8193:p|8192;p=u?p|8:p&-9}p!==n&&(_.lK(a,p),2&p&&Object.freeze(a));if(m&&!(8&p||!a.length&&(f===1||(f!==4?0:2&p||!(16&p)&&
32&c)))){_.nL(p)&&(a=_.LK(a),p=_.oL(p,c),c=_.mL(b,c,e,a,h));d=a;m=p;for(n=0;n<d.length;n++)q=d[n],p=_.jL(q),q!==p&&(d[n]=p);m|=8;p=m=d.length?m|8192:m&-8193;_.lK(a,p)}return a=_.pL(a,p,b,c,e,h,f,l,k)};_.oL=function(a,b){return a=(2&b?a|2:a&-3)&-273};
_.xL=function(a,b,c,d,e,f,h,k){_.lL(a);var l=a.V;a=_.wL(a,l,l[_.hK]|0,c,b,2,d,!0);h&&k?(f!=null||(f=a.length-1),_.uK(a,f),a.splice(f,h),a.length||_.mK(a,8192)):(h?_.vK(a,f):e=e!=null?e:new c,f!=void 0?a.splice(f,h,e):a.push(e),f=c=a===_.rL?7:a[_.hK]|0,_.tK(e)?(c&=-9,a.length===1&&(c&=-8193)):c|=8192,c!==f&&_.lK(a,c))};QK=function(a){return a};
$J=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):_.yc("=.",a[b-1])&&(c=_.yc("=.",a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;_.Oh(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d};var cK,aK,XJ;cK=typeof Uint8Array!=="undefined";_.ZJ=!_.yd&&typeof btoa==="function";aK=/[-_.]/g;XJ={"-":"+",_:"/",".":"="};var yL,RK,TK,gL,pK,VK;_.gK=typeof Symbol==="function"&&typeof Symbol()==="symbol";yL=_.fK("jas",void 0,!0);_.OK=_.fK(void 0,"0di");RK=_.fK(void 0,Symbol());TK=_.fK(void 0,"0ub");gL=_.fK(void 0,"0actk");pK=_.fK("m_m","Sra",!0);VK=_.fK();var jK,iK,zL;jK={gca:{value:0,configurable:!0,writable:!0,enumerable:!1}};iK=Object.defineProperties;_.hK=_.gK?yL:"gca";zL=[];_.lK(zL,7);_.rL=Object.freeze(zL);var qK,sK;qK={};sK={};_.AL=Object.freeze({});var xK=typeof _.Xa.BigInt==="function"&&typeof _.Xa.BigInt(0)==="bigint";var DL,BL,EL,CL;_.aL=_.WH(function(a){return xK?a>=BL&&a<=CL:a[0]==="-"?zK(a,DL):zK(a,EL)});DL=Number.MIN_SAFE_INTEGER.toString();BL=xK?BigInt(Number.MIN_SAFE_INTEGER):void 0;EL=Number.MAX_SAFE_INTEGER.toString();CL=xK?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.AK=0;_.BK=0;var JK=void 0;_.FL=typeof BigInt==="function"?BigInt.asIntN:void 0;_.GL=typeof BigInt==="function"?BigInt.asUintN:void 0;_.HL=Number.isSafeInteger;_.IL=Number.isFinite;_.JL=Math.trunc;var WK={Bsa:!0};var YK;_.KL=_.yK(0);_.LL=function(a,b,c){return _.qL(a.V,b,c)};_.qL=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var h=a[f];if(h!=null&&typeof h==="object"&&h.constructor===Object){c=h[b];var k=!0}else if(e===f)c=h;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return k?h[b]=d:a[e]=d,d}return c}};_.ML=function(a,b,c,d){a=a.V;return _.vL(a,a[_.hK]|0,b,c,d)!==void 0};_.NL=function(a,b,c){this.V=_.fL(a,b,c)};_.NL.prototype.toJSON=function(){return _.cL(this)};_.OL=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("Fb");_.kK(b,32);return new a(b)};_.NL.prototype.getExtension=function(a){_.UK(this.V,a.Ph);XK(this,a.Ph);return a.ctor?a.Oz(this,a.ctor,a.Ph,a.Uu):a.Oz(this,a.Ph,a.defaultValue,a.Uu)};
_.PL=function(a,b){_.UK(a.V,b.Ph);XK(a,b.Ph);a=b.ctor?b.Oz(a,b.ctor,b.Ph,b.Uu):b.Oz(a,b.Ph,null,b.Uu);return a===null?void 0:a};_.NL.prototype.hasExtension=function(a){_.UK(this.V,a.Ph);XK(this,a.Ph);return a.ctor?_.ML(this,a.ctor,a.Ph,a.Uu):_.PL(this,a)!==void 0};_.NL.prototype.clone=function(){var a=this,b=a.V;a=new a.constructor(_.hL(b,b[_.hK]|0));_.mK(a.V,2);return a};_.NL.prototype[pK]=qK;_.NL.prototype.toString=function(){return this.V.toString()};
var iN,lN,qN,rN,sN,tN,xN,yN,JN,MN,NN,ON,QN,RN,UN,EN,mN,qO;_.hN=function(a){var b=a.V,c=b[_.hK]|0;return _.tK(a,c)?a:new a.constructor(_.hL(b,c))};iN=function(){var a=_.AK,b=_.BK;b&2147483648?_.oK()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=_.Aa(_.DK(a,b)),a=b.next().value,b=b.next().value,a="-"+_.FK(a,b)):a=_.FK(a,b);return a};_.jN=function(a){a=Error(a);_.WJ(a,"warning");return a};_.kN=function(a){if(a!=null&&typeof a!=="number")throw Error("nb`"+typeof a+"`"+a);return a};
lN=function(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)};_.nN=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.IL)(a);case "string":return mN.test(a);default:return!1}};_.oN=function(a){if(!(0,_.IL)(a))throw _.jN("enum");return a|0};_.pN=function(a){if(typeof a!=="number")throw _.jN("int32");if(!(0,_.IL)(a))throw _.jN("int32");return a|0};
qN=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.IL)(a)?a|0:void 0};rN=function(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337};sN=function(a){if(rN(a))return a;_.IK(a);return iN()};tN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return _.yK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.oK()?_.yK((0,_.FL)(64,BigInt(a))):_.yK(sN(a))};
_.uN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return sN(a)};_.vN=function(a){a=(0,_.JL)(a);if(!(0,_.HL)(a)){_.EK(a);var b=_.AK,c=_.BK;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=_.GK(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a};_.wN=function(a){a=(0,_.JL)(a);if((0,_.HL)(a))a=String(a);else{var b=String(a);rN(b)?a=b:(_.EK(a),a=iN())}return a};xN=function(a){return(0,_.HL)(a)?_.yK(_.vN(a)):_.yK(_.wN(a))};
yN=function(a,b){b=b===void 0?0:b;if(!_.nN(a))throw _.jN("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return _.uN(a);case "bigint":return String((0,_.FL)(64,a));default:return _.wN(a)}case 1024:switch(c){case "string":return tN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return xN(a)}case 0:switch(c){case "string":return _.uN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return _.vN(a)}default:return _.rb(b,"Unknown format requested type for int64")}};
_.zN=function(a,b){return a==null?a:yN(a,b===void 0?0:b)};_.AN=function(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return _.yK((0,_.FL)(64,a));if(_.nN(a))return b==="string"?tN(a):xN(a)};_.BN=function(a){if(typeof a!=="string")throw Error();return a};_.CN=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};
_.DN=function(a,b,c,d,e){_.lL(a);var f=a.V,h=f[_.hK]|0;if(c==null)return _.mL(f,h,b,void 0,e),a;var k=c===_.rL?7:c[_.hK]|0,l=k,m=_.nL(k),n=m||Object.isFrozen(c);m||(k=0);n||(c=_.LK(c),l=0,k=_.oL(k,h),n=!1);k|=5;var p;m=(p=_.nK(k))!=null?p:0;for(p=0;p<c.length;p++){var q=c[p],r=d(q,m);Object.is(q,r)||(n&&(c=_.LK(c),l=0,k=_.oL(k,h),n=!1),c[p]=r)}k!==l&&(n&&(c=_.LK(c),k=_.oL(k,h)),_.lK(c,k));_.mL(f,h,b,c,e);return a};
_.FN=function(a){if(_.gK){var b;return(b=a[EN])!=null?b:a[EN]=new Map}if(EN in a)return a[EN];b=new Map;Object.defineProperty(a,EN,{value:b});return b};_.GN=function(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var h=d[f];_.qL(b,h,void 0)!=null&&(e!==0&&(c=_.mL(b,c,e,void 0,void 0)),e=h)}a.set(d,e);return e};_.HN=function(a){return a===_.AL?2:4};_.IN=function(a){return JSON.stringify(_.cL(a))};
JN=function(){this.AI=!1;this.ln=null;this.Uj=void 0;this.Pc=1;this.Hn=this.un=0;this.XS=this.Ah=null};_.g=JN.prototype;_.g.Ow=function(){if(this.AI)throw new TypeError("Generator is already running");this.AI=!0};_.g.Fp=function(){this.AI=!1};_.g.Kv=function(a){this.Uj=a};_.g.Zw=function(a){this.Ah={yS:a,lW:!0};this.Pc=this.un||this.Hn};_.g.return=function(a){this.Ah={return:a};this.Pc=this.Hn};_.KN=function(a,b,c){a.Pc=c;return{value:b}};JN.prototype.Vg=function(a){this.Pc=a};
_.LN=function(a){a.un=0;var b=a.Ah.yS;a.Ah=null;return b};MN=function(a){this.qb=new JN;this.Iea=a};MN.prototype.Kv=function(a){this.qb.Ow();if(this.qb.ln)return NN(this,this.qb.ln.next,a,this.qb.Kv);this.qb.Kv(a);return ON(this)};var PN=function(a,b){a.qb.Ow();var c=a.qb.ln;if(c)return NN(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.qb.return);a.qb.return(b);return ON(a)};
MN.prototype.Zw=function(a){this.qb.Ow();if(this.qb.ln)return NN(this,this.qb.ln["throw"],a,this.qb.Kv);this.qb.Zw(a);return ON(this)};NN=function(a,b,c,d){try{var e=b.call(a.qb.ln,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.qb.Fp(),e;var f=e.value}catch(h){return a.qb.ln=null,a.qb.Zw(h),ON(a)}a.qb.ln=null;d.call(a.qb,f);return ON(a)};
ON=function(a){for(;a.qb.Pc;)try{var b=a.Iea(a.qb);if(b)return a.qb.Fp(),{value:b.value,done:!1}}catch(c){a.qb.Uj=void 0,a.qb.Zw(c)}a.qb.Fp();if(a.qb.Ah){b=a.qb.Ah;a.qb.Ah=null;if(b.lW)throw b.yS;return{value:b.return,done:!0}}return{value:void 0,done:!0}};QN=function(a){this.next=function(b){return a.Kv(b)};this.throw=function(b){return a.Zw(b)};this.return=function(b){return PN(a,b)};this[Symbol.iterator]=function(){return this}};
RN=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(h){h.done?d(h.value):Promise.resolve(h.value).then(b,c).then(f,e)}f(a.next())})};_.SN=function(a){return RN(new QN(new MN(a)))};_.TN={};EN=_.fK(void 0,"1oa");_.C={};mN=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;_.VN=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,c,d);return a};_.WN=function(a,b){return _.qL(a.V,b,void 0,lN)};
_.XN=function(a,b){a=a.V;return _.GN(_.FN(a),a,void 0,b)};_.YN=function(a,b,c){return _.XN(a,b)===c?c:-1};_.ZN=function(a,b,c,d){var e=a.V,f=e[_.hK]|0;b=_.vL(e,f,b,c,d);if(b==null)return b;f=e[_.hK]|0;if(!_.tK(a,f)){var h=_.jL(b);h!==b&&(_.kL(a)&&(e=a.V,f=e[_.hK]|0),b=h,_.mL(e,f,c,b,d))}return b};_.$N=function(a,b,c,d,e,f){_.xL(a,b,c,f,e,d,1);return a};_.aO=function(a,b,c,d,e){var f=a.V;return _.wL(a,f,f[_.hK]|0,b,c,d,e,!1,!0)};_.bO=function(a,b,c,d){c==null&&(c=void 0);_.VN(a,b,c,d);return a};
_.cO=function(a,b,c,d){_.lL(a);var e=a.V,f=e[_.hK]|0;if(c==null)return _.mL(e,f,b,void 0,d),a;for(var h=c===_.rL?7:c[_.hK]|0,k=h,l=_.nL(h),m=l||Object.isFrozen(c),n=!0,p=!0,q=0;q<c.length;q++){var r=c[q];l||(r=_.tK(r),n&&(n=!r),p&&(p=r))}l||(h=n?13:5,h=p?h&-8193:h|8192);m&&h===k||(c=_.LK(c),k=0,h=_.oL(h,f));h!==k&&_.lK(c,h);_.mL(e,f,b,c,d);return a};
_.dO=function(a,b){a=_.LL(a,b);a!=null&&(typeof a==="bigint"?(0,_.aL)(a)?a=Number(a):(a=(0,_.FL)(64,a),a=(0,_.aL)(a)?Number(a):String(a)):a=_.nN(a)?typeof a==="number"?_.vN(a):_.uN(a):void 0);return a};_.eO=function(a,b,c){return qN(_.LL(a,b,c))};_.fO=function(a,b,c){return _.NK(_.LL(a,b,c))};_.gO=function(a,b,c){a=_.LL(a,b,c);return a==null?a:(0,_.IL)(a)?a|0:void 0};_.hO=function(a,b,c){c=c===void 0?0:c;var d;return(d=_.eO(a,b))!=null?d:c};
_.iO=function(a,b){var c=c===void 0?"":c;var d;return(d=_.fO(a,b))!=null?d:c};_.jO=function(a,b){var c=c===void 0?0:c;var d;return(d=_.gO(a,b))!=null?d:c};_.kO=function(a,b,c,d){return _.VN(a,b,_.MK(c),d)};_.lO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.pN(c),d)};_.mO=function(a,b,c,d,e){return _.VN(a,b,_.zN(c,d===void 0?0:d),e)};_.nO=function(a,b,c,d){return _.VN(a,b,_.CN(c),d)};_.oO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.oN(c),d)};
_.dL=function(a,b){if(b!==_.TN)throw Error("mb");this.La=a;if(a!=null&&a.length===0)throw Error("lb");};_.pO=function(){return UN||(UN=new _.dL(null,_.TN))};_.dL.prototype.RE=function(){var a=this.La;if(a==null)a="";else if(typeof a!=="string"){if(_.ZJ){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=_.Nh(a);a=this.La=a}return a};
_.dL.prototype.isEmpty=function(){return this.La==null};_.dL.prototype.eta=function(){var a=qO(this);return a?a.length:0};_.rO=function(a,b){if(!a.La||!b.La||a.La===b.La)return a.La===b.La;if(typeof a.La==="string"&&typeof b.La==="string"){var c=a.La,d=b.La;b.La.length>a.La.length&&(d=a.La,c=b.La);if(c.lastIndexOf(d,0)!==0)return!1;for(b=d.length;b<c.length;b++)if(c[b]!=="=")return!1;return!0}c=qO(a);b=qO(b);return _.eK(c,b)};
qO=function(a){if(_.TN!==_.TN)throw Error("mb");var b=a.La;b==null||_.dK(b)||(typeof b==="string"?b=_.bK(b):(_.jd(b),b=null));return b==null?b:a.La=b};_.dL.prototype.YV=function(a){if(typeof a==="string")a=a?new _.dL(a,_.TN):_.pO();else if(a instanceof Uint8Array)a=new _.dL(a,_.TN);else if(!(a instanceof _.dL))return!1;return _.rO(this,a)};
var PO,RO,SO,TO,UO,WO,VO,XO;
PO=function(a){var b=_.Jc();if(a==="Internet Explorer")return _.Qc()?_.Yc(b):"";b=_.Nc(b);var c=_.Xc(b);switch(a){case "Opera":if(_.Pc())return c(["Version","Opera"]);if(_.Uc())return c(["OPR"]);break;case "Microsoft Edge":if(_.Sc())return c(["Edge"]);if(_.Tc())return c(["Edg"]);break;case "Chromium":if(_.Wc())return c(["Chrome","CriOS","HeadlessChrome"])}return a==="Firefox"&&_.Vc()||a==="Safari"&&_.vh()||a==="Android Browser"&&_.wh()||a==="Silk"&&_.Mc("Silk")?(a=b[2])&&a[1]||"":""};
_.QO=function(a){if(_.Oc()&&a!=="Silk"){var b=_.Kc.brands.find(function(c){return c.brand===a});if(!b||!b.version)return NaN;b=b.version.split(".")}else{b=PO(a);if(b==="")return NaN;b=b.split(".")}return b.length===0?NaN:Number(b[0])};RO=function(a){return!Array.isArray(a)||a.length?!1:(a[_.hK]|0)&1?!0:!1};SO=function(a,b){if(typeof b==="string")try{b=_.bK(b)}catch(c){return!1}return _.dK(b)&&_.eK(a,b)};TO=function(a){switch(a){case "bigint":case "string":case "number":return!0;default:return!1}};
UO=function(a,b,c,d,e){var f;return(f=a<d?b[a+e]:void 0)!=null?f:c==null?void 0:c[a]};WO=function(a,b){if(_.rK(a))a=a.V;else if(!Array.isArray(a))return!1;if(_.rK(b))b=b.V;else if(!Array.isArray(b))return!1;return VO(a,b,void 0,2)};
VO=function(a,b,c,d){if(a===b||a==null&&b==null)return!0;if(a instanceof Map)return a.ica(b,c);if(b instanceof Map)return b.ica(a,c);if(a==null||b==null)return!1;if("function"==typeof _.dL&&a instanceof _.dL)return a.YV(b);if("function"==typeof _.dL&&b instanceof _.dL)return b.YV(a);if(_.dK(a))return SO(a,b);if(_.dK(b))return SO(b,a);var e=typeof a,f=typeof b;if(e!=="object"||f!=="object")return Number.isNaN(a)||Number.isNaN(b)?String(a)===String(b):TO(e)&&TO(f)?""+a===""+b:e==="boolean"&&f==="number"||
e==="number"&&f==="boolean"?!a===!b:!1;if(_.rK(a)||_.rK(b))return WO(a,b);if(a.constructor!=b.constructor)return!1;if(a.constructor===Array){f=a[_.hK]|0;var h=b[_.hK]|0,k=a.length,l=b.length,m=Math.max(k,l);e=(f|h|64)&128?0:-1;(d===1||(f|h)&1)&&(d=1);f=k&&a[k-1];h=l&&b[l-1];f!=null&&typeof f==="object"&&f.constructor===Object||(f=null);h!=null&&typeof h==="object"&&h.constructor===Object||(h=null);k=k-e-+!!f;l=l-e-+!!h;for(var n=0;n<m;n++)if(!XO(n-e,a,f,k,b,h,l,e,c,d))return!1;if(f)for(var p in f){d=
a;m=f;n=k;var q=b,r=h,w=l,u=e,x=c,A=+p;if(!(!Number.isFinite(A)||A<n||A<w||XO(A,d,m,n,q,r,w,u,x,2)))return!1}if(h)for(var D in h)if((p=f&&D in f)||(p=a,d=f,m=k,n=b,q=h,r=l,w=e,u=c,x=+D,p=!Number.isFinite(x)||x<m||x<r?!0:XO(x,p,d,m,n,q,r,w,u,2)),!p)return!1;return!0}if(a.constructor===Object)return VO([a],[b],void 0,0);throw Error();};XO=function(a,b,c,d,e,f,h,k,l,m){b=UO(a,b,c,d,k);e=UO(a,e,f,h,k);m=m===1;if(e==null&&RO(b)||b==null&&RO(e))return!0;a=m?l:l==null?void 0:l.Rqa(a);return VO(b,e,a,0)};
_.YO=function(a){return(0,_.aL)(a)?Number(a):String(a)};_.F=function(a,b,c){a=_.LL(a,b,c);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0};_.ZO=function(a,b,c,d){c=c===void 0?!1:c;var e;return(e=_.F(a,b,d))!=null?e:c};_.$O=function(a,b){return a===b||a==null&&b==null||!(!a||!b)&&a instanceof b.constructor&&WO(a,b)};
var xP,yP;xP=function(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467};yP=function(a){if(a<0){_.EK(a);var b=_.FK(_.AK,_.BK);a=Number(b);return(0,_.HL)(a)?a:b}b=String(a);if(xP(b))return b;_.EK(a);return _.GK(_.AK,_.BK)};_.zP=function(a){if(xP(a))return a;_.IK(a);return _.FK(_.AK,_.BK)};_.AP=function(a){a=(0,_.JL)(a);return a>=0&&(0,_.HL)(a)?a:yP(a)};
_.BP=function(a){a=(0,_.JL)(a);if(a>=0&&(0,_.HL)(a))a=String(a);else{var b=String(a);xP(b)?a=b:(_.EK(a),a=_.FK(_.AK,_.BK))}return a};_.CP=function(a){return(0,_.HL)(a)?_.yK(_.AP(a)):_.yK(_.BP(a))};_.DP=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b)&&b>=0)return _.yK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.oK()?_.yK((0,_.GL)(64,BigInt(a))):_.yK(_.zP(a))};
_.EP=function(a,b,c,d,e){var f=a.V,h=f[_.hK]|0;c=_.tK(a,h)?1:c;d=!!d||c===3;c===2&&_.kL(a)&&(f=a.V,h=f[_.hK]|0);a=_.sL(f,b,e);var k=a===_.rL?7:a[_.hK]|0,l=_.tL(k,h);var m=4&l?!1:!0;if(m){4&l&&(a=_.LK(a),k=0,l=_.oL(l,h),h=_.mL(f,h,b,a,e));for(var n=0,p=0;n<a.length;n++){var q=_.NK(a[n]);q!=null&&(a[p++]=q)}p<n&&(a.length=p);l=(l|4)&-513;l&=-1025;l&=-8193}l!==k&&(_.lK(a,l),2&l&&Object.freeze(a));return a=_.pL(a,l,f,h,b,e,c,m,d)};
_.FP=function(a,b,c,d,e,f){_.lL(a);b=_.EP(a,b,2,!0,e);var h;e=(h=_.nK(b===_.rL?7:b[_.hK]|0))!=null?h:0;f&&_.vK(b,d);d!=void 0?b.splice(d,f,_.BN(c,e)):b.push(_.BN(c,e));return a};_.GP=function(a){return a};_.HP=function(a,b,c,d,e){_.xL(a,b,c,e,void 0,d,1,!0);return a};_.IP=function(a,b,c,d){a=_.EP(a,b,3,!0,d);_.uK(a,c);return a[c]};_.JP=function(a,b,c){return _.uL(a,b,_.MK(c),!1)};var NP,KP,LP,MP;
_.OP=function(a,b){var c=b||{};b=c.document||document;var d=_.kc(a).toString(),e=(new _.Zd(b)).createElement("SCRIPT"),f={h_:e,Hi:void 0},h=new _.KH(KP,f),k=null,l=c.timeout!=null?c.timeout:5E3;l>0&&(k=window.setTimeout(function(){LP(e,!0);h.Nl(new MP(1,"Timeout reached for loading script "+d))},l),f.Hi=k);e.onload=e.onreadystatechange=function(){e.readyState&&e.readyState!="loaded"&&e.readyState!="complete"||(LP(e,c.eqa||!1,k),h.callback(null))};e.onerror=function(){LP(e,!0,k);h.Nl(new MP(0,"Error while loading script "+
d))};f=c.attributes||{};_.ij(f,{type:"text/javascript",charset:"UTF-8"});_.ee(e,f);_.Hh(e,a);NP(b).appendChild(e);return h};NP=function(a){var b=(a||document).getElementsByTagName("HEAD");return b&&b.length!==0?b[0]:a.documentElement};KP=function(){if(this&&this.h_){var a=this.h_;a&&a.tagName=="SCRIPT"&&LP(a,!0,this.Hi)}};LP=function(a,b,c){c!=null&&_.Xa.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){_.re(a)},0)};
MP=function(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);_.lb.call(this,c);this.code=a};_.eb(MP,_.lb);
_.h1=function(a,b){b=b===void 0?!1:b;var c=typeof a;if(a==null)return a;if(c==="bigint")return String((0,_.FL)(64,a));if(_.nN(a))return c==="string"?_.uN(a):b?_.wN(a):_.vN(a)};_.i1=function(a){return function(b){return _.OL(a,b)}};_.j1=function(a,b,c){c=c===void 0?_.KL:c;var d;return(d=_.AN(_.LL(a,b)))!=null?d:c};
var Rca,Qca;Rca=function(a,b){var c=Qca;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};Qca={};_.s4=function(a){return Rca(a,function(){return _.Dc(_.Vd,a)>=0})};
var z4;_.t4=function(a,b){a.src=_.kc(b).toString()};_.u4=function(a){return _.Ks(a)};_.v4=function(a){return a};_.w4=function(a,b){var c=_.As(a,b+"Left"),d=_.As(a,b+"Right"),e=_.As(a,b+"Top");a=_.As(a,b+"Bottom");return new _.ms(parseFloat(e),parseFloat(d),parseFloat(a),parseFloat(c))};_.x4=function(a,b){var c=c===void 0?0:c;var d;return(d=_.WN(a,b))!=null?d:c};_.y4=function(a,b,c,d){return _.ZN(a,b,_.YN(a,d,c),void 0)};
z4=function(a){_.Oj.call(this);this.ma=a;this.Nca=_.Dj(this.ma,"focus",this,!0);this.Oca=_.Dj(this.ma,"blur",this,!0)};_.eb(z4,_.Oj);z4.prototype.handleEvent=function(a){var b=new _.rj(a.Cf);b.type=a.type=="focusin"||a.type=="focus"?"focusin":"focusout";this.dispatchEvent(b)};z4.prototype.ua=function(){z4.N.ua.call(this);_.Kj(this.Nca);_.Kj(this.Oca);delete this.ma};_.A4=function(a,b,c){_.Oj.call(this);this.target=a;this.handle=b||a;this.SI=c||new _.Qs(NaN,NaN,NaN,NaN);this.Bc=_.$d(a);this.Fa=new _.jt(this);_.fj(this,this.Fa);this.deltaY=this.deltaX=this.fl=this.dl=this.screenY=this.screenX=this.clientY=this.clientX=0;this.Nh=!0;this.Fn=!1;_.Dj(this.handle,["touchstart","mousedown"],this.Z0,!1,this);this.TD=Sca};_.eb(_.A4,_.Oj);var Sca=_.Xa.document&&_.Xa.document.documentElement&&!!_.Xa.document.documentElement.setCapture&&!!_.Xa.document.releaseCapture;
_.A4.prototype.wb=function(){return this.Fa};_.B4=function(a,b){a.SI=b||new _.Qs(NaN,NaN,NaN,NaN)};_.g=_.A4.prototype;_.g.yc=function(a){this.Nh=a};_.g.ua=function(){_.A4.N.ua.call(this);_.Nj(this.handle,["touchstart","mousedown"],this.Z0,!1,this);this.Fa.removeAll();this.TD&&this.Bc.releaseCapture();this.handle=this.target=null};
_.g.Z0=function(a){var b=a.type=="mousedown";if(!this.Nh||this.Fn||b&&(a.Cf.button!=0||_.Ed&&a.ctrlKey))this.dispatchEvent("earlycancel");else if(this.dispatchEvent(new C4("start",this,a.clientX,a.clientY,a))){this.Fn=!0;b&&a.preventDefault();b=this.Bc;var c=b.documentElement,d=!this.TD;this.Fa.na(b,["touchmove","mousemove"],this.Maa,{capture:d,passive:!1});this.Fa.na(b,["touchend","mouseup"],this.oz,d);this.TD?(c.setCapture(!1),this.Fa.na(c,"losecapture",this.oz)):this.Fa.na(_.ie(b),"blur",this.oz);
this.Hfa&&this.Fa.na(this.Hfa,"scroll",this.Qv,d);this.clientX=this.dl=a.clientX;this.clientY=this.fl=a.clientY;this.screenX=a.screenX;this.screenY=a.screenY;this.deltaX=this.target.offsetLeft;this.deltaY=this.target.offsetTop;this.VJ=_.ss(_.ae(this.Bc))}};_.g.oz=function(a,b){this.Fa.removeAll();this.TD&&this.Bc.releaseCapture();this.Fn?(this.Fn=!1,this.dispatchEvent(new C4("end",this,a.clientX,a.clientY,a,D4(this,this.deltaX),E4(this,this.deltaY),b||a.type=="touchcancel"))):this.dispatchEvent("earlycancel")};
_.g.Maa=function(a){if(this.Nh){var b=a.clientX-this.clientX,c=a.clientY-this.clientY;this.clientX=a.clientX;this.clientY=a.clientY;this.screenX=a.screenX;this.screenY=a.screenY;if(!this.Fn){var d=this.dl-this.clientX,e=this.fl-this.clientY;if(d*d+e*e>0)if(this.dispatchEvent(new C4("start",this,a.clientX,a.clientY,a)))this.Fn=!0;else{this.isDisposed()||this.oz(a);return}}c=F4(this,b,c);b=c.x;c=c.y;this.Fn&&this.dispatchEvent(new C4("beforedrag",this,a.clientX,a.clientY,a,b,c))&&(G4(this,a,b,c),a.preventDefault())}};
var F4=function(a,b,c){var d=_.ss(_.ae(a.Bc));b+=d.x-a.VJ.x;c+=d.y-a.VJ.y;a.VJ=d;a.deltaX+=b;a.deltaY+=c;return new _.os(D4(a,a.deltaX),E4(a,a.deltaY))};_.A4.prototype.Qv=function(a){var b=F4(this,0,0);a.clientX=this.clientX;a.clientY=this.clientY;G4(this,a,b.x,b.y)};
var G4=function(a,b,c,d){a.target.style.left=c+"px";a.target.style.top=d+"px";a.dispatchEvent(new C4("drag",a,b.clientX,b.clientY,b,c,d))},D4=function(a,b){var c=a.SI;a=isNaN(c.left)?null:c.left;c=isNaN(c.width)?0:c.width;return Math.min(a!=null?a+c:Infinity,Math.max(a!=null?a:-Infinity,b))},E4=function(a,b){var c=a.SI;a=isNaN(c.top)?null:c.top;c=isNaN(c.height)?0:c.height;return Math.min(a!=null?a+c:Infinity,Math.max(a!=null?a:-Infinity,b))},C4=function(a,b,c,d,e,f,h){_.qj.call(this,a);this.clientX=
c;this.clientY=d;this.cF=e;this.left=f!==void 0?f:b.deltaX;this.top=h!==void 0?h:b.deltaY};_.eb(C4,_.qj);var H4=function(a){this.Sa=new Map;var b=arguments.length;if(b>1){if(b%2)throw Error("i");for(var c=0;c<b;c+=2)this.set(arguments[c],arguments[c+1])}else a&&this.addAll(a)};_.g=H4.prototype;_.g.Zb=function(){return this.Sa.size};_.g.Xe=function(){return Array.from(this.Sa.values())};_.g.kg=function(){return Array.from(this.Sa.keys())};_.g.El=function(a){return this.Sa.has(a)};
_.g.equals=function(a,b){var c=this;b=b===void 0?function(d,e){return d===e}:b;return this===a?!0:this.Sa.size!=a.Zb()?!1:this.kg().every(function(d){return b(c.Sa.get(d),a.get(d))})};_.g.isEmpty=function(){return this.Sa.size==0};_.g.clear=function(){this.Sa.clear()};_.g.remove=function(a){return this.Sa.delete(a)};_.g.get=function(a,b){return this.Sa.has(a)?this.Sa.get(a):b};_.g.set=function(a,b){this.Sa.set(a,b);return this};
_.g.addAll=function(a){if(a instanceof H4){a=_.Aa(a.Sa);for(var b=a.next();!b.done;b=a.next()){var c=_.Aa(b.value);b=c.next().value;c=c.next().value;this.Sa.set(b,c)}}else if(a)for(a=_.Aa(Object.entries(a)),b=a.next();!b.done;b=a.next())c=_.Aa(b.value),b=c.next().value,c=c.next().value,this.Sa.set(b,c)};_.g.forEach=function(a,b){var c=this;b=b===void 0?this:b;this.Sa.forEach(function(d,e){return a.call(b,d,e,c)})};_.g.clone=function(){return new H4(this)};(function(){for(var a=["ms","moz","webkit","o"],b,c=0;b=a[c]&&!_.Xa.requestAnimationFrame;++c)_.Xa.requestAnimationFrame=_.Xa[b+"RequestAnimationFrame"],_.Xa.cancelAnimationFrame=_.Xa[b+"CancelAnimationFrame"]||_.Xa[b+"CancelRequestAnimationFrame"];if(!_.Xa.requestAnimationFrame){var d=0;_.Xa.requestAnimationFrame=function(e){var f=(new Date).getTime(),h=Math.max(0,16-(f-d));d=f+h;return _.Xa.setTimeout(function(){e(f+h)},h)};_.Xa.cancelAnimationFrame||(_.Xa.cancelAnimationFrame=function(e){clearTimeout(e)})}})();
var I4=[[],[]],J4=0,K4=!1,Tca=0,L4=function(a,b){var c=Tca++,d={lda:{id:c,Qh:a.measure,context:b},tda:{id:c,Qh:a.GX,context:b},state:{},vd:void 0,aB:!1};return function(){arguments.length>0?(d.vd||(d.vd=[]),d.vd.length=0,d.vd.push.apply(d.vd,arguments),d.vd.push(d.state)):d.vd&&d.vd.length!=0?(d.vd[0]=d.state,d.vd.length=1):d.vd=[d.state];d.aB||(d.aB=!0,I4[J4].push(d));K4||(K4=!0,window.requestAnimationFrame(Uca))}},Uca=function(){K4=!1;var a=I4[J4],b=a.length;J4=(J4+1)%2;for(var c,d=0;d<b;++d){c=
a[d];var e=c.lda;c.aB=!1;e.Qh&&e.Qh.apply(e.context,c.vd)}for(d=0;d<b;++d)c=a[d],e=c.tda,c.aB=!1,e.Qh&&e.Qh.apply(e.context,c.vd),c.state={};a.length=0};var Vca=function(a,b){this.ma=a;this.Eb=b};var M4=function(a,b){_.sD.call(this,b);this.Oha=!!a;this.jr=null;this.RZ=L4({GX:this.Gj},this);L4({GX:this.Z1},this)};_.eb(M4,_.sD);_.g=M4.prototype;_.g.yG=null;_.g.kc=!1;_.g.Ya=null;_.g.lb=null;_.g.nh=null;_.g.VE=!1;_.g.Cb=function(){return"shr-a-shr-ne"};_.g.Tl=function(){return this.Ya};
_.g.wa=function(){M4.N.wa.call(this);var a=this.O(),b=_.zc(this.Cb()).split(" ");_.jD(a,b);_.oD(a,!0);_.Ls(a,!1);this.Oha&&!this.lb&&(a=this.Ha().wa("IFRAME",{frameborder:0,style:"border:0;vertical-align:bottom;"}),_.t4(a,_.Ft),this.lb=a,this.lb.className=this.Cb()+"-shr-cc",_.Ls(this.lb,!1),_.Ts(this.lb,0));this.Ya||(this.Ya=this.Ha().wa("DIV",this.Cb()+"-shr-cc"),_.Ls(this.Ya,!1));this.nh||(this.nh=this.Ha().createElement("SPAN"),_.Ls(this.nh,!1),_.oD(this.nh,!0),this.nh.style.position="absolute")};
_.g.QZ=function(){this.VE=!1};_.g.vc=function(){this.lb&&_.qe(this.lb,this.O());_.qe(this.Ya,this.O());M4.N.vc.call(this);_.gt(this.nh,this.O());this.yG=new z4(this.Ha().ub());this.wb().na(this.yG,"focusin",this.FJ);N4(this,!1)};_.g.Yd=function(){this.isVisible()&&this.setVisible(!1);_.cj(this.yG);M4.N.Yd.call(this);_.re(this.lb);_.re(this.Ya);_.re(this.nh)};
_.g.setVisible=function(a){a!=this.kc&&(this.Uo&&this.Uo.stop(),this.Yp&&this.Yp.stop(),this.To&&this.To.stop(),this.Xp&&this.Xp.stop(),this.ob&&N4(this,a),a?this.sN():this.Zu())};var N4=function(a,b){a.CX||(a.CX=new Vca(a.ma,a.Eb));a=a.CX;if(b){a.Uq||(a.Uq=[]);b=a.Eb.LG(a.Eb.ub().body);for(var c=0;c<b.length;c++){var d=b[c];d==a.ma||_.cD(d,"hidden")||(_.bD(d,"hidden",!0),a.Uq.push(d))}}else if(a.Uq){for(b=0;b<a.Uq.length;b++)a.Uq[b].removeAttribute("aria-hidden");a.Uq=null}};
M4.prototype.sN=function(){if(this.dispatchEvent("beforeshow")){try{this.jr=this.Ha().ub().activeElement}catch(a){}this.Z1();this.wb().na(this.Ha().getWindow(),"resize",this.Gj).na(this.Ha().getWindow(),"orientationchange",this.RZ);O4(this,!0);this.focus();this.kc=!0;this.Uo&&this.Yp?(_.Cj(this.Uo,"end",this.GB,!1,this),this.Yp.play(),this.Uo.play()):this.GB()}};M4.prototype.Z1=function(){this.Gj();this.He()};
M4.prototype.Zu=function(){if(this.dispatchEvent("beforehide")){this.wb().Ac(this.Ha().getWindow(),"resize",this.Gj).Ac(this.Ha().getWindow(),"orientationchange",this.RZ);this.kc=!1;this.To&&this.Xp?(_.Cj(this.To,"end",this.Ov,!1,this),this.Xp.play(),this.To.play()):this.Ov();a:{try{var a=this.Ha(),b=a.ub().body,c=a.ub().activeElement||b;if(!this.jr||this.jr==b){this.jr=null;break a}(c==b||a.contains(this.O(),c))&&this.jr.focus()}catch(d){}this.jr=null}}};
var O4=function(a,b){a.lb&&_.Ls(a.lb,b);a.Ya&&_.Ls(a.Ya,b);_.Ls(a.O(),b);_.Ls(a.nh,b)};_.g=M4.prototype;_.g.GB=function(){this.dispatchEvent("show")};_.g.Ov=function(){O4(this,!1);this.dispatchEvent("hide")};_.g.isVisible=function(){return this.kc};_.g.focus=function(){this.iT()};
_.g.Gj=function(){this.lb&&_.Ls(this.lb,!1);this.Ya&&_.Ls(this.Ya,!1);var a=this.Ha().ub();var b=P4(this);var c=Math.max(b.width,Math.max(a.body.scrollWidth,a.documentElement.scrollWidth));a=Math.max(b.height,Math.max(a.body.scrollHeight,a.documentElement.scrollHeight));this.lb&&(_.Ls(this.lb,!0),_.Js(this.lb,c,a));this.Ya&&(_.Ls(this.Ya,!0),_.Js(this.Ya,c,a))};
_.g.He=function(){var a;if(_.Rs(this.O())=="fixed")var b=a=0;else b=_.ss(this.Ha()),a=b.x,b=b.y;var c=_.u4(this.O());var d=P4(this);a=Math.max(a+d.width/2-c.width/2,0);b=Math.max(b+d.height/2-c.height/2,0);_.Ss(this.O(),a,b);_.Ss(this.nh,a,b)};_.g.FJ=function(a){this.VE?this.QZ():a.target==this.nh&&_.sz(this.iT,0,this)};_.g.iT=function(){try{this.O().focus()}catch(a){}};var P4=function(a){a=a.Ha().ub();return _.ge(_.ie(a)||window)};
M4.prototype.ua=function(){_.cj(this.Uo);this.Uo=null;_.cj(this.To);this.To=null;_.cj(this.Yp);this.Yp=null;_.cj(this.Xp);this.Xp=null;M4.N.ua.call(this)};var a5,T4,f5,g5,Wca,Xca;_.U4=function(a,b,c){M4.call(this,b,c);this.hk=a||"shr-bb-shr-cb";this.Hh=_.Q4(_.Q4(new _.R4,_.S4,!0),T4,!1,!0)};_.eb(_.U4,M4);_.g=_.U4.prototype;_.g.iG=!0;_.g.DA=!0;_.g.nJ=!0;_.g.VF=!0;_.g.UE=.5;_.g.GN="";_.g.Kd=null;_.g.mk=null;_.g.En=!1;_.g.Ip=null;_.g.Fs=null;_.g.FN=null;_.g.th=null;_.g.Se=null;_.g.Gh=null;_.g.bw="dialog";_.g.Cb=function(){return this.hk};_.g.setTitle=function(a){this.GN=a;this.Fs&&_.ve(this.Fs,a)};_.g.getTitle=function(){return this.GN};
_.g.getContent=function(){return this.Kd!=null?_.fc(this.Kd).toString():""};_.g.Xn=function(){return this.bw};_.g.fD=function(a){this.bw=a};_.V4=function(a){a.O()||a.va()};_.U4.prototype.Ua=function(){_.V4(this);return this.Se};_.W4=function(a){_.V4(a);return a.Ip};_.X4=function(a){_.V4(a);return a.O()};_.U4.prototype.Tl=function(){_.V4(this);return _.U4.N.Tl.call(this)};_.Y4=function(a,b){a.UE=b;a.O()&&(b=a.Tl())&&_.Ts(b,a.UE)};
_.Z4=function(a,b){a.nJ=b;if(a.ob){var c=a.Ha(),d=a.Tl(),e=a.lb;b?(e&&c.SV(e,a.O()),c.SV(d,a.O())):(c.removeNode(e),c.removeNode(d))}a.isVisible()&&N4(a,b)};_.U4.prototype.setDraggable=function(a){this.VF=a;$4(this,a&&this.ob)};_.U4.prototype.NU=function(){};_.U4.prototype.getDraggable=function(){return this.VF};
var $4=function(a,b){var c=_.zc(a.hk+"-shr-s-shr-le").split(" ");a.O()&&(b?_.jD(a.Ip,c):_.lD(a.Ip,c));b&&!a.mk?(b=new _.A4(a.O(),a.Ip),a.mk=b,_.jD(a.Ip,c),_.Dj(a.mk,"start",a.EL,!1,a),_.Dj(a.mk,"drag",a.NU,!1,a)):!b&&a.mk&&(a.mk.dispose(),a.mk=null)};_.g=_.U4.prototype;
_.g.wa=function(){_.U4.N.wa.call(this);var a=this.O(),b=this.Ha();this.FN=this.getId();var c=this.getId()+".contentEl";this.Ip=b.wa("DIV",this.hk+"-shr-s",this.Fs=b.wa("SPAN",{className:this.hk+"-shr-s-shr-t",id:this.FN},this.GN),this.th=b.wa("SPAN",this.hk+"-shr-s-shr-dc"));_.oe(a,this.Ip,this.Se=b.wa("DIV",{className:this.hk+"-shr-ce",id:c}),this.Gh=b.wa("DIV",this.hk+"-shr-je"));_.aD(this.Fs,"heading");_.aD(this.th,"button");_.oD(this.th,!0);_.bD(this.th,"label","\u5173\u95ed");_.aD(a,this.Xn());
_.bD(a,"labelledby",this.FN||"");this.Kd&&_.Hc(this.Se,this.Kd);_.Ls(this.th,this.DA);this.Hh&&(a=this.Hh,a.ma=this.Gh,a.va());_.Ls(this.Gh,!!this.Hh);_.Y4(this,this.UE)};_.g.vc=function(){_.U4.N.vc.call(this);this.wb().na(this.O(),"keydown",this.fY).na(this.O(),"keypress",this.fY);this.wb().na(this.Gh,"click",this.Fda);$4(this,this.VF);this.wb().na(this.th,"click",this.Zda);var a=this.O();_.aD(a,this.Xn());this.Fs.id!==""&&_.bD(a,"labelledby",this.Fs.id);this.nJ||_.Z4(this,!1)};
_.g.Yd=function(){this.isVisible()&&this.setVisible(!1);$4(this,!1);_.U4.N.Yd.call(this)};_.g.setVisible=function(a){a!=this.isVisible()&&(this.ob||this.va(),_.U4.N.setVisible.call(this,a))};_.g.GB=function(){_.U4.N.GB.call(this);this.dispatchEvent("aftershow")};_.g.Ov=function(){_.U4.N.Ov.call(this);this.dispatchEvent("afterhide");this.En&&this.dispose()};
_.g.EL=function(){var a=this.Ha().ub(),b=_.ge(_.ie(a)||window),c=Math.max(a.body.scrollWidth,b.width);a=Math.max(a.body.scrollHeight,b.height);var d=_.u4(this.O());_.Rs(this.O())=="fixed"?_.B4(this.mk,new _.Qs(0,0,Math.max(0,b.width-d.width),Math.max(0,b.height-d.height))):_.B4(this.mk,new _.Qs(0,0,c-d.width,a-d.height))};_.g.Zda=function(){a5(this)};a5=function(a){if(a.DA){var b=a.Hh,c=b&&b.Ey;c?(b=b.get(c),a.dispatchEvent(new b5(c,b))&&a.setVisible(!1)):a.setVisible(!1)}};
_.c5=function(a){a.DA=!1;a.th&&_.Ls(a.th,a.DA)};_.U4.prototype.AL=_.jb(50);_.U4.prototype.ua=function(){this.Gh=this.th=null;_.U4.N.ua.call(this)};_.d5=function(a,b){a.Hh=b;a.Gh&&(a.Hh?(b=a.Hh,b.ma=a.Gh,b.va()):_.Hc(a.Gh,_.nd),_.Ls(a.Gh,!!a.Hh))};_.U4.prototype.Fda=function(a){a:{for(a=a.target;a!=null&&a!=this.Gh;){if(a.tagName=="BUTTON")break a;a=a.parentNode}a=null}if(a&&!a.disabled){a=a.name;var b=this.Hh.get(a);this.dispatchEvent(new b5(a,b))&&this.setVisible(!1)}};
_.U4.prototype.fY=function(a){var b=!1,c=!1,d=this.Hh,e=a.target;if(a.type=="keydown")if(this.iG&&a.keyCode==27){var f=d&&d.Ey;e=e.tagName=="SELECT"&&!e.disabled;f&&!e?(c=!0,b=d.get(f),b=this.dispatchEvent(new b5(f,b))):e||(b=!0)}else{if(a.keyCode==9&&a.shiftKey&&e==this.O()){this.VE=!0;try{this.nh.focus()}catch(l){}_.sz(this.QZ,0,this)}}else if(a.keyCode==13){if(e.tagName=="BUTTON"&&!e.disabled)f=e.name;else if(e==this.th)a5(this);else if(d){var h=d.Xy,k=h&&_.e5(d,h);e=(e.tagName=="TEXTAREA"||e.tagName==
"SELECT"||e.tagName=="A")&&!e.disabled;!k||k.disabled||e||(f=h)}f&&d&&(c=!0,b=this.dispatchEvent(new b5(f,String(d.get(f)))))}else e!=this.th||a.keyCode!=32&&a.key!=" "||a5(this);if(b||c)a.stopPropagation(),a.preventDefault();b&&this.setVisible(!1)};var b5=function(a,b){this.type="dialogselect";this.key=a;this.caption=b};_.eb(b5,_.qj);_.R4=function(a){H4.call(this);this.Eb=a||_.ae();this.hk="shr-a-shr-ac";this.Ey=this.ma=this.Xy=null};_.eb(_.R4,H4);
_.R4.prototype.clear=function(){H4.prototype.clear.call(this);this.Xy=this.Ey=null};_.R4.prototype.set=function(a,b,c,d){H4.prototype.set.call(this,a,b);c&&(this.Xy=a);d&&(this.Ey=a);return this};_.Q4=function(a,b,c,d){return a.set(b.key,b.caption,c,d)};_.R4.prototype.va=function(){if(this.ma){_.Hc(this.ma,_.nd);var a=_.ae(this.ma);this.forEach(function(b,c){b=a.wa("BUTTON",{name:c},b);c==this.Xy&&(b.className=this.hk+"-shr-jc");this.ma.appendChild(b)},this)}};_.R4.prototype.O=function(){return this.ma};
_.R4.prototype.Ha=function(){return this.Eb};_.e5=function(a,b){a=(a.ma||document).getElementsByTagName("BUTTON");for(var c,d=0;c=a[d];d++)if(c.name==b||c.id==b)return c;return null};_.S4={key:"ok",caption:"\u786e\u5b9a"};T4={key:"cancel",caption:"\u53d6\u6d88"};f5={key:"yes",caption:"\u662f"};g5={key:"no",caption:"\u5426"};Wca={key:"save",caption:"\u4fdd\u5b58"};Xca={key:"continue",caption:"\u7ee7\u7eed"};
typeof document!="undefined"&&(_.Q4(new _.R4,_.S4,!0,!0),_.Q4(_.Q4(new _.R4,_.S4,!0),T4,!1,!0),_.Q4(_.Q4(new _.R4,f5,!0),g5,!1,!0),_.Q4(_.Q4(_.Q4(new _.R4,f5),g5,!0),T4,!1,!0),_.Q4(_.Q4(_.Q4(new _.R4,Xca),Wca),T4,!0,!0));
var hea=function(a){for(var b=typeof a==="string"?a.split(""):a,c=a.length-1;c>=0;c--)if(c in b&&_.v4.call(void 0,b[c],c,a))return c;return-1},S8=function(a){this.action=a;this.wfv=!1},T8=function(a,b){S8.call(this,"set-drive-options");this.appId=a;this.appOrigin=b},U8=function(a){S8.call(this,"visibility");this.visible=a},V8=function(a,b,c){_.U4.call(this,a,b,c)},W8=function(a){a=a&&a.getWindow()||window;return a.gadgets&&a.gadgets.rpc},iea=function(a,b){var c=b&&b.getWindow()||window;if(W8(b))a();
else if(X8)X8.push(a);else var d=X8=[a],e=c.setInterval(function(){if(W8(b)){c.clearInterval(e);for(var f=0;f<d.length;f++)d[f]();X8=null}},100)},jea=function(a,b){var c=new Y8(a);_.fj(b,c);var d={passive:!1};b.na(c,"mousewheel",function(e){return void Z8(a,e)},d).na(a,"scroll",function(e){return void Z8(a,e)},d)},Z8=function(a,b){var c;a:{for(c=b.target;c;){if(c.nodeType==1){var d=_.Bs(c,"overflowY");if(d=="auto"||d=="scroll")break a}c=c.parentNode}c=null}if(!c||!_.ue(a,c)||c.scrollHeight==c.clientHeight||
b.deltaY>0&&Math.abs(c.scrollTop-(c.scrollHeight-c.clientHeight))<=1||b.deltaY<0&&c.scrollTop==0)b.preventDefault(),b.stopPropagation()},b9=function(a,b,c,d,e,f,h,k){k=k===void 0?!1:k;_.U4.call(this,void 0,void 0,void 0);_.U4.call(this,f?f+" picker shr-bb-shr-cb":"picker shr-bb-shr-cb",!0,c);c=a.search(_.xu);f=_.wu(a,"protocol",c);if(!(f<0)){var l=a.indexOf("&",f);if(l<0||l>c)l=c;decodeURIComponent(a.slice(f+9,l!==-1?l:0).replace(/\+/g," "))}this.bI=new $8;this.Zm=_.tu();this.Sm=_.tu();a=_.Ru(a);
a.Og("hostId");c=a.Og("parent");this.e2=c==null?void 0:c.includes("onepick.corp.google.com");k&&_.Qu(a,"fv2","true");this.setUrl(a.toString());this.cZ=d;this.bZ=e;d!==void 0||e!==void 0?d=!0:(d=_.Ru(a.toString()).Og("hostId"),a9||(a9=new Set("DocVerse fusiontables geo geowiki gm gmail-gadget gws hotpot jointly presentations pwa sites templates trix trix-copy-sheet webstore".split(" "))),d=!a9.has(d));this.B7=d;this.Cz=h||this.Ha();b&&(h=this.Cz,b=_.Ru(this.Hd).Og("grugl")=="true"?kea:lea,W8(h)||(h=
h||_.ae(document),d=h.createElement("SCRIPT"),_.Hh(d,b),d.type="text/javascript",h.ub().body.appendChild(d)));b=a.toString();h=null;d=b.indexOf("/picker?");d>-1?h=b.substring(0,d+8-1):_.Mj(b,"/picker")&&(h=b);h&&this.Nj(h+"/resources/rpc_relay.html");this.iG=!1;_.d5(this,null);this.Ab=null},d9=function(a){c9.call(this,a)},f9=function(a){e9.call(this,"upload");this.Ta.query=a},g9=function(){f9.call(this,"docs")},h9=function(a){e9.call(this,a||"all")};_.U4.prototype.AL=_.pb(50,function(a){this.En=a});
_.Zd.prototype.BL=_.pb(0,function(a){this.Bc=a});
var i9=function(a){_.V4(a);return a.Gh},mea=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m){b(m)},h=function(m,n){d--;e[m]=n;d==0&&c(e)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,f,_.bb(h,l));else b(void 0)})},j9=function(a){return _.w4(a,"margin")},nea=function(){var a=null;return(new _.xk(function(b,c){a=_.sz(function(){b(void 0)},5E3);a==-1&&c(Error("xa"))})).DD(function(b){_.tz(a);throw b;})},l9=function(a,b){var c=a.V;return _.wL(a,c,c[_.hK]|0,k9,b,3).length},
m9=function(a){var b=b===void 0?_.KL:b;a=_.LL(a,23);var c=typeof a;a=a==null?a:c==="bigint"?_.yK((0,_.GL)(64,a)):_.nN(a)?c==="string"?_.DP(a):_.CP(a):void 0;return a!=null?a:b},n9=function(a,b,c){return _.uL(a,b,c==null?c:_.oN(c),0)},o9=function(a){this.V=_.fL(a)};_.y(o9,_.NL);o9.prototype.getSeconds=function(){return _.j1(this,1)};o9.prototype.setSeconds=function(a){return _.uL(this,1,_.zN(a,0),"0")};
var p9=function(a){var b=Number;var c=c===void 0?"0":c;var d;var e=(d=_.h1(_.LL(a,1),!0))!=null?d:c;b=b(e);a=_.hO(a,2);return new Date(b*1E3+a/1E6)},q9=function(a){return _.Cd&&(_.Ed||_.Gd)&&a%40!=0?a:a/40},r9=function(a,b,c,d){_.rj.call(this,b);this.type="mousewheel";this.detail=a;this.deltaX=c;this.deltaY=d};_.eb(r9,_.rj);
var Y8=function(a,b){_.Oj.call(this);this.ma=a;a=_.te(this.ma)?this.ma:this.ma?this.ma.body:null;this.Cca=!!a&&_.Ps(a);this.aX=_.Dj(this.ma,_.Bd?"DOMMouseScroll":"mousewheel",this,b)};_.eb(Y8,_.Oj);
Y8.prototype.handleEvent=function(a){var b=0,c=0,d=a.Cf;d.type=="mousewheel"?(a=q9(-d.wheelDelta),d.wheelDeltaX!==void 0?(b=q9(-d.wheelDeltaX),c=q9(-d.wheelDeltaY)):c=a):(a=d.detail,a>100?a=3:a<-100&&(a=-3),d.axis!==void 0&&d.axis===d.HORIZONTAL_AXIS?b=a:c=a);typeof this.sX==="number"&&(b=Math.min(Math.max(b,-this.sX),this.sX));typeof this.tX==="number"&&(c=Math.min(Math.max(c,-this.tX),this.tX));this.Cca&&(b=-b);b=new r9(a,d,b,c);this.dispatchEvent(b)};
Y8.prototype.ua=function(){Y8.N.ua.call(this);_.Kj(this.aX);this.aX=null};var s9=function(a){this.V=_.fL(a)};_.y(s9,_.NL);s9.prototype.getId=function(){return _.iO(this,1)};s9.prototype.Le=function(a){return _.uL(this,1,_.CN(a),"")};var t9=function(a){this.V=_.fL(a)};_.y(t9,_.NL);t9.prototype.getSeconds=function(){return _.j1(this,1)};t9.prototype.setSeconds=function(a){return _.uL(this,1,_.zN(a,0),"0")};var u9=function(a){this.V=_.fL(a)};_.y(u9,_.NL);_.g=u9.prototype;_.g.getUrl=function(){return _.iO(this,1)};_.g.setUrl=function(a){return _.uL(this,1,_.CN(a),"")};_.g.Nc=function(){return _.hO(this,2)};_.g.Sd=function(a){return _.uL(this,2,a==null?a:_.pN(a),0)};_.g.Qb=function(){return _.hO(this,3)};_.g.Me=function(a){return _.uL(this,3,a==null?a:_.pN(a),0)};var v9=function(a){this.V=_.fL(a)};_.y(v9,_.NL);v9.prototype.getDuration=function(){return _.ZN(this,t9,2)};var w9=function(a){this.V=_.fL(a)};_.y(w9,_.NL);var x9=function(a){this.V=_.fL(a)};_.y(x9,_.NL);x9.prototype.On=function(a){return _.IP(this,1,a)};var y9=function(a){this.V=_.fL(a)};_.y(y9,_.NL);var z9=function(a){this.V=_.fL(a)};_.y(z9,_.NL);z9.prototype.RT=function(){return _.iO(this,5)};var A9=function(a){this.V=_.fL(a)};_.y(A9,_.NL);var B9=function(a){this.V=_.fL(a)};_.y(B9,_.NL);var C9=function(a){this.V=_.fL(a)};_.y(C9,_.NL);C9.prototype.hA=function(){return _.iO(this,1)};var D9=function(a){this.V=_.fL(a)};_.y(D9,_.NL);var E9=function(a){this.V=_.fL(a)};_.y(E9,_.NL);var F9=function(a){this.V=_.fL(a)};_.y(F9,_.NL);var G9=[2,4,5,6,7];var H9=function(a){this.V=_.fL(a)};_.y(H9,_.NL);var k9=function(a){this.V=_.fL(a)};_.y(k9,_.NL);_.g=k9.prototype;_.g.getId=function(){return _.ZN(this,s9,1)};_.g.Le=function(a){return _.bO(this,1,a)};_.g.getName=function(){return _.iO(this,2)};_.g.V_=function(a){return _.uL(this,2,_.CN(a),"")};_.g.fU=function(){return _.iO(this,4)};_.g.getType=function(){return _.jO(this,6)};_.g.Td=function(a){return n9(this,6,a)};_.g.getMimeType=function(){return _.iO(this,7)};_.g.getUrl=function(){return _.iO(this,8)};
_.g.setUrl=function(a){return _.uL(this,8,_.CN(a),"")};_.g.WG=function(){return _.iO(this,11)};var I9=function(a){return _.aO(a,u9,10,_.HN())};_.g=k9.prototype;_.g.vU=function(){return _.ZN(this,v9,34)};_.g.iV=function(){return _.ML(this,v9,34)};_.g.Y_=function(a){return _.JP(this,39,a)};_.g.l0=function(a){return _.JP(this,44,a)};_.g.Mq=function(){return _.j1(this,45)};_.g.r0=function(a){return _.uL(this,45,_.zN(a,0),"0")};var J9=function(a){this.V=_.fL(a)};_.y(J9,_.NL);
J9.prototype.vU=function(){return _.ZN(this,v9,3)};J9.prototype.iV=function(){return _.ML(this,v9,3)};J9.prototype.RT=function(){return _.iO(this,10)};var K9=function(a){this.V=_.fL(a)};_.y(K9,_.NL);K9.prototype.Yz=function(){return _.iO(this,2)};K9.prototype.zc=function(a){return _.uL(this,2,_.CN(a),"")};var L9=function(a){this.V=_.fL(a)};_.y(L9,_.NL);_.g=L9.prototype;_.g.getStatus=function(){return _.jO(this,1)};_.g.Vz=function(a){return _.aO(this,k9,2,_.HN(a))};_.g.Xf=function(a){_.xL(this,2,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,2,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,2,k9,a)};_.g.Xh=function(){return l9(this,2)};var M9=function(a){this.V=_.fL(a)};_.y(M9,_.NL);_.g=M9.prototype;_.g.sL=function(a){return n9(this,1,a)};_.g.getVisible=function(){return _.ZO(this,5)};_.g.setVisible=function(a){return _.JP(this,5,a)};_.g.Vz=function(a){return _.aO(this,k9,7,_.HN(a))};_.g.Xf=function(a){_.xL(this,7,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,7,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,7,k9,a)};_.g.Xh=function(){return l9(this,7)};_.g.getQuery=function(){return _.iO(this,8)};
_.g.hb=function(a){return _.uL(this,8,_.CN(a),"")};_.g.Fz=function(){return _.iO(this,9)};_.g.mp=function(a){return _.uL(this,9,_.CN(a),"")};var N9=function(a){this.V=_.fL(a)};_.y(N9,_.NL);_.g=N9.prototype;_.g.sL=function(a){return n9(this,1,a)};_.g.Vz=function(a){return _.aO(this,k9,4,_.HN(a))};_.g.Xf=function(a){_.xL(this,4,k9,void 0,a)};_.g.setItem=function(a,b){return _.$N(this,4,k9,a,b)};_.g.removeItem=function(a){return _.HP(this,4,k9,a)};_.g.Xh=function(){return l9(this,4)};var oea=_.i1(N9);var O9=new Map;O9.set("application/vnd.google-apps.document","application/vnd.google-gsuite.document-blob");O9.set("application/vnd.google-apps.spreadsheet","application/vnd.google-gsuite.spreadsheet-blob");O9.set("application/vnd.google-apps.presentation","application/vnd.google-gsuite.presentation-blob");var P9=new Set;P9.add("application/vnd.google-apps.kix");P9.add("application/vnd.google-apps.ritz");P9.add("application/vnd.google-apps.punch");var yea=function(a,b,c){var d=c&&_.iO(c,1);c=Q9(c);var e=a[0].getId();switch(_.jO(e,2)){case 13:return pea(a,b,d,c);case 3:return qea(a,b,d,c);case 10:return R9(a,b,d,c);case 27:return R9(a,b,d,c);case 12:return rea(a,b);case 2:return sea(a,b,d,c);case 5:return tea(a,b,d,c);case 9:return uea(a,b,d,c);case 6:return vea(a,b,d,c);case 25:return wea(a,b,d,c);case 1:return _.ML(a[0],w9,26)?xea(a,b,d,c):R9(a,b,d,c);default:return{}}},vea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),
serviceId:"youtube",name:e.getName(),description:_.iO(e,3),type:S9(e),lastEditedUtc:p9(_.ZN(e,o9,16)).getTime(),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",url:e.getUrl(),embedUrl:_.iO(e,13),thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"youtube",v2Translated:!0}},sea=function(a,b,c,d){var e=a.map(function(f){var h=f.getId().getId(),k=f.getMimeType(),l=f.getName(),m=S9(f),n=p9(_.ZN(f,o9,12)).getTime(),p=f.getUrl(),q=Number(m9(f)),r=_.iO(f,3),
w=f.WG(),u=T9(I9(f));var x=_.ZN(f,J9,21);x=_.ZO(x,1);var A=_.ZN(f,J9,21).RT(),D=_.iO(f,13);var E=_.ZN(f,J9,21);E=_.ZO(E,9);var N=_.ZN(f,J9,21);N=_.iO(N,11);h={id:h,serviceId:"docs",mimeType:k,name:l,type:m,lastEditedUtc:n,url:p,sizeBytes:q,description:r,iconUrl:w,thumbnails:u,isShared:x,downloadUrl:A,embedUrl:D,copyable:E,resourceKey:N};k=_.ZN(f,J9,21);_.ZO(k,12)&&(h.uploadState="success",h.isNew=!0);_.ZN(f,J9,21).iV()&&(f=_.ZN(f,J9,21).vU(),_.ML(f,t9,2)&&(k=_.YO(f.getDuration().getSeconds()),l=f.getDuration(),
l=_.hO(l,2),h.duration=k+l/1E9),h.aspectRatio=_.x4(f,1));return h});return{action:b,docs:e,viewToken:d,view:c||"all",extraUserInputs:U9(a),v2Translated:!0}},tea=function(a,b,c,d){a=a.map(function(e){var f=_.ZN(e,H9,15);f=_.ZN(f,u9,1);_.xL(e,10,u9,void 0,f);return{id:e.getId().getId(),serviceId:"web",mimeType:e.getMimeType(),name:e.getName(),type:S9(e),url:e.getUrl(),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,
docs:a,viewToken:d,view:c||"image-search",v2Translated:!0}},uea=function(a,b,c,d){a=a.map(function(e){return{serviceId:"url",name:I9(e)[0].getUrl().split("/").pop(),type:S9(e),mimeType:e.getMimeType(),url:e.getUrl(),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"url",v2Translated:!0}},xea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),serviceId:"picasa",name:e.getName(),
type:S9(e),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"webcam",v2Translated:!0}},R9=function(a,b,c,d){return{action:b,docs:a.map(zea),viewToken:d,view:Aea(c||"upload",a),extraUserInputs:U9(a),v2Translated:!0}},Aea=function(a,b){return b.some(function(c){var d;return((d=_.ZN(c,F9,30))==null?void 0:_.jO(d,3))===9})?a+"/gmailphotos":a},qea=function(a,b,c,d){var e=a.map(function(f){var h=
{id:f.getId().getId(),serviceId:"picasa",mimeType:f.getMimeType(),name:f.getName(),type:S9(f),lastEditedUtc:p9(_.ZN(f,o9,12)).getTime(),url:f.getUrl(),sizeBytes:Number(m9(f)),description:_.iO(f,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(f)),mediaKey:f.getId().getId(),parentId:f.fU()},k,l=(k=_.ZN(f,z9,18))==null?void 0:_.ZN(k,y9,3);l&&(h.latitude=_.x4(l,1),h.longitude=_.x4(l,2));var m;f=(m=_.ZN(f,z9,18))==null?void 0:_.j1(m,6);m=f!=null?_.YO(f):
void 0;m&&(h.version=m);return h});return{action:b,docs:e,viewToken:d,view:c||"photos",extraUserInputs:U9(a),v2Translated:!0}},pea=function(a,b,c,d){a=a.map(function(e){return{id:e.getId().getId(),serviceId:"et",name:I9(e)[0].getUrl().split("/").pop(),description:_.iO(e,3),type:"et",iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(e),"etjpg")}});return{action:b,docs:a,viewToken:d,view:c||"et",v2Translated:!0}},wea=function(a,b,c,d){a=a.map(function(e){var f=
e.getId().getId(),h=d["2"];return{id:f,serviceId:h.type!=="gmail_themes"&&h.parent==="6226252643674576769"?"picasa":"static_themes",name:e.getName(),mimeType:e.getMimeType(),type:S9(e),description:_.iO(e,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",url:e.getUrl(),embedUrl:e.getUrl(),thumbnails:T9(I9(e))}});return{action:b,docs:a,viewToken:d,view:c||"photos",v2Translated:!0}},rea=function(a,b){a=a.map(function(c){var d=_.ZN(c,x9,27);d=_.EP(d,1,_.HN())[0];return{id:c.getId().getId(),
serviceId:"contacts",mimeType:c.getMimeType(),name:c.getName(),description:_.iO(c,3),url:"mailto:"+d,thumbnail:[{url:c.WG()}],email:d}});return{action:b,docs:a,view:"contacts",v2Translated:!0}},Q9=function(a){if(!a)return{};var b=_.iO(a,3);b=b&&JSON.parse(b)||{};return{0:_.iO(a,1),1:a.Yz(),2:b}},T9=function(a,b){b=b===void 0?"":b;if(!a.length)return null;b=="etjpg"&&(a=a.filter(function(c){return c.getUrl().includes("w1200-h300")}));return a.map(function(c){return{url:c.getUrl(),height:c.Nc(),width:c.Qb(),
type:b}})},U9=function(a){return{isAttachment:a.some(function(b){return _.ZO(b,31)})}},zea=function(a){var b,c={id:(b=a.getId())==null?void 0:b.getId(),serviceId:Bea(a),mimeType:a.getMimeType(),name:a.getName(),type:S9(a),sizeBytes:Number(m9(a)),description:_.iO(a,3),iconUrl:"https://ssl.gstatic.com/docs/doclist/images/icon_10_generic_list.png",thumbnails:T9(I9(a)),isNew:!0};if(b=_.ZN(a,F9,30)){c.dataUrl=_.iO(b,1)||null;a:{var d=_.ZN(a,F9,30);switch(_.jO(d,3)){case 9:d=_.y4(d,D9,2,G9);d={remoteRefs:d&&
_.iO(d,1)};break a;case 10:case 15:d=_.y4(d,C9,4,G9);var e=I9(a);d={photo_id:d&&d.hA(),media_key:a.getId().getId(),media_type:a.getType()===1?1:2,image_url:e&&e[0].getUrl(),width:e&&String(e[0].Qb()),height:e&&String(e[0].Nc())};break a}d=null}c.uploadMetadata=d;c.uploadId=_.iO(b,9)||null;c.uploadState=Cea(b)||null}(d=b&&_.y4(b,C9,4,G9))&&(c.id=d.hA());(d=b&&_.y4(b,E9,5,G9))&&(c.contentId=_.iO(d,1));(d=b&&_.y4(b,A9,7,G9))&&(c.contentId=_.iO(d,1));(d=b&&_.y4(b,B9,6,G9))&&(c.contentId=_.iO(d,1));(b==
null?void 0:_.jO(b,3))===1&&(c.mediaKey=a.getId().getId());return c},Bea=function(a){var b;switch(((b=_.ZN(a,F9,30))==null?void 0:_.jO(b,3))||0){case 1:return"picasa";case 2:case 3:case 4:case 8:case 12:return"photo";case 10:case 15:return"dragonflyphotos";case 11:return"mapspro";case 13:return"books";case 14:return"cultural"}return null},S9=function(a){switch(a.getType()){case 1:case 21:return"photo";case 2:return"video";case 6:return"folder";case 13:return"calendar";case 14:return"album";case 19:return"contact";
case 3:case 4:case 5:case 7:case 12:return a.getMimeType().startsWith("application/vnd.google-apps.")?"document":"file";default:return"file"}},Cea=function(a){switch(a==null?void 0:_.jO(a,8)){case 0:return"default";case 1:return"canceled";case 2:return"error";case 3:return"running";case 4:return"scheduled";case 5:return"success"}return null};var Dea=_.gd(["https://apis.google.com/js/api.js"]),Eea=_.vc(Dea),Fea=function(a,b){var c=!1,d=V9(a,b).then(function(f){c=!0;return f}),e=nea().then(function(){return c?V9(a,b):W9(b).then(function(f){return X9(f,a)})});return mea([d,e])},V9=function(a,b){return Gea(b).then(function(c){return X9(c,a)})},X9=function(a,b){var c=_.wc(b,"gapi.")?b.slice(5):b;return a[c]?_.Bk(a[c]):new _.xk(function(d,e){var f=_.sz(function(){e(Error("ed"))},3E4);a.load(b,{callback:function(){_.tz(f);d(a[c])},onerror:function(h){_.tz(f);
e(h)}})})},Gea=function(a){return a.gapi&&a.gapi.load?_.Bk(a.gapi):W9(a)},W9=function(a){return _.OP(Eea,{document:a.document}).then(function(){return a.gapi})},Y9=function(){this.Ab=null};Y9.prototype.load=function(a,b){b=b===void 0?window:b;var c=Date.now();return Fea(a,b).then(function(d){return{VW:d,Uca:c,Rca:Date.now()}},function(d){throw d instanceof Error?d:Error(String(d));})};var $8=function(){_.dj.call(this);this.M9=new Y9;this.Ab=this.Cd=null;this.uq=_.Hk();this.Ez=_.Hk();this.Ez.promise.then(null,function(){});this.uq.promise.then(null,function(){});this.rV={}};_.y($8,_.dj);
var Z9=function(a,b){var c=_.wb("gapi.iframes",b);return c?(a=Date.now(),_.Bk({VW:c,Uca:a,Rca:a})):a.M9.load("gapi.iframes",b)},Hea=function(a,b,c,d,e,f){var h=!1;h=h===void 0?!1:h;Z9(a,_.ie(b.ownerDocument)).then(function(k){var l=k.VW;a.Ez.resolve(l);k=a.uq;var m=k.resolve,n=h;n=n===void 0?!1:n;var p={};p["host-message-handler"]=c;a.rV=p;var q=_.Ru(d);q=_.Ru(q);q=_.Fu(_.Eu(_.Cu(new _.Bu,q.Bi),q.Mg()),q.Ag).toString();a.Cd=q;q=_.to((new _.uo).setUrl(d),b);q.T.allowPost=n;n=_.ek(_.dk(q,p),_.wb("makeWhiteListIframesFilter",
l)([a.Cd]));e!=null&&n.Le(e);f!=null&&n.Xm(f);n.Tn().Di({display:"block","min-width":"100%",width:"1px"}).Sd("100%");n.Tn().value().allow="camera 'src' "+a.Cd;l=l.getContext().openChild(n.value());m.call(k,l)},function(k){a.Ez.reject(k);a.uq.reject(k)});return a.uq.promise},Iea=function(a,b){return _.Fk([a.Ez.promise,a.uq.promise]).then(function(c){var d=_.Aa(c);c=d.next().value;return(d=d.next().value)?d.send("picker-message-handler",b,void 0,_.wb("makeWhiteListIframesFilter",c)([a.Cd])):_.Ck(Error("fd"))})};
$8.prototype.ua=function(){Jea(this);_.dj.prototype.ua.call(this)};var Jea=function(a){a.uq.promise.then(function(b){b&&(b.unregister("host-message-handler"),delete a.rV["host-message-handler"])})};var $9=function(){this.yda=0};$9.prototype.getUniqueId=function(){return":"+(this.yda++).toString(36)};var Lea=function(a,b){switch(a.action){case "select-contacts":a=Kea(a.contacts);break;case "visibility":a=a.visible;a=(new M9).sL(7).setVisible(a);var c={};c["iframe-command"]=_.IN(a);a=c;break;default:a=null}return a?Iea(b,a):_.Bk()},Kea=function(a){if(!a||a.length==0)return null;var b=new M9;b.sL(11);var c=new $9;a.forEach(function(d){if(d.email){var e=(new k9).V_(d.name?d.name:d.email);var f=new x9;d=_.FP(f,1,d.email);e=_.bO(e,27,d);d=e.Le;f=(new s9).Le(c.getUniqueId());f=n9(f,2,12);e=d.call(e,
f)}else e=null;e&&b.Xf(e)});a={};a["iframe-command"]=_.IN(b);return a};_.eb(T8,S8);_.eb(U8,S8);_.eb(V8,_.U4);var Mea=_.gd(["//www-onepick-opensocial.googleusercontent.com/gadgets/js/rpc.js?c=1&container=onepick"]),Nea=_.gd(["//apis.google.com/js/rpc.js"]),lea=_.vc(Mea),kea=_.vc(Nea),X8=null;var a9;var Oea=_.gd(["https://about:blank"]),Pea=_.gd(['javascript:""']),Qea=_.gd(["about:blank"]);_.eb(b9,V8);_.g=b9.prototype;_.g.Hd="";_.g.Ja=null;_.g.KA=!1;_.g.YI=!1;_.g.Yb=function(){};_.g.vc=function(){b9.N.vc.call(this);jea(this.Tl(),this.wb())};_.g.va=function(a){var b=this;b9.N.va.call(this,a);this.e2?Z9(this.bI,window).then(function(){return Rea(b)}).then(null,function(c){return void b.Pq(c)}):Sea(this)};
var Rea=function(a){var b=a.Ha().wa("div",["picker-dialog-content","picker-frame"]);_.Ls(b,!1);a.Ua().appendChild(b);return Hea(a.bI,b,function(c){var d=oea(c["iframe-command"]);switch(_.jO(d,1)){case 1:c=a.Yb;d=_.ZN(d,L9,2);var e="";switch(d.getStatus()){case 2:e="cancel";break;case 1:e="picked";break;case 3:e="error"}var f=_.GP(d.Vz(_.AL));d=f.length===0?{action:e}:yea(f,e,_.ZN(d,K9,7));c.call(a,d);a.setVisible(!1);break;case 4:_.c5(a);a.Yb({action:"loaded"});break;case 7:case 8:c=a.Yb;a:{e=_.jO(d,
1);f=_.ZN(d,L9,2);switch(e){case 7:e="uploadScheduled";break;case 8:e="uploadStateChange";break;default:d={};break a}d=_.GP(f.Vz(_.AL));if(d.length===0)d={action:e};else{var h=_.ZN(f,K9,7);f=h&&_.iO(h,1);h=Q9(h);d=R9(d,e,f,h)}}c.call(a,d)}},a.Hd,a.Zm,a.Sm).then(function(c){a.Ja=c.getIframeEl();a$(a);_.Ls(a.Ja.parentElement,!0);b$(a);c$(a);return c})},b$=function(a){_.iD(a.O(),"picker-dialog");_.iD(a.Ja,"picker-dialog-frame");_.iD(_.W4(a),"picker-dialog-title");_.iD(a.Tl(),"picker-dialog-bg");_.iD(a.lb,
"picker-dialog-bg");_.iD(a.Ua(),"picker-dialog-content");i9(a)&&_.iD(i9(a),"picker-dialog-buttons")},Sea=function(a){a.Ja=a.Eb.wa("IFRAME",{id:a.Zm,name:a.Zm,"class":"picker-frame",frameBorder:"0",allow:"camera"});_.t4(a.Ja,Tea(a));b$(a);a.Ua().appendChild(a.Ja);a.wb().na(a.Ja,"load",function(){return void a$(a)});a.Ja.src=a.Hd;c$(a)},Uea=function(a){d$(a,(0,_.z)(function(b){b.setAuthToken(this.Zm,this.Sm)},a))},c$=function(a){var b=_.Ru(a.Hd).Og("title");b&&a.setTitle(b)},Tea=function(a){return _.QO("Internet Explorer")>=
7&&_.wc(a.Hd,"https")?_.vc(Oea):_.yd?_.vc(Pea):_.vc(Qea)},a$=function(a){Uea(a);a.KA=!0;_.oD(a.Ja,!0);a.isVisible()&&a.focus()};b9.prototype.IH=function(a){a.keyCode==27&&(this.setVisible(!1),this.Yb({action:"cancel"}),a.stopPropagation(),a.preventDefault())};var d$=function(a,b){var c=a.Cz;iea(function(){b(c.getWindow().gadgets.rpc)},c)};_.g=b9.prototype;
_.g.setUrl=function(a){a=_.Qu(new _.Bu(a),"rpcService",this.Zm);_.Qu(a,"rpctoken",this.Sm);a.Tk("rpctoken="+this.Sm);_.Qu(a,"thirdParty","true");_.Cu(a,"https");this.Hd=a.toString();this.Ja&&(this.Ja.src=this.Hd)};_.g.mp=function(a){this.gy=a;this.YI&&e$(this)};_.g.Vr=function(a){this.Yb=a;d$(this,(0,_.z)(function(b){b.register(this.Zm,(0,_.z)(this.Oaa,this))},this))};
_.g.Oaa=function(a){var b=a.action;b=="loaded"&&(this.YI=!0,f$(this,new U8(this.isVisible())),e$(this),_.c5(this),this.wb().Ac(this.Ha().getWindow(),"keydown",this.IH),_.oD(this.Ja,!0));b!="picked"&&b!="cancel"||this.setVisible(!1);this.Yb(a)};_.g.zA=function(a){return _.Ru(this.Hd).Og(a)=="true"};var e$=function(a){a.gy&&f$(a,new T8(a.gy,window.location.protocol+"//"+window.location.host))};b9.prototype.Nj=function(a){d$(this,(0,_.z)(function(b){b.setRelayUrl(this.Zm,a)},this))};
var f$=function(a,b){a.e2&&Lea(b,a.bI).then(null,function(c){return void a.Pq(c)});d$(a,(0,_.z)(function(c){c.call(this.Zm,"picker",null,b)},a))};_.g=b9.prototype;
_.g.HK=function(){var a=this.Ha().ub();a=_.ie(a)||window;if(this.zA("ignoreLimits"))a=new _.rd(this.cZ,this.bZ);else if(this.zA("shadeDialog")){var b=j9(_.X4(this)),c=_.ge(a);a=c.width-80;c=c.height-40;b&&(a-=b.left?b.left:0,a-=b.right?b.right:0,c-=b.top?b.top:0,c-=b.bottom?b.bottom:0);a=new _.rd(a>0?a:0,c>0?c:0)}else(b=this.cZ)?(b=Math.max(320,Math.min(1051,b)),(c=this.bZ)||(c=_.ge(a).height*.85),c=Math.max(480,Math.min(650,c))):(b=_.ge(a),c=b.width*.618,c=c<b.height?Math.round(Math.max(480,Math.min(650,
c*.85))):Math.round(Math.max(480,Math.min(650,b.height*.85))),b=Math.round(c/.618)),a=_.ge(a),b=Math.min(b,Math.max(a.width,320)),c=Math.min(c,Math.max(a.height,480)),a=new _.rd(b,c);_.Js(this.Ua(),a);this.He()};_.g.He=function(){if(this.zA("shadeDialog")){var a=_.u4(this.O()),b=_.ge(this.Ha().getWindow());a=Math.floor(b.width/2-a.width/2);if(b=j9(_.X4(this))){var c=b.left?b.left:0;c+=b.right?b.right:0;a=Math.floor(a-c/2)}a=a>0?a:0;b=_.ss(this.Ha()).y;_.Ss(this.O(),a,b)}else b9.N.He.call(this)};
_.g.setVisible=function(a){if(a!=this.isVisible()&&this.B7){var b=this.Ha().getWindow();a?(this.HK(),this.wb().na(b,"resize",this.HK),this.KA||this.wb().na(b,"keydown",this.IH)):(this.wb().Ac(b,"resize",this.HK),this.KA||this.wb().Ac(b,"keydown",this.IH))}b9.N.setVisible.call(this,a);f$(this,new U8(a))};_.g.focus=function(){b9.N.focus.call(this);if(this.Ja&&this.KA&&this.YI)try{this.Ja.focus()}catch(a){}};_.g.Pq=function(){this.Yb({action:"error"})};var e9=function(a){this.Da=a;this.Ta={}};_.g=e9.prototype;_.g.zc=function(a){this.LW=a||void 0;return this};_.g.setOptions=function(a){this.Ta=a;return this};_.g.hb=function(a){this.Ta.query=a;return this};_.g.Mc=function(a){this.Ta.mimeTypes=a;return this};_.g.Nb=function(a){this.Ta.parent=a;return this};_.g.D1=function(){var a=_.gi(this.Ta,function(b){return b!==null});a=_.Gh(a)?null:a;a=[this.Da,this.LW,a];return a=a.slice(0,hea(a)+1)};
_.g.toString=function(){var a=this.D1();return"("+_.Ib(a,function(b){return JSON.stringify(b)}).join(",")+")"};_.g.getId=function(){return this.Da};_.g.Yz=function(){return this.LW};_.g.getOptions=function(){return _.fk(this.Ta)};_.g.getQuery=function(){return this.Ta.query};var g$=function(){e9.call(this,"image-search");this.Ta.license="crwm"};_.y(g$,e9);_.g=g$.prototype;_.g.Ew=function(a){this.Ta.site=a;return this};_.g.Td=function(a){this.Ta.type=a;return this};_.g.Fga=function(a){a=="*"?delete this.Ta.license:this.Ta.license=a;return this};_.g.setSize=function(a){this.Ta.imgsz=a;return this};_.g.setColor=function(a){this.Ta.imgcolor=a;return this};_.g.mD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};
_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var h$=function(){e9.call(this,"maps")};_.y(h$,e9);_.g=h$.prototype;_.g.bD=function(a){this.Ta.mode=a;return this};_.g.setCenter=function(a,b){this.Ta.center=[a,b];return this};_.g.setZoom=function(a){this.Ta.zoom=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var i$=function(){e9.call(this,"photos")};_.y(i$,e9);_.g=i$.prototype;_.g.bD=function(a){this.Ta.mode=a;return this};_.g.Td=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var j$=function(){e9.call(this,"url")};_.y(j$,e9);_.g=j$.prototype;_.g.Ew=function(a){this.Ta.site=a;return this};_.g.Td=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var k$=function(){e9.call(this,"video-search")};_.y(k$,e9);_.g=k$.prototype;_.g.Ew=function(a){this.Ta.site=a;return this};_.g.mD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var l$=function(){e9.call(this,"web")};_.y(l$,e9);_.g=l$.prototype;_.g.Ew=function(a){this.Ta.site=a;return this};_.g.mD=function(a){this.Ta.safe=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var m$=function(a){this.Rd=typeof a==="string"?new e9(a):a;this.Lf=[];this.Ta={}},Vea=function(a){switch(a){case "image-search":return new g$;case "maps":return new h$;case "photos":return new i$;case "url":return new j$;case "video-search":return new k$;case "web":return new l$}return new e9(a)};m$.prototype.Tx=function(a){this.Lf.push(typeof a==="string"?Vea(a):a);return this};m$.prototype.uP=function(a){this.Lf.push((new e9(null)).zc(a));return this};
m$.prototype.Ux=function(a){this.Lf.push(a);return this};var n$=function(a){return"("+_.Ib(a.Lf,function(b){return b.toString()}).join(",")+")"},Wea=function(a){a=_.gi(a.Ta,function(b){return b!==null});return(a=_.Gh(a)?null:a)?JSON.stringify(_.FH(a,function(b){return b.toString()})):""};m$.prototype.toString=function(){if(this.Rd){var a=["{root:",this.Rd.toString(),",items:",n$(this)],b=Wea(this);b&&(a.push(",options:"),a.push(b));a.push("}");return a.join("")}return n$(this)};
m$.prototype.getOptions=function(){return _.fk(this.Ta)};m$.prototype.nga=function(a){this.Ta.collapsible=a;return this};var c9=function(a){this.E7=a||"https://docs.google.com/picker";this.BB=new m$};_.g=c9.prototype;_.g.BB=null;_.g.yd=null;_.g.uP=function(){throw Error("gd");};_.g.Tx=function(a){this.BB.Tx(a);return this};_.g.Ux=function(a){this.BB.Ux(a);return this};_.g.Fz=function(){return this.gy};_.g.Ha=function(){return this.yd};_.g.Bba=function(){return this.setTitle("")};_.g.mp=function(a){this.gy=a;return this};_.g.Vr=function(a){this.Yb=a;return this};_.g.BL=function(a){this.yd=new _.Zd(a);return this};_.eb(d9,c9);var o$=function(a,b){b=b===void 0?b9:b;c9.call(this,a);this.tea=b;this.jc=new Map;this.jc.set("protocol","gadgets");window.google&&(a=(a=window.google)&&a.picker&&a.picker.LoadArgs)&&(a=(new _.Hu(a)).get("hl"))&&this.bs(a.toString());(a=window.location.origin)||(a=window.location.protocol+"//"+window.location.host);this.Lj(a);this.YY=[];this.Cz=void 0};_.y(o$,d9);_.g=o$.prototype;_.g.En=!1;
_.g.Jb=function(){this.jc.set("hostId",window.location.host.split(":")[0]);this.Fz()&&this.jc.has("oauth_token")&&this.jc.set("appId",this.Fz());this.Fu()||this.Nj(_.Gu(_.Ru(window.location.href)).Tk("").setPath("/favicon.ico").toString());this.jc.set("ifls",Date.now());if(this.jc.get("minimal"))throw Error("hd");var a=new this.tea(this.G1().toString(),!0,this.Ha(),this.tR,this.sR,"",this.Cz,!1,!1);a.AL(this.En);a.mp(this.Fz());a.Vr(this.Yb);return a};_.g.G8=function(a){this.jc.delete(a);return this};
_.g.a9=function(a){this.jc.set(a,"true");return this};_.g.Nc=function(){return this.sR};_.g.Vn=function(){return this.jc.get("hl")};_.g.Fu=function(){return this.jc.get("parent")};_.g.getTitle=function(){return this.jc.get("title")};_.g.Qb=function(){return this.tR};_.g.sca=function(a){return this.jc.get(a)=="true"};_.g.pga=function(a){this.jc.set("developerKey",a);return this};_.g.lga=function(a){this.jc.set("authuser",a);return this};_.g.AL=function(a){this.En=a};
_.g.Gga=function(a){this.jc.set("maxItems",a);return this};_.g.Lj=function(a){a&&this.jc.set("origin",a);return this};_.g.Aga=function(a){a instanceof e9?this.jc.set("view",a.toString()):this.jc.set("view",a);return this};_.g.bs=function(a){this.jc.set("hl",a);return this};_.g.nM=function(a){this.jc.set("oauth_token",a);return this};_.g.Nj=function(a){this.jc.set("parent",a);return this};_.g.Mga=function(a){this.jc.set("selectableMimeTypes",a);return this};
_.g.setSize=function(a,b){this.tR=a;this.sR=b;return this};_.g.Rga=function(a){this.jc.set("uploadToAlbumId",a);return this};_.g.setTitle=function(a){this.jc.set("title",a);return this};_.g.G1=function(){this.YY.length&&this.jc.set("pp",JSON.stringify(this.YY));this.jc.set("nav",n$(this.BB));var a=new _.Bu(this.E7);this.jc.forEach(function(b,c){_.Qu(a,c,b)});return a};_.eb(f9,e9);f9.prototype.hb=function(){throw Error("id");};f9.prototype.zc=function(a){f9.N.zc.call(this,a);return this};f9.prototype.Mc=function(a){f9.N.Mc.call(this,a);return this};f9.prototype.Nb=function(a){f9.N.Nb.call(this,a);return this};_.eb(g9,f9);_.g=g9.prototype;_.g.WL=function(a){this.Ta.includeFolders=a;return this};_.g.zc=function(a){g9.N.zc.call(this,a);return this};_.g.hb=function(a){g9.N.hb.call(this,a);return this};_.g.Mc=function(a){g9.N.Mc.call(this,a);return this};_.g.Nb=function(a){g9.N.Nb.call(this,a);return this};_.eb(h9,e9);var Xea=["dr","fileIds","parent"];_.g=h9.prototype;_.g.bD=function(a){this.Ta.mode=a;return this};_.g.rga=function(a){p$(this);this.Ta.dr=a;return this};_.g.sga=function(a){this.Ta.td=a;return this};_.g.Y_=function(a){a!==void 0?this.Ta.ownedByMe=a:delete this.Ta.ownedByMe;return this};_.g.l0=function(a){this.Ta.starred=a;return this};_.g.WL=function(a){this.Ta.includeFolders=a;return this};_.g.Lga=function(a){this.Ta.selectFolder=a;return this};
_.g.qga=function(a){this.Ta.docTypesDropDown=a;return this};_.g.zc=function(a){h9.N.zc.call(this,a);return this};_.g.hb=function(a){h9.N.hb.call(this,a);return this};_.g.Mc=function(a){h9.N.Mc.call(this,a);return this};_.g.wga=function(a){p$(this);this.Ta.fileIds=a;return this};_.g.Nb=function(a){p$(this);h9.N.Nb.call(this,a);return this};var p$=function(a){for(var b=_.Aa(Xea),c=b.next();!c.done;c=b.next())c=c.value,a.Ta[c]&&delete a.Ta[c]};var q$=function(){e9.call(this,"photo-albums")};_.y(q$,e9);_.g=q$.prototype;_.g.Td=function(a){this.Ta.type=a;return this};_.g.zc=function(a){e9.prototype.zc.call(this,a);return this};_.g.hb=function(a){e9.prototype.hb.call(this,a);return this};_.g.Mc=function(a){e9.prototype.Mc.call(this,a);return this};_.g.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var Yea={DoclistBlob:"file",doc:"document",drawing:"drawing",folder:"folder",kix:"document",pres:"presentation",spread:"spreadsheet"};var r$=function(a,b){e9.call(this,"webcam");a&&(this.Ta.type=a);b&&(this.Ta.query=b)};_.y(r$,e9);r$.prototype.hb=function(){throw Error("jd");};r$.prototype.zc=function(a){e9.prototype.zc.call(this,a);return this};r$.prototype.Mc=function(a){e9.prototype.Mc.call(this,a);return this};r$.prototype.Nb=function(a){e9.prototype.Nb.call(this,a);return this};var Zea=_.gd([".picker-dialog-frame{width:100%;height:100%;border:0;overflow:hidden}.picker-dialog-bg{position:absolute;top:0;left:0;background-color:#fff;z-index:1000}.picker-dialog{position:absolute;top:0;left:0;background-color:#fff;border:1px solid #acacac;width:auto;padding:0;z-index:1001;overflow:auto;box-shadow:0 4px 16px rgba(0,0,0,.2)}.picker-dialog-content{height:100%;font-size:0;padding:0}.picker-dialog-buttons,.picker-dialog-title{display:none}"]),$ea=_.hc(Zea[0]);try{_.Ns($ea)}catch(a){_.Vf.error("Failed to install picker styles : "+a),_.Xa.setTimeout(function(){_.Ta.hX(a)},0)};_.t("gapi.picker.api.Action",{Jia:"cancel",uma:"picked",xoa:"uploadProgress",yoa:"uploadScheduled",Aoa:"uploadStateChange",nla:"loaded",Aia:"blurred",Zja:"enableWhiteCallout",Ioa:"viewChanged",Noa:"viewUpdated",Joa:"viewContentRendered",ERROR:"error",Mma:"received"});_.t("gapi.picker.api.Action.CANCEL","cancel");_.t("gapi.picker.api.Action.PICKED","picked");_.t("gapi.picker.api.Audience",{nma:"ownerOnly",vO:"limited",nia:"allPersonalCircles",fka:"extendedCircles",Ija:"domainPublic",KO:"public"});
_.t("gapi.picker.api.Audience.PUBLIC","public");_.t("gapi.picker.api.Audience.DOMAIN_PUBLIC","domainPublic");_.t("gapi.picker.api.Audience.EXTENDED_CIRCLES","extendedCircles");_.t("gapi.picker.api.Audience.ALL_PERSONAL_CIRCLES","allPersonalCircles");_.t("gapi.picker.api.Audience.LIMITED","limited");_.t("gapi.picker.api.Audience.OWNER_ONLY","ownerOnly");
_.t("gapi.picker.api.Document",{sia:"audience",zia:"blobId",Mia:"children",j3:"contentId",Wia:"copyable",rja:"coverPhotoId",uja:"crop",wja:"customerId",zja:"dataUrl",Cja:"description",Jja:"domainUsersOnly",Lja:"downloadUrl",Sja:"driveSuccess",Qja:"driveError",Oja:"drivesId",Pja:"drivesName",Tja:"email",Uja:"embedUrl",Rka:"iconUrl",qE:"id",Tka:"isLocalProfilePhoto",Uka:"isNew",Vka:"isRoot",Wka:"isShared",Xka:"kansasVersionInfo",hla:"lastEditedUtc",ila:"lastModifiedByMeUtc",jla:"lastViewedByMeUtc",
kla:"latitude",qla:"longitude",yla:"markedForRemoval",Ala:"mediaKey",Ela:"mimeType",Sla:"name",ama:"numChildren",bma:"numTagged",cma:"numUntagged",jma:"organizationDisplayName",kma:"organizeIntoTeamDrive",mma:"otherParents",gma:"ogv",pma:"parentId",vE:"people",wma:"placeId",READ_ONLY:"readOnly",Qma:"rpt",Rma:"rptn",Vma:"resourceKey",ana:"rotation",bna:"rotationDegree",pna:"serviceId",Hna:"sizeBytes",Pna:"sourceTeamDriveId",Qna:"sourceTeamDriveName",koa:"teamDriveId",loa:"teamDriveName",moa:"teamMembersOnly",
ooa:"thumbnails",TYPE:"type",toa:"undoable",voa:"uploadId",woa:"uploadMetadata",zoa:"uploadState",URL:"url",VERSION:"version",Ooa:"visibility"});_.t("gapi.picker.api.Document.ADDRESS_LINES","addressLines");_.t("gapi.picker.api.Document.AUDIENCE","audience");_.t("gapi.picker.api.Document.DESCRIPTION","description");_.t("gapi.picker.api.Document.DURATION","duration");_.t("gapi.picker.api.Document.EMBEDDABLE_URL","embedUrl");_.t("gapi.picker.api.Document.ICON_URL","iconUrl");
_.t("gapi.picker.api.Document.ID","id");_.t("gapi.picker.api.Document.IS_NEW","isNew");_.t("gapi.picker.api.Document.LAST_EDITED_UTC","lastEditedUtc");_.t("gapi.picker.api.Document.LATITUDE","latitude");_.t("gapi.picker.api.Document.LONGITUDE","longitude");_.t("gapi.picker.api.Document.MIME_TYPE","mimeType");_.t("gapi.picker.api.Document.NAME","name");_.t("gapi.picker.api.Document.NUM_CHILDREN","numChildren");_.t("gapi.picker.api.Document.PARENT_ID","parentId");
_.t("gapi.picker.api.Document.PHONE_NUMBERS","phoneNumbers");_.t("gapi.picker.api.Document.SERVICE_ID","serviceId");_.t("gapi.picker.api.Document.THUMBNAILS","thumbnails");_.t("gapi.picker.api.Document.TYPE","type");_.t("gapi.picker.api.Document.URL","url");_.t("gapi.picker.api.DocsUploadView",g9);g9.prototype.setIncludeFolders=g9.prototype.WL;e9.prototype.setParent=g9.prototype.Nb;_.t("gapi.picker.api.DocsView",h9);h9.prototype.setIncludeFolders=h9.prototype.WL;
h9.prototype.setSelectFolderEnabled=h9.prototype.Lga;h9.prototype.setMode=h9.prototype.bD;e9.prototype.setParent=h9.prototype.Nb;h9.prototype.setOwnedByMe=h9.prototype.Y_;h9.prototype.setStarred=h9.prototype.l0;h9.prototype.setDocTypesDropDownEnabled=h9.prototype.qga;h9.prototype.setEnableDrives=h9.prototype.rga;h9.prototype.setEnableTeamDrives=h9.prototype.sga;h9.prototype.setFileIds=h9.prototype.wga;_.t("gapi.picker.api.DocsViewMode",{Ika:"grid",mla:"list"});
_.t("gapi.picker.api.DocsViewMode.GRID","grid");_.t("gapi.picker.api.DocsViewMode.LIST","list");_.t("gapi.picker.api.Feature",{tja:"cropA11y",Xja:"showAttach",Yja:"edbe",pka:"ftd",vka:"formsEnabled",Mka:"horizNav",Ska:"ignoreLimits",Fla:"mineOnly",Gla:"minimal",Hla:"minew",Pla:"multiselectEnabled",Ula:"navHidden",Wla:"newDriveView",Xla:"newHorizNav",Yla:"newPhotoGridView",hma:"odv",Gma:"profilePhoto",qna:"shadeDialog",vna:"simpleUploadEnabled",Ona:"sawffmi",foa:"sdr",goa:"std",Boa:"urlInputVisible"});
_.t("gapi.picker.api.Feature.MULTISELECT_ENABLED","multiselectEnabled");_.t("gapi.picker.api.Feature.NAV_HIDDEN","navHidden");_.t("gapi.picker.api.Feature.MINE_ONLY","mineOnly");_.t("gapi.picker.api.Feature.SIMPLE_UPLOAD_ENABLED","simpleUploadEnabled");_.t("gapi.picker.api.Feature.SUPPORT_DRIVES","sdr");_.t("gapi.picker.api.Feature.SUPPORT_TEAM_DRIVES","std");_.t("gapi.picker.api.ImageSearchView",g$);_.t("gapi.picker.api.ImageSearchView.License",{NONE:"*",Xma:"r",Qia:"cr",Yma:"rwm",Ria:"crwm"});
_.t("gapi.picker.api.ImageSearchView.License.COMMERCIAL_REUSE","cr");_.t("gapi.picker.api.ImageSearchView.License.COMMERCIAL_REUSE_WITH_MODIFICATION","crwm");_.t("gapi.picker.api.ImageSearchView.License.NONE","*");_.t("gapi.picker.api.ImageSearchView.License.REUSE","r");_.t("gapi.picker.api.ImageSearchView.License.REUSE_WITH_MODIFICATION","rwm");
_.t("gapi.picker.api.ImageSearchView.Size",{Ina:"qsvga",Kna:"vga",Jna:"svga",Nna:"xga",Lna:"wxga",Mna:"wxga2",Bna:"2mp",Dna:"4mp",Ena:"6mp",Gna:"8mp",wna:"10mp",xna:"12mp",zna:"15mp",Ana:"20mp",Cna:"40mp",Fna:"70mp",yna:"140mp"});_.t("gapi.picker.api.ImageSearchView.Size.SIZE_10MP","10mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_12MP","12mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_140MP","140mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_15MP","15mp");
_.t("gapi.picker.api.ImageSearchView.Size.SIZE_20MP","20mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_2MP","2mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_40MP","40mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_4MP","4mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_6MP","6mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_70MP","70mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_8MP","8mp");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_QSVGA","qsvga");
_.t("gapi.picker.api.ImageSearchView.Size.SIZE_SVGA","svga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_VGA","vga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_WXGA","wxga");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_WXGA2","wxga2");_.t("gapi.picker.api.ImageSearchView.Size.SIZE_XGA","xga");g$.prototype.setLicense=g$.prototype.Fga;g$.prototype.setSite=g$.prototype.Ew;g$.prototype.setSize=g$.prototype.setSize;g$.prototype.setSafeSearch=g$.prototype.mD;_.t("gapi.picker.api.MapsView",h$);
h$.prototype.setCenter=h$.prototype.setCenter;h$.prototype.setZoom=h$.prototype.setZoom;_.t("gapi.picker.api.PhotoAlbumsView",q$);_.t("gapi.picker.api.PhotosView",i$);_.t("gapi.picker.api.PhotosView.Type",{BANNER:"banner",yia:"bannergallery",Iia:"camerasync",lka:"featured",Fka:"gmail_themes",rka:"flat",Cka:"getty",Lka:"highlights",R4:"mediacollection",Jla:"moment",fma:"ofuser",Xna:"streamid",joa:"tpp",uoa:"uploaded",Foa:"videos-camerasync",Goa:"videos-uploaded",Soa:"localalbum",Lpa:"ytbanner"});
_.t("gapi.picker.api.PhotosView.Type.FEATURED","featured");_.t("gapi.picker.api.PhotosView.Type.GETTY","getty");_.t("gapi.picker.api.PhotosView.Type.UPLOADED","uploaded");e9.prototype.setParent=i$.prototype.Nb;i$.prototype.setType=i$.prototype.Td;_.t("gapi.picker.api.ResourceId.generate",function(a){return a?[a.mimeType=="application/pdf"?"pdf":Yea[a.serviceId]||"file",":",a.id].join(""):null});
_.t("gapi.picker.api.Response",{kia:"action",gka:"extraUserInputs",Hja:"docs",oma:"parents",Mx:"view",Moa:"viewToken",Eoa:"v2Translated"});_.t("gapi.picker.api.Response.ACTION","action");_.t("gapi.picker.api.Response.DOCUMENTS","docs");_.t("gapi.picker.api.Response.PARENTS","parents");_.t("gapi.picker.api.Response.VIEW","viewToken");
_.t("gapi.picker.api.ServiceId",{Cia:"books",V2:"calendar",Sia:"contacts",Via:"contrib",Hka:"gready",sja:"cportal",vja:"cultural",Gja:"docs",Kja:"photo",Nja:"dragonflyphotos",nE:"drive",Rja:"drive-select",kka:"feag",tka:"fonts",cka:"et",Bka:"geodiscussion",wla:"maps",xla:"mapspro",zla:"media",rma:"party",tma:"picasa",vma:"places",Pma:"relatedcontent",Una:"static_themes",Wna:"stories",Kpa:"youtube",Roa:"web",Poa:"vr-assets",vE:"people",jna:"search-api",URL:"url",Nma:"recent",qja:"cosmo",Fja:"DoclistBlob",
mE:"doc",lO:"drawing",X3:"form",wka:"freebird",fla:"kix",O5:"pres",Jma:"punch",Zma:"ritz",TO:"spread"});_.t("gapi.picker.api.ServiceId.DOCS","docs");_.t("gapi.picker.api.ServiceId.MAPS","maps");_.t("gapi.picker.api.ServiceId.PHOTOS","picasa");_.t("gapi.picker.api.ServiceId.SEARCH_API","search-api");_.t("gapi.picker.api.ServiceId.URL","url");_.t("gapi.picker.api.ServiceId.YOUTUBE","youtube");_.t("gapi.picker.api.Thumbnail",{pE:"height",URL:"url",FE:"width",TYPE:"type"});
_.t("gapi.picker.api.Thumbnail.HEIGHT","height");_.t("gapi.picker.api.Thumbnail.WIDTH","width");_.t("gapi.picker.api.Thumbnail.URL","url");_.t("gapi.picker.api.Type",{mia:"album",oia:"android-app",V2:"calendar",Pia:"chrome-app",CIRCLE:"circle",h3:"contact",mE:"document",qO:"event",bka:"et",jka:"faces",oka:"file",rO:"folder",ska:"font",LOCATION:"location",vla:"map",R4:"mediacollection",IO:"person",sma:"photo",URL:"url",fP:"video"});_.t("gapi.picker.api.Type.ALBUM","album");
_.t("gapi.picker.api.Type.DOCUMENT","document");_.t("gapi.picker.api.Type.LOCATION","location");_.t("gapi.picker.api.Type.PHOTO","photo");_.t("gapi.picker.api.Type.URL","url");_.t("gapi.picker.api.Type.VIDEO","video");_.t("gapi.picker.api.VideoSearchView",k$);_.t("gapi.picker.api.VideoSearchView.YOUTUBE","youtube.com");k$.prototype.setSite=k$.prototype.Ew;k$.prototype.setSafeSearch=k$.prototype.mD;_.t("gapi.picker.api.View",e9);e9.prototype.getId=e9.prototype.getId;e9.prototype.setLabel=e9.prototype.zc;
e9.prototype.setMimeTypes=e9.prototype.Mc;e9.prototype.setQuery=e9.prototype.hb;e9.prototype.getQuery=e9.prototype.getQuery;e9.prototype.getLabel=e9.prototype.Yz;_.t("gapi.picker.api.ViewGroup",m$);m$.prototype.addLabel=m$.prototype.uP;m$.prototype.addView=m$.prototype.Tx;m$.prototype.addViewGroup=m$.prototype.Ux;m$.prototype.setCollapsible=m$.prototype.nga;_.t("gapi.picker.api.ViewId.DOCS","all");_.t("gapi.picker.api.ViewId.DOCS_IMAGES","docs-images");
_.t("gapi.picker.api.ViewId.DOCS_IMAGES_AND_VIDEOS","docs-images-and-videos");_.t("gapi.picker.api.ViewId.DOCS_VIDEOS","docs-videos");_.t("gapi.picker.api.ViewId.DOCUMENTS","documents");_.t("gapi.picker.api.ViewId.DRAWINGS","drawings");_.t("gapi.picker.api.ViewId.FOLDERS","folders");_.t("gapi.picker.api.ViewId.FORMS","forms");_.t("gapi.picker.api.ViewId.IMAGE_SEARCH","image-search");_.t("gapi.picker.api.ViewId.MAPS","maps");_.t("gapi.picker.api.ViewId.PDFS","pdfs");
_.t("gapi.picker.api.ViewId.PHOTO_ALBUMS","photo-albums");_.t("gapi.picker.api.ViewId.PHOTOS","photos");_.t("gapi.picker.api.ViewId.PHOTO_UPLOAD","photo-upload");_.t("gapi.picker.api.ViewId.PRESENTATIONS","presentations");_.t("gapi.picker.api.ViewId.RECENTLY_PICKED","recently-picked");_.t("gapi.picker.api.ViewId.SPREADSHEETS","spreadsheets");_.t("gapi.picker.api.ViewId.VIDEO_SEARCH","video-search");_.t("gapi.picker.api.ViewId.WEBCAM","webcam");_.t("gapi.picker.api.ViewId.YOUTUBE","youtube");
_.t("gapi.picker.api.ViewToken",{Koa:0,gla:1,Loa:2});_.t("gapi.picker.api.ViewToken.LABEL",1);_.t("gapi.picker.api.ViewToken.VIEW_ID",0);_.t("gapi.picker.api.ViewToken.VIEW_OPTIONS",2);_.t("gapi.picker.api.WebCamView",r$);_.t("gapi.picker.api.WebCamViewType.STANDARD","standard");(_.Xa.google=_.Xa.google||{}).picker=_.Xa.gapi.picker.api;_.t("gapi.picker.api.Picker",b9);b9.prototype.isVisible=_.U4.prototype.isVisible;b9.prototype.setAppId=b9.prototype.mp;b9.prototype.setCallback=b9.prototype.Vr;b9.prototype.setRelayUrl=b9.prototype.Nj;b9.prototype.setVisible=b9.prototype.setVisible;b9.prototype.dispose=b9.prototype.dispose;_.t("gapi.picker.api.PickerBuilder",o$);o$.prototype.addView=o$.prototype.Tx;o$.prototype.addViewGroup=o$.prototype.Ux;o$.prototype.build=o$.prototype.Jb;o$.prototype.disableFeature=o$.prototype.G8;
o$.prototype.enableFeature=o$.prototype.a9;o$.prototype.getRelayUrl=o$.prototype.Fu;o$.prototype.getTitle=o$.prototype.getTitle;o$.prototype.hideTitleBar=o$.prototype.Bba;o$.prototype.isFeatureEnabled=o$.prototype.sca;o$.prototype.setAppId=o$.prototype.mp;o$.prototype.setAuthUser=o$.prototype.lga;o$.prototype.setCallback=o$.prototype.Vr;o$.prototype.setDeveloperKey=o$.prototype.pga;o$.prototype.setDocument=o$.prototype.BL;o$.prototype.setInitialView=o$.prototype.Aga;o$.prototype.setLocale=o$.prototype.bs;
o$.prototype.setMaxItems=o$.prototype.Gga;o$.prototype.setOAuthToken=o$.prototype.nM;o$.prototype.setOrigin=o$.prototype.Lj;o$.prototype.setRelayUrl=o$.prototype.Nj;o$.prototype.setSelectableMimeTypes=o$.prototype.Mga;o$.prototype.setSize=o$.prototype.setSize;o$.prototype.setTitle=o$.prototype.setTitle;o$.prototype.setUploadToAlbumId=o$.prototype.Rga;o$.prototype.toUri=o$.prototype.G1;(_.Xa.google=_.Xa.google||{}).picker=_.Xa.gapi.picker.api;
});
// Google Inc.
