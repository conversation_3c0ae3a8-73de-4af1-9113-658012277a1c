# ClashMeta 高级优化配置
# 最后更新：2024-10-28
# 作者：🥞煎饼果子卷鲨鱼辣椒🌶️

#------------------------基础配置------------------------#
mixed-port: 7890            # 混合端口：HTTP(S)和SOCKS5共用端口
geodata-mode: true          # GEO模式：true使用geoip.dat数据库,false使用mmdb数据库
tcp-concurrent: true        # TCP并发：允许并发连接TCP,提高并发性能
unified-delay: true         # 统一延迟：统一显示节点延迟
allow-lan: true            # 局域网连接：允许其他设备经过本机代理
bind-address: "*"          # 监听地址：*表示绑定所有IP地址
find-process-mode: strict
ipv6: false               # IPv6开关：是否启用IPv6支持

# 运行模式(任选其一):
# rule: 规则模式 - 根据规则匹配来选择代理
# global: 全局模式 - 全部流量走代理
# direct: 直连模式 - 全部流量不走代理
mode: rule

# 日志等级(按详细程度排序):
# debug: 调试
# info: 信息
# warning: 警告
# error: 错误
# silent: 静默
log-level: error

# 外部控制设置
external-controller: 0.0.0.0:9090  # 外部控制器监听地址
external-ui: ui               # 外部控制器UI目录
secret: ""                        # 外部控制器密码

#------------------------性能调优------------------------#
tcp-concurrent-users: 64      # TCP并发连接数,根据服务器性能调整,建议值:16-128
keep-alive-interval: 15       # 保活心跳间隔(秒),建议值:15-30
inbound-tfo: true            # 入站TCP Fast Open
outbound-tfo: true           # 出站TCP Fast Open
# Windows示例
#interface-name: WLAN   # Windows中的无线网卡名称
# 或
#interface-name: 以太网  # Windows中的有线网卡名称
# macOS示例
#interface-name: en0    # macOS中通常是Wi-Fi
# 或
#interface-name: en1    # macOS中通常是有线网卡
# Linux示例
#interface-name: eth0   # Linux中常见的有线网卡名
# 或
#interface-name: wlan0  # Linux中常见的无线网卡名

# 连接池配置
connection-pool-size: 256     # 连接池大小,建议值:128-512
idle-timeout: 60             # 空闲超时时间(秒)

#------------------------TLS 配置------------------------#
tls:
  enable: true               # 启用TLS支持
  skip-cert-verify: true    # 是否跳过证书验证
  alpn:                      # 应用层协议协商
    - h2                     # HTTP/2
    - http/1.1              # HTTP/1.1
  min-version: "1.2"        # 最低TLS版本
  max-version: "1.3"        # 最高TLS版本
  cipher-suites:            # 加密套件优先级
    - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
    - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
    - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305
    - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305

#------------------------DNS 配置------------------------#
dns:
  enable: true              # 启用DNS服务器
  prefer-h3: true          # 优先使用HTTP/3查询
  ipv6: false              # DNS解析IPv6
  listen: 0.0.0.0:53       # DNS监听地址
  enhanced-mode: redir-host
  use-hosts: false          # 使用hosts文件

  # 默认DNS服务器(用于解析其他DNS服务器的域名)
  default-nameserver:
    - *********            # 阿里DNS
    - ************         # 腾讯DNS

  # DNS服务器分流策略
  nameserver-policy:
    'www.google.com': 'https://dns.google/dns-query'      # Google域名使用Google DNS
    'www.facebook.com': 'https://dns.google/dns-query'    # Facebook域名使用Google DNS
    '.cn': 'https://doh.pub/dns-query'                    # 中国域名使用国内DNS

  # Fake-IP配置
  fake-ip-range: **********/16    # Fake-IP地址段
  fake-ip-filter:                 # Fake-IP过滤清单
    - "*.lan"                     # 本地域名
    - "localhost.ptlogin2.qq.com" # QQ登录

  # 主要DNS服务器
  nameserver:
    # 国内DNS服务器
    - https://doh.pub/dns-query#h3=true                # DNSPod DOH
    - https://dns.alidns.com/dns-query#h3=true         # 阿里 DOH
    - tls://*********:853                              # 阿里 DOT

    # 国外DNS服务器
    - https://dns.google/dns-query#h3=true             # Google DOH
    - https://cloudflare-dns.com/dns-query#h3=true     # Cloudflare DOH
    - quic://dns.adguard.com:784                       # AdGuard DOQ

  # 备用DNS服务器(用于解析国外域名)
  fallback:
    - https://dns.google/dns-query#h3=true
    - https://*******/dns-query#h3=true
    - tls://*******:853


# 代理提供商配置

proxy-providers:
      
  200566:
    type: http
    url: "https://aini.200566.xyz/tzjz55/download/collection/tzjz66"
    interval: 21600
    path: ./proxy_providers/200566.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800
      
  cat.ikkt.cn:
    type: http
    url: "https://cat.ikkt.cn/api/v1/client/subscribe?token=bf587ad6317f74983bb98612a522a196"
    interval: 21600
    path: ./proxy_providers/cat.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  宝可梦3590:
    type: http
    url: "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=9a81cce9b917d0d72cff15cd78cc2c05"
    interval: 21600
    path: ./proxy_providers/宝可梦3590.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  宝可梦*****************:
    type: http
    url: "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=ce49a7b716f172022c8525528797adce"
    interval: 21600
    path: ./proxy_providers/宝可梦******************
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  便宜机场:
    type: http
    url: "https://pianyi.sub.sub.subsub123456789.com/answer/land?token=2b233c2f0b074ca6a551ff0fbfb0bc29"
    interval: 21600
    path: ./proxy_providers/便宜机场.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  哈库亚:
    type: http
    url: "http://j2s.buzz/v3/subscr?id=a2ff5f8bf2ae446a96ff7bbe2c240bf2#showRemaining&cacheKey"
    interval: 21600
    path: ./proxy_providers/哈库亚.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  "TG里的欢乐时光":
    type: http
    url: "https://owo.o00o.ooo/ooo"
    interval: 21600
    path: ./proxy_providers/o00o.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800
  ikuuu:
    type: http
    url: "https://6w6tf.no-mad-world.club/link/yR9YM15eeAxijQeG?clash=3&extend=1#showRemaining"
    interval: 21600
    path: ./proxy_providers/ikuuu.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  nachoneko:
    type: http
    url: "https://nachoneko.cn/api/v1/client/subscribe?token=7817c0778025c4ecc9d26436b6d90b09"
    interval: 21600
    path: ./proxy_providers/nachoneko.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

 # alistlocal:
  #  type: http
  #  url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/alistlocal"
  #  interval: 21600
  #  path: ./proxy_providers/alistlocal.yaml
   # health-check:
   #   enable: true
   #   url: http://www.google.com/generate_204
   #   interval: 1800

 # 便宜机场:
  #  type: http
 #   url: "https://pianyi.sub.sub.subsub123456789.com/answer/land?token=74fc2b1b20390dcc167525ce10abcba2&flag=meta#showRemaining"
 #   interval: 21600
 #   path: ./proxy_providers/便宜机场.yaml
  #  health-check:
#      enable: true
 #     url: http://www.google.com/generate_204
#      interval: 1800

  云鸟:
    type: http
    url: "https://niaodi.top/niao?token=1afe9e79c15c16d98605eab966ef5691#resetday=28&startdate=2024-11-04&showRemaining"
    interval: 21600
    path: ./proxy_providers/云鸟.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  GT:
    type: http
    url: "https://sub.grempt.com/link/135708/DH7OQvp1MRKB#showRemaining"
    interval: 21600
    path: ./proxy_providers/GT.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  秒连云:
    type: http
    url: "https://sub.miaolianyun.vip/mly_sub?token=2d734e0e01b3ee4aaad02e2a4d42f655#resetday=4&startdate=2024-11-04&showRemaining"
    interval: 21600
    path: ./proxy_providers/秒连云.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  "gistagg自动上传":
    type: http
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/23f3d4ba07b980535ea99f4faf48f6b1/raw/clash.yaml"
    interval: 21600
    path: ./proxy_providers/gistagg自动上传.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800

  v2ray:
    type: http
    url: "https://ac59a7cd-11af-3e35-a098-b785688ec627.aliyunsub.com/link/1DswsoSCjJpogBnA?sub=3&amp;extend=1"
    interval: 21600
    path: ./proxy_providers/v2ray.yaml
    health-check:
      enable: true
      url: http://www.google.com/generate_204
      interval: 1800


#代理分组
#自动引入【proxy-providers】所有代理集合，顺序将按照名称排序
proxy-groups:
  #------------------------基础分组------------------------#
  - name: 🚀 节点选择
    type: select
    proxies:
      - ♻️ 自动选择
      - 🔯 故障转移
      - 🔮 负载均衡
      - 🇭🇰 香港节点
      - 🇨🇳 台湾节点
      - 🇯🇵 日本节点
      - 🇰🇷 韩国节点
      - 🇺🇲 美国节点
      - 🇬🇧 英国节点
      - 🇩🇪 德国节点
      - 🇫🇷 法国节点
      - 🇮🇳 印度节点
      - 🇸🇬 狮城节点
      - 🇮🇩 印尼节点
      - 🇻🇳 越南节点
      - 🇹🇭 泰国节点
      - 🇦🇺 澳洲节点
      - 🇧🇷 巴西节点
      - 🌍 其他节点
      - DIRECT

  - name: ♻️ 自动选择
    type: url-test
    include-all-providers: true
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 100
    filter: "^(?!.*(港|台|韩|新|hk|HK|tw|TW|kr|KR|sg|SG|徐州|济南|镇江|山东|武汉|到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁)).*$"

  - name: 🔯 故障转移
    type: fallback
    include-all-providers: true
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    filter: "^(?!.*(港|台|韩|新|hk|HK|tw|TW|kr|KR|sg|SG|徐州|济南|镇江|山东|武汉|到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁)).*$"

  - name: 🔮 负载均衡
    type: load-balance
    strategy: consistent-hashing
    include-all-providers: true
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    filter: "^(?!.*(港|台|韩|新|hk|HK|tw|TW|kr|KR|sg|SG|徐州|济南|镇江|山东|武汉|到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁)).*$"

  #------------------------地区分组------------------------#
  - name: 🇭🇰 香港节点
    type: url-test
    include-all-providers: true
    filter: "(?i)港|hk|hongkong|hong kong(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇨🇳 台湾节点
    type: url-test
    include-all-providers: true
    filter: "(?i)台|tw|taiwan|taipei(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇯🇵 日本节点
    type: url-test
    include-all-providers: true
    filter: "(?i)日本|jp|japan(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇰🇷 韩国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)韩|kr|korea|seoul(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇺🇲 美国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)美|us|united states|america|los angeles|san jose|silicon valley(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇬🇧 英国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)英|uk|united kingdom|london(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇩🇪 德国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)德|de|germany|frankfurt(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇫🇷 法国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)法|fr|france|paris(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇮🇳 印度节点
    type: url-test
    include-all-providers: true
    filter: "(?i)印度|in|india|mumbai(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇸🇬 狮城节点
    type: url-test
    include-all-providers: true
    filter: "(?i)新|sg|singapore(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇮🇩 印尼节点
    type: url-test
    include-all-providers: true
    filter: "(?i)印尼|印度尼西亚|id|indonesia|jakarta(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇻🇳 越南节点
    type: url-test
    include-all-providers: true
    filter: "(?i)越|vn|vietnam(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇹🇭 泰国节点
    type: url-test
    include-all-providers: true
    filter: "(?i)泰|th|thailand|bangkok(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇦🇺 澳洲节点
    type: url-test
    include-all-providers: true
    filter: "(?i)澳大|au|australia|sydney(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🇧🇷 巴西节点
    type: url-test
    include-all-providers: true
    filter: "(?i)巴西|br|brazil(?!.*(到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|徐州|济南|镇江|山东|武汉)).*"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  - name: 🌍 其他节点
    type: url-test
    include-all-providers: true
    filter: "^(?!.*(港|台|日|韩|新|美|英|德|法|印|泰|越|尼|澳|巴|hk|tw|jp|kr|sg|us|uk|de|fr|in|th|vn|id|au|br|HK|US|TW|JP|KR|SG|DE|FR|IN|TH|VN|AU|BR|徐州|济南|镇江|山东|武汉|到期|优惠|套餐|传家宝|剩余|重置|更新|客户端|免费|导航|通知|白嫖|公益|❗️سرور متصله 💜|抽奖|官网|实验|发布|移动|联通|活动|促销|限时|特价|法律|政治|违|禁|秒)).*$"
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100

  #------------------------场景分组------------------------#

  - name: 🎬 国外媒体
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港节点
      - 🇨🇳 台湾节点
      - 🇯🇵 日本节点
      - 🇺🇲 美国节点
      - 🇬🇧 英国节点
      - 🇩🇪 德国节点
      - 🇫🇷 法国节点
      - 🇦🇺 澳洲节点
      - 🇸🇬 狮城节点

  - name: 📱 即时通讯
    type: select
    proxies:
      - 🚀 节点选择
      - 🔯 故障转移
      - 🇭🇰 香港节点
      - 🇯🇵 日本节点
      - 🇺🇲 美国节点
      - 🇬🇧 英国节点
      - 🇩🇪 德国节点
      - 🇫🇷 法国节点
      - 🇦🇺 澳洲节点
      - 🇸🇬 狮城节点

  - name: 🤖 AI平台
    type: select
    proxies:
      - 🇯🇵 日本节点
      - 🇺🇲 美国节点
      - 🇬🇧 英国节点
      - 🇩🇪 德国节点
      - 🇫🇷 法国节点
      - 🇦🇺 澳洲节点
      - 🚀 节点选择
      - 🔯 故障转移

  - name: 🔧 GitHub
    type: select
    proxies:
      - 🚀 节点选择
      - 🔯 故障转移
      - 🇭🇰 香港节点
      - 🇨🇳 台湾节点
      - 🇯🇵 日本节点
      - 🇺🇲 美国节点
      - 🇬🇧 英国节点
      - 🇩🇪 德国节点
      - 🇫🇷 法国节点
      - 🇦🇺 澳洲节点
      - 🇸🇬 狮城节点
      - DIRECT

  - name: Ⓜ️ 微软服务
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港节点
      - 🇨🇳 台湾节点
      - 🇯🇵 日本节点
      - 🇺🇲 美国节点
      - 🇸🇬 狮城节点
      - DIRECT

  #------------------------特殊分组------------------------#
  - name: 🎯 全球直连
    type: select
    proxies:
      - DIRECT
      - 🚀 节点选择

  - name: 🛑 广告拦截
    type: select
    proxies:
      - REJECT
      - DIRECT

  - name: 🍃 应用净化
    type: select
    proxies:
      - REJECT
      - DIRECT

  - name: 🆎 AdBlock
    type: select
    proxies:
      - REJECT
      - DIRECT

  - name: 🛡️ 隐私防护
    type: select
    proxies:
      - REJECT
      - DIRECT

  - name: 🐟 漏网之鱼
    type: select
    proxies:
      - 🚀 节点选择
      - 🎯 全球直连
      - ♻️ 自动选择
      - 🔯 故障转移

# 规则提供商配置 - 优化版

  
rule-providers:
  # 广告规则
  reject:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/reject.yaml
    interval: 86400

  # 隐私规则
  privacy:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/Privacy/Privacy.yaml"
    path: ./ruleset/privacy.yaml
    interval: 86400

  # 广告扩展规则
  reject-extra:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/AdvertisingLite/AdvertisingLite.yaml"
    path: ./ruleset/reject-extra.yaml
    interval: 86400

  # AI平台规则
  ai-platforms:
    type: http
    behavior: classical
    url: "https://raw.githubusercontent.com/ObesityChow/clash-ai-ruleset/refs/heads/main/AI.yaml"
    path: ./ruleset/ai.list
    interval: 86400

  # 流媒体规则
  streaming:
    type: http
    behavior: classical
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/GlobalMedia/GlobalMedia.yaml"
    path: ./ruleset/streaming.yaml
    interval: 86400

  # 社交通讯规则
  social:
    type: http
    behavior: classical
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/Telegram/Telegram.yaml"
    path: ./ruleset/social.yaml
    interval: 86400

  # 微软服务规则
  microsoft:
    type: http
    behavior: classical
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/Microsoft/Microsoft.yaml"
    path: ./ruleset/microsoft.yaml
    interval: 86400

  # 开发平台规则
  dev-platforms:
    type: http
    behavior: classical
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script/rule/Clash/GitHub/GitHub.yaml"
    path: ./ruleset/dev-platforms.yaml
    interval: 86400
    
  # NSFW
  NSFW:
    type: http
    behavior: classical
    url: "https://raw.githubusercontent.com/tanmoumou252/NSFWruleset/main/NSFW.yaml"
    path: ./ruleset/NSFW.yaml
    
  Google:      
    type: http
    behavior: classical
    interval: 86400
    format: yaml
    url: "https://ghp.ci/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Google/Google.yaml"
    path: ./ruleset/google.yaml
  
# 规则配置 - 优化版
rules:
  # 优先处理
  - RULE-SET,reject,🛑 广告拦截,no-resolve
  - RULE-SET,privacy,🛡️ 隐私防护,no-resolve
  - RULE-SET,reject-extra,🆎 AdBlock,no-resolve

  # 本地局域网
  - DOMAIN-SUFFIX,local,DIRECT
  - DOMAIN-SUFFIX,localhost,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT
  - IP-CIDR,*********/4,DIRECT
  - IP-CIDR6,fe80::/10,DIRECT
  # 科技公司
  - GEOSITE,google,📱 即时通讯,no-resolve
  - GEOSITE,google-cn,📱 即时通讯,no-resolve
  

  # 应用分流
  - RULE-SET,ai-platforms,🤖 AI平台,no-resolve
  - RULE-SET,streaming,🎬 国外媒体,no-resolve
  - RULE-SET,Google,📱 即时通讯,no-resolve
  - RULE-SET,social,📱 即时通讯,no-resolve
  - RULE-SET,microsoft,Ⓜ️ 微软服务,no-resolve
  - RULE-SET,dev-platforms,🔧 GitHub,no-resolve
  - RULE-SET,NSFW,🎬 国外媒体

  # 自定义规则
  - PROCESS-NAME,tw.nekomimi.nekogram,📱 即时通讯
  - PROCESS-NAME,com.android.shell,🔧 GitHub
  - PROCESS-NAME,ai.chat.gpt.bot,🤖 AI平台
  - PROCESS-NAME,com.smarter.technologist.android.smarterbookmarks,🤖 AI平台
  - PROCESS-NAME,xyz.chatboxapp.chatbox,🤖 AI平台
  - PROCESS-NAME,com.microsoft.copilot,🤖 AI平台
  - PROCESS-NAME,com.tencent.androidqqmail,📱 即时通讯
  - PROCESS-NAME,allen.town.focus.reader,📱 即时通讯
  - PROCESS-NAME,com.flyersoft.seekbooks,🎬 国外媒体
  - PROCESS-NAME,io.legado.app.release,🎬 国外媒体
  - PROCESS-NAME,com.fongmi.android.tv,🎬 国外媒体
  - PROCESS-NAME,com.hiker.youtoo,🎬 国外媒体
  
  - DOMAIN,linux.do,📱 即时通讯
  - DOMAIN,google.com,📱 即时通讯
  - DOMAIN,median.co,📱 即时通讯
  - DOMAIN,luxirty.com,📱 即时通讯
  - DOMAIN,mpyit.com,🎬 国外媒体

  # 地域规则
  - GEOIP,LAN,DIRECT,no-resolve
  - GEOIP,CN,DIRECT,no-resolve

  # 兜底规则
  - MATCH,🔧 GitHub



tproxy-port: 9898
redir-port: 9797