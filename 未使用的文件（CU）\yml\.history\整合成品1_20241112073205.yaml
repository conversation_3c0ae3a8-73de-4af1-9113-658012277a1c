rule-providers:
  private:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.yaml
    path: ./ruleset/private.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.yaml
    path: ./ruleset/cn_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  youtube_domain:
    url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/YouTube/YouTube.yaml
    path: ./ruleset/youtube_domain.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  telegram_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.yaml
    path: ./ruleset/telegram_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  google_domain:
    url: https://raw.githubusercontent.com/6otho/yamal-pako/refs/heads/main/google.yaml
    path: ./ruleset/google_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  geolocation-!cn:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.yaml
    path: ./ruleset/geolocation-!cn.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.yaml
    path: ./ruleset/cn_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  telegram_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.yaml
    path: ./ruleset/telegram_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  google_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.yaml
    path: ./ruleset/google_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  bing:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Bing/Bing.yaml
    path: ./ruleset/bing.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  copilot:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Copilot/Copilot.yaml
    path: ./ruleset/copilot.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  claude:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Claude/Claude.yaml
    path: ./ruleset/claude.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  bard:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/BardAI/BardAI.yaml
    path: ./ruleset/bard.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  openai:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/OpenAI/OpenAI.yaml
    path: ./ruleset/openai.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  steam:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Steam/Steam.yaml
    path: ./ruleset/steam.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  netflix_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Netflix/Netflix.yaml
    path: ./ruleset/netflix_domain.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  emby_domain:
    url: https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Emby/Emby.yaml
    path: ./ruleset/emby_domain.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http

proxy-groups:
  - icon: https://ghp.ci/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Available.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: PROXY
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://camo.githubusercontent.com/62aebe7250bb15f769b79814bb31a53fcf797e0baedb46c74a9577ad9df3cbf3/68747470733a2f2f666173746c792e6a7364656c6976722e6e65742f67682f4f727a2d332f6d696e69406d61737465722f436f6c6f722f596f75547562652e706e67
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: Youtube
    type: select
    proxies:
      - PROXY
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO  
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Urltest.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/OpenAI.png
    name: AIGC
    type: select
    proxies:
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Telegram.png
    name: Telegram
    type: select
    proxies:
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Google.png
    name: Google
    type: select
    proxies:
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Netflix.png
    name: Netflix
    type: select
    proxies:
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://camo.githubusercontent.com/ea89de75a833039c0e83b79ed5c937380c678243f94068cc0e2ebeb5ddd70b06/68747470733a2f2f666173746c792e6a7364656c6976722e6e65742f67682f4f727a2d332f6d696e69406d61737465722f436f6c6f722f537465616d2e706e67
    name: Steam
    type: select
    proxies:
      - PROXY
      - DIRECT
      - SG AUTO
      - JP AUTO
      - US AUTO
      - KR AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/HK.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: (?i)香港|Hong Kong|HK|🇭🇰
    name: HK AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/SG.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: (?i)新加坡|Singapore|🇸🇬
    name: SG AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/JP.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: (?i)日本|Japan|🇯🇵
    name: JP AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/US.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: (?i)美国|USA|🇺🇸
    name: US AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/KR.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: (?i)韩国|South Korea|🇰🇷
    name: KR AUTO
    type: url-test
    interval: 300


rules:
  - DOMAIN,domainname,PROXY
  - RULE-SET,youtube_domain,Youtube
  - RULE-SET,copilot,AIGC
  - RULE-SET,claude,AIGC
  - RULE-SET,bard,AIGC
  - RULE-SET,openai,AIGC
  - RULE-SET,google_domain,Google
  - RULE-SET,google_ip,Google
  - RULE-SET,telegram_domain,Telegram
  - RULE-SET,telegram_ip,Telegram
  - RULE-SET,private,PROXY
  - RULE-SET,netflix_domain,Netflix
  - RULE-SET,emby_domain,Netflix
  - RULE-SET,bing,Google
  - RULE-SET,steam,Steam
  - RULE-SET,geolocation-!cn,PROXY
  - RULE-SET,cn_domain,DIRECT
  - RULE-SET,cn_ip,DIRECT
  - MATCH,PROXY
