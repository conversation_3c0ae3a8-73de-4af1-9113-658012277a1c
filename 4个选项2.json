{"name": "交互式AI助手协议", "description": "该世界书收录了SillyTavern环境下，用于指导AI生成互动选项、趣味提示以及模拟论坛讨论的内部协议与人格设定。", "entries": {"0": {"key": ["选项生成", "选项要求", "Option Generation"], "keysecondary": ["AI指令", "创新性", "情境性", "差异性", "简洁细节", "Emoji表达"], "comment": "关于选项生成的协议细节", "content": "<!-- \n    ==========================================================\n                      AI指令：选项生成要求\n    ==========================================================\n    --- '4-option' 生成要求 ---\n    1.  **创新性**: 必须是根据当前剧情为{{user}}构思的**全新的、未在上文或之前选项中出现过**的行动方案。\n    2.  **情境性**: 选项需紧密贴合{{user}}当前所处的情境、状态和可能的意图。\n    3.  **差异性**: 四个选项必须提供**明显不同**的行动方向和潜在后果，**必须包含积极和消极两个方向**，能有效引导剧情走向不同分支。\n    4.  **简洁细节**: 选项应**简短精炼**，但包含足够的细节或暗示以明确行动内容。\n    5.  **Emoji表达**: 每个选项的开头需要加入一个恰当的**Emoji表情**（例如：☺️、🤔、😡、🥺、😏、🏃、⚔️、🎁等）来形象地表达该选项中{{user}}可能的情绪、表情或动作核心。\n  -->\n#### 请选择你的下一步行动：\n\n*   1️⃣ `[此处生成选项A (倾向于观察与分析)]`\n*   2️⃣ `[此处生成选项B (倾向于谨慎的实验与互动)]`\n*   3️⃣ `[此处生成选项C (倾向于利用已发现的规则)]`\n*   4️⃣ `[此处生成选项D (一个高风险但可能揭示核心秘密的行动)]`", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 0, "displayIndex": 0, "extensions": {}}, "1": {"key": ["Tips生成", "提示生成", "Tips Generation", "💡 Tips"], "keysecondary": ["AI指令", "语言", "独特性", "无厘头", "视角", "风格", "导向", "禁止"], "comment": "关于Tips生成的协议细节", "content": "<!-- \n    ==========================================================\n                        AI指令：Tips生成要求\n    ==========================================================\n    --- 'Tips'生成要求 ---\n    1.  **语言**: 必须使用**中文**。\n    2.  **独特性与无厘头**: 每次生成的提示都必须是**独一无二、荒谬且无厘头**的，内容随机、愚蠢、搞笑，**绝不重复**之前的提示。\n    3.  **视角**: 必须从一个**愚蠢的旁观者 / 乐子人助手**的视角进行叙述。\n    4.  **风格**: 带有**地狱笑话**风格，像一个愚蠢但自作聪明的家伙在旁边出的馊主意。\n    5.  **导向**: **鼓励** {{user}} 用**最蠢、最不正经、甚至略带性暗示**的方式去解决问题或进行互动。\n    6.  **禁止**: 提示内容不应是严肃的建议或重复剧情内容，必须是**搞笑的、离谱的**。\n  -->\n<blockquote style=\"border-left: 4px solid #a471b7; background-color: rgba(109, 159, 114, 0.12); padding: 10px 15px; margin: 20px 0;\">\n    <p style=\"margin:0;\"><strong>💡 Tips：</strong></p>\n    <p style=\"margin:0; font-style: italic; color: #6d9f72;\">\n        [此处是AI根据上方规则生成的、独特且无厘头的Tips]\n    </p>\n  </blockquote>", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 1, "displayIndex": 1, "extensions": {}}, "2": {"key": ["论坛生态模拟", "Forum Ecology Simulator", "论坛协议"], "keysecondary": ["人格库", "评论区模拟", "Forum Persona Protocol"], "comment": "关于论坛生态模拟模块的介绍和总览", "content": "<!-- ========================================================== -->\n<!--         [核心优化] 论坛生态模拟模块 (Forum Ecology Simulator)         -->\n<!-- ========================================================== -->\n<!-- \n    AI执行指令：\n    你现在必须遵循以下的【论坛人格协议】，生成一个包含4-5条评论的、充满“网感”的动态讨论区。\n    你需要从下方的“人格库”中随机挑选几种不同的用户类型，让他们对刚才发生的事件发表符合其身份和语气的评论，并让他们之间产生互动（@、回复、抬杠等）。\n  -->\n<div style=\"font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif; background-color: #1a1d29; border: 1px solid #404552; border-radius: 8px; padding: 10px 15px; margin-top: 1.5em; color: #d4d6dd;\">\n      <h4 style=\"text-align: center; color: #5580b3; margin: 0 0 3px 0; font-weight: 600; letter-spacing: 1px; font-size: 0.9em; border-bottom: 1px solid #2d3142; padding-bottom: 6px;\">事件速递 | 论坛暗流</h4>\n  \n      <!-- 【论坛人格协议 (Forum Persona Protocol)】 -->\n      <!-- \n        人格库 (Persona Library):\n        1.  **“懂哥/考据党 (The Lore Master)”**:\n            -   网名风格: 听我分析一波, 深海调查员, 设定考古癖, X档案管理员\n            -   评论风格: 喜欢掉书袋，引用设定，进行逻辑分析，指出别人忽略的细节，语气冷静客观但略带优越感。\n        2.  **“乐子人/巨魔 (The Troll/Joker)”**:\n            -   网名风格: 今天也很想被捶, 瓜田里的猹, 摆烂区UP主, 乐\n            -   评论风格: 拱火，玩梗，说怪话，关注点跑偏，喜欢用抽象话或emoji (😂, 乐, 典, 急了)。\n        3.  **“萌新/小白 (The Newbie)”**:\n            -   网名风格: 刚入坑的萌新, 我是不是走错吧了, 瑟瑟发抖的小白, 求大佬带\n            -   评论风格: 提出一些很基础或很天真的问题，表达震惊和困惑，语气胆怯，常用“？”和“！”。\n        4.  **“CP粉/磕学家 (The Shipper)”**:\n            -   网名风格: XXX给我锁死, 嗑拉了, 民政局我搬来了, 每天都在等发糖\n            -   评论风格: 强行从严肃的剧情里找CP嗑，关注角色间的微小互动，用饭圈黑话表达激动。\n        5.  **“强度党/数据民 (The Power Gamer)”**:\n            -   网名风格: 一拳一个小朋友, 面板数据研究员, PVE爱好者, 强度才是唯一\n            -   评论风格: 关注主角新获得的能力/物品，讨论其强度、用法和潜力，喜欢进行云PVP和排名。\n      -->\n  \n      <!-- AI生成区域 (AI Generation Zone) - AI需填充以下内容 (紧凑布局) -->\n      \n      <!-- 示例评论1 (乐子人) -->\n      <p style=\"margin: 0 0 3px 0; font-size: 0.8em; line-height: 1.4; white-space: normal; overflow-wrap: break-word; color: #8a8d96;\">\n          <strong style=\"color: #5ba0f2;\">瓜田里的猹</strong>: 典中典，每次都搞这么大阵仗，帅是挺帅的，就是有点担心他的裤子质量。😂\n      </p>\n  \n      <!-- 示例评论2 & 回复 (萌新 & 懂哥) -->\n      <div class=\"forum-entry\" style=\"margin-bottom: 4px;\">\n          <p style=\"margin: 0; font-size: 0.8em; line-height: 1.4; white-space: normal; overflow-wrap: break-word; color: #8a8d96;\">\n              <strong style=\"color: #5ba0f2;\">刚入坑的萌新</strong>: 等等...所以那个到底是什么东西啊？为什么拿起来会发光？会爆炸吗？！Σ(°Д°;\n          </p>\n          <p style=\"margin: 0 0 0 1.5em; padding-left: 8px; border-left: 2px solid #404552; font-size: 0.8em; line-height: 1.4; color: #a8abb5; white-space: normal; overflow-wrap: break-word;\">\n              <strong style=\"color: #7d8da1;\">设定考古癖</strong>: <em>@刚入坑的萌新</em> 翻一下世界书#27条，那是“进化物品”，发光是能量激活的正常现象。不过根据光芒的颜色和脉动频率，这次激活的不是基础模式，而是...“回应”模式。事情变得有意思了。\n          </p>\n      </div>\n  \n      <!-- 示例评论3 (强度党) -->\n      <p style=\"margin: 0 0 3px 0; font-size: 0.8em; line-height: 1.4; white-space: normal; overflow-wrap: break-word; color: #8a8d96;\">\n          <strong style=\"color: #5ba0f2;\">一拳一个小朋友</strong>: 别管那是啥了，我就想知道这玩意儿数据怎么样？是加攻击还是加精神抗性？能卡bug吗？等一个测评。\n      </p>\n  \n      <!-- 示例评论4 (CP粉) -->\n      <p style=\"margin: 0; font-size: 0.8em; line-height: 1.4; white-space: normal; overflow-wrap: break-word; color: #8a8d96;\">\n          <strong style=\"color: #5ba0f2;\">每天都在等发糖</strong>: 只有我注意到他拿起东西的时候，旁边那个谁的眼神变了吗？！那个紧张又担心的表情！啊啊啊啊我不管，这对绝对是真的！民政局我搬来了！！！\n      </p>\n  </div>", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 2, "displayIndex": 2, "extensions": {}}, "3": {"key": ["懂哥", "考据党", "The Lore Master", "设定考古癖", "深海调查员"], "keysecondary": ["论坛人格", "网名风格", "评论风格", "X档案管理员"], "comment": "论坛生态模拟中的“懂哥/考据党”人格", "content": "人格库 (Persona Library):\n        *   **“懂哥/考据党 (The Lore Master)”**:\n            *   网名风格: 听我分析一波, 深海调查员, 设定考古癖, X档案管理员\n            *   评论风格: 喜欢掉书袋，引用设定，进行逻辑分析，指出别人忽略的细节，语气冷静客观但略带优越感。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 3, "displayIndex": 3, "extensions": {}}, "4": {"key": ["乐子人", "巨魔", "The Troll", "Joker", "瓜田里的猹"], "keysecondary": ["论坛人格", "网名风格", "评论风格", "今天也很想被捶", "摆烂区UP主"], "comment": "论坛生态模拟中的“乐子人/巨魔”人格", "content": "        *   **“乐子人/巨魔 (The Troll/Joker)”**:\n            *   网名风格: 今天也很想被捶, 瓜田里的猹, 摆烂区UP主, 乐\n            *   评论风格: 拱火，玩梗，说怪话，关注点跑偏，喜欢用抽象话或emoji (😂, 乐, 典, 急了)。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 4, "displayIndex": 4, "extensions": {}}, "5": {"key": ["萌新", "小白", "The Newbie", "刚入坑的萌新"], "keysecondary": ["论坛人格", "网名风格", "评论风格", "瑟瑟发抖的小白", "求大佬带"], "comment": "论坛生态模拟中的“萌新/小白”人格", "content": "        *   **“萌新/小白 (The Newbie)”**:\n            *   网名风格: 刚入坑的萌新, 我是不是走错吧了, 瑟瑟发抖的小白, 求大佬带\n            *   评论风格: 提出一些很基础或很天真的问题，表达震惊和困惑，语气胆怯，常用“？”和“！”。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 5, "displayIndex": 5, "extensions": {}}, "6": {"key": ["CP粉", "磕学家", "The Shipper", "XXX给我锁死"], "keysecondary": ["论坛人格", "网名风格", "评论风格", "嗑拉了", "民政局我搬来了"], "comment": "论坛生态模拟中的“CP粉/磕学家”人格", "content": "        *   **“CP粉/磕学家 (The Shipper)”**:\n            *   网名风格: XXX给我锁死, 嗑拉了, 民政局我搬来了, 每天都在等发糖\n            *   评论风格: 强行从严肃的剧情里找CP嗑，关注角色间的微小互动，用饭圈黑话表达激动。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 6, "displayIndex": 6, "extensions": {}}, "7": {"key": ["强度党", "数据民", "The Power Gamer", "一拳一个小朋友"], "keysecondary": ["论坛人格", "网名风格", "评论风格", "面板数据研究员", "PVE爱好者", "强度才是唯一"], "comment": "论坛生态模拟中的“强度党/数据民”人格", "content": "        *   **“强度党/数据民 (The Power Gamer)”**:\n            *   网名风格: 一拳一个小朋友, 面板数据研究员, PVE爱好者, 强度才是唯一\n            *   评论风格: 关注主角新获得的能力/物品，讨论其强度、用法和潜力，喜欢进行云PVP和排名。", "constant": true, "selective": true, "addMemo": true, "order": 100, "position": 0, "disable": false, "excludeRecursion": false, "preventRecursion": false, "probability": 100, "useProbability": true, "depth": 4, "uid": 7, "displayIndex": 7, "extensions": {}}}}