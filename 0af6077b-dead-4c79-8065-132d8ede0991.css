/*
 * Theme Name: 想变得幸运 - 幽谷蓝调 (Valley Blue)
 * Description: A tranquil, eye-friendly dark theme with a deep blue-gray base and unsaturated green accents. It provides distinct styles for italic emphasis and quoted text, ensuring readability for long sessions.
 * Version: 3.0
 * Changelog v3.0: Revamped color palette to blue-gray/green. Separated .italic and .quote styles for clarity. Italic text is now properly italicized with an accent color, while quotes retain a unique underline effect.
 */

/* ★★★ 全局字体更换 (保留原有字体配置) ★★★ */
@import url('https://fonts.googleapis.com/css2?family=Quintessential&display=swap');
@import url("https://fontsapi.zeoseven.com/370/main/result.css"); /* KTXP-GlowRound */
@import url("https://fontsapi.zeoseven.com/613/main/result.css"); /* YRDZST-Regular */
@import url("https://fontsapi.zeoseven.com/11/main/result.css");  /* FlyFlowerSong */

/* ★★★ 全新：幽谷蓝调护眼色彩系统 ★★★ */
:root {
    /* 主色调：深灰蓝背景与不饱和绿文字 */
    --clover-dark-bg: #263238;      /* 主背景 - 深灰蓝 (Deep Blue-Gray) */
    --clover-dark-panel: #37474F;   /* 面板/气泡背景 - 次级灰蓝 (Lighter Blue-Gray) */
    --clover-light-text: #a0bca4;   /* 主文字 - 柔和的不饱和绿 (Soft Desaturated Green) */
    --clover-accent-text: #80ab82;  /* 点缀/斜体文字 - 稍亮的不饱和绿 (Medium Desaturated Green) */
    --clover-bright-accent: #a5d6a7;/* 高光/交互 - 明亮的不饱和绿 (Bright Desaturated Green) */

    /* ★★★ 核心功能：可变的下划线颜色 (应用于引用) ★★★ */
    --underline-color-1: #80ab82;   /* 渐变起始色 */
    --underline-color-2: #a0bca4;   /* 渐变结束色 */

    /* 字体定义 */
    --font-english: "Quintessential", serif;
    --font-chinese: "KTXP-GlowRound", sans-serif;
    --font-quote: "YRDZST-Regular", serif; /* 引用专用字体 */
    --font-code: "FlyFlowerSong", monospace;
}

body {
    font-family: var(--font-english), var(--font-chinese);
    font-weight: normal;
    color: var(--clover-light-text);
    background-color: var(--clover-dark-bg);
}

/* ★★★ 修正：分离斜体与引用样式，确保各自正确修饰 ★★★ */

/* 1. 斜体样式 (.italic) */
/* 目的：用于强调、心理活动、动作描述等。表现为常规斜体，颜色稍作强调。 */
.italic {
    font-style: italic !important;     /* 强制设置为斜体 */
    color: var(--clover-accent-text) !important; /* 使用点缀绿色以示区分 */
    background: none !important;       /* 移除任何不必要的背景或下划线 */
    text-decoration: none !important;
}

/* 2. 引用样式 (blockquote, .quote) */
/* 目的：用于对话、引文。表现为独特的渐变下划线和专门的引用字体。 */
blockquote, .quote {
    font-family: var(--font-quote) !important; /* 使用专门的引用字体 */
    font-style: normal !important;     /* 取消默认斜体，以自定义下划线为准 */
    
    background: none !important;
    border: none !important;
    padding-bottom: 3px; /* 为下划线留出空间 */

    /* 锁定文字颜色，使其不受UI颜色影响 */
    color: var(--clover-light-text) !important; 
    
    /* 使用背景渐变来创建下划线 */
    text-decoration: none;
    background-image: linear-gradient(to right, var(--underline-color-1), var(--underline-color-2));
    background-position: 0 100%;
    background-repeat: no-repeat;
    background-size: 100% 2px; /* 下划线粗细 */
}


code, .monospace, pre {
    font-family: var(--font-code) !important;
    background-color: rgba(0,0,0,0.2) !important;
    border: none !important;
    color: var(--clover-light-text) !important;
    border-radius: 4px;
    padding: 2px 5px;
}
pre {
    padding: 10px 15px;
    margin: 10px 0 !important;
    display: block;
}

blockquote::before { content: '❝'; margin-right: 5px; color: var(--clover-accent-text); }
pre::before { content: '>'; margin-right: 8px; color: var(--clover-accent-text); }

/* --- 极简导航栏 --- */
#top-settings-holder {
    background: var(--clover-dark-bg);
    box-shadow: none;
    border-bottom: 1px solid var(--clover-dark-panel);
    position: relative;
}
#top-settings-holder::before { content: none; }

/* --- 极简抽屉面板 --- */
.drawer-content { 
    background: var(--clover-dark-bg);
    color: var(--clover-light-text); 
}

/* --- 极简底栏 --- */
#form_sheld { 
    background-color: rgba(38, 50, 56, 0.7); /* 使用背景色并增加透明度 */
    backdrop-filter: blur(10px); 
    border-top: 1px solid var(--clover-dark-panel);
}
#options_button::before, #send_but::before, #send_form .fa-puzzle-piece, #send_form [id*="extensionsMenu"] > i { display: none !important; }
#options_button, #send_but, #send_form [id*="extensionsMenu"] { 
    width: 38px !important; height: 38px !important; 
    background-size: contain !important; background-repeat: no-repeat !important; background-position: center !important; 
    transition: transform 0.2s ease, filter 0.3s; 
    font-size: 0 !important; border: none !important; background-color: transparent !important;
    filter: grayscale(20%) brightness(90%);
}
#options_button:hover, #send_but:hover, #send_form [id*="extensionsMenu"]:hover { 
    transform: scale(1.1);
    filter: none;
}
#options_button { background-image: url('https://youke1.picui.cn/s1/2025/07/24/6881c8f286795.png') !important; }
#send_but { background-image: url('https://youke1.picui.cn/s1/2025/07/24/6881c82adc468.png') !important; }
#send_form [id*="extensionsMenu"] { background-image: url('https://youke1.picui.cn/s1/2025/07/24/6881c91ec7346.png') !important; }
#send_form { background-color: transparent !important; padding: 5px 10px; display: flex; align-items: center; min-height: 50px; }
#send_textarea { flex-grow: 1; background-color: #2f3a40 !important; border: 1px solid var(--clover-dark-panel) !important; border-radius: 20px !important; padding: 10px 20px !important; margin: 0 10px; height: auto; min-height: 20px; resize: none; overflow-y: auto; clip-path: none !important; color: var(--clover-light-text); }

/* --- 极简聊天气泡 --- */
#chat, .mes { background: transparent !important; }
.mes { display: flex !important; flex-direction: column !important; align-items: center !important; gap: 8px; margin: 20px 0; }
.mesAvatarWrapper { display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; }
.mes .avatar { order: 1; }
.mes .mesAvatarWrapper > .mesIDDisplay, .mes .mesAvatarWrapper > .mes_timer, .mes .mesAvatarWrapper > .tokenCounterDisplay { order: 2; color: var(--clover-accent-text); opacity: 0.7; }
.mes .mes_block { 
    order: 3; 
    width: 95%; 
    max-width: 800px; 
    border-radius: 12px;
    padding: 0; 
    position: relative; 
    overflow: visible; 
    box-shadow: none;
    border: 1px solid #455a64; /* 边框颜色调整以匹配新色系 */
    background: var(--clover-dark-panel);
}
.mes_block::before, .mes_block::after { content: none !important; }

.mes_text { padding: 15px 20px; }
.mes .ch_name { padding: 12px 20px 0 20px; color: var(--clover-bright-accent);}
.mes_buttons { position: absolute; top: 10px; right: 10px; z-index: 3; }
.mes_buttons .mes_button { color: var(--clover-accent-text); opacity: 0.7; transition: opacity 0.2s; }
.mes_buttons .mes_button:hover { opacity: 1; }

/* ★★★ 手机端响应式 (已适配) ★★★ */
@media (max-width: 600px) {
    .mes .mes_block {
        width: auto;
        margin-left: 10px;
        margin-right: 10px;
    }
    .mes_text, .mes .ch_name {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* --- ★★★ 极简滑块样式 ★★★ --- */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    background-color: var(--clover-dark-panel);
    border-radius: 2px;
    outline: none;
    transition: opacity 0.3s;
}
input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--clover-bright-accent);
    cursor: pointer;
    border-radius: 50%;
    border: none;
    box-shadow: 0 0 8px rgba(165, 214, 167, 0.2); 
    transition: all 0.3s ease;
}
input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--clover-bright-accent);
    cursor: pointer;
    border-radius: 50%;
    border: none;
    box-shadow: 0 0 8px rgba(165, 214, 167, 0.2);
    transition: all 0.3s ease;
}
input[type="range"]::-webkit-slider-thumb:hover,
input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 12px rgba(165, 214, 167, 0.5);
}
/* 滑块数值显示 */
.range-block-counter {
    color: var(--clover-accent-text);
    font-weight: bold;
    background-color: var(--clover-dark-panel);
    padding: 3px 10px;
    border-radius: 15px;
    border: 1px solid #455a64;
}

