gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ha,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.K2=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.K2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Aa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.zf=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.UP=function(h){if(this.zf==null){this.zf=[];var k=this;this.VP(function(){k.h9()})}this.zf.push(h)};var d=_.na.setTimeout;b.prototype.VP=function(h){d(h,0)};b.prototype.h9=function(){for(;this.zf&&this.zf.length;){var h=this.zf;this.zf=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Wp(m)}}}this.zf=null};b.prototype.Wp=function(h){this.VP(function(){throw h;
})};var e=function(h){this.Ca=0;this.nf=void 0;this.Dr=[];this.vW=!1;var k=this.IF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.IF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.pfa),reject:h(this.xK)}};e.prototype.pfa=function(h){if(h===this)this.xK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Uga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.ofa(h):this.sT(h)}};e.prototype.ofa=function(h){var k=void 0;try{k=h.then}catch(l){this.xK(l);return}typeof k=="function"?this.Vga(k,h):this.sT(h)};e.prototype.xK=function(h){this.t0(2,h)};e.prototype.sT=function(h){this.t0(1,h)};e.prototype.t0=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.nf=k;this.Ca===2&&this.Efa();this.i9()};e.prototype.Efa=function(){var h=this;d(function(){if(h.Bda()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.nf)}},
1)};e.prototype.Bda=function(){if(this.vW)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.nf;return l(h)};e.prototype.i9=function(){if(this.Dr!=null){for(var h=0;h<this.Dr.length;++h)f.UP(this.Dr[h]);
this.Dr=null}};var f=new b;e.prototype.Uga=function(h){var k=this.IF();h.Ay(k.resolve,k.reject)};e.prototype.Vga=function(h,k){var l=this.IF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.Ay(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.Ay=function(h,k){function l(){switch(m.Ca){case 1:h(m.nf);
break;case 2:k(m.nf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Dr==null?f.UP(l):this.Dr.push(l);this.vW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Aa(h),n=m.next();!n.done;n=m.next())c(n.value).Ay(k,l)})};e.all=function(h){var k=_.Aa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).Ay(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Aa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Aa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Aa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.Ue?m.Ue.value=l:(m.Ue={next:this[1],Kk:this[1].Kk,head:this[1],key:k,value:l},m.list.push(m.Ue),this[1].Kk.next=m.Ue,this[1].Kk=m.Ue,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.Ue&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.Ue.Kk.next=k.Ue.next,k.Ue.next.Kk=
k.Ue.Kk,k.Ue.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Kk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).Ue};c.prototype.get=function(k){return(k=d(this,k).Ue)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,Ue:p}}return{id:m,list:n,index:-1,Ue:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Kk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Kk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Aa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Aa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{vV:e,WD:f}}return{vV:-1,WD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).WD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).vV}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.lt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.MZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&4096),Pb=!!(_.$a[0]&8192),Qb=!!(_.$a[0]&16),Rb=!!(_.$a[0]>>15&1);_.Sb=Ob?Pb:xb(610401301);_.Ub=Ob?Qb:xb(**********);_.Vb=Ob?Rb:xb(651175828);_.Wb=function(a){_.Wb[" "](a);return a};_.Wb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,pc,Ac,Lc,Zc,id;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.iZ;throw Error("j");};_.hc=function(a){return new _.gc(a)};_.jc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.ic(a)};_.kc=function(a){if(a instanceof _.ic)return a.jZ;throw Error("j");};_.mc=function(a){return a instanceof _.lc};_.nc=function(a){if(_.mc(a))return a.lZ;throw Error("j");};pc=function(a){return new _.oc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.rc=function(a){if(qc.test(a))return a};
_.sc=function(a){return a instanceof _.lc?_.nc(a):_.rc(a)};_.tc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.ksa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.Dea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.lsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.vc=function(a){var b=_.uc.apply(1,arguments);if(b.length===0)return _.jc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.jc(c)};_.wc=function(a,b){return a.lastIndexOf(b,0)==0};_.xc=function(a){return/^[\s\xa0]*$/.test(a)};_.yc=function(a,b){return a.indexOf(b)!=-1};
_.Dc=function(a,b){var c=0;a=(0,_.zc)(String(a)).split(".");b=(0,_.zc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Ac(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Ac(f[2].length==0,h[2].length==0)||Ac(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Ac=function(a,b){return a<b?-1:a>b?1:0};_.Ec=function(a,b){b=_.sc(b);b!==void 0&&(a.href=b)};_.Fc=function(a,b,c,d){b=_.sc(b);return b!==void 0?a.open(b,c,d):null};_.Gc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Hc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Jc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Lc=function(a){if(!_.Sb||!_.Kc)return!1;for(var b=0;b<_.Kc.brands.length;b++){var c=_.Kc.brands[b].brand;if(c&&_.yc(c,a))return!0}return!1};_.Mc=function(a){return _.yc(_.Jc(),a)};_.Nc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Oc=function(){return _.Sb?!!_.Kc&&_.Kc.brands.length>0:!1};_.Pc=function(){return _.Oc()?!1:_.Mc("Opera")};
_.Qc=function(){return _.Oc()?!1:_.Mc("Trident")||_.Mc("MSIE")};_.Sc=function(){return _.Oc()?!1:_.Mc("Edge")};_.Tc=function(){return _.Oc()?Lc("Microsoft Edge"):_.Mc("Edg/")};_.Uc=function(){return _.Oc()?Lc("Opera"):_.Mc("OPR")};_.Vc=function(){return _.Mc("Firefox")||_.Mc("FxiOS")};_.Wc=function(){return _.Oc()?Lc("Chromium"):(_.Mc("Chrome")||_.Mc("CriOS"))&&!_.Sc()||_.Mc("Silk")};
_.Xc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.Yc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
Zc=function(){return _.Sb?!!_.Kc&&!!_.Kc.platform:!1};_.$c=function(){return Zc()?_.Kc.platform==="Android":_.Mc("Android")};_.ad=function(){return _.Mc("iPhone")&&!_.Mc("iPod")&&!_.Mc("iPad")};_.bd=function(){return _.ad()||_.Mc("iPad")||_.Mc("iPod")};_.cd=function(){return Zc()?_.Kc.platform==="macOS":_.Mc("Macintosh")};_.dd=function(){return Zc()?_.Kc.platform==="Windows":_.Mc("Windows")};_.ed=function(){return Zc()?_.Kc.platform==="Chrome OS":_.Mc("CrOS")};
_.fd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.gd=function(a){return _.fd(a,a)};_.uc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.jd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.kd=function(a){var b=_.jd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.ld=function(){return Date.now()};var md=globalThis.trustedTypes,$b=md,bc;_.dc=function(a){this.iZ=a};_.dc.prototype.toString=function(){return this.iZ+""};_.nd=function(){return new _.dc(md?md.emptyHTML:"")}();_.gc=function(a){this.kZ=a};_.gc.prototype.toString=function(){return this.kZ};_.ic=function(a){this.jZ=a};_.ic.prototype.toString=function(){return this.jZ+""};_.lc=function(a){this.lZ=a};_.lc.prototype.toString=function(){return this.lZ};_.od=new _.lc("about:invalid#zClosurez");var qc;_.oc=function(a){this.wj=a};_.pd=[pc("data"),pc("http"),pc("https"),pc("mailto"),pc("ftp"),new _.oc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.qd=function(){return typeof URL==="function"}();qc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.rd=function(a,b){this.width=a;this.height=b};_.sd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.rd.prototype;_.g.clone=function(){return new _.rd(this.width,this.height)};_.g.iy=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.iy()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.zc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.td=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.ud=Math.random()*2147483648|0;var vd;vd=_.Xa.navigator;_.Kc=vd?vd.userAgentData||null:null;var Nd,Od,Wd;_.xd=_.Pc();_.yd=_.Qc();_.zd=_.Mc("Edge");_.Ad=_.zd||_.yd;_.Bd=_.Mc("Gecko")&&!(_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge"))&&!(_.Mc("Trident")||_.Mc("MSIE"))&&!_.Mc("Edge");_.Cd=_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge");_.Dd=_.Cd&&_.Mc("Mobile");_.Ed=_.cd();_.Fd=_.dd();_.Gd=(Zc()?_.Kc.platform==="Linux":_.Mc("Linux"))||_.ed();_.Id=_.$c();_.Jd=_.ad();_.Kd=_.Mc("iPad");_.Ld=_.Mc("iPod");_.Md=_.bd();Nd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Pd="",Td=function(){var a=_.Jc();if(_.Bd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.zd)return/Edge\/([\d\.]+)/.exec(a);if(_.yd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/WebKit\/(\S+)/.exec(a);if(_.xd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Td&&(Pd=Td?Td[1]:"");if(_.yd){var Ud=Nd();if(Ud!=null&&Ud>parseFloat(Pd)){Od=String(Ud);break a}}Od=Pd}_.Vd=Od;if(_.Xa.document&&_.yd){var Xd=Nd();Wd=Xd?Xd:parseInt(_.Vd,10)||void 0}else Wd=void 0;_.Yd=Wd;var de,ke,je;_.ae=function(a){return a?new _.Zd(_.$d(a)):id||(id=new _.Zd)};_.be=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.ce=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.ee=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:de.hasOwnProperty(d)?a.setAttribute(de[d],c):_.wc(d,"aria-")||_.wc(d,"data-")?a.setAttribute(d,c):a[d]=c})};de={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ge=function(a){return _.fe(a||window)};
_.fe=function(a){a=a.document;a=_.he(a)?a.documentElement:a.body;return new _.rd(a.clientWidth,a.clientHeight)};_.ie=function(a){return a?a.defaultView:window};_.le=function(a,b){var c=b[1],d=je(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.ee(d,c));b.length>2&&ke(a,d,b,2);return d};
ke=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.kd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Yb(f):f,e)}}};_.me=function(a){return je(document,a)};
je=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.he=function(a){return a.compatMode=="CSS1Compat"};_.ne=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.oe=function(a,b){ke(_.$d(a),a,arguments,1)};_.pe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.qe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.re=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.se=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.te=function(a){return _.vb(a)&&a.nodeType==1};
_.ue=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.$d=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.ve=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.pe(a),a.appendChild(_.$d(a).createTextNode(String(b)))};_.Zd=function(a){this.Bc=a||_.Xa.document||document};_.g=_.Zd.prototype;_.g.Ha=_.ae;_.g.BL=_.jb(0);_.g.ub=function(){return this.Bc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Bc).getElementsByTagName(String(a))};
_.g.BH=_.jb(2);_.g.wa=function(a,b,c){return _.le(this.Bc,arguments)};_.g.createElement=function(a){return je(this.Bc,a)};_.g.createTextNode=function(a){return this.Bc.createTextNode(String(a))};_.g.getWindow=function(){return this.Bc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.oe;_.g.canHaveChildren=_.ne;_.g.ke=_.pe;_.g.SV=_.qe;_.g.removeNode=_.re;_.g.LG=_.se;_.g.isElement=_.te;_.g.contains=_.ue;_.g.eH=_.$d;_.g.uj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.we=function(a){return a===null?"null":a===void 0?"undefined":a};_.xe=window;_.ye=document;_.ze=_.xe.location;_.Ae=/\[native code\]/;_.Be=function(a,b,c){return a[b]=a[b]||c};_.Ce=function(){var a;if((a=Object.create)&&_.Ae.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.De=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Ee=function(a,b){a=a||{};for(var c in a)_.De(a,c)&&(b[c]=a[c])};_.Fe=_.Be(_.xe,"gapi",{});_.Ge=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.He=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ie=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Je=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Le=function(a,b,c){_.Ke(a,b,c,"add","at")};_.Ke=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Me={};_.Me=_.Be(_.xe,"___jsl",_.Ce());_.Be(_.Me,"I",0);_.Be(_.Me,"hel",10);var Ne,Pe,Qe,Re,Ue,Se,Te,Ve,We;Ne=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Pe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Qe=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Re=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Qe(a[d])&&!Qe(b[d])?Re(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Qe(b[d])?[]:{},Re(a[d],b[d])):a[d]=b[d])};
Ue=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ne("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Se())if(e=Te(c),d.push(25),typeof e===
"object")return e;return{}}};Se=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Te=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Re(c,b);a.push(c)};
We=function(a){Pe(!0);var b=window.___gcfg,c=Ne("cu"),d=window.___gu;b&&b!==d&&(Ve(c,b),window.___gu=b);b=Ne("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ne("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Ue(f,h))&&b.push(f));a&&Ve(c,a);d=Ne("cd");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);d=Ne("ci");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Re(Pe(),c[a],!0)};_.Xe=function(a,b){var c=Pe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ye=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;We(c)};var Ze=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Be(_.Me,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Ze&&Ze();We();_.t("gapi.config.get",_.Xe);_.t("gapi.config.update",_.Ye);
_.$e=function(a){a=_.we(a);return _.ec(a)};
_.Fg=(window.gapi||{}).load;
_.lo=_.Be(_.Me,"rw",_.Ce());
var mo=function(a,b){(a=_.lo[a])&&a.state<b&&(a.state=b)};var no=function(a){a=(a=_.lo[a])?a.oid:void 0;if(a){var b=_.ye.getElementById(a);b&&b.parentNode.removeChild(b);delete _.lo[a];no(a)}};_.oo=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.po=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.qo=function(a,b){var c={},d=a.wc(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();mo(e,2);a:{e=a.getSiteEl();c=c||{};var k;if(_.Me.oa&&(k=d.id)){f=(f=_.lo[k])?f.state:void 0;if(f===1||f===4)break a;no(k)}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";l.margin=
"0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&mo(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.ro=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.so=function(a){a.where=_.oo(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.qo(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.ro(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.po(e)}};
_.ef=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ye(a());return{register:function(b,c,d){d&&d(_.Xe())},get:function(b){return _.Xe(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ye(b)},init:function(){}}}();_.t("gadgets.config.register",_.ef.register);_.t("gadgets.config.get",_.ef.get);_.t("gadgets.config.init",_.ef.init);_.t("gadgets.config.update",_.ef.update);
var ff,gf,hf,jf,lf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Nf,Of,Pf,Sf,Tf;hf=void 0;jf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};lf=function(a){return Object.prototype.toString.call(a)};nf=lf(0);of=lf(new Date(0));pf=lf(!0);qf=lf("");rf=lf({});sf=lf([]);
tf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=lf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==sf||a.constructor!==Array&&a.constructor!==Object)&&(e!==rf||a.constructor!==Array&&a.constructor!==Object)&&e!==qf&&e!==nf&&e!==pf&&e!==of))return tf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===nf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===pf)b[b.length]=String(!!Number(a));else{if(e===of)return tf(a.toISOString.call(a),c);if(e===sf&&lf(a.length)===nf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=tf(a[f],c)||"null";b[b.length]="]"}else if(e==qf&&lf(a.length)===nf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=tf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=tf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};uf=/[\0-\x07\x0b\x0e-\x1f]/;
vf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;wf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;yf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;zf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Af=/[ \t\n\r]+/g;Bf=/[^"]:/;Df=/""/g;Gf=/true|false|null/g;Hf=/00/;If=/[\{]([^0\}]|0[^:])/;Jf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Kf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(uf.test(a)||vf.test(a)||wf.test(a)||xf.test(a))return!1;var b=a.replace(yf,'""');b=b.replace(zf,"0");b=b.replace(Af,"");if(Bf.test(b))return!1;b=b.replace(Df,"0");b=b.replace(Gf,"0");if(Hf.test(b)||If.test(b)||Jf.test(b)||Kf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=hf?[jf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((ff===void 0||hf===void 0||gf!==a)&&gf!==-1){ff=hf=!1;gf=-1;try{try{hf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&jf("true")===!0&&jf('[{"a":3}]')[0].a===3}catch(b){}ff=hf&&!jf("[00]")&&!jf('"\u0007"')&&!jf('"\\0"')&&!jf('"\\v"')}finally{gf=a}}};_.Qf=function(a){if(gf===-1)return!1;Pf();return(ff?jf:Of)(a)};
_.Rf=function(a){if(gf!==-1)return Pf(),hf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):tf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
_.t("gadgets.json.stringify",_.Rf);_.t("gadgets.json.parse",_.Qf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.bf=function(e){a(2,e)};_.cf=function(e){a(3,e)};_.df=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.af=_.af||{};
_.af=_.af||{};(function(){var a=[];_.af.psa=function(b){a.push(b)};_.af.Dsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.af=_.af||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.af.Qg=a;a()})();_.t("gadgets.util.getUrlParameters",_.af.Qg);
var Uf=function(){this.Dg=window.console};Uf.prototype.log=function(a){this.Dg&&this.Dg.log&&this.Dg.log(a)};Uf.prototype.error=function(a){this.Dg&&(this.Dg.error?this.Dg.error(a):this.Dg.log&&this.Dg.log(a))};Uf.prototype.warn=function(a){this.Dg&&(this.Dg.warn?this.Dg.warn(a):this.Dg.log&&this.Dg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Wf=function(){var a=_.ye.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.xe.addEventListener?(_.xe.addEventListener("load",c,!1),_.xe.addEventListener("DOMContentLoaded",c,!1)):_.xe.attachEvent&&(_.xe.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.xe.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Be(_.Me,"watt",_.Ce());_.Be(c,a,b)};_.Ge(_.xe.location.href,"rpctoken")&&_.Le(_.ye,"unload",function(){});var Zf=Zf||{};Zf.c_=null;Zf.MX=null;Zf.JA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.aO||(Zf.aO=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.df();var k=_.$f.Zn(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.cf("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{IT:function(){return"wpm"},Aca:function(){return!0},init:function(f,h){_.ef.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.Zn(f),m=_.$f.hP(f);l?window.setTimeout(function(){var n=_.Rf(k);_.df();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.cf("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.EZ=_.$f.registerDefault,_.$f.W1=_.$f.unregisterDefault,_.$f.nT=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Fu=_.$f.getRelayUrl,_.$f.Nj=_.$f.setRelayUrl,_.$f.SC=_.$f.setAuthToken,_.$f.Hw=_.$f.setupReceiver,_.$f.Nn=_.$f.getAuthToken,_.$f.CK=_.$f.removeReceiver,_.$f.jU=_.$f.getRelayChannel,_.$f.zZ=_.$f.receive,
_.$f.AZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.Zn=_.$f.getTargetOrigin,_.$f.hP=_.$f._getTargetWin,_.$f.Z6=_.$f._parseSiblingId);else{_.$f=function(){function a(I,ka){if(!T[I]){var ma=cb;ka||(ma=Oa);T[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var U=ka[Fa];U.t=E[I];ma.call(I,U.f,U)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,U){E[ka]&&E[ka]===ma||(_.cf("Invalid gadgets.rpc token. "+E[ka]+" vs "+ma),qb(ka,2));U.onunload=function(){R[ka]&&!Mb&&(qb(ka,1),_.$f.CK(ka))};b();Fa=_.Qf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(E[I.f]&&E[I.f]!==I.t&&(_.cf("Invalid gadgets.rpc token. "+E[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.$f.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var U=e(Fa)}catch(Ga){}Fa&&U==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.$f.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var U=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&U!=="80"||I==="https"&&U!=="443")ma=":"+U}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(R[I]!==!0){typeof R[I]===
"undefined"&&(R[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?R[I]!==!0&&R[I]++<10?window.setTimeout(function(){l(I,ka)},500):(T[I]=Oa,R[I]=!0):R[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(D[I]=!!ma)}function p(I,ka){ka=ka||"";E[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);O=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Zf.JA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var U=Fa.parentRelayUrl||"";U=e(aa.parent||ka)+U;n("..",U,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!aa.parent&&
ka?ma({}):_.ef.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||aa.rpctoken||aa.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.af)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.af.Qg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},D={},E={},N=0,H={},R={},aa={},T={},K={},O=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{IT:function(){return"noop"},Aca:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.af&&(aa=_.af.Qg());var Mb=!1,Hb=!1,cb=function(){if(aa.rpctx=="rmr")return Zf.c_;var I=typeof window.postMessage==="function"?Zf.aO:typeof window.postMessage==="object"?Zf.aO:window.ActiveXObject?Zf.MX?Zf.MX:Zf.JA:navigator.userAgent.indexOf("WebKit")>0?Zf.c_:navigator.product==="Gecko"?Zf.frameElement:Zf.JA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=H[I];ma&&(delete H[I],ma.call(this,ka))};return{config:function(I){typeof I.q_==="function"&&(qb=I.q_)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},EZ:function(I){x[""]=I},W1:function(){delete x[""]},nT:function(){},call:function(I,ka,ma,Fa){I=I||"..";var U="..";I===".."?U=La:I.charAt(0)=="/"&&(U=
e(window.location.href),U="/"+La+(U?"|"+U:""));++N;ma&&(H[N]=ma);var Ga={s:ka,f:U,c:ma?N:0,a:Array.prototype.slice.call(arguments,3),t:E[I],l:!!D[I]};a:if(O==="bidir"||O==="c2p"&&I===".."||O==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=T[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:N),Ga.g=!0,Ga.r=U,Ha?(D[I]&&(Ha=Zf.JA),Ha.call(I,U,Ga)===!1&&(T[I]=Oa,cb.call(I,U,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Fu:m,Nj:n,SC:p,Hw:u,Nn:function(I){return E[I]},CK:function(I){delete A[I];delete D[I];delete E[I];delete R[I];delete T[I]},jU:function(){return cb.IT()},zZ:function(I,ka){I.length>4?cb.Opa(I,d):c.apply(null,I.concat(ka))},AZ:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,Zn:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=aa.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.ef.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},hP:h,Z6:f,jia:"__ack",cna:La||"..",mna:0,lna:1,kna:2}}();_.$f.init()};_.$f.config({q_:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.$f.config);_.t("gadgets.rpc.register",_.$f.register);_.t("gadgets.rpc.unregister",_.$f.unregister);_.t("gadgets.rpc.registerDefault",_.$f.EZ);_.t("gadgets.rpc.unregisterDefault",_.$f.W1);_.t("gadgets.rpc.forceParentVerifiable",_.$f.nT);_.t("gadgets.rpc.call",_.$f.call);_.t("gadgets.rpc.getRelayUrl",_.$f.Fu);_.t("gadgets.rpc.setRelayUrl",_.$f.Nj);_.t("gadgets.rpc.setAuthToken",_.$f.SC);_.t("gadgets.rpc.setupReceiver",_.$f.Hw);_.t("gadgets.rpc.getAuthToken",_.$f.Nn);
_.t("gadgets.rpc.removeReceiver",_.$f.CK);_.t("gadgets.rpc.getRelayChannel",_.$f.jU);_.t("gadgets.rpc.receive",_.$f.zZ);_.t("gadgets.rpc.receiveSameDomain",_.$f.AZ);_.t("gadgets.rpc.getOrigin",_.$f.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.$f.Zn);
_.Ig=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("s`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("t`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
var Mg=function(){this.blockSize=-1},Ng=function(){this.blockSize=-1;this.blockSize=64;this.Rc=[];this.fF=[];this.M6=[];this.SB=[];this.SB[0]=128;for(var a=1;a<this.blockSize;++a)this.SB[a]=0;this.ID=this.Zq=0;this.reset()};_.eb(Ng,Mg);Ng.prototype.reset=function(){this.Rc[0]=1732584193;this.Rc[1]=4023233417;this.Rc[2]=2562383102;this.Rc[3]=271733878;this.Rc[4]=3285377520;this.ID=this.Zq=0};
var Og=function(a,b,c){c||(c=0);var d=a.M6;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Rc[0];c=a.Rc[1];e=a.Rc[2];for(var f=a.Rc[3],h=a.Rc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Rc[0]=a.Rc[0]+b&4294967295;a.Rc[1]=a.Rc[1]+c&4294967295;a.Rc[2]=a.Rc[2]+e&4294967295;a.Rc[3]=a.Rc[3]+f&4294967295;a.Rc[4]=a.Rc[4]+h&4294967295};
Ng.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.fF,f=this.Zq;d<b;){if(f==0)for(;d<=c;)Og(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Og(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Og(this,e);f=0;break}}this.Zq=f;this.ID+=b}};
Ng.prototype.digest=function(){var a=[],b=this.ID*8;this.Zq<56?this.update(this.SB,56-this.Zq):this.update(this.SB,this.blockSize-(this.Zq-56));for(var c=this.blockSize-1;c>=56;c--)this.fF[c]=b&255,b/=256;Og(this,this.fF);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Rc[c]>>d&255,++b;return a};_.Pg=function(){this.iN=new Ng};_.g=_.Pg.prototype;_.g.reset=function(){this.iN.reset()};_.g.Y1=function(a){this.iN.update(a)};_.g.uR=function(){return this.iN.digest()};_.g.tx=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.Y1(b)};_.g.Ri=function(){for(var a=this.uR(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
var th;_.sh=function(a){_.Xa.setTimeout(function(){throw a;},0)};th=0;_.uh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++th)};
_.vh=function(){return _.Mc("Safari")&&!(_.Wc()||(_.Oc()?0:_.Mc("Coast"))||_.Pc()||_.Sc()||_.Tc()||_.Uc()||_.Vc()||_.Mc("Silk")||_.Mc("Android"))};_.wh=function(){return _.Mc("Android")&&!(_.Wc()||_.Vc()||_.Pc()||_.Mc("Silk"))};_.yh=_.Vc();_.zh=_.ad()||_.Mc("iPod");_.Ah=_.Mc("iPad");_.Bh=_.wh();_.Ch=_.Wc();_.Dh=_.vh()&&!_.bd();
_.di=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ei=function(a){var b=_.di();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.fi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.gi=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var hi;hi=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ii=function(a){var b=_.ei("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Ge(a,"authuser")||null,b==null&&(b=(b=a.match(hi))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.vi=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var wi=function(){var a=_.Me.ms||_.Me.u;if(a)return(new URL(a)).origin};var Di=function(a){this.fT=a;this.count=this.count=0};Di.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.fT&&(b===void 0||b)&&this.fT()};Di.prototype.get=function(){return this.count};Di.prototype.reset=function(){this.count=0};var Fi,Ii;Fi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Ry=new Map;this.YE=!1;var c=wi();c&&(this.url=c+"/js/gen_204",c=_.ei("gen204logger")||{},this.iu=c.interval,this.gT=c.rate,this.YE=c.aqa,a&&this.url&&Ei(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.Gi=function(){Fi.iX||(Fi.iX=new Fi);return Fi.iX};
Ii=function(a){var b=_.Me.dm||[];if(b&&b.length!==0){b=_.Aa(b);for(var c=b.next();!c.done;c=b.next())_.Hi(a,c.value).rb(1,!1);delete _.Me.dm;a.flush()}};_.Hi=function(a,b){a.Ry.has(b)||a.Ry.set(b,new Di(a.YE?void 0:function(){a.flush()}));return a.Ry.get(b)};
Fi.prototype.flush=function(){var a=this;if(this.url&&this.gT){Ii(this);for(var b="",c=_.Aa(this.Ry),d=c.next();!d.done;d=c.next()){var e=_.Aa(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.gT){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){Ei(a)})}}};
Fi.prototype.setInterval=function(a){this.iu=a};var Ei=function(a){a.iu&&a.YE&&setTimeout(function(){a.flush()},a.iu)};var Ki,Ji,Qi,Ri,Li,Oi,Mi,Si,Ni;_.Pi=function(){_.Hi(_.Gi(),50).rb();if(Ji){var a=new _.xe.Uint32Array(1);Ki.getRandomValues(a);a=Number("0."+a[0])}else a=Li,a+=parseInt(Mi.substr(0,20),16),Mi=Ni(Mi),a/=Oi+1.2089258196146292E24;return a};Ki=_.xe.crypto;Ji=!1;Qi=0;Ri=0;Li=1;Oi=0;Mi="";Si=function(a){a=a||_.xe.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Li=Li*b%Oi;Qi>0&&++Ri==Qi&&_.Ke(_.xe,"mousemove",Si,"remove","de")};
Ni=function(a){var b=new _.Pg;b.tx(a);return b.Ri()};Ji=!!Ki&&typeof Ki.getRandomValues=="function";Ji||(Oi=(screen.width*screen.width+screen.height)*1E6,Mi=Ni(_.ye.cookie+"|"+_.ye.location+"|"+(new Date).getTime()+"|"+Math.random()),Qi=_.ei("random/maxObserveMousemove")||0,Qi!=0&&_.Le(_.xe,"mousemove",Si));
_.bj=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
var hj;_.gj=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.ij=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<hj.length;f++)c=hj[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};hj="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.jj=[];_.kj=[];_.lj=!1;
_.mj=function(a){_.jj[_.jj.length]=a;if(_.lj)for(var b=0;b<_.kj.length;b++)a((0,_.z)(_.kj[b].wrap,_.kj[b]))};
var bk=function(a){this.T=a};_.g=bk.prototype;_.g.value=function(){return this.T};_.g.Me=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.Sd=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Di=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.ck=function(a){this.T=a||{}};_.g=_.ck.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Di=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Le=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.Xm=function(a){this.T.rpctoken=a;return this};_.dk=function(a,b){a.T.messageHandlers=b;return a};_.ek=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.ck.prototype;_.g.Ur=_.jb(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Tn=function(){this.T.attributes=this.T.attributes||{};return new bk(this.T.attributes)};_.g.Jz=_.jb(5);
var jk;_.fk=function(a){var b={},c;for(c in a)b[c]=a[c];return b};jk=function(){for(var a;a=gk.remove();){try{a.Qh.call(a.scope)}catch(b){_.sh(b)}hk.put(a)}ik=!1};_.kk=function(a){if(!(a instanceof Array)){a=_.Aa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.lk=function(){};_.mk=function(a){a.prototype.$goog_Thenable=!0};_.nk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ok=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var pk=function(a,b){this.x8=a;this.mfa=b;this.EB=0;this.EA=null};pk.prototype.get=function(){if(this.EB>0){this.EB--;var a=this.EA;this.EA=a.next;a.next=null}else a=this.x8();return a};pk.prototype.put=function(a){this.mfa(a);this.EB<100&&(this.EB++,a.next=this.EA,this.EA=a)};_.qk=function(a){return a};_.mj(function(a){_.qk=a});var rk=function(){this.aE=this.Ms=null};rk.prototype.add=function(a,b){var c=hk.get();c.set(a,b);this.aE?this.aE.next=c:this.Ms=c;this.aE=c};rk.prototype.remove=function(){var a=null;this.Ms&&(a=this.Ms,this.Ms=this.Ms.next,this.Ms||(this.aE=null),a.next=null);return a};var hk=new pk(function(){return new sk},function(a){return a.reset()}),sk=function(){this.next=this.scope=this.Qh=null};sk.prototype.set=function(a,b){this.Qh=a;this.scope=b;this.next=null};
sk.prototype.reset=function(){this.next=this.scope=this.Qh=null};var tk,ik,gk,uk;ik=!1;gk=new rk;_.vk=function(a,b){tk||uk();ik||(tk(),ik=!0);gk.add(a,b)};uk=function(){var a=Promise.resolve(void 0);tk=function(){a.then(jk)}};var yk,zk,Ak,Ok,Sk,Qk,Tk;_.xk=function(a,b){this.Ca=0;this.nf=void 0;this.Zp=this.Al=this.Gb=null;this.uA=this.mG=!1;if(a!=_.lk)try{var c=this;a.call(b,function(d){wk(c,2,d)},function(d){wk(c,3,d)})}catch(d){wk(this,3,d)}};yk=function(){this.next=this.context=this.Cr=this.Nv=this.vn=null;this.Yx=!1};yk.prototype.reset=function(){this.context=this.Cr=this.Nv=this.vn=null;this.Yx=!1};zk=new pk(function(){return new yk},function(a){a.reset()});
Ak=function(a,b,c){var d=zk.get();d.Nv=a;d.Cr=b;d.context=c;return d};_.Bk=function(a){if(a instanceof _.xk)return a;var b=new _.xk(_.lk);wk(b,2,a);return b};_.Ck=function(a){return new _.xk(function(b,c){c(a)})};_.Ek=function(a,b,c){Dk(a,b,c,null)||_.vk(_.bb(b,a))};_.Fk=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,_.bb(f,l),h);else b(e)})};
_.Hk=function(){var a,b,c=new _.xk(function(d,e){a=d;b=e});return new Gk(c,a,b)};_.xk.prototype.then=function(a,b,c){return Ik(this,(0,_.ok)(typeof a==="function"?a:null),(0,_.ok)(typeof b==="function"?b:null),c)};_.mk(_.xk);var Kk=function(a,b,c,d){Jk(a,Ak(b||_.lk,c||null,d))};_.xk.prototype.finally=function(a){var b=this;a=(0,_.ok)(a);return new Promise(function(c,d){Kk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.xk.prototype.DD=function(a,b){return Ik(this,null,(0,_.ok)(a),b)};
_.xk.prototype.catch=_.xk.prototype.DD;_.xk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.Lk(a);_.vk(function(){Mk(this,b)},this)}};
var Mk=function(a,b){if(a.Ca==0)if(a.Gb){var c=a.Gb;if(c.Al){for(var d=0,e=null,f=null,h=c.Al;h&&(h.Yx||(d++,h.vn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Mk(c,b):(f?(d=f,d.next==c.Zp&&(c.Zp=d),d.next=d.next.next):Nk(c),Ok(c,e,3,b)))}a.Gb=null}else wk(a,3,b)},Jk=function(a,b){a.Al||a.Ca!=2&&a.Ca!=3||Pk(a);a.Zp?a.Zp.next=b:a.Al=b;a.Zp=b},Ik=function(a,b,c,d){var e=Ak(null,null,null);e.vn=new _.xk(function(f,h){e.Nv=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Cr=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.Lk?h(k):f(l)}catch(m){h(m)}}:h});e.vn.Gb=a;Jk(a,e);return e.vn};_.xk.prototype.Eha=function(a){this.Ca=0;wk(this,2,a)};_.xk.prototype.Fha=function(a){this.Ca=0;wk(this,3,a)};
var wk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,Dk(c,a.Eha,a.Fha,a)||(a.nf=c,a.Ca=b,a.Gb=null,Pk(a),b!=3||c instanceof _.Lk||Qk(a,c)))},Dk=function(a,b,c,d){if(a instanceof _.xk)return Kk(a,b,c,d),!0;if(_.nk(a))return a.then(b,c,d),!0;if(_.vb(a))try{var e=a.then;if(typeof e==="function")return Rk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Rk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Pk=function(a){a.mG||(a.mG=!0,_.vk(a.rz,a))},Nk=function(a){var b=null;a.Al&&(b=a.Al,a.Al=b.next,b.next=null);a.Al||(a.Zp=null);return b};_.xk.prototype.rz=function(){for(var a;a=Nk(this);)Ok(this,a,this.Ca,this.nf);this.mG=!1};Ok=function(a,b,c,d){if(c==3&&b.Cr&&!b.Yx)for(;a&&a.uA;a=a.Gb)a.uA=!1;if(b.vn)b.vn.Gb=null,Sk(b,c,d);else try{b.Yx?b.Nv.call(b.context):Sk(b,c,d)}catch(e){Tk.call(null,e)}zk.put(b)};
Sk=function(a,b,c){b==2?a.Nv.call(a.context,c):a.Cr&&a.Cr.call(a.context,c)};Qk=function(a,b){a.uA=!0;_.vk(function(){a.uA&&Tk.call(null,b)})};Tk=_.sh;_.Lk=function(a){_.lb.call(this,a);this.MZ=!1};_.eb(_.Lk,_.lb);_.Lk.prototype.name="cancel";var Gk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Uk=function(a){return new _.xk(a)};
var bl=function(){this.mx={GZ:Vk?"../"+Vk:null,dz:Wk,xU:Xk,Wra:Yk,co:Zk,Nsa:$k};this.Uf=_.xe;this.XY=this.E8;this.y9=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.mx.GZ){this.Uf=this.mx.xU(this.Uf,this.mx.GZ);var a=this.Uf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.XY=this.Uf.doPostMsg}this.jN={};this.MN={};a=(0,_.z)(this.KH,
this);_.Le(this.Uf,"message",a);_.Be(_.Me,"RPMQ",[]).push(a);this.Uf!=this.Uf.parent&&al(this,this.Uf.parent,this.cJ(this.Uf.name),"*")};bl.prototype.cJ=function(a){return'{"h":"'+escape(a)+'"}'};var cl=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},dl=function(a){if(!/^\s*{/.test(a))return!1;a=_.Qf(a);return a!==null&&typeof a==="object"&&!!a.g};
bl.prototype.KH=function(a){var b=String(a.data);_.Vf.debug("gapix.rpc.receive("+Yk+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=dl(b);if(!c&&!d){if(!d&&(c=cl(b))){if(this.jN[c])this.jN[c]();else this.MN[c]=1;return}var e=a.origin,f=this.mx.dz;this.y9?_.xe.setTimeout(function(){f(b,e)},0):f(b,e)}};bl.prototype.Ib=function(a,b){a===".."||this.MN[a]?(b(),delete this.MN[a]):this.jN[a]=b};
var al=function(a,b,c,d){var e=dl(c)?"":"!_";_.Vf.debug("gapix.rpc.send("+Yk+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.XY(b,e+c,d)};bl.prototype.E8=function(a,b,c){a.postMessage(b,c)};bl.prototype.send=function(a,b,c){(a=this.mx.xU(this.Uf,a))&&!a.closed&&al(this,a,b,c)};var el,fl,gl,hl,il,jl,kl,Vk,Yk,ll,ml,nl,Xk,Zk,pl,ql,Al,Bl,Dl,$k,Fl,El,rl,sl,Gl,Wk,Hl,Il;el=0;fl=[];gl={};hl={};il=_.xe.location.href;jl=_.Ge(il,"rpctoken");kl=_.Ge(il,"parent")||_.ye.referrer;Vk=_.Ge(il,"rly");Yk=Vk||(_.xe!==_.xe.top||_.xe.opener)&&_.xe.name||"..";ll=null;ml={};nl=function(){};_.ol={send:nl,Ib:nl,cJ:nl};
Xk=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.xe.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.Aa(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("F`"+c+"`"+a);}}else return null}return c};Zk=function(a){return(a=gl[a])&&a.token};pl=function(a){if(a.f in{})return!1;var b=a.t,c=gl[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
ql=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.tl=function(a,b,c){a=rl(a);hl[a.name]={Qh:b,Bv:a.Bv,Ks:c||pl};sl()};_.ul=function(a){a=rl(a);delete hl[a.name]};Al={};Bl=function(a,b){(a=Al["_"+a])&&a[1](this)&&a[0].call(this,b)};Dl=function(a){var b=a.c;if(!b)return nl;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.Cl.apply(null,e)}};
$k=function(a){ll=a};Fl=function(a){ml[a]||(ml[a]=_.xe.setTimeout(function(){ml[a]=!1;El(a)},0))};El=function(a){var b=gl[a];if(b&&b.ready){var c=b.pK;for(b.pK=[];c.length;)_.ol.send(a,_.Rf(c.shift()),b.origin)}};rl=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),Bv:!0}:{name:a,Bv:!1}};
sl=function(){for(var a=_.ei("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=fl[d];++d){var e=c.hp;if(!e||a>0&&b-c.timestamp>a)fl.splice(d,1),--d;else{var f=e.s,h=hl[f]||hl["*"];if(h)if(fl.splice(d,1),--d,e.origin=c.origin,c=Dl(e),e.callback=c,h.Ks(e)){if(f!=="__cb"&&!!h.Bv!=!!e.g)break;e=h.Qh.apply(e,e.a);e!==void 0&&c(e)}else _.Vf.debug("gapix.rpc.rejected("+Yk+"): "+f)}}};Gl=function(a,b,c){fl.push({hp:a,origin:b,timestamp:(new Date).getTime()/1E3});c||sl()};
Wk=function(a,b){a=_.Qf(a);Gl(a,b,!1)};Hl=function(a){for(;a.length;)Gl(a.shift(),this.origin,!0);sl()};Il=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",yI:b}};
_.Jl=function(a,b,c,d){var e=Il(a);d&&(_.xe.frames[e.id]=_.xe.frames[e.id]||d);a=e.id;if(!gl.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Ig(kl),c=c||jl;else if(!e.yI){var f=_.ye.getElementById(a);f&&(f=f.src,d=_.Ig(f),c=c||_.Ge(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);gl[a]={token:c,pK:[],origin:d,yfa:b,yZ:function(){var h=a;gl[h].ready=1;El(h)}};_.ol.Ib(a,gl[a].yZ)}return gl[a].yZ};
_.Cl=function(a,b,c,d){a=a||"..";_.Jl(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Yk,m=jl,n=gl[f],p=l,q=Il(f);if(n&&f!==".."){if(q.yI){if(!(m=gl[f].yfa)){m=ll?ll.substring(1).split("/"):[Yk];p=m.length-1;for(f=_.xe.parent;f!==_.xe.top;){var r=f.parent;if(!p--){for(var w=null,u=r.frames.length,x=0;x<u;++x)r.frames[x]==f&&(w=x);m.unshift("{"+w+"}")}f=r}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=pl,q.yI&&(n=ql(q)),Al["_"+ ++el]=[k,n],k=el):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=rl(e);h.s=e.name;h.g=e.Bv;gl[a].pK.push(h);Fl(a)};if(typeof _.xe.postMessage==="function"||typeof _.xe.postMessage==="object")_.ol=new bl,_.tl("__cb",Bl,function(){return!0}),_.tl("_processBatch",Hl,function(){return!0}),_.Jl("..");
var Ll,Ml,Nl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Xl,Yl,bm,cm,dm,em,fm,gm,hm,im;_.Kl=function(a,b){if(!a)throw Error(b||"");};Ll=/&/g;Ml=/</g;Nl=/>/g;Ol=/"/g;Pl=/'/g;Ql=function(a){return String(a).replace(Ll,"&amp;").replace(Ml,"&lt;").replace(Nl,"&gt;").replace(Ol,"&quot;").replace(Pl,"&#39;")};Rl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Sl=/%([a-f]|[0-9a-fA-F][a-f])/g;Tl=/^(https?|ftp|file|chrome-extension):$/i;
Ul=function(a){a=String(a);a=a.replace(Rl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Ie,function(e){return e.replace(/%/g,"%25")}).replace(Sl,function(e){return e.toUpperCase()});a=a.match(_.He)||[];var b=_.Ce(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Tl);b.lt=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Wi=a[7]?[d(a[7])]:[];return b};Xl=function(a){return a.lt+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Wi.length>0?"#"+a.Wi.join("&"):"")};Yl=function(a,b){var c=[];if(a)for(var d in a)if(_.De(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Zl=function(a,b,c,d){a=Ul(a);a.query.push.apply(a.query,Yl(b,d));a.Wi.push.apply(a.Wi,Yl(c,d));return Xl(a)};
_.$l=function(a,b){var c=Ul(b);b=c.lt;c.query.length&&(b+="?"+c.query.join(""));c.Wi.length&&(b+="#"+c.Wi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Je,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Ul(b);b=c.lt;c.query.length&&(b+="?"+c.query.join(""));c.Wi.length&&(b+="#"+c.Wi.join(""));_.Ec(a,new _.lc(_.we(b)));e.appendChild(a);_.Hc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Ul(b+d);b=
c.lt;c.query.length&&(b+="?"+c.query.join(""));c.Wi.length&&(b+="#"+c.Wi.join(""));return b};_.am=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;cm=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};dm=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
em=function(){var a=_.ei("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(dm))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};fm=function(){var a=_.Me.onl;if(!a){a=_.Ce();_.Me.onl=a;var b=_.Ce();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};gm=function(a,b){b=b.onload;return typeof b==="function"?(fm().a(a,b),b):null};
hm=function(a){_.Kl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};im=function(a){fm().r(a)};var km,lm,pm;_.jm={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};km={allowtransparency:!0,onload:!0};lm=0;_.mm=function(a,b){var c=0;do var d=b.id||["I",lm++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.Kl(c<5,"Error creating iframe id");return d};_.nm=function(a,b){return a?b+"/"+a:""};
_.om=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.Ee(d.queryParams||{},e);_.Ee(d.fragmentParams||{},f);var h=d.pfname;var k=_.Ce();_.ei("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Ge(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Ge(a.location.href,"_gfid","")||_.Ge(a.location.href,"id",""),h=_.nm(h,_.Ge(a.location.href,"pfname","")));h||(c=_.Qf(_.Ge(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.nm(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Ge(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Pi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.Ee(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Ce();(h=_.Ge(k,"_bsh",_.Me.bsh))&&(a._bsh=h);(k=_.Me.dpo?_.Me.h:_.Ge(k,"jsh",_.Me.h))&&(a.jsh=k);d.hintInFragment?_.Ee(a,f):_.Ee(a,e);return _.Zl(b,e,f,d.paramsSerializer)};
pm=function(a){_.Kl(!a||_.am.test(a),"Illegal url for new iframe - "+a)};
_.qm=function(a,b,c,d,e){pm(c.src);var f,h=gm(d,c),k=h?hm(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Ql(String(c.frameborder))+'" scrolling="'+Ql(String(c.scrolling))+'" '+k+' name="'+Ql(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.ae(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},im(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.Ee(a,f.style):km[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||cm(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var rm,um;rm=/^:[\w]+$/;_.sm=/:([a-zA-Z_]+):/g;_.tm=function(){var a=_.ii()||"0",b=em();var c=_.ii()||a;var d=em(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ei("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ei("iframes/:socialhost:"),h=_.ei("iframes/:im_socialhost:");return bm={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};um=function(a,b){return _.tm()[b]||""};
_.vm=function(a){return _.$l(_.ye,a.replace(_.sm,um))};_.wm=function(a){var b=a;rm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ei(b),_.Kl(!!b,"Unknown iframe url config for - "+a));return _.vm(b)};
_.xm=function(a,b,c){c=c||{};var d=c.attributes||{};_.Kl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.wm(a);d=b.ownerDocument||_.ye;var e=_.mm(d,c);a=_.om(d,a,e,c);var f=c,h=_.Ce();_.Ee(_.jm,h);_.Ee(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Ul(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.qm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.qm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Xl(c);_.Kl(_.am.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.sc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.qm(d,b,h,e,f);return b};
var ym;
ym=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.zm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return ym();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var Am=function(a,b){return _.fi(a,b,!0)},Bm=function(a){this.T=a||{}},Cm=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,
d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};
b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=
function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},Dm=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,
[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,
[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,
[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,
[c,d])};return b},Em,Fm,Im,Km,Mm,Rm,Zm,$m,bn,fn,gn,kn,mn,nn,pn,on,qn;_.ck.prototype.Jz=_.pb(5,function(){return this.T.controller});_.ck.prototype.Ur=_.pb(4,function(a){this.T.apis=a;return this});Em=function(a,b){a.T.onload=b};Fm=function(a){return a.T.rpctoken};_.Gm=function(a,b){a.T.queryParams=b;return a};_.Hm=function(a,b){a.T.relayOpen=b;return a};Im=function(a){return a.T.apis};_.Jm=function(a,b){a.T.onClose=b;return a};Km=function(a,b){a.T.controllerData=b};
_.Lm=function(a){a.T.waitForOnload=!0};Mm=function(a){return(a=a.T.timeout)?a:null};_.Nm=function(a){return!!a&&typeof a==="object"&&_.Ae.test(a.push)};_.Om=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.Pm=function(a,b,c){if(a){_.Kl(_.Nm(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};
_.Qm=function(a,b,c){if(a)if(_.Nm(a))_.Pm(a,b,c);else{_.Kl(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.De(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};Rm=function(a){this.T=a||{}};Rm.prototype.value=function(){return this.T};Rm.prototype.getIframe=function(){return this.T.iframe};var Sm=function(a,b){a.T.role=b;return a},Tm=function(a,b){a.T.data=b;return a};Rm.prototype.Uk=function(a){this.T.setRpcReady=a;return this};var Um=function(a){return a.T.setRpcReady};
Rm.prototype.Xm=function(a){this.T.rpctoken=a;return this};var Vm=function(a){a.T.selfConnect=!0;return a};Bm.prototype.value=function(){return this.T};var Xm=function(a){var b=new Wm;b.T.role=a;return b};Bm.prototype.mU=function(){return this.T.role};Bm.prototype.Fc=function(a){this.T.handler=a;return this};Bm.prototype.wb=function(){return this.T.handler};var Ym=function(a,b){a.T.filter=b;return a};Bm.prototype.Ur=function(a){this.T.apis=a;return this};bn=/^[\w\.\-]*$/;
_.cn=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.dn=function(){return!0};_.en=function(a){for(var b=_.Ce(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Cd]}};fn=function(a,b,c){a=Zm[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.Bk(a[e].call(c,b,c)));return d};gn=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.Kl(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=fn(a,d,b);!c&&d.length>0&&_.Fk(d).then(e)}}};
_.hn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");$m[a]={map:b,filter:c}};_.jn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");_.Be($m,a,{map:{},filter:_.cn}).map[b]=c};kn=function(a,b){_.Be($m,"_default",{map:{},filter:_.dn}).map[a]=b;_.Qm(_.an.Zf,function(c){c.register(a,b,_.dn)})};_.ln=function(){return _.an};mn=/^https?:\/\/[^\/%\\?#\s]+$/i;
nn={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};pn=function(a){this.resolve=this.reject=null;this.promise=_.Uk((0,_.z)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=on(this.promise,a))};on=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};qn=function(a){this.hg=a;this.Context=Cm(a);this.Iframe=Dm(a)};_.g=qn.prototype;
_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.hg().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.hg().SAME_ORIGIN_IFRAMES_FILTER(a)};_.g.create=function(a,b,c){return this.hg().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.hg().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.hg().getContext()};_.g.getStyle=function(a){return this.hg().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.hg().makeWhiteListIframesFilter(a)};
_.g.registerBeforeOpenStyle=function(a,b){return this.hg().registerBeforeOpenStyle(a,b)};_.g.registerIframesApi=function(a,b,c){return this.hg().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.hg().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.hg().registerStyle(a,b)};var rn=function(){this.xi=[]};rn.prototype.hg=function(a){return this.xi.length?sn(this.xi[0],a):void 0};var sn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.ctor?b(a.ctor):a.instance},tn=function(){rn.apply(this,arguments)};_.y(tn,rn);var vn=function(a){var b=un.oR,c=a.priority,d=~Am(b.xi,function(e){return e.priority<c?-1:1});b.xi.splice(d,0,a)};var un=new function(){var a=this;this.oR=new tn;this.instance=new qn(function(){return a.oR.hg()()})};vn({instance:function(){return window.gapi.iframes},priority:1});_.wn=un.instance;var xn,yn;xn={height:!0,width:!0};yn=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.zn=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var An=function(){Rm.apply(this,arguments)};_.y(An,Rm);var Wm=function(){Bm.apply(this,arguments)};_.y(Wm,Bm);var Bn=function(){_.ck.apply(this,arguments)};_.y(Bn,_.ck);var Cn=function(a){Bn.call(this,a)};_.y(Cn,Bn);var Dn=function(a,b){a.T.frameName=b;return a};Cn.prototype.getFrameName=function(){return this.T.frameName};var En=function(a,b){a.T.rpcAddr=b;return a};Cn.prototype.mg=function(){return this.T.rpcAddr};var Fn=function(a,b){a.T.retAddr=b;return a};Cn.prototype.Yh=function(){return this.T.retAddr};Cn.prototype.Lj=function(a){this.T.origin=a;return this};Cn.prototype.getOrigin=function(){return this.T.origin};
Cn.prototype.Uk=function(a){this.T.setRpcReady=a;return this};var Gn=function(a){return a.T._popupWindow};Cn.prototype.qp=function(a){this.T.context=a};var Hn=function(a,b){a.T._rpcReadyFn=b};Cn.prototype.getIframeEl=function(){return this.T.iframeEl};var In=function(a,b,c){var d=a.mg(),e=b.Yh();Fn(En(c,a.Yh()+"/"+b.mg()),e+"/"+d);Dn(c,b.getFrameName()).Lj(b.getOrigin())};var Kn=function(a,b,c){a.setTimeout(function(){b.closed||c==5?Jn(b):(b.close(),c++,Kn(a,b,c))},1E3)},Jn=function(a){a.closed||a.document&&a.document.body&&_.ve(a.document.body,"Please close this window.")};_.Ln=function(a,b,c,d){this.Kg=!1;this.qb=a;this.KK=b;this.sq=c;this.Ka=d;this.YZ=this.Ka.Yh();this.Cd=this.Ka.getOrigin();this.Nba=this.Ka.getIframeEl();this.P0=this.Ka.T.where;this.xi=[];this.applyIframesApi("_default");a=Im(this.Ka)||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.qb.Zf[c]=this};_.g=_.Ln.prototype;_.g.isDisposed=function(){return this.Kg};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.xi.length;a++)this.unregister(this.xi[a]);delete _.an.Zf[this.getFrameName()];this.Kg=!0}};_.g.getContext=function(){return this.qb};_.g.getOptions=function(){return this.Ka};_.g.mg=function(){return this.KK};_.g.Yh=function(){return this.YZ};_.g.getFrameName=function(){return this.sq};_.g.getIframeEl=function(){return this.Nba};_.g.getSiteEl=function(){return this.P0};_.g.setSiteEl=function(a){this.P0=a};_.g.Uk=function(){(0,this.Ka.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ka.value()[a]=b};_.g.getParam=function(a){return this.Ka.value()[a]};_.g.wc=function(){return this.Ka.value()};_.g.getId=function(){return this.Ka.getId()};_.g.getOrigin=function(){return this.Cd};var Mn=function(a,b){var c=a.sq;a=a.qb.getFrameName();return c+":"+a+":"+b};_.g=_.Ln.prototype;
_.g.register=function(a,b,c){_.Kl(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.Kl((c||_.cn)(this),"Rejecting untrusted message "+a);c=Mn(this,a);_.Be(Zm,c,[]).push(b)==1&&(this.xi.push(a),_.tl(c,gn(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=Mn(this,a),d=Zm[c];d&&(b?(b=_.Om.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.Om.call(this.xi,a),b>=0&&this.xi.splice(b,1),_.ul(c)))};_.g.N$=function(){return this.xi};
_.g.applyIframesApi=function(a){this.OE=this.OE||[];if(!(_.Om.call(this.OE,a)>=0)){this.OE.push(a);a=$m[a]||{map:{}};for(var b in a.map)_.De(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.cn(this))return null;var a=Gn(this.Ka);if(a)return a;var b=this.KK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var Nn=function(a){var b={};if(a)for(var c in a)_.De(a,c)&&_.De(xn,c)&&yn.test(a[c])&&(b[c]=a[c]);return b};_.g=_.Ln.prototype;_.g.close=function(a,b){return On(this,"_g_close",a,b)};_.g.restyle=function(a,b){return On(this,"_g_restyle",a,b)};_.g.Or=function(a,b){return On(this,"_g_restyleDone",a,b)};_.g.j8=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.ufa=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.vfa=function(a){var b=this.Ka.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?Nn(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.De(a,"height")&&(a.height=_.zn(a.height)),_.De(a,"width")&&(a.width=_.zn(a.width)),_.Ee(a,b.style))};
_.g.k8=function(a){var b=this.Ka.T.onClose;b&&b.call(this,a,this);if(b=Gn(this.getOptions())){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.Dd&&_.Dh&&c?(c.focus(),Kn(c,b,0)):(b.close(),Jn(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ka.Jz())c={},c.frameName=this.getFrameName(),On(b,"_g_disposeControl",c);b=Mn(this,"_g_wasClosed");fn(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.Yha=function(){delete this.getContext().Zf[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.z)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.Kl(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.Kl((d||_.cn)(this),"Wrong target for message "+a);c=new pn(c);a=this.qb.getFrameName()+":"+this.sq+":"+a;_.Cl(this.KK,a,c.resolve,b);return c.promise};var On=function(a,b,c,d){return a.send(b,c,d,_.dn)};_.g=_.Ln.prototype;_.g.uea=function(a){return a};_.g.ping=function(a,b){return On(this,"_g_ping",b,a)};
_.g.t8=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.mg()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.Kl(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.mg();a._parentRetAddr=this.Yh();this.getContext();b=new _.Pn(a);this.Ida&&this.Ida(b,a.controllerData);this.AF=this.AF||[];this.AF.push(b,a.controllerData)};
_.g.K8=function(a){a=(a||{}).frameName;for(var b=this.AF||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.Mda&&this.Mda(a);return}_.Kl(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.q8=function(a){var b=new Cn(a);a=new An(b.value());if(a.T.selfConnect)var c=this;else(_.Kl(mn.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().Zf[b.getFrameName()],c)?Um(b)&&(c.Uk(),On(c,"_g_rpcReady")):(b=Dn(Fn(En(new Cn,b.mg()),b.Yh()).Lj(b.getOrigin()),b.getFrameName()).Uk(Um(b)).Xm(Fm(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;Qn(b);d=d||"";_.Be(b.yF,d,[]).push({Ze:c,data:a});Rn(c,a,b.DJ[d])};
_.g.pM=function(a,b){(new Cn(b)).T._relayedDepth||(b={},Vm(Sm(new An(b),"_opener")),On(a,"_g_connect",b))};
_.g.qY=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.Jm(_.ek(_.dk(a,null),null),null);return On(this,"_g_open",a.value()).then(function(f){var h=new Cn(f[0]),k=h.getFrameName();f=new Cn;var l=b.Yh(),m=h.Yh();Fn(En(f,b.mg()+"/"+h.mg()),m+"/"+l);Dn(f,k);f.Lj(h.getOrigin());f.Ur(Im(h));f.Xm(Fm(a));_.dk(f,c);_.ek(f,d);_.Jm(f,e);(h=b.getContext().Zf[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.NK=function(a){var b=a.getUrl();_.Kl(!b||_.am.test(b),"Illegal url for new iframe - "+b);var c=a.Tn().value();b={};for(var d in c)_.De(c,d)&&_.De(nn,d)&&(b[d]=c[d]);_.De(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=Nn(d)));a.value().attributes=b};
_.g.eea=function(a){a=new Cn(a);this.NK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=Fm(a);a.Xm(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=Im(new Cn(e.wc())),h=new Cn;In(e,d,h);b==0&&Sm(new An(h.value()),"_opener");h.Uk(!0);h.Xm(c);On(e,"_g_connect",h.value());h=new Cn;Dn(Fn(En(h,e.mg()),e.YZ),e.getFrameName()).Lj(e.getOrigin()).Ur(f);return h.value()})};
_.g.tfa=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.dn)},null,_.dn)};var Wn;_.Sn=_.Ce();_.Tn=_.Ce();_.Un=function(a,b){_.Sn[a]=b};_.Vn=function(a){return _.Sn[a]};Wn=function(a,b){_.Fe.load("gapi.iframes.style."+a,b)};_.Xn=function(a,b){_.Tn[a]=b};_.Yn=function(a){return _.Tn[a]};_.Pn=function(a){a=a||{};this.Kg=!1;this.vi=_.Ce();this.Zf=_.Ce();this.Uf=a._window||_.xe;this.Hd=this.Uf.location.href;this.HY=(this.XJ=Zn(this.Hd,"parent"))?Zn(this.Hd,"pfname"):"";this.Da=this.XJ?Zn(this.Hd,"_gfid")||Zn(this.Hd,"id"):"";this.sq=_.nm(this.Da,this.HY);this.Cd=_.Ig(this.Hd);if(this.Da){var b=new Cn;En(b,a._parentRpcAddr||"..");Fn(b,a._parentRetAddr||this.Da);b.Lj(_.Ig(this.XJ||this.Hd));Dn(b,this.HY);this.Gb=this.attach(b.value())}else this.Gb=null};_.g=_.Pn.prototype;
_.g.isDisposed=function(){return this.Kg};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.Aa(Object.values(this.Zf)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Kg=!0}};_.g.getFrameName=function(){return this.sq};_.g.getOrigin=function(){return this.Cd};_.g.getWindow=function(){return this.Uf};_.g.ub=function(){return this.Uf.document};_.g.setGlobalParam=function(a,b){this.vi[a]=b};_.g.getGlobalParam=function(a){return this.vi[a]};
_.g.attach=function(a){_.Kl(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new Cn(a);a.mg()||En(a,a.getId());a.Yh()||Fn(a,"..");a.getOrigin()||a.Lj(_.Ig(a.getUrl()));a.getFrameName()||Dn(a,_.nm(a.getId(),this.sq));var b=a.getFrameName();if(this.Zf[b])return this.Zf[b];var c=a.mg(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.Yh(),f=Fm(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Ge(f,"rpctoken"));Hn(a,_.Jl(d,e,f,Gn(a)));d=((window.gadgets||
{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.Ln(this,c,b,a),k=a.T.messageHandlersFilter;_.Qm(a.T.messageHandlers,function(l,m){h.register(m,l,k)});Um(a)&&h.Uk();On(h,"_g_rpcReady");return h};_.g.NK=function(a){Dn(a,null);var b=a.getId();!b||bn.test(b)&&!this.getWindow().document.getElementById(b)||(_.Vf.log("Ignoring requested iframe ID - "+b),a.Le(null))};var Zn=function(a,b){var c=_.Ge(a,b);c||(c=_.Qf(_.Ge(a,"jcp",""))[b]);return c||""};
_.Pn.prototype.openChild=function(a){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new Cn(a);$n(this,b);var c=b.getFrameName();if(c&&this.Zf[c])return this.Zf[c];this.NK(b);c=b.getUrl();_.Kl(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.Gm(b,d);d=this.bV&&this.bV(c,b);d||(d=b.T.where,_.Kl(!!d,"No location for new iframe"),c=_.xm(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));En(b,d).Le(d);b.Lj(_.Ig(b.T.eurl||""));this.nX&&this.nX(b,b.getIframeEl());
c=this.attach(a);c.pM&&c.pM(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var ao=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.Kl(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.Kl(e&&_.Ig(e)===a.Cd&&_.Ig(d)===a.Cd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.Lm(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.wm(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},bo=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.Ln?
(e=d,_.Hm(b,0)):Number(d)>0&&_.Hm(b,Number(d)-1);if(e){_.Kl(!!e.qY,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Tn[d])b.qp(a),d(b.value()),b.qp(null);b.T.openerIframe=null;c.resolve(e.qY(b));return!0}}return!1},co=function(a,b,c){var d=b.getStyle();if(d)if(_.Kl(!!_.Vn,"Defer style is disabled, when requesting style "+d),_.Sn[d])$n(a,b);else return Wn(d,function(){_.Kl(!!_.Sn[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.Pn.prototype.open=function(a,b){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new Cn(a);b=ao(this,c,b);var d=new pn(b);(b=c.getUrl())&&c.setUrl(_.wm(b));if(bo(this,c,d)||co(this,c,d)||bo(this,c,d))return d.promise;if(Mm(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+Mm(c)+"milliseconds"})},Mm(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&Em(c.Tn(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.Pn.prototype.getParentIframe=function(){return this.Gb};var eo=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.Ze,b.params));return _.Bk(d).then(function(e){return e&&c?(b.FY&&b.FY.call(a,b.params),e=b.sender?b.sender(b.params):On(c,b.message,b.params),b.Wha?e.then(function(){return!0}):!0):!1})};_.g=_.Pn.prototype;
_.g.closeSelf=function(a,b,c){a=eo(this,{sender:function(d){var e=_.an.getParentIframe();_.Qm(_.an.Zf,function(f){f!==e&&On(f,"_g_wasClosed",d)});return On(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,Ze:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new pn(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new pn(b);b.resolve(eo(this,{message:"_g_restyleMe",params:a,Ze:c,filter:this.getGlobalParam("onRestyleSelfFilter"),Wha:!0,FY:this.X1}));return b.promise};
_.g.X1=function(a){a.height==="auto"&&(a.height=_.zm())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var $n=function(a,b){var c=b.getStyle();if(c){b.Di(null);var d=_.Sn[c];_.Kl(d,"No such style: "+c);b.qp(a);d(b.value());b.qp(null)}};
_.Pn.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.Qm(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.X1(h);f&&f.send("_ready",h,c,_.dn)};
_.Pn.prototype.connectIframes=function(a,b){a=new An(a);var c=new An(b),d=Um(a);b=a.getIframe();var e=c.getIframe();if(e){var f=Fm(a),h=new Cn;In(b,e,h);Tm(Sm((new An(h.value())).Xm(f),a.T.role),a.T.data).Uk(d);var k=new Cn;In(e,b,k);Tm(Sm((new An(k.value())).Xm(f),c.T.role),c.T.data).Uk(!0);On(b,"_g_connect",h.value(),function(){d||On(e,"_g_connect",k.value())});d&&On(e,"_g_connect",k.value())}else c={},Tm(Sm(Vm(new An(c)),a.T.role),a.T.data),On(b,"_g_connect",c)};
var Qn=function(a){a.yF||(a.yF=_.Ce(),a.DJ=_.Ce())};_.Pn.prototype.addOnConnectHandler=function(a,b,c,d){Qn(this);typeof a==="object"?(b=new Wm(a),c=b.mU()||""):(b=Ym(Xm(a).Fc(b).Ur(c),d),c=a);d=this.yF[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Rn(this.Zf[d[e].Ze.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.Be(this.DJ,c,[]);a||b.T.dontWait||c.push(b)};
_.Pn.prototype.removeOnConnectHandler=function(a,b){a=_.Be(this.DJ,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].wb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var Rn=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.cn;if(a&&f(a)){f=Im(e)||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.wb()&&e.wb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.Pn.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Ym(Xm("_opener").Fc(a).Ur(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.Pn.prototype.nX=function(a,b){var c=a.Jz();if(c){_.Kl(c.Cd===a.getOrigin(),"Wrong controller origin "+this.Cd+" !== "+a.getOrigin());var d=a.mg();En(a,c.mg());Fn(a,c.Yh());var e=new Cn;Km(En(e,d),a.T.controllerData);_.Le(b,"load",function(){c.send("_g_control",e.value())})}};
var fo=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.mm(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.vm(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.Kl(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.qm(d,h,l,f);h=f+"_relay"}b=_.wm(b);var m=_.om(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.xe.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Fc(a,m,h,b);return{id:f,B2:n}};_.Pn.prototype.bV=function(a,b){if(b.T.openAsWindow){a=fo(this,a,b);var c=a.id;_.Kl(!!a.B2,"Open popup window failed");b.T._popupWindow=a.B2}return c};Zm=_.Ce();$m=_.Ce();_.an=new _.Pn;kn("_g_rpcReady",_.Ln.prototype.Uk);kn("_g_discover",_.Ln.prototype.N$);kn("_g_ping",_.Ln.prototype.uea);kn("_g_close",_.Ln.prototype.j8);kn("_g_closeMe",_.Ln.prototype.k8);kn("_g_restyle",_.Ln.prototype.ufa);kn("_g_restyleMe",_.Ln.prototype.vfa);kn("_g_wasClosed",_.Ln.prototype.Yha);_.jn("control","_g_control",_.Ln.prototype.t8);_.jn("control","_g_disposeControl",_.Ln.prototype.K8);var go=_.an.getParentIframe();
go&&go.register("_g_restyleDone",_.Ln.prototype.tfa,_.dn);kn("_g_connect",_.Ln.prototype.q8);var ho={};ho._g_open=_.Ln.prototype.eea;_.hn("_open",ho,_.dn);var io={Context:_.Pn,Iframe:_.Ln,SAME_ORIGIN_IFRAMES_FILTER:_.cn,CROSS_ORIGIN_IFRAMES_FILTER:_.dn,makeWhiteListIframesFilter:_.en,getContext:_.ln,registerIframesApi:_.hn,registerIframesApiHandler:_.jn,registerStyle:_.Un,registerBeforeOpenStyle:_.Xn,getStyle:_.Vn,getBeforeOpenStyle:_.Yn,create:_.xm};vn({instance:function(){return io},priority:2});_.jn("gapi.load","_g_gapi.load",function(a){return new _.xk(function(b){_.Fe.load(a&&typeof a==="object"&&a.features||"",b)})});
_.to=function(a,b){a.T.where=b;return a};_.uo=function(){_.ck.apply(this,arguments)};_.y(_.uo,_.ck);
_.vo=_.Ce();
_.Eo={};window.iframer=_.Eo;
var Go=function(a){var b=[new Fo];if(b.length===0)throw Error("j");if(b.map(function(c){if(c instanceof Fo)c=c.hZ;else throw Error("j");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("k`data-gapiscan");a.setAttribute("data-gapiscan","true")},Fo=function(){this.hZ=Ho[0].toLowerCase()},Io,Jo,Ko,Lo,Mo,Qo,Ro;Fo.prototype.toString=function(){return this.hZ};Io=function(a){if(_.Ae.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.De(a,c)&&b.push(c);return b};
Jo={button:!0,div:!0,span:!0};Ko=function(a){var b=_.Be(_.Me,"sws",[]);return _.Om.call(b,a)>=0};Lo=function(a){return _.Be(_.Me,"watt",_.Ce())[a]};Mo=function(a){return function(b,c){return a?_.tm()[c]||a[c]||"":_.tm()[c]||""}};_.No={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.Oo=!1;
_.Po=function(){if(!_.Oo){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.wc(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.No[c]&&d&&(_.vo[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.No)_.No[e]>0&&(b=_.Ge(a,e,""))&&(_.vo[e]=b)}_.Oo=!0}e=_.Ce();_.Ee(_.vo,e);return e};Qo=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.$l(document,a)};
Ro=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=Qo(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.So=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.To=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?Qo(a):Ro(d)};
_.Uo=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.Vo=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.Wo=function(a,b,c){return _.To(a,b,c,b.action?void 0:"publisher")};var Xo,Yo,Zo,$o,ap,bp,dp,cp;Xo={se:"0"};Yo={post:!0};Zo={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};$o="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");ap=_.Be(_.Me,"WI",_.Ce());bp=["style","data-gapiscan"];
dp=function(a){for(var b=_.Ce(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.Om.call(bp,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=cp(a&&a.height))&&(b.height=String(c));(a=cp(a&&a.width))&&(b.width=String(a));return b};
_.fp=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.ep(a));b=_.Ce();b[">type"]=a;_.Ee(c,b);a=_.xm(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.ep=function(a){_.Be(ap,a,0);return"___"+a+"_"+ap[a]++};cp=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var Ho=_.gd(["data-"]),jp,kp,lp,mp,np=/(?:^|\s)g-((\S)*)(?:$|\s)/,op={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};jp=_.Be(_.Me,"SW",_.Ce());kp=_.Be(_.Me,"SA",_.Ce());lp=_.Be(_.Me,"SM",_.Ce());mp=_.Be(_.Me,"FW",[]);
var pp=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},tp=function(a,b){var c;qp.ps0=(new Date).getTime();rp("ps0");a=pp(a,_.ye);var d=_.ye.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:Io(jp).concat(Io(kp)).concat(Io(lp));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.Ce();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&np.exec(n))&&(m=n[1]);h=!m||!(jp[m]||kp[m]||lp[m])||h&&m!==h?null:m}h&&(op[h]||f.nodeName.toLowerCase().indexOf("g:")==0||Io(dp(f)).length!=0)&&(Go(f),_.Be(a,h,[]).push(f))}for(p in a)mp.push(p);qp.ps1=(new Date).getTime();rp("ps1");if(b=mp.join(":"))try{_.Fe.load(b,void 0)}catch(q){_.Vf.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],sp(c,f,dp(f),e,b)}};var up=function(a,b){var c=Lo(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Fe.load(a,function(){var d=Lo(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Fe[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},sp=function(a,b,c,d,e,f,h){switch(vp(b,a,f)){case 0:a=lp[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;up(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.De(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Rf(c[k])}catch(x){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Xe("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in Xo)f[n]=n+"/"+(c[n]||Xo[n])+"/";var n=_.$l(_.ye,l.replace(_.sm,Mo(f)));m="iframes/"+a+"/params/";f={};_.Ee(c,f);(l=_.Xe("lang")||_.Xe("gwidget/lang"))&&
(f.hl=l);Yo[a]||(f.origin=_.So());f.exp=_.Xe(m+"exp");if(m=_.Xe(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.xe.location[p]}switch(a){case "plus":case "follow":f.url=_.Wo(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?Qo(m):Ro();f.url=m;f.db=_.Uo(c.db,void 0,_.Xe());f.ecp=_.Vo(c.ecp,void 0,_.Xe());delete f.href;break;case "signin":f.url=Ro()}_.Me.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in Xo)f[q]&&delete f[q];f.gsrc=_.Xe("iframes/:source:");q=
_.Xe("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var r in f)_.De(f,r)&&q.test(r)&&(e[r.replace(q,"")]=f[r],delete f[r]);r=_.Xe("iframes/"+a+"/params/si")=="q"?f:e;q=_.Po();for(var w in q)!_.De(q,w)||_.De(f,w)||_.De(e,w)||(r[w]=q[w]);w=[].concat($o);r=_.Xe("iframes/"+a+"/methods");_.Nm(r)&&(w=w.concat(r));for(u in c)_.De(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(w.push(u),delete f[u]);delete f.callback;e._methods=w.join(",");var u=_.Zl(n,f,e);w=h||{};w.allowPost=
1;w.attributes=Zo;w.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.fp(a,b,c,u,w,h);b=h.id;c=_.Ce();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.lo[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),up(a,b))}},vp=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(lp[b]){if(Jo[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(kp[b])return 0;if(jp[b])return 1}}return null};_.Be(_.Fe,"platform",{}).go=function(a,b){tp(a,b)};var wp=_.Be(_.Me,"perf",_.Ce()),qp=_.Be(wp,"g",_.Ce()),xp=_.Be(wp,"i",_.Ce()),yp,zp,Ap,rp,Cp,Dp,Ep;_.Be(wp,"r",[]);yp=_.Ce();zp=_.Ce();Ap=function(a,b,c,d){yp[c]=yp[c]||!!d;_.Be(zp,c,[]);zp[c].push([a,b])};rp=function(a,b,c){var d=wp.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};Cp=function(a,b,c,d){if(b=="_p")throw Error("H");_.Bp(a,b,c,d)};_.Bp=function(a,b,c,d){Dp(b,c)[a]=d||(new Date).getTime();rp(a,b,c)};Dp=function(a,b){a=_.Be(xp,a,_.Ce());return _.Be(a,b,_.Ce())};
Ep=function(a,b,c){var d=null;b&&c&&(d=Dp(b,c)[a]);return d||qp[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.bQ={};window.__gapi_jstiming__.cfa=1;var Fp=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},Gp=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+Fp(a,l,h[m][0])):k&&e.push(l+"."+Fp(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},Hp=function(a,b,c){a=Gp(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.cfa++;
window.__gapi_jstiming__.bQ[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.bQ[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else Hp(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return Hp(a,b,c)}};var Ip={g:"gapi_global",m:"gapi_module",w:"gwidget"},Jp=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.As=b};Jp.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.As;case "w":return this.type+"."+this.name+this.As}};
var Kp=new Jp,Lp=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),Mp=_.Be(wp,"_c",_.Ce()),Np=Math.random()<(_.Xe("csi/rate")||0),Pp=function(a,b,c){for(var d=new Jp(b,c),e=_.Be(Mp,d.key(),_.Ce()),f=zp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=Ep(k[1],n,p);m=Ep(m,n,p);e[l]=k&&m?m-k:null}yp[a]&&Np&&(Op(Kp),Op(d))},Qp=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},Op=function(a){var b=_.xe.__gapi_jstiming__;b.sn=Ip[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.As;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=Mp[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;Mp[e]=_.Ce();d&&(h=[],h.push("l"+(_.Xe("isPlusUser")?"1":"0")),d="m"+(Lp?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.As):a.type=="w"&&(e="n"+a.As,h.push(e),a.As=="0"&&h.push(d+e)),h.push("u"+(_.Xe("isLoggedIn")?"1":"0")),a=Qp("",h),a=Qp("abc_",a).join(","),b.report(c,{e:a}))};
Ap("blt","bs0","bs1");Ap("psi","ps0","ps1");Ap("rpcqi","rqe","rqd");Ap("bsprt","bsrt0","bsrt1");Ap("bsrqt","bsrt1","bsrt2");Ap("bsrst","bsrt2","bsrt3");Ap("mli","ml0","ml1");Ap("mei","me0","me1",!0);Ap("wcdi","wrs","wcdi");Ap("wci","wrs","wdc");Ap("wdi","wrs","wrdi");Ap("wdt","bs0","wrdt");Ap("wri","wrs","wrri",!0);Ap("wrt","bs0","wrrt");Ap("wji","wje0","wje1",!0);Ap("wjli","wjl0","wjl1");Ap("whi","wh0","wh1",!0);Ap("wai","waaf0","waaf1",!0);Ap("wadi","wrs","waaf1",!0);Ap("wadt","bs0","waaf1",!0);
Ap("wprt","wrt0","wrt1");Ap("wrqt","wrt1","wrt2");Ap("wrst","wrt2","wrt3",!0);Ap("fbprt","fsrt0","fsrt1");Ap("fbrqt","fsrt1","fsrt2");Ap("fbrst","fsrt2","fsrt3",!0);Ap("fdns","fdns0","fdns1");Ap("fcon","fcon0","fcon1");Ap("freq","freq0","freq1");Ap("frsp","frsp0","frsp1");Ap("fttfb","fttfb0","fttfb1");Ap("ftot","ftot0","ftot1",!0);var Rp=wp.r;if(typeof Rp!=="function"){for(var Sp;Sp=Rp.shift();)Pp.apply(null,Sp);wp.r=Pp};var Tp=["div"],Up="onload",Vp=!0,Wp=!0,Xp=function(a){return a},Yp=null,Zp=function(a){var b=_.Xe(a);return typeof b!=="undefined"?b:_.Xe("gwidget/"+a)},cq,dq,eq,fq,gq,hq,iq,oq,jq,pq,qq,rq,sq,tq,kq,mq,uq,lq,vq,wq,xq,yq,zq,Aq;Yp=_.Xe();_.Xe("gwidget");var $p=Zp("parsetags");Up=$p==="explicit"||$p==="onload"?$p:Up;var aq=Zp("google_analytics");typeof aq!=="undefined"&&(Vp=!!aq);var bq=Zp("data_layer");typeof bq!=="undefined"&&(Wp=!!bq);cq=function(){var a=this&&this.getId();a&&(_.Me.drw=a)};
dq=function(){_.Me.drw=null};eq=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};fq=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};gq=function(){return Zp("lang")||"en-US"};
hq=function(a){if(!_.Ta.wb("attach")){var b={},c=_.Ta.wb("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.wc().renderData.id;f=document.getElementById(f);if(!f)throw Error("I");return c.attach(e,f)};_.Ta.Fc("attach",b)}a.style="attach"};iq=function(){var a={};a.width=[eq(450)];a.height=[eq(24)];a.onready=[fq];a.lang=[gq,"hl"];a.iloader=[function(){return _.Me.ILI},"iloader"];return a}();
oq=function(a){var b={};b.Pe=a[0];b.Np=-1;b.sta="___"+b.Pe+"_";b.Zha="g:"+b.Pe;b.Ara="g-"+b.Pe;b.KZ=[];b.config={};b.By=[];b.i2={};b.JD={};var c=function(e){for(var f in e)if(_.De(e,f)){b.config[f]=[fq];b.By.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.pra,l=h.KD,m=h.UN);m&&(b.By.push(m),b.config[m]=[fq],b.i2[f]=m);k&&(b.config[f]=[k]);l&&(b.JD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.Zha]=1;b.gda=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in iq)iq.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=iq[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.Nm=a[5]);b.lta=a[6]===!0;b.Cea=a[7];b.Nha=a[8];b.gda||d(Tp);b.LJ=function(e){b.Np++;Cp("wrs",b.Pe,String(b.Np));var f=[],h=e.element,k=e.config,l=":"+b.Pe;l==":plus"&&e.Ed&&e.Ed.action&&(l+="_"+e.Ed.action);var m=jq(b,k),n={};_.Ee(_.Po(),n);for(var p in e.Ed)e.Ed[p]!=null&&(n[p]=e.Ed[p]);p={container:h.id,renderData:e.Xea,
style:"inline",height:k.height,width:k.width};hq(p);b.Nm&&(f[2]=p,f[3]=n,f[4]=m,b.Nm("i",f));l=_.Ta.open(l,p,n,m);e=e.y8;kq(l,k);lq(l,h);mq(b,l,e);nq(b.Pe,b.Np.toString(),l);f[5]=l;b.Nm&&b.Nm("e",f)};return b};
jq=function(a,b){for(var c={},d=a.By.length-1;d>=0;--d){var e=a.By[d],f=b[a.i2[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.JD)a.JD.hasOwnProperty(k)&&(c[k]=pq(c[k]||function(){},a.JD[k]));c.drefresh=cq;c.erefresh=dq;return c};
pq=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(Vp){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(Wp&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};qq=function(a){return _.Ln&&a instanceof _.Ln};rq=function(a){return qq(a)?"_renderstart":"renderstart"};sq=function(a){return qq(a)?"_ready":"ready"};tq=function(){return!0};kq=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(sq(a),d,tq);a.register(rq(a),d,tq)}};
mq=function(a,b,c){var d=a.Pe,e=String(a.Np),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&Cp("wrdt",d,e),Cp("wrdi",d,e))};b.register(rq(b),h,tq);var k=!1;a=function(){k||(k=!0,h(),c&&Cp("wrrt",d,e),Cp("wrri",d,e))};b.register(sq(b),a,tq);qq(b)?b.register("widget-interactive-"+b.id,a,tq):_.$f.register("widget-interactive-"+b.id,a);_.$f.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?Cp("wdc",d,e,n):l==="wje0"?Cp("wje0",d,e,n):l==="wje1"?Cp("wje1",d,e,n):l=="wh0"?_.Bp("wh0",d,e,n):l=="wh1"?
_.Bp("wh1",d,e,n):l=="wcdi"&&_.Bp("wcdi",d,e,n)})};uq=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};lq=function(a,b){var c=function(d){d=d||a;var e=uq(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=uq(d.height))&&b.style.height!=d&&(b.style.height=d)};qq(a)?a.setParam("onRestyle",c):(a.register("ready",c,tq),a.register("renderstart",c,tq),a.register("resize",c,tq))};vq=function(a,b){for(var c in iq)if(iq.hasOwnProperty(c)){var d=iq[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
wq=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||Xp)(b[d.toLowerCase()],b,Yp));return c};xq=function(a){if(a=a.Cea)for(var b=0;b<a.length;b++)(new Image).src=a[b]};yq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=wq(a.config,c);a.KZ.push({element:d,config:e,Ed:vq(e,wq(a.parameters,c)),vsa:3,y8:!!c["data-onload"],Xea:b})}b=a.KZ;for(a=a.LJ;b.length>0;)a(b.shift())};
zq=function(a,b){a.Np++;Cp("wrs",a.Pe,String(a.Np));var c=b.userParams,d=wq(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=jq(a,d),l=wq(a.parameters,c);_.Ee(_.Po(),l);l=vq(d,l);c=!!c["data-onload"];var m=_.an,n=_.Ce();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.dn;_.so(n);f=l;a.Nm&&(e[2]=n,e[3]=f,e[4]=k,a.Nm("i",e));k=m.attach(n);k.id=b.id;k.pM(k,n);kq(k,d);lq(k,h);mq(a,
k,c);nq(a.Pe,a.Np.toString(),k);e[5]=k;a.Nm&&a.Nm("e",e)};Aq=function(a,b){var c=b.url;a.Nha||_.bj(c)?zq(a,b):_.Ta.open?yq(a,b):(0,_.Fg)("iframes",function(){yq(a,b)})};
_.Bq=function(a){var b=oq(a);xq(b);_.Yf(b.Pe,function(d){Aq(b,d)});jp[b.Pe]=!0;var c={va:function(d,e,f){var h=e||{};h.type=b.Pe;e=h.type;delete h.type;var k=pp(d);if(k){d={};for(var l in h)_.De(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;sp(e,k,d,[],0,l,f)}else _.Vf.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){tp(d,b.Pe)},xsa:function(){var d=_.Be(_.Me,"WI",_.Ce()),e;for(e in d)delete d[e]}};a=function(){Up==="onload"&&c.go()};if(!Ko(b.Pe)){if(!_.Wf())try{a()}catch(d){}_.Xf(a)}_.t("gapi."+
b.Pe+".go",c.go);_.t("gapi."+b.Pe+".render",c.va);return c};var Cq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},nq=function(a,b,c){if(Cq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
Cp("wrt0",a,b,Math.round(f.startTime));Cp("wrt1",a,b,h);Cp("wrt2",a,b,k);Cp("wrt3",a,b,l)}},1E3)};c.register(rq(c),e,tq);c.register(sq(c),e,tq)}};_.t("gapi.widget.make",_.Bq);
_.af=_.af||{};_.af.Gv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.af.pB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.af=_.af||{};_.af.u7=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.bf("cannot attachBrowserEvent: mousemove")};_.af.Vea=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.bf("cannot removeBrowserEvent: mousemove")};
_.af=_.af||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.af.escape=function(c,d){if(c){if(typeof c==="string")return _.af.hG(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.af.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.af.hG(e)]=_.af.escape(c[e],!0));return d}}return c};_.af.hG=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.af.mta=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Qg=function(){function a(m){var n=new _.Pg;n.tx(m);return n.Ri()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Xe("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.af.Vea(k)};
c!=0&&_.af.u7(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+1.2089258196146292E24)}}();_.t("shindig.random",_.Qg);
_.Ta.Ma={};_.Ta.Ma.Li={};_.Ta.Ma.Li.M7=function(a){try{return!!a.document}catch(b){}return!1};_.Ta.Ma.Li.uU=function(a){var b=a.parent;return a!=b&&_.Ta.Ma.Li.M7(b)?_.Ta.Ma.Li.uU(b):a};_.Ta.Ma.Li.qra=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Ta.Ma.Li.Gv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var Jq,Kq,Lq,Mq,Pq,Qq,Rq,Sq,Tq,Uq,Vq,Wq,Xq;Jq=function(){_.$f.register("_noop_echo",function(){this.callback(_.Ta.I$(_.Ta.lm[this.f]))})};Kq=function(){window.setTimeout(function(){_.$f.call("..","_noop_echo",_.Ta.mea)},0)};Lq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.$f.call.apply(_.$f,f)};d._iframe_wrapped_rpc_=!0;return d};
Mq=function(a){_.Ta.hC[a]||(_.Ta.hC[a]={},_.$f.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Ta.hC[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Ta.Nq,a)&&(h=_.Ta.Nq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Vf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Ta.hC[a]};_.Nq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.Oq=function(){return _.xe.location.origin||_.xe.location.protocol+"//"+_.xe.location.host};
Pq=function(a){_.Me.h=a};Qq=function(a){_.Me.bsh=a};Rq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Sq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Tq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Sq(a[d])&&!Sq(b[d])?Tq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Sq(b[d])?[]:{},Tq(a[d],b[d])):a[d]=b[d])};
Uq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Vq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
Wq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Rq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Uq())if(e=Vq(c),d.push(25),typeof e===
"object")return e;return{}}};Xq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Tq(c,b);a.push(c)};
_.Yq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.di(!0);d=window.___gcfg;b=Rq("cu");a=window.___gu;d&&d!==a&&(Xq(b,d),window.___gu=d);d=Rq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,Rq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=Wq(f,h))&&d.push(f));c&&Xq(b,c);a=Rq("cd");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);a=Rq("ci");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);c=0;for(d=b.length;c<d;++c)Tq(_.di(),b[c],!0)};var Zq,$q=window.location.href,ar=$q.indexOf("?"),br=$q.indexOf("#");
Zq=(br===-1?$q.substr(ar+1):[$q.substr(ar+1,br-ar-1),"&",$q.substr(br+1)].join("")).split("&");for(var cr=window.decodeURIComponent?decodeURIComponent:unescape,dr=0,er=Zq.length;dr<er;++dr){var fr=Zq[dr].indexOf("=");if(fr!==-1){Zq[dr].substring(0,fr);var gr=Zq[dr].substring(fr+1);gr=gr.replace(/\+/g," ");try{cr(gr)}catch(a){}}};if(window.ToolbarApi)hr=window.ToolbarApi,hr.Ia=window.ToolbarApi.getInstance,hr.prototype=window.ToolbarApi.prototype,_.g=hr.prototype,_.g.openWindow=hr.prototype.openWindow,_.g.JQ=hr.prototype.closeWindow,_.g.W_=hr.prototype.setOnCloseHandler,_.g.sQ=hr.prototype.canClosePopup,_.g.TZ=hr.prototype.resizeWindow;else{var hr=function(){};hr.Ia=function(){!ir&&window.external&&window.external.GTB_IsToolbar&&(ir=new hr);return ir};_.g=hr.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.JQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.W_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.sQ=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.TZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var ir=null;window.ToolbarApi=hr;window.ToolbarApi.getInstance=hr.Ia};var jr=/^[-_.0-9A-Za-z]+$/,kr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},lr={onBeforeParentOpen:"beforeparentopen"},mr={onOpen:function(a){var b=a.wc();a.dh(b.container||b.element);return a},onClose:function(a){a.remove()}},nr=function(){_.Ta.CV++;return["I",_.Ta.CV,"_",(new Date).getTime()].join("")},or,pr,qr,tr,ur,vr,wr,yr,xr;_.Ta.Tn=function(a){var b=_.Ce();_.Ee(_.jm,b);_.Ee(a,b);return b};
or=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Rf(a):a};pr=function(a){var b=_.ei("googleapis.config/elog");if(b)try{b(a)}catch(c){}};qr=function(a){a&&a.match(jr)&&_.Yq("googleapis.config/gcv",a)};_.rr=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.sr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=Mq(h);p[k]=p[k]||{};n=_.Ta.Ma.Li.Gv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Ta.Nq)_.Ta.Nq.hasOwnProperty(q)&&f.push(q);return f.join(",")};tr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=Lq(f,b,c)}}return d};
ur=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.Eo&&_.Eo._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};vr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
wr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ei(a);a={};_.Ee(b,a);(b=a.url)&&(a.url=_.vm(b));a.params||(a.params={});return a}return{url:_.vm(a)}};yr=function(a){function b(){}b.prototype=xr.prototype;a.prototype=new b};
xr=function(a,b,c,d,e,f,h,k){this.config=wr(a);this.openParams=this.KB=b||{};this.params=c||{};this.methods=d;this.yD=!1;zr(this,b.style);this.callbacks={};Ar(this,function(){var l;(l=this.KB.style)&&_.Ta.Qw[l]?l=_.Ta.Qw[l]:l?(_.Vf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=mr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Ta.Ma.Li.Gv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Br(this,e[q],_.Ta.Ma.Li.Gv(m,l))}f&&Br(this,"close",f)});this.Hk=this.ac=h;this.RJ=(k||[]).slice();h&&this.RJ.unshift(h.getId())};xr.prototype.wc=function(){return this.KB};xr.prototype.fH=function(){return this.params};xr.prototype.aA=function(){return this.methods};xr.prototype.kd=function(){return this.Hk};
var zr=function(a,b){a.yD||((b=b&&!_.Ta.Qw[b]&&_.Ta.RF[b])?(a.QF=[],b(function(){a.yD=!0;for(var c=a.QF.length,d=0;d<c;++d)a.QF[d].call(a)})):a.yD=!0)},Ar=function(a,b){a.yD?b.call(a):a.QF.push(b)};xr.prototype.xe=function(a,b){Ar(this,function(){Br(this,a,b)})};var Br=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};xr.prototype.ep=function(a,b){Ar(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
xr.prototype.ei=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Vf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),pr(k)}return h};var Cr=function(a){return typeof a=="number"?{value:a,AG:a+"px"}:a=="100%"?{value:100,AG:"100%",tW:!0}:null};xr.prototype.send=function(a,b,c){_.Ta.s_(this,a,b,c)};
xr.prototype.register=function(a,b){var c=this;c.xe(a,function(d){b.call(c,d)})};var Dr=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,kr,e,f,h);this.id=b.id||nr();this.ow=b.rpctoken&&String(b.rpctoken)||Math.round(_.Pi()*1E9);this.zba=vr(this.params,this.config);this.oG={};Ar(this,function(){k.ei("open");_.rr(k.oG,k)})};yr(Dr);_.g=Dr.prototype;
_.g.dh=function(a,b){if(!this.config.url)return _.Vf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Ta.lm[c]=this;var d=_.rr(this.methods);d._ready=this.JB;d._close=this.close;d._open=this.rY;d._resizeMe=this.UZ;d._renderstart=this.lY;var e=this.zba;this.ow&&(e.rpctoken=this.ow);e._methods=_.sr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Vf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.wc().allowPost&&(d.allowPost=!0);this.wc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=or;this.ii=_.xm(this.config.url,a,d);a=this.ii.getAttribute("data-postorigin")||this.ii.src;_.Ta.lm[c]=this;
_.$f.SC(this.id,this.ow);_.$f.Nj(this.id,a);return this};_.g.Oh=function(a,b){this.oG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ii};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.JB=function(a){var b=tr(a,this.id,"");this.Hk&&typeof this.methods._ready=="function"&&(a._methods=_.sr(b,this.Hk.getId(),this.id,this,!1),this.methods._ready(a));_.rr(a,this);_.rr(b,this);this.ei("ready",a)};
_.g.lY=function(a){this.ei("renderstart",a)};_.g.close=function(a){a=this.ei("close",a);delete _.Ta.lm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.rY=function(a){var b=tr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(ur(a.openParams))new Er(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new Dr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Ar(c,function(){var e={childId:c.getId()},f=c.oG;f._toclose=c.close;e._methods=_.sr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.UZ=function(a){if(this.ei("resize",a)===void 0&&this.ii){var b=Cr(a.width);b!=null&&(this.ii.style.width=b.AG);a=Cr(a.height);a!=null&&(this.ii.style.height=a.AG);this.ii.parentElement&&(b!=null&&b.tW||a!=null&&a.tW)&&(this.ii.parentElement.style.display="block")}};
var Er=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,lr,e,f,h);this.url=a;this.Hp=null;this.lK=nr();Ar(this,function(){k.ei("beforeparentopen");var l=_.rr(k.methods);l._onopen=k.dea;l._ready=k.JB;l._onclose=k.bea;k.params._methods=_.sr(l,"..",k.lK,k,!0);l={};for(var m in k.params)l[m]=or(k.params[m]);_.Eo._open({url:k.config.url,openParams:k.KB,params:l,proxyId:k.lK,openedByProxyChain:k.RJ})})};yr(Er);Er.prototype.T$=function(){return this.Hp};
Er.prototype.dea=function(a){this.Hp=a.childId;var b=tr(a,"..",this.Hp);_.rr(b,this);this.close=b._toclose;_.Ta.lm[this.Hp]=this;this.Hk&&this.methods._onopen&&(a._methods=_.sr(b,this.Hk.getId(),this.Hp,this,!1),this.methods._onopen(a))};Er.prototype.JB=function(a){var b=String(this.Hp),c=tr(a,"..",b);_.rr(a,this);_.rr(c,this);this.ei("ready",a);this.Hk&&this.methods._ready&&(a._methods=_.sr(c,this.Hk.getId(),b,this,!1),this.methods._ready(a))};
Er.prototype.bea=function(a){if(this.Hk&&this.methods._onclose)this.methods._onclose(a);else return a=this.ei("close",a),delete _.Ta.lm[this.Hp],a};
var Fr=function(a,b,c,d,e,f,h){xr.call(this,a,b,c,d,lr,f,h);this.id=b.id||nr();this.Aha=e;d._close=this.close;this.onClosed=this.eY;this.C2=0;Ar(this,function(){this.ei("beforeparentopen");var k=_.rr(this.methods);this.params._methods=_.sr(k,"..",this.lK,this,!0);k={};k.queryParams=this.params;a=_.om(_.ye,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.sQ(l))};e.W_(l,this);this.C2=l})};yr(Fr);
Fr.prototype.close=function(a){a=this.ei("close",a);this.Aha.JQ(this.C2);return a};Fr.prototype.eY=function(){this.ei("close")};_.Eo.send=function(a,b,c){_.Ta.s_(_.Eo,a,b,c)};
(function(){function a(h){return _.Ta.Qw[h]}function b(h,k){_.Ta.Qw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.zm());var k=window&&hr&&hr.Ia();k?k.TZ(h.width||0,h.height||0):_.Eo&&_.Eo._resizeMe&&_.Eo._resizeMe(h)}function d(h){qr(h)}_.Ta.lm={};_.Ta.Qw={};_.Ta.RF={};_.Ta.CV=0;_.Ta.hC={};_.Ta.Nq={};_.Ta.UB=null;_.Ta.TB=[];_.Ta.mea=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.Eo.id)}}catch(m){}try{_.Ta.UB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Ta.TB.length;++h)_.Ta.TB[h](_.Ta.UB);_.Ta.TB=[]}catch(m){pr(m)}};_.Ta.I$=function(h){var k=h&&h.Hk,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.RJ);return l};Jq();if(window.parent!=window){var e=_.Nq();e.gcv&&qr(e.gcv);var f=e.jsh;f&&Pq(f);_.rr(tr(e,"..",""),_.Eo);_.rr(e,_.Eo);Kq()}_.Ta.wb=a;_.Ta.Fc=b;_.Ta.xga=d;_.Ta.resize=c;_.Ta.g$=function(h){return _.Ta.RF[h]};_.Ta.wL=function(h,
k){_.Ta.RF[h]=k};_.Ta.SZ=c;_.Ta.Sga=d;_.Ta.yA={};_.Ta.yA.get=a;_.Ta.yA.set=b;_.Ta.allow=function(h,k){Mq(h);_.Ta.Nq[h]=k||window[h]};_.Ta.tqa=function(h){delete _.Ta.Nq[h]};_.Ta.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&hr?hr.Ia():null;return q?new Fr(h,k,l,m,q,n,p):ur(k)?new Er(h,k,l,m,n,p):new Dr(h,k,l,m,n,p)};_.Ta.close=function(h,k){_.Eo&&_.Eo._close&&_.Eo._close(h,k)};_.Ta.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.zm());m._methods=_.sr(k||{},"..","",_.Eo,!0);_.Eo&&_.Eo._ready&&_.Eo._ready(m,l)};_.Ta.gU=function(h){_.Ta.UB?h(_.Ta.UB):_.Ta.TB.push(h)};_.Ta.fea=function(h){return!!_.Ta.lm[h]};_.Ta.p$=function(){return["https://ssl.gstatic.com/gb/js/",_.ei("googleapis.config/gcv")].join("")};_.Ta.mZ=function(h){var k={mouseover:1,mouseout:1};if(_.Eo._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.Eo._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Ta.s_=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Ta.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Ta.L7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Ta.gU(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.$f.call.apply(_.$f,m))})};_.Ta.Rea=function(h,k){_.$f.register(h,k)};_.Ta.Ega=Pq;_.Ta.A_=
Qq;_.Ta.hX=pr;_.Ta.DV=_.Eo})();_.t("iframes.allow",_.Ta.allow);_.t("iframes.callSiblingOpener",_.Ta.L7);_.t("iframes.registerForOpenedSibling",_.Ta.Rea);_.t("iframes.close",_.Ta.close);_.t("iframes.getGoogleConnectJsUri",_.Ta.p$);_.t("iframes.getHandler",_.Ta.wb);_.t("iframes.getDeferredHandler",_.Ta.g$);_.t("iframes.getParentInfo",_.Ta.gU);_.t("iframes.iframer",_.Ta.DV);_.t("iframes.open",_.Ta.open);_.t("iframes.openedId_",_.Ta.fea);_.t("iframes.propagate",_.Ta.mZ);_.t("iframes.ready",_.Ta.ready);_.t("iframes.resize",_.Ta.resize);
_.t("iframes.setGoogleConnectJsVersion",_.Ta.xga);_.t("iframes.setBootstrapHint",_.Ta.A_);_.t("iframes.setJsHint",_.Ta.Ega);_.t("iframes.setHandler",_.Ta.Fc);_.t("iframes.setDeferredHandler",_.Ta.wL);_.t("IframeBase",xr);_.t("IframeBase.prototype.addCallback",xr.prototype.xe);_.t("IframeBase.prototype.getMethods",xr.prototype.aA);_.t("IframeBase.prototype.getOpenerIframe",xr.prototype.kd);_.t("IframeBase.prototype.getOpenParams",xr.prototype.wc);_.t("IframeBase.prototype.getParams",xr.prototype.fH);
_.t("IframeBase.prototype.removeCallback",xr.prototype.ep);_.t("Iframe",Dr);_.t("Iframe.prototype.close",Dr.prototype.close);_.t("Iframe.prototype.exposeMethod",Dr.prototype.Oh);_.t("Iframe.prototype.getId",Dr.prototype.getId);_.t("Iframe.prototype.getIframeEl",Dr.prototype.getIframeEl);_.t("Iframe.prototype.getSiteEl",Dr.prototype.getSiteEl);_.t("Iframe.prototype.openInto",Dr.prototype.dh);_.t("Iframe.prototype.remove",Dr.prototype.remove);_.t("Iframe.prototype.setSiteEl",Dr.prototype.setSiteEl);
_.t("Iframe.prototype.addCallback",Dr.prototype.xe);_.t("Iframe.prototype.getMethods",Dr.prototype.aA);_.t("Iframe.prototype.getOpenerIframe",Dr.prototype.kd);_.t("Iframe.prototype.getOpenParams",Dr.prototype.wc);_.t("Iframe.prototype.getParams",Dr.prototype.fH);_.t("Iframe.prototype.removeCallback",Dr.prototype.ep);_.t("IframeProxy",Er);_.t("IframeProxy.prototype.getTargetIframeId",Er.prototype.T$);_.t("IframeProxy.prototype.addCallback",Er.prototype.xe);_.t("IframeProxy.prototype.getMethods",Er.prototype.aA);
_.t("IframeProxy.prototype.getOpenerIframe",Er.prototype.kd);_.t("IframeProxy.prototype.getOpenParams",Er.prototype.wc);_.t("IframeProxy.prototype.getParams",Er.prototype.fH);_.t("IframeProxy.prototype.removeCallback",Er.prototype.ep);_.t("IframeWindow",Fr);_.t("IframeWindow.prototype.close",Fr.prototype.close);_.t("IframeWindow.prototype.onClosed",Fr.prototype.eY);_.t("iframes.util.getTopMostAccessibleWindow",_.Ta.Ma.Li.uU);_.t("iframes.handlers.get",_.Ta.yA.get);_.t("iframes.handlers.set",_.Ta.yA.set);
_.t("iframes.resizeMe",_.Ta.SZ);_.t("iframes.setVersionOverride",_.Ta.Sga);_.t("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Ta.CROSS_ORIGIN_IFRAMES_FILTER);_.t("IframeBase.prototype.send",xr.prototype.send);_.t("IframeBase.prototype.register",xr.prototype.register);_.t("Iframe.prototype.send",Dr.prototype.send);_.t("Iframe.prototype.register",Dr.prototype.register);_.t("IframeProxy.prototype.send",Er.prototype.send);_.t("IframeProxy.prototype.register",Er.prototype.register);
_.t("IframeWindow.prototype.send",Fr.prototype.send);_.t("IframeWindow.prototype.register",Fr.prototype.register);_.t("iframes.iframer.send",_.Ta.DV.send);
var St=_.Ta.Fc,Tt={open:function(a){var b=_.oo(a.wc());return a.dh(b,{style:_.po(b)})},attach:function(a,b){var c=_.oo(a.wc()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.ow=f;a.el=c;a.ii=b;_.Ta.lm[d]=a;b=_.rr(a.methods);b._ready=a.JB;b._close=a.close;b._open=a.rY;b._resizeMe=a.UZ;b._renderstart=a.lY;_.sr(b,d,"",a,!0);_.$f.SC(a.id,a.ow);_.$f.Nj(a.id,e);c=_.Ta.Tn({style:_.po(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.ii.style.cssText=c[h]:a.ii.setAttribute(h,c[h]))}};Tt.onready=_.qo;Tt.onRenderStart=_.qo;Tt.close=_.ro;St("inline",Tt);
_.Eh=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.kd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.uh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Gh=function(a){for(var b in a)return!1;return!0};
_.Hh=function(a,b){a.src=_.kc(b);(b=_.Gc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ih=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Jh,Kh,Mh;Jh={};Kh=null;_.Lh=_.Bd||_.Cd||!_.Dh&&typeof _.Xa.atob=="function";_.Nh=function(a,b){b===void 0&&(b=0);Mh();b=Jh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Oh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Kh[m];if(n!=null)return n;if(!_.xc(m))throw Error("w`"+m);}return l}Mh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Mh=function(){if(!Kh){Kh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Jh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Kh[f]===void 0&&(Kh[f]=e)}}}};
var ki;_.ji=function(a){this.Bc=a||{cookie:""}};_.g=_.ji.prototype;_.g.isEnabled=function(){if(!_.Xa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{fJ:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Esa;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.fJ}if(/[;=\s]/.test(a))throw Error("z`"+a);if(/[;\r\n]/.test(b))throw Error("A`"+b);k===void 0&&(k=-1);this.Bc.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Bc.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.zc(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.El(a);this.set(a,"",{fJ:0,path:b,domain:c});return d};_.g.kg=function(){return ki(this).keys};_.g.Xe=function(){return ki(this).values};_.g.isEmpty=function(){return!this.Bc.cookie};_.g.Zb=function(){return this.Bc.cookie?(this.Bc.cookie||"").split(";").length:0};
_.g.El=function(a){return this.get(a)!==void 0};_.g.clear=function(){for(var a=ki(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};ki=function(a){a=(a.Bc.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.zc(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.li=new _.ji(typeof document=="undefined"?null:document);
_.ti={};_.ui=function(a){return _.ti[a||"token"]||null};
_.cj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.dj=function(){this.Kg=this.Kg;this.Oo=this.Oo};_.dj.prototype.Kg=!1;_.dj.prototype.isDisposed=function(){return this.Kg};_.dj.prototype.dispose=function(){this.Kg||(this.Kg=!0,this.ua())};_.dj.prototype[Symbol.dispose]=function(){this.dispose()};_.fj=function(a,b){_.ej(a,_.bb(_.cj,b))};_.ej=function(a,b){a.Kg?b():(a.Oo||(a.Oo=[]),a.Oo.push(b))};_.dj.prototype.ua=function(){if(this.Oo)for(;this.Oo.length;)this.Oo.shift()()};
var nj;nj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.oj=function(a){this.src=a;this.ie={};this.nx=0};_.qj=function(a,b){this.type="function"==typeof _.pj&&a instanceof _.pj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.cw=!1};_.qj.prototype.stopPropagation=function(){this.cw=!0};_.qj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.rj=function(a,b){_.qj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.eK=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Cf=null;a&&this.init(a,b)};_.eb(_.rj,_.qj);
_.rj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Cd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Cd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.eK=_.Ed?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Cf=a;a.defaultPrevented&&_.rj.N.preventDefault.call(this)};_.rj.prototype.stopPropagation=function(){_.rj.N.stopPropagation.call(this);this.Cf.stopPropagation?this.Cf.stopPropagation():this.Cf.cancelBubble=!0};_.rj.prototype.preventDefault=function(){_.rj.N.preventDefault.call(this);var a=this.Cf;a.preventDefault?a.preventDefault():a.returnValue=!1};_.sj="closure_listenable_"+(Math.random()*1E6|0);_.tj=function(a){return!(!a||!a[_.sj])};var uj=0;var vj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.If=e;this.key=++uj;this.hw=this.zy=!1},wj=function(a){a.hw=!0;a.listener=null;a.proxy=null;a.src=null;a.If=null};_.oj.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.ie[f];a||(a=this.ie[f]=[],this.nx++);var h=xj(a,b,d,e);h>-1?(b=a[h],c||(b.zy=!1)):(b=new vj(b,this.src,f,!!d,e),b.zy=c,a.push(b));return b};_.oj.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.ie))return!1;var e=this.ie[a];b=xj(e,b,c,d);return b>-1?(wj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.ie[a],this.nx--),!0):!1};
_.yj=function(a,b){var c=b.type;if(!(c in a.ie))return!1;var d=_.gj(a.ie[c],b);d&&(wj(b),a.ie[c].length==0&&(delete a.ie[c],a.nx--));return d};_.oj.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.ie)if(!a||c==a){for(var d=this.ie[c],e=0;e<d.length;e++)++b,wj(d[e]);delete this.ie[c];this.nx--}return b};_.oj.prototype.Fq=function(a,b,c,d){a=this.ie[a.toString()];var e=-1;a&&(e=xj(a,b,c,d));return e>-1?a[e]:null};
_.oj.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return nj(this.ie,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var xj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.hw&&f.listener==b&&f.capture==!!c&&f.If==d)return e}return-1};var zj,Aj,Bj,Fj,Hj,Ij,Jj,Lj;zj="closure_lm_"+(Math.random()*1E6|0);Aj={};Bj=0;_.Dj=function(a,b,c,d,e){if(d&&d.once)return _.Cj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Dj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.na(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!1,d,e)};
Fj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.vb(e)?!!e.capture:!!e,k=_.Gj(a);k||(a[zj]=k=new _.oj(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=Hj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.vi||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ij(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");Bj++;return c};
Hj=function(){var a=Jj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.Cj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Cj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.nr(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!0,d,e)};
_.Kj=function(a){if(typeof a==="number"||!a||a.hw)return!1;var b=a.src;if(_.tj(b))return b.KN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ij(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bj--;(c=_.Gj(b))?(_.yj(c,a),c.nx==0&&(c.src=null,b[zj]=null)):wj(a);return!0};Ij=function(a){return a in Aj?Aj[a]:Aj[a]="on"+a};
Jj=function(a,b){if(a.hw)a=!0;else{b=new _.rj(b,this);var c=a.listener,d=a.If||a.src;a.zy&&_.Kj(a);a=c.call(d,b)}return a};_.Gj=function(a){a=a[zj];return a instanceof _.oj?a:null};Lj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.Ej=function(a){if(typeof a==="function")return a;a[Lj]||(a[Lj]=function(b){return a.handleEvent(b)});return a[Lj]};_.mj(function(a){Jj=a(Jj)});
_.Mj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Zd.prototype.O=_.pb(1,function(a){return _.be(this.Bc,a)});_.Nj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Nj(a,b[f],c,d,e);else d=_.vb(d)?!!d.capture:!!d,c=_.Ej(c),_.tj(a)?a.Ac(b,c,d,e):a&&(a=_.Gj(a))&&(b=a.Fq(b,c,d,e))&&_.Kj(b)};_.Oj=function(){_.dj.call(this);this.qk=new _.oj(this);this.g7=this;this.WJ=null};_.eb(_.Oj,_.dj);_.Oj.prototype[_.sj]=!0;_.g=_.Oj.prototype;_.g.Wn=function(){return this.WJ};
_.g.eD=function(a){this.WJ=a};_.g.addEventListener=function(a,b,c,d){_.Dj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Nj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Wn();if(c)for(b=[];c;c=c.Wn())b.push(c);c=this.g7;var d=a.type||a;if(typeof a==="string")a=new _.qj(a,c);else if(a instanceof _.qj)a.target=a.target||c;else{var e=a;a=new _.qj(d,c);_.ij(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.cw&&f>=0;f--){var h=a.currentTarget=b[f];e=h.hu(d,!0,a)&&e}a.cw||(h=a.currentTarget=c,e=h.hu(d,!0,a)&&e,a.cw||(e=h.hu(d,!1,a)&&e));if(b)for(f=0;!a.cw&&f<b.length;f++)h=a.currentTarget=b[f],e=h.hu(d,!1,a)&&e;return e};
_.g.ua=function(){_.Oj.N.ua.call(this);this.AK();this.WJ=null};_.g.na=function(a,b,c,d){return this.qk.add(String(a),b,!1,c,d)};_.g.nr=function(a,b,c,d){return this.qk.add(String(a),b,!0,c,d)};_.g.Ac=function(a,b,c,d){return this.qk.remove(String(a),b,c,d)};_.g.KN=function(a){return _.yj(this.qk,a)};_.g.AK=function(){this.qk&&this.qk.removeAll(void 0)};
_.g.hu=function(a,b,c){a=this.qk.ie[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.hw&&f.capture==b){var h=f.listener,k=f.If||f.src;f.zy&&this.KN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Fq=function(a,b,c,d){return this.qk.Fq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.qk.hasListener(a!==void 0?String(a):void 0,b)};
var Gr;Gr=function(){var a=_.Jc();if(_.Qc())return _.Yc(a);a=_.Nc(a);var b=_.Xc(a);return _.Pc()?b(["Version","Opera"]):_.Sc()?b(["Edge"]):_.Tc()?b(["Edg"]):_.Mc("Silk")?b(["Silk"]):_.Wc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.Hr=function(a){return _.Dc(Gr(),a)>=0};_.Jr=function(){return _.Sb&&_.Kc?_.Kc.mobile:!_.Ir()&&(_.Mc("iPod")||_.Mc("iPhone")||_.Mc("Android")||_.Mc("IEMobile"))};
_.Ir=function(){return _.Sb&&_.Kc?!_.Kc.mobile&&(_.Mc("iPad")||_.Mc("Android")||_.Mc("Silk")):_.Mc("iPad")||_.Mc("Android")&&!_.Mc("Mobile")||_.Mc("Silk")};_.Kr=function(){return!_.Jr()&&!_.Ir()};
var ct;ct=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.dt=function(a,b,c,d){return Array.prototype.splice.apply(a,ct(arguments,1))};_.et=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.ft=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.ce(c,"*",a,b)[0]||null);return a||null};
_.gt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.it=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.jt=function(a){_.dj.call(this);this.qg=a;this.mc={}};_.eb(_.jt,_.dj);var kt=[];_.jt.prototype.na=function(a,b,c,d){return this.Cv(a,b,c,d)};
_.jt.prototype.Cv=function(a,b,c,d,e){Array.isArray(b)||(b&&(kt[0]=b.toString()),b=kt);for(var f=0;f<b.length;f++){var h=_.Dj(a,b[f],c||this.handleEvent,d||!1,e||this.qg||this);if(!h)break;this.mc[h.key]=h}return this};_.jt.prototype.nr=function(a,b,c,d){return lt(this,a,b,c,d)};var lt=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)lt(a,b,c[h],d,e,f);else{b=_.Cj(b,c,d||a.handleEvent,e,f||a.qg||a);if(!b)return a;a.mc[b.key]=b}return a};
_.jt.prototype.Ac=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Ac(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.vb(d)?!!d.capture:!!d,e=e||this.qg||this,c=_.Ej(c),d=!!d,b=_.tj(a)?a.Fq(b,c,d,e):a?(a=_.Gj(a))?a.Fq(b,c,d,e):null:null,b&&(_.Kj(b),delete this.mc[b.key]);return this};_.jt.prototype.removeAll=function(){_.Zb(this.mc,function(a,b){this.mc.hasOwnProperty(b)&&_.Kj(a)},this);this.mc={}};_.jt.prototype.ua=function(){_.jt.N.ua.call(this);this.removeAll()};
_.jt.prototype.handleEvent=function(){throw Error("K");};
var Xu,Yu,Zu,$u,av,cv,dv,ev,fv,hv;_.Vu=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.Wu=!1;Xu=function(a){try{_.Wu&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};Yu=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};Zu=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
$u=function(a,b){function c(){}if(!a)throw Error("N");if(!b)throw Error("O");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};av=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.bv=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};cv=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
dv=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("P`"+b);};ev={token:1,id_token:1};fv=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.gv=window.JSON;hv=function(a){this.WN=a||[];this.qc={}};
hv.prototype.addEventListener=function(a,b){if(!(Zu(this.WN,a)>=0))throw Error("R`"+a);if(!av(b))throw Error("S`"+a);this.qc[a]||(this.qc[a]=[]);Zu(this.qc[a],b)<0&&this.qc[a].push(b)};hv.prototype.removeEventListener=function(a,b){if(!(Zu(this.WN,a)>=0))throw Error("R`"+a);av(b)&&this.qc[a]&&this.qc[a].length&&(b=Zu(this.qc[a],b),b>=0&&this.qc[a].splice(b,1))};
hv.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&Zu(this.WN,b)>=0))throw Error("T`"+b);if(this.qc[b]&&this.qc[b].length)for(var c=this.qc[b].length,d=0;d<c;d++)this.qc[b][d](a)};var iv,jv,lv,pv,qv,Hv,Iv,Kv,Lv,Nv,Rv,Sv,Tv,Xv;iv={};jv={};_.kv=function(){if(_.ed()&&!_.Hr("118"))return!1;var a=_.Wc()&&!_.Tc()&&!_.Uc(),b=_.$c()||_.Kr();return"IdentityCredential"in window&&a&&b&&_.Hr("132")&&(_.Kr()||_.$c())};lv={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.mv=function(a,b){if(a=lv[a])return a[b]};
_.nv=function(a,b){if(!a)throw Error("U");if(!b.authServerUrl)throw Error("V");if(!b.idpIFrameUrl)throw Error("W");lv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?lv[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(lv[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.ov=void 0;
pv=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};qv=function(){this.Ji=window;this.Wy=this.yn=this.Yv=this.wi=null};
qv.prototype.open=function(a,b,c,d){rv(this);this.Yv?(this.yn&&(this.yn(),this.yn=null),sv(this)):this.Yv="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.wi=this.Ji.open(a,this.Yv,b);try{this.wi.focus();if(this.wi.closed||typeof this.wi.closed=="undefined")throw Error("Y");_.ov=this.wi}catch(e){d&&setTimeout(d,0);this.wi=null;break a}c&&(this.yn=c,tv(this))}};
var rv=function(a){try{if(a.wi==null||a.wi.closed)a.wi=null,a.Yv=null,sv(a),a.yn&&(a.yn(),a.yn=null)}catch(b){a.wi=null,a.Yv=null,sv(a)}},tv=function(a){a.Wy=window.setInterval(function(){rv(a)},300)},sv=function(a){a.Wy&&(window.clearInterval(a.Wy),a.Wy=null)};jv=jv||{};var uv=function(a,b){this.Yb=a;this.rI=b;this.Qc=null;this.oo=!1};uv.prototype.start=function(){if(!this.oo&&!this.Qc){var a=this;this.Qc=window.setTimeout(function(){a.clear();a.oo||(a.Yb(),a.oo=!0)},jv.sU(this.rI))}};
uv.prototype.clear=function(){this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};var vv=function(a,b){var c=jv.ct;this.Aba=jv.Ss;this.t2=c;this.Yb=a;this.rI=b;this.Qc=null;this.oo=!1;var d=this;this.u2=function(){document[d.Aba]||(d.clear(),d.start())}};vv.prototype.start=function(){if(!this.oo&&!this.Qc){dv(document,this.t2,this.u2);var a=this;this.Qc=window.setTimeout(function(){a.clear();a.oo||(a.Yb(),a.oo=!0)},jv.sU(this.rI))}};
vv.prototype.clear=function(){var a=this.t2,b=this.u2,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("Q`"+a);this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};jv.Ss=null;jv.ct=null;
jv.cca=function(){var a=document;typeof a.hidden!=="undefined"?(jv.Ss="hidden",jv.ct="visibilitychange"):typeof a.msHidden!=="undefined"?(jv.Ss="msHidden",jv.ct="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(jv.Ss="webkitHidden",jv.ct="webkitvisibilitychange")};jv.cca();jv.w8=function(a,b){return jv.Ss&&jv.ct?new vv(a,b):new uv(a,b)};jv.sU=function(a){return Math.max(1,a-(new Date).getTime())};
var wv=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},xv=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=
w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<
q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ri:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}},yv=
window.crypto,zv=!1,Av=0,Bv=1,Cv=0,Dv="",Ev=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bv=Bv*b%Cv;if(++Av==3)if(a=window,b=Ev,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("Q`mousemove");},Fv=function(a){var b=xv();b.update(a);return b.Ri()};zv=!!yv&&typeof yv.getRandomValues=="function";
zv||(Cv=(screen.width*screen.width+screen.height)*1E6,Dv=Fv(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),dv(window,"mousemove",Ev));iv=iv||{};iv.y4="ssIFrame_";
_.Gv=function(a,b,c){c=c===void 0?!1:c;this.Bb=a;if(!this.Bb)throw Error("Z");a=_.mv(a,"idpIFrameUrl");if(!a)throw Error("$");this.AV=a;if(!b)throw Error("aa");this.Sm=b;a=this.AV;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.aI=a.join("");this.Bfa=[location.protocol,"//",location.host].join("");
this.Xw=this.ZH=this.wo=!1;this.wV=null;this.IB=[];this.Kr=[];this.ek={};this.xo=void 0;this.Ds=c};_.g=_.Gv.prototype;_.g.show=function(){var a=this.xo;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){pv(this.xo)};
_.g.nB=function(a){if(this.wo)a&&a(this);else{if(!this.xo){var b=iv.y4+this.Bb;var c=this.Bb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&Zu(e.split("|"),c)>=0)?wv(e,d):wv(e?e+"|"+c:c,d);c=!f;var h=this.AV,k=this.Bfa;d=this.Sm;e=this.Ds;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&av(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");pv(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.Wu&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.xo=f}a&&this.IB.push(a)}};_.g.EW=function(){return this.wo&&this.Xw};_.g.Un=function(){return this.wV};
Hv=function(a){for(var b=0;b<a.IB.length;b++)a.IB[b](a);a.IB=[]};_.Jv=function(a,b,c,d){if(a.wo){if(a.wo&&a.ZH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.Un(),Xu(a),Error(a);Iv(a,{method:b,params:c},d)}else a.Kr.push({hp:{method:b,params:c},callback:d}),a.nB()};Iv=function(a,b,c){if(c){for(var d=b.id;!d||a.ek[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.ek[d]=c}b.rpcToken=a.Sm;a.xo.contentWindow.postMessage(_.gv.stringify(b),a.aI)};
Kv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ba");};_.Gv.prototype.yj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;Kv(f);b=_.bv(b);_.Jv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.Gv.prototype.lB=function(a,b,c,d,e){b=_.bv(b);_.Jv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};Lv=function(a,b,c){Kv(b.identifier);_.Jv(a,"getSessionSelector",b,c)};
_.Mv=function(a,b,c,d,e){Kv(b.identifier);_.Jv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};Nv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.Jv(a,"monitorClient",b,h)};_.Gv.prototype.revoke=_.jb(8);_.Gv.prototype.vt=_.jb(10);iv.IA={};iv.XG=function(a){return iv.IA[a]};
iv.nB=function(a,b,c){c=c===void 0?!1:c;var d=iv.XG(a);if(!d){d=String;if(zv){var e=new window.Uint32Array(1);yv.getRandomValues(e);e=Number("0."+e[0])}else e=Bv,e+=parseInt(Dv.substr(0,20),16),Dv=Fv(Dv),e/=Cv+1.2089258196146292E24;d=new _.Gv(a,d(2147483647*e),c);iv.IA[a]=d}d.nB(b)};iv.i$=function(a){for(var b in iv.IA){var c=iv.XG(b);if(c&&c.xo&&c.xo.contentWindow==a.source&&c.aI==a.origin)return c}};iv.K$=function(a){for(var b in iv.IA){var c=iv.XG(b);if(c&&c.aI==a)return c}};iv=iv||{};
var Pv=function(){var a=[],b;for(b in _.Ov)a.push(_.Ov[b]);hv.call(this,a);this.om={};Xu("EventBus is ready.")};$u(Pv,hv);_.Ov={e6:"sessionSelectorChanged",BE:"sessionStateChanged",Ps:"authResult",u3:"displayIFrame"};Rv=function(a,b){var c=Qv;a&&b&&(c.om[a]||(c.om[a]=[]),Zu(c.om[a],b)<0&&c.om[a].push(b))};Sv=function(a){var b=Qv;a&&(b.om[a]||(b.om[a]=[]))};Tv=function(a,b,c){return b&&a.om[b]&&Zu(a.om[b],c)>=0};_.g=Pv.prototype;
_.g.Hea=function(a){var b,c=!!a.source&&(a.source===_.ov||a.source.opener===window);if(b=c?iv.K$(a.origin):iv.i$(a)){try{var d=_.gv.parse(a.data)}catch(e){Xu("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.Sm){Xu("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.ek[c.id])delete b.ek[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?Xu("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.zV[a.type]?(d=this.zV[a.type],
c&&!d.l7?Xu("Bad IDP event. Source window cannot be a popup."):d.Ks&&!d.Ks.call(this,b,a)?Xu("Bad IDP event."):d.If.call(this,b,a)):Xu("Bad IDP event.")}else Xu("Bad event, no corresponding Idp Stub.")};_.g.hga=function(a,b){return Tv(this,a.Bb,b.clientId)};_.g.gga=function(a,b){a=a.Bb;b=b.clientId;return!b||Tv(this,a,b)};_.g.x7=function(a,b){return Tv(this,a.Bb,b.clientId)};
_.g.Qda=function(a,b){a.wo=!0;a.Xw=!!b.cookieDisabled;Hv(a);for(b=0;b<a.Kr.length;b++)Iv(a,a.Kr[b].hp,a.Kr[b].callback);a.Kr=[]};_.g.Pda=function(a,b){b={error:b.error};a.wo=!0;a.ZH=!0;a.wV=b;a.Kr=[];Hv(a)};_.g.iC=function(a,b){b.originIdp=a.Bb;this.dispatchEvent(b)};var Qv=new Pv,Uv=Qv,Vv={};Vv.idpReady={If:Uv.Qda};Vv.idpError={If:Uv.Pda};Vv.sessionStateChanged={If:Uv.iC,Ks:Uv.hga};Vv.sessionSelectorChanged={If:Uv.iC,Ks:Uv.gga};Vv.authResult={If:Uv.iC,Ks:Uv.x7,l7:!0};Vv.displayIFrame={If:Uv.iC};
Qv.zV=Vv||{};dv(window,"message",function(a){Qv.Hea.call(Qv,a)});
_.Wv=function(a,b){this.Ne=!1;if(!a)throw Error("ca");var c=[],d;for(d in a)c.push(a[d]);hv.call(this,c);this.Cd=[location.protocol,"//",location.host].join("");this.Wd=b.crossSubDomains?b.domain||this.Cd:this.Cd;if(!b)throw Error("da");if(!b.idpId)throw Error("ea");if(!_.mv(b.idpId,"authServerUrl")||!_.mv(b.idpId,"idpIFrameUrl"))throw Error("fa`"+b.idpId);this.Bb=b.idpId;this.Ob=void 0;this.H8=!!b.disableTokenRefresh;this.E9=!!b.forceTokenRefresh;this.dha=!!b.skipTokenCache;this.Ds=!!b.supportBlocked3PCookies;
b.pluginName&&(this.wea=b.pluginName);b.ackExtensionDate&&(this.c7=b.ackExtensionDate);this.d2=b.useFedCm;this.o9=this.Ds&&_.kv();this.setOptions(b);this.Jt=[];this.Xw=this.Bk=this.pW=!1;this.oj=void 0;this.FZ();this.Nd=void 0;var e=this,f=function(){Xu("Token Manager is ready.");if(e.Jt.length)for(var h=0;h<e.Jt.length;h++)e.Jt[h].call(e);e.pW=!0;e.Jt=[]};iv.nB(this.Bb,function(h){e.Nd=h;h.wo&&h.ZH?(e.Bk=!0,e.oj=h.Un(),e.Br(e.oj)):(e.Xw=h.EW(),e.Ob?Nv(e.Nd,e.Ob,e.wea,e.c7,e.d2,e.o9,function(k){var l=
!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.oj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Bk=!0,e.Br(e.oj)):l?m?n?(Yu("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.oj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Bk=!0,e.Br(e.oj)):(Yu("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.oj={error:"Not a valid origin for the client: "+e.Cd+" has not been registered for client ID "+e.Ob+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Bk=!0,e.Br(e.oj))}):(Sv(e.Bb),f()))},this.Ds)};$u(_.Wv,hv);_.g=_.Wv.prototype;_.g.setOptions=function(){};_.g.FZ=function(){};_.g.Br=function(){};_.g.EW=function(){return this.Xw};_.g.Un=function(){return this.oj};Xv=function(a,b,c){return function(){b.apply(a,c)}};
_.Yv=function(a,b,c){if(a.pW)b.apply(a,c);else{if(a.Bk)throw a.oj;a.Jt.push(Xv(a,b,c))}};_.Wv.prototype.AQ=_.jb(11);_.Wv.prototype.vt=_.jb(9);_.$v=function(a,b){_.Wv.call(this,a,b);this.WY=new qv;this.Lk=this.So=null;Zv(this)};$u(_.$v,_.Wv);_.$v.prototype.setOptions=function(){};
var aw=function(a,b){a.Ke={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.Wd};b.crossSubDomains&&(a.Ke.policy=b.policy)},bw=function(a,b){if(!b.authParameters)throw Error("ga");if(!b.authParameters.scope)throw Error("ha");if(!b.authParameters.response_type)throw Error("ia");a.nn=b.authParameters;a.nn.redirect_uri||(a.nn.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Hj=_.bv(b.rpcAuthParameters||a.nn);if(!a.Hj.scope)throw Error("ja");if(!a.Hj.response_type)throw Error("ka");
a:{var c=a.Hj.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!ev[c[d]]){c=!0;break a}c=!1}if(c)throw Error("la");if(b.enableSerialConsent||b.enableGranularConsent)a.nn.enable_granular_consent=!0,a.Hj.enable_serial_consent=!0;b.authResultIdentifier&&(a.y7=b.authResultIdentifier);b.spec_compliant&&(a.Hj.spec_compliant=b.spec_compliant)};
_.$v.prototype.FZ=function(){var a=this;Qv.addEventListener(_.Ov.e6,function(b){a.Ne&&a.Ke&&b.originIdp==a.Bb&&!b.crossSubDomains==!a.Ke.crossSubDomains&&b.domain==a.Ke.domain&&b.id==a.Ke.id&&a.nY(b)});Qv.addEventListener(_.Ov.BE,function(b){a.Ne&&b.originIdp==a.Bb&&b.clientId==a.Ob&&a.oY(b)});Qv.addEventListener(_.Ov.Ps,function(b){_.ov=void 0;a.Ne&&b.originIdp==a.Bb&&b.clientId==a.Ob&&b.id==a.Ik&&(a.So&&(window.clearTimeout(a.So),a.So=null),a.Ik=void 0,a.No(b))});Qv.addEventListener(_.Ov.u3,function(b){a.Ne&&
b.originIdp==a.Bb&&(b.hide?a.Nd.hide():a.Nd.show())})};_.$v.prototype.nY=function(){};_.$v.prototype.oY=function(){};_.$v.prototype.No=function(){};var dw=function(a,b){cw(a);a.H8||(a.Lk=jv.w8(function(){a.yj(!0)},b-3E5),navigator.onLine&&a.Lk.start())},cw=function(a){a.Lk&&(a.Lk.clear(),a.Lk=null)},Zv=function(a){var b=window;fv()&&(b=document.body);dv(b,"online",function(){a.Lk&&a.Lk.start()});dv(b,"offline",function(){a.Lk&&a.Lk.clear()})};_.$v.prototype.yj=function(){};_.$v.prototype.OX=_.jb(12);
_.$v.prototype.Mca=function(a,b){if(!this.Ob)throw Error("pa");this.Nd.lB(this.Ob,this.Hj,this.Ke,a,b)};_.$v.prototype.lB=function(a,b){_.Yv(this,this.Mca,[a,b])};_.fw=function(a){this.Ee=void 0;this.Lh=!1;this.Tr=void 0;_.$v.call(this,ew,a)};$u(_.fw,_.$v);var ew={EO:"noSessionBound",bt:"userLoggedOut",L2:"activeSessionChanged",BE:"sessionStateChanged",x6:"tokenReady",w6:"tokenFailed",Ps:"authResult",ERROR:"error"};
_.fw.prototype.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.fw.prototype.Br=function(a){this.dispatchEvent({type:ew.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Bb})};var gw=function(a){cw(a);a.Tr=void 0;a.OI=void 0};_.g=_.fw.prototype;
_.g.nY=function(a){var b=a.newValue||{};if(this.Ee!=b.hint||this.Lh!=!!b.disabled){a=this.Ee;var c=!this.Ee||this.Lh;gw(this);this.Ee=b.hint;this.Lh=!!b.disabled;(b=!this.Ee||this.Lh)&&!c?this.dispatchEvent({type:ew.bt,idpId:this.Bb}):b||(a!=this.Ee&&this.dispatchEvent({type:ew.L2,idpId:this.Bb}),this.Ee&&this.yj())}};
_.g.oY=function(a){this.Lh||(this.Ee?a.user||this.Tr?a.user==this.Ee&&(this.Tr?a.sessionState?this.Tr=a.sessionState:(gw(this),this.dispatchEvent({type:ew.bt,idpId:this.Bb})):a.sessionState&&(this.Tr=a.sessionState,this.yj())):this.yj():this.dispatchEvent({type:ew.BE,idpId:this.Bb}))};_.g.No=function(a){this.dispatchEvent({type:ew.Ps,authResult:a.authResult})};_.g.xu=_.jb(14);_.g.qu=function(a){_.Yv(this,this.KG,[a])};_.g.KG=function(a){Lv(this.Nd,this.Ke,a)};
_.g.xD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("ra");gw(this);this.Ee=a;this.Lh=!1;b&&_.Mv(this.Nd,this.Ke,!1,this.Ee);this.Ne=!0;this.yj(c,!0,d)};_.g.start=function(){_.Yv(this,this.Ow,[])};
_.g.Ow=function(){var a=this.Ob==cv("client_id")?cv("login_hint"):void 0;var b=this.Ob==cv("client_id")?cv("state"):void 0;this.yJ=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.xD(a,!0,!0,!0);else{var c=this;this.qu(function(d){c.Ne=!0;d&&d.hint?(gw(c),c.Ee=d.hint,c.Lh=!!d.disabled,c.Lh?c.dispatchEvent({type:ew.bt,idpId:c.Bb}):c.xD(d.hint)):(gw(c),c.Ee=void 0,c.Lh=!(!d||!d.disabled),c.dispatchEvent({type:ew.EO,
autoOpenAuthUrl:!c.Lh,idpId:c.Bb}))})}};_.g.A9=function(){var a=this;this.qu(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:ew.bt,idpId:a.Bb}):a.yj(!0):a.dispatchEvent({type:ew.EO,idpId:a.Bb})})};_.g.oT=function(){_.Yv(this,this.A9,[])};
_.g.yj=function(a,b,c){var d=this;this.Nd.yj(this.Ob,this.Hj,this.Ee,this.Ke,function(e,f){(f=f||e.error)?f=="user_logged_out"?(gw(d),d.dispatchEvent({type:ew.bt,idpId:d.Bb})):(d.OI=null,d.dispatchEvent({type:ew.w6,idpId:d.Bb,error:f})):(d.OI=e,d.Tr=e.session_state,dw(d,e.expires_at),e.idpId=d.Bb,b&&d.yJ&&(e.state=d.yJ,d.yJ=void 0),d.dispatchEvent({type:ew.x6,idpId:d.Bb,response:e}))},this.Da,a,!1,c===void 0?!1:c)};_.g.revoke=_.jb(7);_.g.b_=_.jb(15);
_.hw=function(a){this.qn=null;_.$v.call(this,{},a);this.Ne=!0};$u(_.hw,_.$v);_.g=_.hw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.g.Br=function(a){this.qn&&(this.qn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.qn=null)};_.g.No=function(a){if(this.qn){var b=this.qn;this.qn=null;b(a)}};_.g.xu=_.jb(13);_.g.qu=function(a){this.Bk?a(this.Un()):_.Yv(this,this.KG,[a])};
_.g.KG=function(a){Lv(this.Nd,this.Ke,a)};_.iw=function(a,b,c){a.Bk?c(a.Un()):_.Yv(a,a.cea,[b,c])};_.hw.prototype.cea=function(a,b){this.Nd.yj(this.Ob,this.Hj,a,this.Ke,function(c,d){d?b({error:d}):b(c)},this.Da,this.E9,this.dha)};_.hw.prototype.cX=_.jb(16);
var jw=function(a){var b=window.location;a=_.sc(a);a!==void 0&&b.assign(a)},kw=function(a){return Array.prototype.concat.apply([],arguments)},lw=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Nh(a,3).substring(0,64)},mw=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
nw=function(a,b){(b===void 0?0:b)||window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});jw(a)},ow=function(a,b,c){if(!a.Ne)throw Error("ma");b?_.Mv(a.Nd,a.Ke,!0,void 0,c):_.Mv(a.Nd,a.Ke,!0,a.Ee,c)},pw=function(a){if(!a.Ne)throw Error("ma");return a.OI},qw,rw,sw,tw,uw,vw,ww,xw,yw,zw,Aw,Bw,Cw,Dw,Gw,Jw,Kw;
_.hw.prototype.cX=_.pb(16,function(a,b){var c=this.Nd,d=this.Ob,e=this.Ke,f=_.bv(this.Hj);delete f.response_type;_.Jv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.fw.prototype.b_=_.pb(15,function(a){pw(this)&&pw(this).access_token&&(this.Nd.revoke(this.Ob,pw(this).access_token,a),ow(this,!0))});
_.fw.prototype.xu=_.pb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.eC?(b.authResult.client_id=a.Ob,nw(a.eC+"#"+mw(b.authResult))):a.xD(b.authResult.login_hint,a.Lh||b.authResult.login_hint!=a.Ee,!0,!0))}});
_.hw.prototype.xu=_.pb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.qu(function(d){_.Mv(b.Nd,b.Ke,d&&d.disabled,c.authResult.login_hint,function(){_.iw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.$v.prototype.OX=_.pb(12,function(){this.Ob&&_.Jv(this.Nd,"startPolling",{clientId:this.Ob,origin:this.Cd,id:this.Ik})});
_.Gv.prototype.revoke=_.pb(8,function(a,b,c){_.Jv(this,"revoke",{clientId:a,token:b},c)});_.fw.prototype.revoke=_.pb(7,function(a){_.Yv(this,this.b_,[a])});qw="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
rw=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
sw=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};tw=function(a){return a.length>0&&a.every(function(b){return qw.includes(b)})};
uw=function(a,b,c,d,e,f,h){var k=_.mv(a,"authServerUrl");if(!k)throw Error("X`"+a);a=_.bv(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=sw())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+mw(a)};vw=function(a,b,c,d){if(!a.Ob)throw Error("na");a.Ik=c||a.y7||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Cd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Ik;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return uw(a.Bb,a.Wd,a.Ob,a.nn,!0,b,d)};
ww=function(a,b,c){if(!a.Ob)throw Error("na");return uw(a.Bb,a.Wd,a.Ob,a.nn,!1,b,c)};xw=function(a,b){a.So&&window.clearTimeout(a.So);a.So=window.setTimeout(function(){a.Ik==b&&(_.ov=void 0,a.So=null,a.Ik=void 0,a.No({authResult:{error:"popup_closed_by_user"}}))},1E3)};
yw=function(a,b,c){if(!a.Ob)throw Error("oa");c=c||{};c=vw(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||rw())&&_.Yv(a,a.OX,[]);var d=a.Ik;a.WY.open(c,b,function(){a.Ik==d&&xw(a,d)},function(){a.Ik=void 0;a.No({authResult:{error:"popup_blocked_by_browser"}})})};
zw=function(a,b){var c=b||{};b=_.bv(a.nn);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.Wd;d=_.mv(a.Bb,"fedcmConfigUrl");c=c.responseType;
b.response_type=c;b.scope=e.join(" ");!b.nonce&&c.includes("id_token")&&(b.nonce="notprovided");c=navigator.userActivation.isActive?"active":"passive";e=tw(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Ob,fields:e,params:b}],mode:c},mediation:"required"}};
Aw=function(a,b,c){if(!a.Ob)throw Error("oa");b=zw(a,b);navigator.credentials.get(b).then(function(d){d=JSON.parse(d.token);var e={client_id:d.client_id,login_hint:d.login_hint,expires_in:3600,scope:d.scope};d.code&&(e.code=d.code);d.id_token&&(e.id_token=d.id_token);a.No({type:_.Ov.Ps,idpId:a.Bb,authResult:e})},function(d){d.message.indexOf("identity-credentials-get")>=0||d.message.indexOf("Content Security Policy")>=0?c():a.No({type:_.Ov.Ps,idpId:a.Bb,authResult:{error:d}})})};
Bw=function(a,b,c){a.Ds&&_.kv()?Aw(a,c,function(){return yw(a,b,c)}):yw(a,b,c)};Cw=function(a,b){b=b||{};var c=ww(a,b.sessionMeta,b.responseType);a.Ds&&_.kv()&&a.d2?(a.eC=b.sessionMeta.extraQueryParams.redirect_uri,Aw(a,b,function(){return nw(c,!0)})):nw(c,!0)};Dw=function(a,b,c){a.Bk?c(a.Un()):_.Yv(a,a.cX,[b,c])};_.Ew=function(a){_.Be(_.Me,"le",[]).push(a)};
_.Fw=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
Gw=function(a){var b=[];_.Oh(a,function(c){b.push(c)});return b};_.Hw=function(a,b){_.ti[b||"token"]=a};_.Iw=function(a){delete _.ti[a||"token"]};Kw=function(){if(typeof MessageChannel!=="undefined"){var a=new MessageChannel,b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;var d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(d){_.Xa.setTimeout(d,0)}};_.gv={parse:function(a){a=_.Qf("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Rf(a)}};_.hw.prototype.wG=function(a,b){_.Yv(this,this.q9,[a,b])};_.hw.prototype.q9=function(a,b){this.Nd.wG(this.Ob,a,this.Hj,this.Ke,b)};_.Gv.prototype.wG=function(a,b,c,d,e){c=_.bv(c);_.Jv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Lw,Mw=["client_id","cookie_policy","scope"],Nw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),Ow=["authuser","after_redirect","access_type","hl"],Pw=["login_hint","prompt"],Qw={clientid:"client_id",cookiepolicy:"cookie_policy"},
Rw=["approval_prompt","authuser","login_hint","prompt","hd"],Sw=["login_hint","g-oauth-window","status"],Tw=Math.min(_.Xe("oauth-flow/authWindowWidth",599),screen.width-20),Uw=Math.min(_.Xe("oauth-flow/authWindowHeight",600),screen.height-30);var Vw=function(a){_.lb.call(this,a)};_.y(Vw,_.lb);Vw.prototype.name="gapi.auth2.ExternallyVisibleError";var Ww=function(){};Ww.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var Xw=function(){};Xw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var Yw=function(a){this.z7=a};
Yw.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.z7){d.login_hint?b(d):b();return}}b()};var Zw=function(a){this.re=a;this.QC=[]};Zw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.QC[b];f?(b++,c.re.lB(function(h){h?f.select(h,d):d()})):a()}};d()};var $w=function(a){a=new Zw(a);a.QC.push(new Ww);return a},ax=function(a){a=new Zw(a);a.QC.push(new Xw);return a},bx=function(a,b){b===void 0||b===null?b=$w(a):(a=new Zw(a),a.QC.push(new Yw(b)),b=a);return b};var cx=function(a){this.If=a;this.isActive=!0};cx.prototype.remove=function(){this.isActive=!1};cx.prototype.trigger=function(){};var dx=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},ex=function(){this.qc=[]};ex.prototype.add=function(a){this.qc.push(a)};ex.prototype.notify=function(a){for(var b=this.qc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),e=fx(e.If,a),e=(0,_.qk)(e),e=(0,_.ok)(e),Jw||(Jw=Kw()),Jw(e))}this.qc=c};var fx=function(a,b){return function(){a(b)}};var ox=function(a){this.La=null;this.Uha=new gx(this);this.qc=new ex;a!=void 0&&this.set(a)};ox.prototype.set=function(a){a!=this.La&&(this.La=a,this.Uha.value=a,this.qc.notify(this.La))};ox.prototype.get=function(){return this.La};ox.prototype.na=function(a){a=new px(this,a);this.qc.add(a);return a};ox.prototype.get=ox.prototype.get;var px=function(a,b){cx.call(this,b);this.Qca=a};_.y(px,cx);px.prototype.trigger=function(){var a=this.If;a(this.Qca.get())};
var gx=function(a){this.value=null;this.na=function(b){return new dx(a.na(b))}};var qx={nka:"fetch_basic_profile",pla:"login_hint",Ima:"prompt",Oma:"redirect_uri",gna:"scope",Doa:"ux_mode",Sna:"state"},rx=function(a){this.Ka={};if(a&&!_.Gh(a))if(typeof a.get=="function")this.Ka=a.get();else for(var b in qx){var c=qx[b];c in a&&(this.Ka[c]=a[c])}};rx.prototype.get=function(){return this.Ka};rx.prototype.e0=function(a){this.Ka.scope=a;return this};rx.prototype.Hu=function(){return this.Ka.scope};
var sx=function(a,b){var c=a.Ka.scope;b=kw(b.split(" "),c?c.split(" "):[]);_.Fh(b);a.Ka.scope=b.join(" ")};_.g=rx.prototype;_.g.Hga=function(a){this.Ka.prompt=a;return this};_.g.M$=function(){return this.Ka.prompt};_.g.kga=function(){_.Vf.warn("Property app_package_name no longer supported and was not set");return this};_.g.Q9=function(){_.Vf.warn("Property app_package_name no longer supported")};_.g.tf=function(a){this.Ka.state=a};_.g.getState=function(){return this.Ka.state};var tx=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Tw,"height="+Uw,"top="+(screen.height-Uw)/2,"left="+(screen.width-Tw)/2].join()},ux=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;var b=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");a=JSON;var c=a.parse;b=Gw(b);return c.call(a,_.Fw(b))},vx=function(){Lw=_.Xe("auth2/idpValue","google");var a=_.Xe("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.Xe("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Xe("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.nv(Lw,a)},wx=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.vb(a[e])||(a[e]={});a=a[e]}},xx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},
zx=function(){var a=yx();a.storage_path&&window.sessionStorage.setItem(a.storage_path,xx()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new Vw("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Vf.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},Ax=function(a){return _.li.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.Me.le||[]).includes("fedcm_migration_mod")?
!0:!1};var Bx=function(a){var b=a?(b=ux(a))?b.sub:null:null;this.Da=b;this.Ic=a?_.fk(a):null};_.g=Bx.prototype;_.g.getId=function(){return this.Da};_.g.UG=function(){var a=ux(this.Ic);return a?a.hd:null};_.g.vg=function(){return!!this.Ic};_.g.Sl=function(a){if(a)return this.Ic;a=Cx;var b=_.fk(this.Ic);!a.TA||a.TH||a.pba||(delete b.access_token,delete b.scope);return b};_.g.yK=function(){return Cx.yK()};_.g.Yk=function(){this.Ic=null};_.g.q$=function(){return this.Ic?this.Ic.scope:null};
_.g.update=function(a){this.Da=a.Da;this.Ic=a.Ic;this.Ic.id_token?this.py=new Dx(this.Ic):this.py&&(this.py=null)};var Ex=function(a){return a.Ic&&typeof a.Ic.session_state=="object"?_.fk(a.Ic.session_state.extraQueryParams||{}):{}};_.g=Bx.prototype;_.g.IG=function(){var a=Ex(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.Xk=function(a){var b=Cx,c=new rx(a);b.TH=c.Hu()?!0:!1;Cx.TA&&sx(c,"openid profile email");return new _.xk(function(d,e){var f=Ex(this);f.login_hint=this.getId();f.scope=c.Hu();Fx(b,d,e,f)},this)};_.g.Ou=function(a){return new _.xk(function(b,c){var d=a||{},e=Cx;d.login_hint=this.getId();e.Ou(d).then(b,c)},this)};_.g.caa=function(a){return this.Xk(a)};_.g.disconnect=function(){return Cx.disconnect()};_.g.S9=function(){return this.py};
_.g.BA=function(a){if(!this.vg())return!1;var b=this.Ic&&this.Ic.scope?this.Ic.scope.split(" "):"";return _.Nb(a?a.split(" "):[],function(c){return _.tb(b,c)})};var Dx=function(a){a=ux(a);this.K9=a.sub;this.Zg=a.name;this.Y$=a.given_name;this.n9=a.family_name;this.EV=a.picture;this.kz=a.email};_.g=Dx.prototype;_.g.getId=function(){return this.K9};_.g.getName=function(){return this.Zg};_.g.o$=function(){return this.Y$};_.g.k$=function(){return this.n9};_.g.w$=function(){return this.EV};_.g.On=function(){return this.kz};var yx,Gx;yx=function(){var a=_.li.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Vf.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Gx=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Hx=function(a){if(!a)throw new Vw("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new Vw("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Mj(b,"."+a)&&!_.Mj(b,a))throw new Vw("Invalid cookiePolicy domain");return d};var Jx=function(a){var b=a||{},c=Ix();_.Bb(Nw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Ix=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;Qw[d]&&(d=Qw[d]);_.tb(Nw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Kx=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Lx=function(a){_.Bb(Nw,
function(b){var c=Kx(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Mx=function(a){a=Jx(a);Lx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Nw+Ow,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},Nx=function(a,b){if(!a)throw new Vw("Empty initial options.");for(var c=0;c<Mw.length;++c)if(!(b&&Mw[c]=="scope"||a[Mw[c]]))throw new Vw("Missing required parameter '"+Mw[c]+"'");_.Hx(a.cookie_policy)},Px=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Gx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Lw};Ox(b,a);_.Bb(Pw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Xe("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},Ox=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||lw());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},Ux=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=Qx(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Gx(c),idpId:Lw};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||lw());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.Bb(Pw.concat(Ow),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new Rx(a.response_type||"token"),Sx(b)&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),Tx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Xe("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},Qx=function(a){a=new Rx(a.response_type||"token");var b=[];Tx(a)&&b.push("token");Vx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},Wx=["permission","id_token"],Xx=/(^|[^_])token/,Rx=function(a){this.Nr=[];this.oI(a)};Rx.prototype.oI=function(a){a?((a.indexOf("permission")>=0||a.match(Xx))&&this.Nr.push("permission"),a.indexOf("id_token")>=0&&this.Nr.push("id_token"),a.indexOf("code")>=0&&this.Nr.push("code")):this.Nr=Wx};var Sx=function(a){return Vx(a,"code")},Tx=function(a){return Vx(a,"permission")};Rx.prototype.toString=function(){return this.Nr.join(" ")};
var Vx=function(a,b){var c=!1;_.Bb(a.Nr,function(d){d==b&&(c=!0)});return c};var Zx=function(a,b,c){this.AJ=b;this.Eda=a;for(var d in a)a.hasOwnProperty(d)&&Yx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.AJ[c[a]]},Yx=function(a,b){a[b]=function(){return a.Eda[b].apply(a.AJ,arguments)}};Zx.prototype.then=function(a,b,c){var d=this;return _.Bk().then(function(){return $x(d.AJ,a,b,c)})};_.mk(Zx);var Cx,ay,cy;Cx=null;_.by=function(){return Cx?ay():null};ay=function(){return new Zx(cy.prototype,Cx,["currentUser","isSignedIn"])};cy=function(a){delete a.include_granted_scopes;this.Ka=Px(a);this.u8=a.cookie_policy;this.pba=!!a.scope;(this.TA=a.fetch_basic_profile!==!1)&&(this.Ka.authParameters.scope=dy(this,"openid profile email"));this.Ka.supportBlocked3PCookies=Ax(a.use_fedcm);this.ev=a.hosted_domain;this.Sha=a.ux_mode||"popup";this.eC=a.redirect_uri||null;this.lI()};
cy.prototype.lI=function(){this.currentUser=new ox(new Bx(null));this.isSignedIn=new ox(!1);this.re=new _.fw(this.Ka);this.YA=this.gr=null;this.Bca=new _.xk(function(a,b){this.gr=a;this.YA=b},this);this.HB={};this.uv=!0;ey(this);this.re.start()};
var ey=function(a){a.re.addEventListener("error",function(b){a.uv&&a.gr&&(a.uv=!1,a.YA({error:b.error,details:b.details}),a.gr=null,a.YA=null)});a.re.addEventListener("authResult",function(b){b&&b.authResult&&a.Bf(b);a.re.xu()(b)});a.re.addEventListener("tokenReady",function(b){var c=new Bx(b.response);if(a.ev&&a.ev!=c.UG())a.Bf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.UG(),expectedDomain:a.ev});else{a.currentUser.get().update(c);
var d=a.currentUser;d.qc.notify(d.La);a.isSignedIn.set(!0);c=c.IG();(d=_.Hx(a.u8))&&c&&_.li.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.ef?"S":"H",d.Si].join(""),c,{domain:d.domain,secure:d.isSecure});_.Hw(b.response);a.Bf(b)}});a.re.addEventListener("noSessionBound",function(b){a.uv&&b.autoOpenAuthUrl?(a.uv=!1,$w(a.re).select(function(c){if(c&&c.login_hint){var d=a.re;_.Yv(d,d.xD,[c.login_hint,!0])}else a.currentUser.set(new Bx(null)),a.isSignedIn.set(!1),_.Iw(),a.Bf(b)})):(a.currentUser.set(new Bx(null)),
a.isSignedIn.set(!1),_.Iw(),a.Bf(b))});a.re.addEventListener("tokenFailed",function(b){a.Bf(b)});a.re.addEventListener("userLoggedOut",function(b){a.currentUser.get().Yk();var c=a.currentUser;c.qc.notify(c.La);a.isSignedIn.set(!1);_.Iw();a.Bf(b)})},$x=function(a,b,c,d){return a.Bca.then(function(e){if(b)return b(e.aaa)},c,d)};cy.prototype.Bf=function(a){if(a){this.uv=!1;var b=a.type||"";if(this.HB[b])this.HB[b](a);this.gr&&(this.gr({aaa:this}),this.YA=this.gr=null)}};
var fy=function(a,b){_.Zb(b,function(c,d){a.HB[d]=function(e){a.HB={};c(e)}})},Fx=function(a,b,c,d){d=_.fk(d);a.ev&&(d.hd=a.ev);var e=d.ux_mode||a.Sha;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.eC||xx()+window.location.pathname),gy(a,f)):(delete d.redirect_uri,hy(a,f),fy(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):fy(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};cy.prototype.Xk=function(a){return new _.xk(function(b,c){var d=new rx(a);this.TH=d.Hu()?!0:!1;this.TA?(d.Ka.fetch_basic_profile=!0,sx(d,"email profile openid")):d.Ka.fetch_basic_profile=!1;var e=dy(this,d.Hu());d.e0(e);Fx(this,b,c,d.get())},this)};
cy.prototype.Ou=function(a){var b=a||{};this.TH=!!b.scope;a=dy(this,b.scope);if(a=="")return _.Ck({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.Bb(Rw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=iy(this,c):(c.redirect_uri=b.redirect_uri,gy(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.Bk({message:"Redirecting to IDP."}));return a};
var iy=function(a,b){b.origin=xx();delete b.redirect_uri;hy(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.xk(function(c,d){fy(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},hy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Bw(a.re,tx(),b)},gy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Cw(a.re,b)};
cy.prototype.Yk=function(a){var b=a||!1;return new _.xk(function(c){ow(this.re,b,function(){c()})},this)};cy.prototype.aU=function(){return this.Ka.authParameters.scope};var dy=function(a,b){a=a.aU();b=kw(b?b.split(" "):[],a?a.split(" "):[]);_.Fh(b);return b.join(" ")};cy.prototype.yK=function(){var a=this;return new _.xk(function(b,c){fy(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.re.oT()})};
cy.prototype.WP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.Dj(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.Xk(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};cy.prototype.disconnect=function(){return new _.xk(function(a){this.re.revoke(function(){a()})},this)};cy.prototype.attachClickHandler=cy.prototype.WP;var jy;_.xk.prototype["catch"]=_.xk.prototype.DD;jy=null;_.ky=function(a){zx();a=Mx(a);if(Cx){if(_.Vu(a,jy||{}))return ay();throw new Vw("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}Nx(a,a.fetch_basic_profile!==!1);vx();jy=a;Cx=new cy(a);_.Me.ga=1;return ay()};var my,oy,ly,qy,py,ry;
_.ny=function(a,b){zx();vx();a=Mx(a);Nx(a);var c=Ux(a);c.supportBlocked3PCookies=Ax(a.use_fedcm);var d=new _.hw(c);a.prompt=="none"?ly(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):my(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.WY.wi;b(e)})};
my=function(a,b,c){var d=new Rx(b.response_type);c=oy(a,d,c);var e={responseType:d.toString()};wx(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");Sx(d)&&wx(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&wx(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&wx(e,["sessionMeta","extraQueryParams","state"],b.state);b=tx();a.Bk?c({authResult:{error:"idpiframe_initialization_failed",details:a.Un().error}}):(a.qn=
c,Bw(a,b,e))};oy=function(a,b,c){if(Tx(b)){var d=py(c);return function(e){e&&e.authResult&&!e.authResult.error?a.xu(function(f){f&&!f.error?(f=_.fk(f),Sx(b)&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.fk(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
ly=function(a,b,c){if(Sx(new Rx(b.response_type))&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=py(c);b.login_hint?a.wG(b.login_hint,function(e){e?qy(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?bx(a,b.authuser).select(function(e){e&&e.login_hint?qy(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.qu(function(e){e&&e.hint?qy(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?ax(a):$w(a)).select(function(f){f&&f.login_hint?qy(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};qy=function(a,b,c,d){b=new Rx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.ij(f,k),e==0&&d(f))};(Tx(b)||Vx(b,"id_token"))&&e++;Sx(b)&&e++;(Tx(b)||Vx(b,"id_token"))&&_.iw(a,c,h);Sx(b)&&Dw(a,c,h)};
py=function(a){return function(b){if(!b||b.error)_.Iw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.fk(b);ry(c);delete c.id_token;delete c.code;_.Hw(c)}a(b)}}};ry=function(a){_.Bb(Sw,function(b){delete a[b]})};_.t("gapi.auth2.init",_.ky);_.t("gapi.auth2.authorize",function(a,b){if(Cx!=null)throw new Vw("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.ny(a,function(c){ry(c);b(c)})});_.t("gapi.auth2._gt",function(){return _.ui()});_.t("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.Wu=a!="0"&&!!a});_.t("gapi.auth2.getAuthInstance",_.by);
_.t("gapi.auth2.BasicProfile",Dx);_.t("gapi.auth2.BasicProfile.prototype.getId",Dx.prototype.getId);_.t("gapi.auth2.BasicProfile.prototype.getName",Dx.prototype.getName);_.t("gapi.auth2.BasicProfile.prototype.getGivenName",Dx.prototype.o$);_.t("gapi.auth2.BasicProfile.prototype.getFamilyName",Dx.prototype.k$);_.t("gapi.auth2.BasicProfile.prototype.getImageUrl",Dx.prototype.w$);_.t("gapi.auth2.BasicProfile.prototype.getEmail",Dx.prototype.On);_.t("gapi.auth2.GoogleAuth",cy);
_.t("gapi.auth2.GoogleAuth.prototype.attachClickHandler",cy.prototype.WP);_.t("gapi.auth2.GoogleAuth.prototype.disconnect",cy.prototype.disconnect);_.t("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",cy.prototype.Ou);_.t("gapi.auth2.GoogleAuth.prototype.signIn",cy.prototype.Xk);_.t("gapi.auth2.GoogleAuth.prototype.signOut",cy.prototype.Yk);_.t("gapi.auth2.GoogleAuth.prototype.getInitialScopes",cy.prototype.aU);_.t("gapi.auth2.GoogleUser",Bx);_.t("gapi.auth2.GoogleUser.prototype.grant",Bx.prototype.caa);
_.t("gapi.auth2.GoogleUser.prototype.getId",Bx.prototype.getId);_.t("gapi.auth2.GoogleUser.prototype.isSignedIn",Bx.prototype.vg);_.t("gapi.auth2.GoogleUser.prototype.getAuthResponse",Bx.prototype.Sl);_.t("gapi.auth2.GoogleUser.prototype.getBasicProfile",Bx.prototype.S9);_.t("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Bx.prototype.q$);_.t("gapi.auth2.GoogleUser.prototype.getHostedDomain",Bx.prototype.UG);_.t("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Bx.prototype.Ou);
_.t("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Bx.prototype.BA);_.t("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Bx.prototype.yK);_.t("gapi.auth2.LiveValue",ox);_.t("gapi.auth2.LiveValue.prototype.listen",ox.prototype.na);_.t("gapi.auth2.LiveValue.prototype.get",ox.prototype.get);_.t("gapi.auth2.SigninOptionsBuilder",rx);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",rx.prototype.Q9);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",rx.prototype.kga);
_.t("gapi.auth2.SigninOptionsBuilder.prototype.getScope",rx.prototype.Hu);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setScope",rx.prototype.e0);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",rx.prototype.M$);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",rx.prototype.Hga);_.t("gapi.auth2.SigninOptionsBuilder.prototype.get",rx.prototype.get);
_.af=_.af||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.af.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.af.XQ=function(b){var c=_.af.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.af.hG(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.af.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.af.BT=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.af.Tqa=function(b){return a(b)}})();
_.Gg=window.gapi&&window.gapi.util||{};
_.Gg=_.Gg={};_.Gg.getOrigin=function(a){return _.Ig(a)};
_.Ny=function(a){if(a.indexOf("GCSC")!==0)return null;var b={wj:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Ly(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.ef;return!d&&(c!=="U"||e.ef)||d&&!_.My?b:{wj:!0,ef:d,e8:a.substr(1),domain:e.domain,Si:e.Si}};_.Oy=function(a,b){this.Zg=a;a=b||{};this.ida=Number(a.maxAge)||0;this.Wd=a.domain;this.Km=a.path;this.Qfa=!!a.secure};_.Oy.prototype.read=function(){for(var a=this.Zg+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.Oy.prototype.write=function(a,b){if(!Py.test(this.Zg))throw"Invalid cookie name";if(!Qy.test(a))throw"Invalid cookie value";a=this.Zg+"="+a;this.Wd&&(a+=";domain="+this.Wd);this.Km&&(a+=";path="+this.Km);b=typeof b==="number"?b:this.ida;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.Qfa&&(a+=";secure");document.cookie=a;return!0};_.Oy.prototype.clear=function(){this.write("",0)};var Qy=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,Py=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.Oy.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.Ry=function(a){this.Mf=a};_.Ry.prototype.read=function(){if(Sy.hasOwnProperty(this.Mf))return Sy[this.Mf]};_.Ry.prototype.write=function(a){Sy[this.Mf]=a;return!0};_.Ry.prototype.clear=function(){delete Sy[this.Mf]};var Sy={};_.Ry.iterate=function(a){for(var b in Sy)Sy.hasOwnProperty(b)&&a(b,Sy[b])};var Ty=function(){this.La=null;this.key=function(){return null};this.getItem=function(){return this.La};this.setItem=function(a,b){this.La=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.La=null;this.length=0};this.length=0},Uy=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},Vy=function(a,b){this.Zg=a;this.yN=
Uy(b)?b||window.sessionStorage:new Ty};Vy.prototype.read=function(){return this.yN.getItem(this.Zg)};Vy.prototype.write=function(a){try{this.yN.setItem(this.Zg,a)}catch(b){return!1}return!0};Vy.prototype.clear=function(){this.yN.removeItem(this.Zg)};Vy.iterate=function(a){if(Uy())for(var b=window.sessionStorage.length,c=0;c<b;++c){var d=window.sessionStorage.key(c);a(d,window.sessionStorage[d])}};_.My=window.location.protocol==="https:";_.Wy=_.My||window.location.protocol==="http:"?_.Oy:_.Ry;_.Ly=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{ef:a.charAt(0)=="S",domain:d,Si:c}};var Xy,Yy,az,bz;Xy=_.Ce();Yy=_.Ce();_.Zy=_.Ce();_.$y=_.Ce();az="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");bz=function(a){this.QY=a;this.hJ=null};
bz.prototype.write=function(a){var b=_.Ce(),c=_.Ce(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.De(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=az.length;for(e=0;e<d;++e)delete c[az[e]];a=String(a.authuser||0);d=_.Ce();d[a]=c;c=_.Rf(d);this.QY.write(c);this.hJ=b};bz.prototype.read=function(){return this.hJ};bz.prototype.clear=function(){this.QY.clear();this.hJ=_.Ce()};_.cz=function(a){return a?{domain:a.domain,path:"/",secure:a.ef}:null};
Vy.iterate(function(a){var b=_.Ny(a);b&&b.wj&&(Xy[a]=new bz(new Vy(a)))});_.Wy.iterate(function(a){Xy[a]&&(Yy[a]=new _.Wy(a,_.cz(_.Ny(a))))});
_.mi=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+
D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ri:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}};var oi=function(a,b,c){var d=String(_.Xa.location.href);return d&&a&&b?[b,ni(_.Ig(d),a,c||null)].join(" "):null},ni=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.Bb(d,function(k){e.push(k)}),pi(e.join(" "));var f=[],h=[];_.Bb(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.Bb(d,function(k){e.push(k)});a=pi(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},pi=function(a){var b=
_.mi();b.update(a);return b.Ri().toLowerCase()};var ri;_.qi=function(){var a=_.Xa.__SAPISID||_.Xa.__APISID||_.Xa.__3PSAPISID||_.Xa.__1PSAPISID||_.Xa.__OVERRIDE_SID;if(a)return!0;typeof document!=="undefined"&&(a=new _.ji(document),a=a.get("SAPISID")||a.get("APISID")||a.get("__Secure-3PAPISID")||a.get("__Secure-1PAPISID"));return!!a};ri=function(a,b,c,d){(a=_.Xa[a])||typeof document==="undefined"||(a=(new _.ji(document)).get(b));return a?oi(a,c,d):null};
_.si=function(a){var b=_.Ig(String(_.Xa.location.href)),c=[];if(_.qi()){b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==0||b.indexOf("moz-extension:")==0;var d=b?_.Xa.__SAPISID:_.Xa.__APISID;d||typeof document==="undefined"||(d=new _.ji(document),d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID"));(d=d?oi(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d);b&&((b=ri("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),(a=
ri("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a))}return c.length==0?null:c.join(" ")};
var ts,us;_.ls=function(a){if(a instanceof _.gc)return a.kZ;throw Error("j");};_.ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.ns=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.os=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.os.prototype;_.g.clone=function(){return new _.os(this.x,this.y)};_.g.equals=function(a){return a instanceof _.os&&_.ns(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.os?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.ps=function(a){return a.scrollingElement?a.scrollingElement:!_.Cd&&_.he(a)?a.documentElement:a.body||a.documentElement};
_.qs=function(a){var b=_.ps(a);a=a.defaultView;return new _.os(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.rs=function(a,b,c,d){return _.ce(a.Bc,b,c,d)};_.ss=function(a){return _.qs(a.Bc)};ts=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};us=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.vs=function(a){return _.be(document,a)};_.g=_.ms.prototype;_.g.Qb=function(){return this.right-this.left};_.g.Nc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.vb(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var ys,ws,Cs,Es;_.xs=function(a,b,c){if(typeof b==="string")(b=ws(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=ws(c,d);f&&(c.style[f]=e)}};ys={};ws=function(a,b){var c=ys[b];if(!c){var d=ts(b);c=d;a.style[d]===void 0&&(d=(_.Cd?"Webkit":_.Bd?"Moz":null)+us(d),a.style[d]!==void 0&&(c=d));ys[b]=c}return c};_.zs=function(a,b){var c=a.style[ts(b)];return typeof c!=="undefined"?c:a.style[ws(a,b)]||""};
_.As=function(a,b){var c=_.$d(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Bs=function(a,b){return _.As(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Cs=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Fs=function(a,b){b=b||_.ps(document);var c=b||_.ps(document);var d=_.Ds(a),e=_.Ds(c),f=_.As(c,"borderLeftWidth");var h=_.As(c,"borderRightWidth");var k=_.As(c,"borderTopWidth"),l=_.As(c,"borderBottomWidth");h=new _.ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.ps(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Es(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.os(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Ds=function(a){var b=_.$d(a),c=new _.os(0,0);if(a==(b?_.$d(b):document).documentElement)return c;a=Cs(a);b=_.ss(_.ae(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Hs=function(a,b){var c=new _.os(0,0),d=_.ie(_.$d(a));a:{try{_.Wb(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Ds(a):_.Gs(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.Gs=function(a){a=Cs(a);return new _.os(a.left,a.top)};_.Js=function(a,b,c){if(b instanceof _.rd)c=b.height,b=b.width;else if(c==void 0)throw Error("J");a.style.width=_.Is(b,!0);a.style.height=_.Is(c,!0)};_.Is=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.Ks=function(a){var b=Es;if(_.Bs(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Es=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.Cd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Cs(a),new _.rd(a.right-a.left,a.bottom-a.top)):new _.rd(b,c)};_.Ls=function(a,b){a.style.display=b?"":"none"};
_.Ns=function(a){var b=_.ae(void 0),c=_.rs(b,"HEAD")[0];if(!c){var d=_.rs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Gc("style",document))&&d.setAttribute("nonce",e);_.Ms(d,a);b.appendChild(c,d)};_.Ms=function(a,b){b=_.ls(b);_.Xa.trustedTypes?_.ve(a,b):a.innerHTML=b};_.Os=_.Bd?"MozUserSelect":_.Cd||_.zd?"WebkitUserSelect":null;
_.dz=function(a){_.dj.call(this);this.Mf=1;this.YB=[];this.dC=0;this.Sf=[];this.Qj={};this.t7=!!a};_.eb(_.dz,_.dj);_.g=_.dz.prototype;_.g.subscribe=function(a,b,c){var d=this.Qj[a];d||(d=this.Qj[a]=[]);var e=this.Mf;this.Sf[e]=a;this.Sf[e+1]=b;this.Sf[e+2]=c;this.Mf=e+3;d.push(e);return e};_.g.Rw=_.jb(18);_.g.unsubscribe=function(a,b,c){if(a=this.Qj[a]){var d=this.Sf;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.nl(a)}return!1};
_.g.nl=function(a){var b=this.Sf[a];if(b){var c=this.Qj[b];this.dC!=0?(this.YB.push(a),this.Sf[a+1]=function(){}):(c&&_.gj(c,a),delete this.Sf[a],delete this.Sf[a+1],delete this.Sf[a+2])}return!!b};
_.g.Yo=function(a,b){var c=this.Qj[a];if(c){var d=Array(arguments.length-1),e=arguments.length,f;for(f=1;f<e;f++)d[f-1]=arguments[f];if(this.t7)for(f=0;f<c.length;f++)e=c[f],ez(this.Sf[e+1],this.Sf[e+2],d);else{this.dC++;try{for(f=0,e=c.length;f<e&&!this.isDisposed();f++){var h=c[f];this.Sf[h+1].apply(this.Sf[h+2],d)}}finally{if(this.dC--,this.YB.length>0&&this.dC==0)for(;c=this.YB.pop();)this.nl(c)}}return f!=0}return!1};var ez=function(a,b,c){_.vk(function(){a.apply(b,c)})};
_.dz.prototype.clear=function(a){if(a){var b=this.Qj[a];b&&(b.forEach(this.nl,this),delete this.Qj[a])}else this.Sf.length=0,this.Qj={}};_.dz.prototype.Zb=function(a){if(a){var b=this.Qj[a];return b?b.length:0}a=0;for(b in this.Qj)a+=this.Zb(b);return a};_.dz.prototype.ua=function(){_.dz.N.ua.call(this);this.clear();this.YB.length=0};
_.fz=function(a){this.Bha=a};_.gz=function(a){_.dj.call(this);this.je=new _.dz(a);_.fj(this,this.je)};_.fz.prototype.toString=function(){return this.Bha};_.eb(_.gz,_.dj);_.g=_.gz.prototype;_.g.subscribe=function(a,b,c){return this.je.subscribe(a.toString(),b,c)};_.g.Rw=_.jb(17);_.g.unsubscribe=function(a,b,c){return this.je.unsubscribe(a.toString(),b,c)};_.g.nl=function(a){return this.je.nl(a)};_.g.Yo=function(a,b){return this.je.Yo(a.toString(),b)};_.g.clear=function(a){this.je.clear(a!==void 0?a.toString():void 0)};_.g.Zb=function(a){return this.je.Zb(a!==void 0?a.toString():void 0)};
var hz,iz,lz,jz,mz,nz,kz;hz=function(a){var b=_.tc("");return _.ec(a.map(function(c){return _.fc(_.tc(c))}).join(_.fc(b).toString()))};iz=function(a){return hz(a)};lz=function(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!jz.test(e))throw Error("j");if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error("j");kz.indexOf(e.toLowerCase())!==-1&&(f=_.mc(f)?f.toString():_.rc(String(f))||"about:invalid#zClosurez");f=e+'="'+_.tc(String(f))+'"';b+=" "+f}}return b};
_.oz=function(a,b){if(!jz.test("div"))throw Error("j");if(mz.indexOf("DIV")!==-1)throw Error("j");var c="<div";a&&(c+=lz(a));Array.isArray(b)||(b=b===void 0?[]:[b]);nz.indexOf("DIV")!==-1?c+=">":(a=iz(b.map(function(d){return d instanceof _.dc?d:_.tc(String(d))})),c+=">"+a.toString()+"</div>");return _.ec(c)};jz=/^[a-z][a-z\d-]*$/i;mz="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");nz="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" ");
kz=["action","formaction","href"];_.pz=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.Xo+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.xs(a,"transition",b.join(","))};_.qz=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=_.me("DIV"),b=_.Cd?"-webkit":_.Bd?"-moz":null,c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");_.Hc(a,_.oz({style:c}));return _.zs(a.firstChild,"transition")!=""});
_.rz=function(a,b){_.Oj.call(this);this.Am=a||1;this.ex=b||_.Xa;this.jQ=(0,_.z)(this.yha,this);this.TW=_.ld()};_.eb(_.rz,_.Oj);_.g=_.rz.prototype;_.g.enabled=!1;_.g.Hc=null;_.g.setInterval=function(a){this.Am=a;this.Hc&&this.enabled?(this.stop(),this.start()):this.Hc&&this.stop()};
_.g.yha=function(){if(this.enabled){var a=_.ld()-this.TW;a>0&&a<this.Am*.8?this.Hc=this.ex.setTimeout(this.jQ,this.Am-a):(this.Hc&&(this.ex.clearTimeout(this.Hc),this.Hc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Hc||(this.Hc=this.ex.setTimeout(this.jQ,this.Am),this.TW=_.ld())};_.g.stop=function(){this.enabled=!1;this.Hc&&(this.ex.clearTimeout(this.Hc),this.Hc=null)};_.g.ua=function(){_.rz.N.ua.call(this);this.stop();delete this.ex};
_.sz=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("wa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.tz=function(a){_.Xa.clearTimeout(a)};
_.vz=function(){_.uz="oauth2relay"+String(2147483647*(0,_.Qg)()|0)};_.wz=new _.gz;_.xz=new _.fz("oauth");_.vz();_.Xe("oauth-flow/client_id");var yz=String(_.Xe("oauth-flow/redirectUri"));if(yz)yz.replace(/[#][\s\S]*/,"");else{var zz=_.Gg.getOrigin(window.location.href);_.Xe("oauth-flow/callbackUrl");encodeURIComponent(zz)}_.Gg.getOrigin(window.location.href);
var Bz,Cz,Dz,Ez,Fz,Gz,Hz,Iz,Jz,Kz,Lz,Nz,Oz,Pz,Qz,Rz,Sz,Yz,Zz,$z,aA,bA,cA,dA,eA,fA,gA,hA,iA,jA,kA,lA,mA,nA,oA,pA,qA,rA,sA,tA,uA,xA,wA,yA,zA,AA,BA,CA,DA,EA;_.Az=function(a,b){if(_.Lh&&!b)return _.Xa.atob(a);var c="";_.Oh(a,function(d){c+=String.fromCharCode(d)});return c};Bz=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Cz=function(a){return _.ei("enableMultilogin")&&a("cookie_policy")&&!Bz(a)?!0:!1};
Fz=function(){var a,b=null;_.Wy.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Ly(c),!a||c.ef&&!a.ef||c.ef==a.ef&&c.Si>a.Si)&&(a=c,b=d)});return{G7:a,authuser:b}};Gz=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Hz=function(a){a=a.toUpperCase();for(var b=Gz.length,c=0;c<b;++c){var d=a.split(Gz[c]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Pg,b.tx(a),a=b.Ri().toUpperCase());return a};
Iz=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Jz=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],iga:Iz(a[1]),Jra:Iz(a[2]),Cqa:Iz(a[3])}};Kz=function(a){var b=Fz(),c=b.G7;b=b.authuser;var d=a&&Hz(a);if(b!==null){var e;_.Wy.iterate(function(h,k){(h=_.Ny(h))&&h.wj&&(d&&h.e8!=d||h.ef==c.ef&&h.Si==c.Si&&(e=k))});if(e){var f=Jz(e);a=f&&f.iga[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,Hsa:a,clientId:f}}}return null};
Lz=function(a,b){a=_.ui(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Mz=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.Hw(c,d):_.Iw(d)};
Nz=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Ig(a));var b=window.location.hostname,c=b,d=_.My;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Vf.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Vf.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,ef:d,Si:b}};
Oz=function(a){var b=Nz(a);if(!b)return new _.Ry("G_USERSTATE_");a=["G_USERSTATE_",_.My&&b.ef?"S":"H",b.Si].join("");var c=_.$y[a];c||(c={fJ:63072E3},_.Ee(_.cz(b),c),c=new _.Oy(a,c),_.$y[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};Pz=function(a){var b=Oz(a).read();a=_.Ce();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
Qz=function(a,b,c){var d=Pz(b),e=d[a];d[a]="0";var f=[];_.Qm(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=Oz(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};Rz=function(a,b){b=Pz(b);return b[a]=="0"||b[a]=="X"};Sz=function(a){a=Nz(a.g_user_cookie_policy);if(!a||a.ef&&!_.My)a=null;else{var b=["G_AUTHUSER_",_.My&&a.ef?"S":"H",a.Si].join(""),c=_.Zy[b];c||(c=new _.Wy(b,_.cz(a)),_.Zy[b]=c);a=c}_.Ye("googleapis.config/sessionIndex",null);a.clear()};Yz=function(a){return Bz(function(b){return a[b]})};
Zz=0;$z=!1;aA=[];bA={};cA={};dA=null;eA=function(a){var b=_.uz;return function(c){if(this.f==b&&this.t==_.$f.Nn(this.f)&&this.origin==_.$f.Zn(this.f))return a.apply(this,arguments)}};fA=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("ya");};gA=function(a){var b=_.af.Qg,c=b(a).jsh;if(c!=null)return fA(c),a;if(b=String(b().jsh||_.Me.h||""))fA(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};
hA=function(){return!!_.Xe("oauth-flow/usegapi")};iA=function(a,b){hA()?dA.unregister(a):_.$f.unregister(a+":"+b)};jA=function(a,b,c){hA()?dA.register(a,c,_.dn):_.$f.register(a+":"+b,eA(c))};kA=function(){Dz.parentNode.removeChild(Dz)};
lA=function(a){var b=Dz;_.pz(b,[{Xo:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.pz(b,[{Xo:"transform",duration:1,timing:"ease",delay:0}]);_.sz(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};mA=function(){var a=Ez+88;lA(a);Ez=a};nA=function(){var a=Ez-88;lA(a);Ez=a};
oA=function(a){var b=a?mA:nA,c=a?nA:mA;a=a?"-":"";Ez=parseInt(a+88,10);Dz.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Dz.style.transform="translate3d(0px,"+a+88+"px,0px)";Dz.style.display="";Dz.style.visibility="visible";b();_.sz(c,4E3);_.sz(kA,5E3)};
pA=function(a){var b=_.Xe("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Dz=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.xs(c,"visibility","hidden");_.xs(c,b,"-40px");_.xs(c,"height","128px");var d=c;if(_.Kr()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Ez=parseInt(e+88,10);Dz.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Dz.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.an.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?oA(!0):oA(!1)}})};
qA=function(a){var b=_.Po(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.Om.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
rA=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Xe("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Kz(c)!=null||!Yz(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};sA=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Qf(_.Az(a,!0));if(a===!1)throw Error("za");return a};tA=function(a){return(a=sA(a))?a.sub:null};
uA=function(a){a&&aA.push(a);a=_.uz;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(Zz&&c-Zz<6E4)return;var d=_.$f.Nn(a);d&&(iA("oauth2relayReady",d),iA("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.vz();a=_.uz}Zz=c;var e=String(2147483647*(0,_.Qg)()|0);b=_.Xe("oauth-flow/proxyUrl")||_.Xe("oauth-flow/relayUrl");hA()?dA=_.an.openChild({where:_.af.BT(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Gg.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.af.BT(),d=_.af.XQ({name:a,id:a}),d.src=gA(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.$f.Hw(a));jA("oauth2relayReady",e,function(){iA("oauth2relayReady",e);var f=aA;if(f!==null){aA=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});jA("oauth2callback",e,function(f){var h=_.af.Qg;h=h(f);var k=h.state;f=k;f=f.replace(/\|.*$/,"");f={}.hasOwnProperty.call(cA,f)?cA[f]:null;h.state=f;if(h.state!=null){f=bA[k];delete bA[k];k=f&&f.key||"token";var l=h=rA(f&&f.params,h);var m=(m=tA(l))?Rz(m,l.cookie_policy):!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Xe("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",$z||($z=!0,pA(l)));_.Mz(k,h);h=Lz(k);if(f){k=
f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(n){}f.callback&&(f.callback(h),f.callback=null)}}})};_.vA=function(a){aA!==null?uA(a):a&&a()};xA=function(a,b){var c=wA,d=tA(a);d&&(Sz(a),Qz(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=rA(null,e);c(e)}}))};
wA=function(a){a||(a=Lz(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.Me.drw=null);_.Mz(a);if(b=a.authuser)_.Xe("googleapis.config/sessionIndex"),_.Ye("googleapis.config/sessionIndex",b);_.wz.Yo(_.xz,a);return a};yA=["client_id","cookie_policy","response_type"];zA="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
AA=function(a){var b=_.fk(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Hw(b);return b};
BA=function(a){if(a.include_granted_scopes===void 0){var b=_.Xe("include_granted_scopes");a.include_granted_scopes=!!b}};CA=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
DA=function(a){var b=a||{},c={};_.Bb(zA,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Xe("googleapis/overrideClientId");a!=null&&(c.client_id=a);BA(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Hx(c.cookie_policy)}catch(d){c.cookie_policy&&
CA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.ii(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Xe("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
EA=function(a,b){var c=DA(a),d=new _.xk(function(e,f){_.ny(c,function(h){var k=h||{};_.Bb(yA,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=AA(k),h.authuser!=null&&_.Ye("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};var FA,HA;FA=null;_.IA=function(a,b){if(a.approvalprompt!=="force"){a=_.GA(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)FA?(a.client_id!==FA.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(FA=a,b=!1);b||HA(a)}};
_.GA=function(a){var b=a.redirecturi||"postmessage",c=_.zc((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=_.zc(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Xe("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Xe("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};HA=function(a){_.Bp("waaf0","signin","0");EA(a,function(b){_.Bp("waaf1","signin","0");wA(b)})};
_.JA=function(a){a=_.GA(a);_.Ye("oauth-flow/authWindowWidth",445);_.Ye("oauth-flow/authWindowHeight",615);HA(a)};_.KA=function(a){_.wz.unsubscribe(_.xz,a);_.wz.subscribe(_.xz,a)};var RA,UA;_.MA=function(a){return a.cookiepolicy?!0:(_.LA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.LA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.QA=function(a,b){var c=_.Po();_.Ee(a,c);c=qA(c);if(_.MA(c)){var d=_.NA();_.OA(c);b?_.Le(b,"click",function(){_.PA(c,d)}):_.PA(c,d)}};
_.NA=function(){var a=new RA;_.KA(function(b){a.gJ&&b&&(b.access_token&&_.Ye("isPlusUser",!0),b["g-oauth-window"]&&(a.gJ=!1,_.Vf.warn("OTA app install is no longer supported.")))});return a};RA=function(){this.gJ=!1};_.OA=function(a){a=_.SA(a);_.TA(a.callback);_.vA(function(){_.IA(a)})};_.SA=function(a){UA(a);a.redirecturi&&delete a.redirecturi;Cz(function(b){return a[b]})||(a.authuser=0);return a};UA=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.TA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.LA('Callback function named "'+a+'" not found');return}a&&_.KA(a)};_.PA=function(a,b){b.gJ=!0;a=_.SA(a);_.JA(a)};_.t("gapi.auth.authorize",EA);_.t("gapi.auth.checkSessionState",function(a,b){var c=_.Ce();c.client_id=a.client_id;c.session_state=a.session_state;_.vA(function(){hA()?dA.send("check_session_state",c,function(d){b.call(null,d[0])},_.dn):_.$f.call(_.uz,"check_session_state",eA(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.t("gapi.auth.getAuthHeaderValueForFirstParty",function(a,b){_.Hi(_.Gi(),51).rb();return _.si(a,b)});_.t("gapi.auth.getToken",Lz);
_.t("gapi.auth.getVersionInfo",function(a,b){_.vA(function(){var c=_.si()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?hA()?dA.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.dn):_.$f.call(_.uz,"get_versioninfo",eA(function(f){a(f)}),d,b):a()})});_.t("gapi.auth.init",_.vA);_.t("gapi.auth.setToken",_.Mz);_.t("gapi.auth.signIn",function(a){_.QA(a)});_.t("gapi.auth.signOut",function(){var a=Lz();a&&xA(a,a.cookie_policy)});
_.t("gapi.auth.unsafeUnpackIdToken",sA);_.t("gapi.auth._pimf",_.IA);_.t("gapi.auth._oart",pA);_.t("gapi.auth._guss",function(a){return Oz(a).read()});
var VA=_.Po();VA.clientid&&VA.scope&&VA.callback&&!_.Xe("disableRealtimeCallback")&&_.OA(VA);
var sy=function(){};var uy;uy=function(){};_.eb(uy,sy);uy.prototype.Sy=function(){return new XMLHttpRequest};_.ty=new uy;
_.Lg=window.googleapis&&window.googleapis.server||{};
var Rg=function(a){return{execute:function(b){var c={method:a.httpMethod||"GET",root:a.root,path:a.url,params:a.urlParams,headers:a.headers,body:a.body},d=window.gapi,e=function(){var f=d.config.get("client/apiKey"),h=d.config.get("client/version");try{var k=d.config.get("googleapis.config/developerKey"),l=d.config.get("client/apiKey",k);d.config.update("client/apiKey",l);d.config.update("client/version","1.0.0-alpha");var m=d.client;m.request.call(m,c).then(b,b)}finally{d.config.update("client/apiKey",
f),d.config.update("client/version",h)}};d.client?e():d.load.call(d,"client",e)}}},Sg=function(a,b){return function(c){var d={};c=c.body;var e=_.Qf(c),f={};if(e&&e.length)for(var h=e.length,k=0;k<h;++k){var l=e[k];f[l.id]=l}h=b.length;for(k=0;k<h;++k)l=b[k].id,d[l]=e&&e.length?f[l]:e;a(d,c)}},Tg=function(a){a.transport={name:"googleapis",execute:function(b,c){for(var d=[],e=b.length,f=0;f<e;++f){var h=b[f],k=h.method,l=String(k).split(".")[0];l=_.Xe("googleapis.config/versions/"+k)||_.Xe("googleapis.config/versions/"+
l)||"v1";d.push({jsonrpc:"2.0",id:h.id,method:k,apiVersion:String(l),params:h.params})}b=Rg({httpMethod:"POST",root:a.transport.root,url:"/rpc?pp=0",headers:{"Content-Type":"application/json"},body:d});b.execute.call(b,Sg(c,d))},root:void 0}},Ug=function(a){var b=this.method,c=this.transport;c.execute.call(c,[{method:b,id:b,params:this.rpc}],function(d){d=d[b];d.error||(d=d.data||d.result);a(d)})},Wg=function(){for(var a=Vg,b=a.split("."),c=function(k){k=k||{};k.groupId=k.groupId||"@self";k.userId=
k.userId||"@viewer";k={method:a,rpc:k||{}};Tg(k);k.execute=Ug;return k},d=_.Xa,e=b.length,f=0;f<e;++f){var h=d[b[f]]||{};f+1==e&&(h=c);d=d[b[f]]=h}if(b.length>1&&b[0]!="googleapis")for(b[0]="googleapis",b[b.length-1]=="delete"&&(b[b.length-1]="remove"),d=_.Xa,e=b.length,f=0;f<e;++f)h=d[b[f]]||{},f+1==e&&(h=c),d=d[b[f]]=h},Vg;for(Vg in _.Xe("googleapis.config/methods"))Wg();_.t("googleapis.newHttpRequest",function(a){return Rg(a)});_.t("googleapis.setUrlParameter",function(a,b){if(a!=="trace")throw Error("u");_.Ye("client/trace",b)});
_.Ph=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Qh=function(a){return a==null?"":String(a)};_.Rh=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Sh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Th=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Uh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Uh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Vh=function(a){var b=[],c;for(c in a)_.Uh(c,a[c],b);return b.join("&")};
_.Wh=function(a,b){b=_.Vh(b);return _.Th(a,b)};
var Ui,Vi;_.Ti=function(a){var b={SAPISIDHASH:!0,SAPISID3PHASH:!0,SAPISID1PHASH:!0,APISIDHASH:!0};return a&&(a.OriginToken||a.Authorization&&b[String(a.Authorization).split(" ")[0]])?!0:!1};Ui={cV:_.Ti,yca:_.qi,dU:function(){var a=null;_.qi()&&(a=window.__PVT,a==null&&(a=(new _.ji(document)).get("BEAT")));return a},R9:_.si};
Vi=function(a,b){a=_.af.XQ({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=c.indexOf("Opera")!=0&&c.indexOf("WebKit")==-1&&d=="Gecko"&&c.indexOf("rv:1.")>0}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;typeof a.setAttribute==="function"?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";document.body.appendChild(a);
c&&(a.src=b);return a};Ui={cV:_.Ti,yca:_.qi,dU:function(){var a=null;_.qi()&&(a=window.__PVT,a==null&&(a=(new _.ji(document)).get("BEAT")));return a},R9:_.si};var Xi,Wi;Xi=function(){return!!Wi("auth/useFirstPartyAuthV2")};Wi=function(a){return _.Xe("googleapis.config/"+a)};
_.Yi=function(a,b,c){a=a===void 0?{}:a;b=b===void 0?window.location.href:b;c=c===void 0?"auto":c;if(c=="none")return a;var d=a.Authorization,e=a.OriginToken;if(!d&&!e){(e=_.ui())&&e.access_token&&(c=="oauth2"||c=="auto")&&(d=String(e.token_type||"Bearer")+" "+e.access_token);if(e=!d)e=(!!Wi("auth/useFirstPartyAuth")||c=="1p")&&c!="oauth2";if(e&&_.qi()){if(Xi()){d=Wi("primaryEmail");c=Wi("appDomain");e=Wi("fogId");var f=[];d&&f.push({key:"e",value:d});c&&f.push({key:"a",value:c});e&&f.push({key:"u",
value:e});d=_.si(f)}else d=_.si();d&&(c=a["X-Goog-AuthUser"],b=_.ii(b),b=c||b,_.xc(_.Qh(b))&&(!Xi()||Xi()&&_.xc(_.Qh(Wi("primaryEmail")))&&_.xc(_.Qh(Wi("appDomain")))&&_.xc(_.Qh(Wi("fogId"))))&&(b="0"),_.xc(_.Qh(b))||(a["X-Goog-AuthUser"]=b))}d?a.Authorization=d:Wi("auth/useOriginToken")!==!1&&(e=Ui.dU())&&(a.OriginToken=e)}return a};_.Zi=function(){function a(n,p,q,r,w){var u=f("proxy");if(r||!u){u=f("root");var x=f("root-1p")||u;u=u||"https://content.googleapis.com";x=x||"https://clients6.google.com";var A=f("xd3")||"/static/proxy.html";u=(r||String(p?x:u))+A}u=String(u);q&&(u+=(u.indexOf("?")>=0?"&":"?")+"usegapi=1");(p=_.af.Qg().jsh||_.Me.h)&&(u+=(u.indexOf("?")>=0?"&":"?")+"jsh="+encodeURIComponent(p));u+="#parent="+encodeURIComponent(w!=null?String(w):_.Gg.getOrigin(document.location.href));return u+("&rpctoken="+n)}function b(n,
p,q,r,w){var u=d(q,r,w);k[u]||(q=Vi(u,p),_.$f.register("ready:"+n,function(){_.$f.unregister("ready:"+n);if(!l[u]){l[u]=!0;var x=m[u];m[u]=[];for(var A=0,D=x.length;A<D;++A){var E=x[A];e(E.hp,E.hfa,E.callback)}}}),_.$f.Hw(u,p),k[u]=q)}function c(n,p,q){var r=String(2147483647*_.Pi()|0),w=a(r,n,p,q);_.Xf(function(){b(r,w,n,p,q)})}function d(n,p,q){n=a("",n,p,q,"");q=h[n+p];if(!q){q=new _.Pg;q.tx(n);q=q.Ri().toLowerCase();var r=_.Pi();q+=r;h[n+p]=q}return"apiproxy"+q}function e(n,p,q){var r=void 0,
w=!1;if(n!=="makeHttpRequests")throw'only "makeHttpRequests" RPCs are implemented';var u=function(N){if(N){if(typeof r!="undefined"&&typeof N.root!="undefined"&&r!=N.root)throw"all requests in a batch must have the same root URL";r=N.root||r;w=Ui.cV(N.headers)}};if(p)for(var x=0,A=p.length;x<A;++x){var D=p[x];D&&u(D.params)}u=!!f("useGapiForXd3");var E=d(w,u,r);k[E]||c(w,u,r);l[E]?_.$f.call(E,n,function(N){if(this.f==E&&this.t==_.$f.Nn(this.f)&&this.origin==_.$f.Zn(this.f)){var H=_.Qf(N);q(H,N)}},
p):(m[E]||(m[E]=[]),m[E].push({hp:n,hfa:p,callback:q}))}function f(n){return _.Xe("googleapis.config/"+n)}var h={},k={},l={},m={};return{Qpa:function(n,p,q){return _.Yi(n,p,q)},Vm:e}}();
var Xg={uia:"Authorization",j3:"Content-ID",Tia:"Content-Transfer-Encoding",Uia:"Content-Type",Aja:"Date",lma:"OriginToken",Nka:"hotrod-board-name",Oka:"hotrod-chrome-cpu-model",Pka:"hotrod-chrome-processors",Coa:"User-Agent",Uoa:"WWW-Authenticate",Woa:"X-Ad-Manager-Impersonation",Voa:"X-Ad-Manager-Debug-Info",Yoa:"X-ClientDetails",Zoa:"X-Cloudaicompanion-Trace-Id",apa:"X-Compass-Routing-Destination",dpa:"X-Goog-AuthUser",ipa:"X-Goog-Encode-Response-If-Executable",bpa:"X-Google-Consent",cpa:"X-Google-EOM",
kpa:"X-Goog-Meeting-ABR",lpa:"X-Goog-Meeting-Botguardid",mpa:"X-Goog-Meeting-Bot-Info",npa:"X-Goog-Meeting-ClientInfo",opa:"X-Goog-Meeting-ClientVersion",ppa:"X-Goog-Meeting-Debugid",qpa:"X-Goog-Meeting-Identifier",rpa:"X-Goog-Meeting-Interop-Cohorts",spa:"X-Goog-Meeting-Interop-Type",tpa:"X-Goog-Meeting-OidcIdToken",upa:"X-Goog-Meeting-RtcClient",vpa:"X-Goog-Meeting-StartSource",wpa:"X-Goog-Meeting-Token",xpa:"X-Goog-Meeting-Viewer-Token",ypa:"X-Goog-PageId",Apa:"X-Goog-Safety-Content-Type",Bpa:"X-Goog-Safety-Encoding",
fpa:"X-Goog-Drive-Client-Version",gpa:"X-Goog-Drive-Resource-Keys",Cpa:"X-HTTP-Method-Override",Dpa:"X-JavaScript-User-Agent",Epa:"X-Origin",Fpa:"X-Referer",Gpa:"X-Requested-With",Jpa:"X-Use-HTTP-Status-Code-Override",Hpa:"X-Server-Timeout",jpa:"X-Goog-First-Party-Reauth",Ipa:"X-Server-Token",epa:"x-goog-chat-space-id",zpa:"x-goog-pan-request-context",Xoa:"X-AppInt-Credentials",hpa:"X-Goog-Earth-Gcp-Project"},Yg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding User-Agent Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-223261916-bin x-goog-ext-*********-bin x-goog-ext-233818517-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project".split(" "),
Zg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination x-goog-ext-*********-bin x-goog-ext-*********-bin".split(" ");var $g,ah,bh,ch,eh,fh,gh,hh,ih,jh,kh,lh;$g=null;ah=null;bh=null;ch=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.dh=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};eh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
fh={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
gh=function(a){if(!_.kd(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();ch(d,e)&&(b[e]=d)}}for(var f in Xg)Object.prototype.hasOwnProperty.call(Xg,f)&&(a=Xg[f],c=a.toLowerCase(),ch(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};hh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");ih=/[ \t]*(\r?\n[ \t]+)+/g;jh=/^[ \t]+|[ \t]+$/g;
kh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=kh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(ih," "),a=a.replace(jh,""),a.replace(hh,"")==""&&a))return a};lh=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.mh=function(a){if(typeof a!=="string"||!a||!a.match(lh))return null;a=a.toLowerCase();if(bh==null){var b=[],c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);(c=_.Xe("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);for(var d in Xg)Object.prototype.hasOwnProperty.call(Xg,d)&&b.push(Xg[d]);bh=gh(b)}return bh!=null&&bh.hasOwnProperty(a)?bh[a]:a};
_.nh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||fh[a])return null;if($g==null){b=[];var c=_.Xe("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);$g=gh(b)}return $g!=null&&$g.hasOwnProperty(a)?$g[a]:null};
_.oh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(eh[a])return null;if(ah==null){b=[];var c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);ah=gh(b)}return ah!=null&&ah.hasOwnProperty(a)?a:null};
_.ph=function(a,b){if(_.mh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&ch(d,b)){var e=kh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.qh=function(a,b,c,d){var e=_.mh(b);if(e){c&&(c=kh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&ch(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.rh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.mh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=kh(f))if(k=_.oh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.ph(c,k),h!==void 0&&(f=h+", "+f),_.qh(c,k,f,!0)}}}return c};
/\uffff/.test("\uffff");
var wy;_.vy=function(a){var b=0,c;for(c in a)b++;return b};wy=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.Uh(a[b],a[b+1],c);return c.join("&")};_.xy=function(a,b){var c=arguments.length==2?wy(arguments[1],0):wy(arguments,1);return _.Th(a,c)};_.yy=function(a,b){_.Mj(a,"/")&&(a=a.slice(0,-1));_.wc(b,"/")&&(b=b.slice(1));return a+"/"+b};_.zy=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};var By,Cy,Dy;_.Ay=function(a){_.Oj.call(this);this.headers=new Map;this.H2=a||null;this.Wf=!1;this.Va=null;this.kB="";this.ir=0;this.zo=this.hI=this.LA=this.gG=!1;this.Es=0;this.Qc=null;this.Qm="";this.wh=!1;this.SE=this.JN=null};_.eb(_.Ay,_.Oj);_.Ay.prototype.Ab=null;By=/^https?$/i;Cy=["POST","PUT"];Dy=[];_.Ey=function(a,b,c,d,e,f,h){var k=new _.Ay;Dy.push(k);b&&k.na("complete",b);k.nr("ready",k.Z7);f&&k.pD(f);h&&(k.wh=h);k.send(a,c,d,e)};_.g=_.Ay.prototype;
_.g.Z7=function(){this.dispose();_.gj(Dy,this)};_.g.pD=function(a){this.Es=Math.max(0,a)};_.g.setTrustToken=function(a){this.JN=a};_.g.setAttributionReporting=function(a){this.SE=a};
_.g.send=function(a,b,c,d){if(this.Va)throw Error("ta`"+this.kB+"`"+a);b=b?b.toUpperCase():"GET";this.kB=a;this.ir=0;this.gG=!1;this.Wf=!0;this.Va=this.H2?this.H2.Sy():_.ty.Sy();this.Va.onreadystatechange=(0,_.ok)((0,_.z)(this.jY,this));try{this.hI=!0,this.Va.open(b,String(a),!0),this.hI=!1}catch(h){this.pz(5,h);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=_.Aa(d.keys());
for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("ua`"+String(d));d=Array.from(c.keys()).find(function(h){return"content-type"==h.toLowerCase()});e=_.Xa.FormData&&a instanceof _.Xa.FormData;!_.tb(Cy,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.Aa(c);for(d=b.next();!d.done;d=b.next())c=_.Aa(d.value),d=c.next().value,c=c.next().value,this.Va.setRequestHeader(d,c);this.Qm&&(this.Va.responseType=this.Qm);"withCredentials"in this.Va&&
this.Va.withCredentials!==this.wh&&(this.Va.withCredentials=this.wh);if("setTrustToken"in this.Va&&this.JN)try{this.Va.setTrustToken(this.JN)}catch(h){}if("setAttributionReporting"in this.Va&&this.SE)try{this.Va.setAttributionReporting(this.SE)}catch(h){}try{this.Qc&&(clearTimeout(this.Qc),this.Qc=null),this.Es>0&&(this.Qc=setTimeout(this.Hi.bind(this),this.Es)),this.LA=!0,this.Va.send(a),this.LA=!1}catch(h){this.pz(5,h)}};
_.g.Hi=function(){typeof _.Va!="undefined"&&this.Va&&(this.ir=8,this.dispatchEvent("timeout"),this.abort(8))};_.g.pz=function(a){this.Wf=!1;this.Va&&(this.zo=!0,this.Va.abort(),this.zo=!1);this.ir=a;Fy(this);Gy(this)};var Fy=function(a){a.gG||(a.gG=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.Ay.prototype.abort=function(a){this.Va&&this.Wf&&(this.Wf=!1,this.zo=!0,this.Va.abort(),this.zo=!1,this.ir=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Gy(this))};
_.Ay.prototype.ua=function(){this.Va&&(this.Wf&&(this.Wf=!1,this.zo=!0,this.Va.abort(),this.zo=!1),Gy(this,!0));_.Ay.N.ua.call(this)};_.Ay.prototype.jY=function(){this.isDisposed()||(this.hI||this.LA||this.zo?Hy(this):this.KJ())};_.Ay.prototype.KJ=function(){Hy(this)};
var Hy=function(a){if(a.Wf&&typeof _.Va!="undefined")if(a.LA&&_.Iy(a)==4)setTimeout(a.jY.bind(a),0);else if(a.dispatchEvent("readystatechange"),_.Iy(a)==4){a.Wf=!1;try{a.hr()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.ir=6,a.getStatus(),Fy(a))}finally{Gy(a)}}},Gy=function(a,b){if(a.Va){a.Qc&&(clearTimeout(a.Qc),a.Qc=null);var c=a.Va;a.Va=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};_.Ay.prototype.isActive=function(){return!!this.Va};
_.Ay.prototype.hr=function(){var a=this.getStatus(),b;if(!(b=_.zy(a))){if(a=a===0)a=String(this.kB).match(_.Sh)[1]||null,!a&&_.Xa.self&&_.Xa.self.location&&(a=_.Xa.self.location.protocol.slice(0,-1)),a=!By.test(a?a.toLowerCase():"");b=a}return b};_.Iy=function(a){return a.Va?a.Va.readyState:0};_.Ay.prototype.getStatus=function(){try{return _.Iy(this)>2?this.Va.status:-1}catch(a){return-1}};_.Jy=function(a){try{return a.Va?a.Va.responseText:""}catch(b){return""}};
_.Ky=function(a){try{if(!a.Va)return null;if("response"in a.Va)return a.Va.response;switch(a.Qm){case "":case "text":return a.Va.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.Va)return a.Va.mozResponseArrayBuffer}return null}catch(b){return null}};_.Ay.prototype.getResponseHeader=function(a){if(this.Va&&_.Iy(this)==4)return a=this.Va.getResponseHeader(a),a===null?void 0:a};
_.Ay.prototype.getAllResponseHeaders=function(){return this.Va&&_.Iy(this)>=2?this.Va.getAllResponseHeaders()||"":""};_.mj(function(a){_.Ay.prototype.KJ=a(_.Ay.prototype.KJ)});
var vu,Au;_.ru=function(a,b){var c=_.kd(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.su=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.su(a[c]);return b};
_.tu=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.ld()).toString(36)};_.uu=function(a,b,c){return _.le(document,arguments)};vu=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.wu=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.xu=/#|$/;_.yu=function(a){if(a.Xe&&typeof a.Xe=="function")return a.Xe();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.kd(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.ub(a)};
_.zu=function(a){if(a.kg&&typeof a.kg=="function")return a.kg();if(!a.Xe||typeof a.Xe!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.kd(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Ph(a)}}};
Au=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.kd(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.zu(a),e=_.yu(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};var Ou,Iu,Su,Ku,Ju,Mu,Lu,Pu,Nu,Tu;
_.Bu=function(a,b){this.Wd=this.uh=this.Bi="";this.Ag=null;this.BG=this.Km="";this.Sg=!1;var c;a instanceof _.Bu?(this.Sg=b!==void 0?b:a.Sg,_.Cu(this,a.Bi),_.Du(this,a.uh),_.Eu(this,a.Mg()),_.Fu(this,a.Ag),this.setPath(a.getPath()),_.Gu(this,a.Pd.clone()),this.Tk(a.Qz())):a&&(c=String(a).match(_.Sh))?(this.Sg=!!b,_.Cu(this,c[1]||"",!0),_.Du(this,c[2]||"",!0),_.Eu(this,c[3]||"",!0),_.Fu(this,c[4]),this.setPath(c[5]||"",!0),_.Gu(this,c[6]||"",!0),this.Tk(c[7]||"",!0)):(this.Sg=!!b,this.Pd=new _.Hu(null,
this.Sg))};_.Bu.prototype.toString=function(){var a=[],b=this.Bi;b&&a.push(Iu(b,Ju,!0),":");var c=this.Mg();if(c||b=="file")a.push("//"),(b=this.uh)&&a.push(Iu(b,Ju,!0),"@"),a.push(Ku(encodeURIComponent(String(c)))),c=this.Ag,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Wd&&c.charAt(0)!="/"&&a.push("/"),a.push(Iu(c,c.charAt(0)=="/"?Lu:Mu,!0));(c=this.Pd.toString())&&a.push("?",c);(c=this.Qz())&&a.push("#",Iu(c,Nu));return a.join("")};
_.Bu.prototype.resolve=function(a){var b=this.clone(),c=!!a.Bi;c?_.Cu(b,a.Bi):c=!!a.uh;c?_.Du(b,a.uh):c=!!a.Wd;c?_.Eu(b,a.Mg()):c=a.Ag!=null;var d=a.getPath();if(c)_.Fu(b,a.Ag);else if(c=!!a.Km){if(d.charAt(0)!="/")if(this.Wd&&!this.Km)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.yc(e,"./")||_.yc(e,"/.")){d=_.wc(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.Sq();c?_.Gu(b,a.Pd.clone()):c=!!a.BG;c&&b.Tk(a.Qz());return b};_.Bu.prototype.clone=function(){return new _.Bu(this)};_.Cu=function(a,b,c){a.Bi=c?Ou(b,!0):b;a.Bi&&(a.Bi=a.Bi.replace(/:$/,""));return a};_.Du=function(a,b,c){a.uh=c?Ou(b):b;return a};_.Bu.prototype.Mg=function(){return this.Wd};_.Eu=function(a,b,c){a.Wd=c?Ou(b,!0):b;return a};
_.Fu=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("L`"+b);a.Ag=b}else a.Ag=null;return a};_.Bu.prototype.getPath=function(){return this.Km};_.Bu.prototype.setPath=function(a,b){this.Km=b?Ou(a,!0):a;return this};_.Bu.prototype.Sq=function(){return this.Pd.toString()!==""};_.Gu=function(a,b,c){b instanceof _.Hu?(a.Pd=b,a.Pd.SL(a.Sg)):(c||(b=Iu(b,Pu)),a.Pd=new _.Hu(b,a.Sg));return a};_.Bu.prototype.hb=function(a,b){return _.Gu(this,a,b)};_.Bu.prototype.getQuery=function(){return this.Pd.toString()};
_.Qu=function(a,b,c){a.Pd.set(b,c);return a};_.g=_.Bu.prototype;_.g.Og=function(a){return this.Pd.get(a)};_.g.Qz=function(){return this.BG};_.g.Tk=function(a,b){this.BG=b?Ou(a):a;return this};_.g.removeParameter=function(a){this.Pd.remove(a);return this};_.g.SL=function(a){this.Sg=a;this.Pd&&this.Pd.SL(a)};_.Ru=function(a,b){return a instanceof _.Bu?a.clone():new _.Bu(a,b)};Ou=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
Iu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Su),c&&(a=Ku(a)),a):null};Su=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Ku=function(a){return a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")};Ju=/[#\/\?@]/g;Mu=/[#\?:]/g;Lu=/[#\?]/g;Pu=/[#\?@]/g;Nu=/#/g;_.Hu=function(a,b){this.ze=this.Lc=null;this.ig=a||null;this.Sg=!!b};Tu=function(a){a.Lc||(a.Lc=new Map,a.ze=0,a.ig&&vu(a.ig,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};
_.g=_.Hu.prototype;_.g.Zb=function(){Tu(this);return this.ze};_.g.add=function(a,b){Tu(this);this.ig=null;a=Uu(this,a);var c=this.Lc.get(a);c||this.Lc.set(a,c=[]);c.push(b);this.ze+=1;return this};_.g.remove=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)?(this.ig=null,this.ze-=this.Lc.get(a).length,this.Lc.delete(a)):!1};_.g.clear=function(){this.Lc=this.ig=null;this.ze=0};_.g.isEmpty=function(){Tu(this);return this.ze==0};_.g.El=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)};
_.g.forEach=function(a,b){Tu(this);this.Lc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.kg=function(){Tu(this);for(var a=Array.from(this.Lc.values()),b=Array.from(this.Lc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Xe=function(a){Tu(this);var b=[];if(typeof a==="string")this.El(a)&&(b=b.concat(this.Lc.get(Uu(this,a))));else{a=Array.from(this.Lc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.g.set=function(a,b){Tu(this);this.ig=null;a=Uu(this,a);this.El(a)&&(this.ze-=this.Lc.get(a).length);this.Lc.set(a,[b]);this.ze+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Xe(a);return a.length>0?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.ig=null,this.Lc.set(Uu(this,a),_.Yb(b)),this.ze+=b.length)};
_.g.toString=function(){if(this.ig)return this.ig;if(!this.Lc)return"";for(var a=[],b=Array.from(this.Lc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Xe(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.ig=a.join("&")};_.g.clone=function(){var a=new _.Hu;a.ig=this.ig;this.Lc&&(a.Lc=new Map(this.Lc),a.ze=this.ze);return a};var Uu=function(a,b){b=String(b);a.Sg&&(b=b.toLowerCase());return b};
_.Hu.prototype.SL=function(a){a&&!this.Sg&&(Tu(this),this.ig=null,this.Lc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Sg=a};_.Hu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Au(arguments[b],function(c,d){this.add(d,c)},this)};
var XA=function(a){if(!a||typeof a!=="function")throw new WA("Must provide a function.");this.Bg=null;this.j9=a},YA=!1,jB,kB,lB,mB,nB,oB,pB,qB,rB,sB,tB,uB,vB,wB,xB;YA=!1;
var ZA=function(a){return new _.xk(function(b){var c=a.length,d=[];if(c)for(var e=function(k,l,m){c--;d[k]=l?{Bz:!0,value:m}:{Bz:!1,reason:m};c==0&&b(d)},f,h=0;h<a.length;h++)f=a[h],_.Ek(f,_.bb(e,h,!0),_.bb(e,h,!1));else b(d)})},$A,aB,bB,cB={eQ:function(a){$A=a;try{delete cB.eQ}catch(b){}},fQ:function(a){aB=a;try{delete cB.fQ}catch(b){}},gQ:function(a){bB=a;try{delete cB.gQ}catch(b){}}},dB=function(a){return _.zy(a.status)},eB=function(){var a=!0,b=_.ty.Sy();b&&b.withCredentials!==void 0||(a=!1);
return a},fB=function(a,b){if(b==null)return b;b=String(b);b.match(/^\/\/.*/)&&(b=(window.location.protocol=="http:"?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,
"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&(b=b.toLowerCase());c=_.Xe("client/rewrite");_.vb(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||
(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.Xe("client/firstPartyRewrite"),_.vb(a)&&Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,"$1://$2.clients6.google.com"),
b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},WA=function(a){_.lb.call(this,a)};_.y(WA,_.lb);WA.prototype.name="gapix.client.GapiClientError";XA.prototype.then=function(a,b,c){this.Bg||(this.Bg=this.j9());return this.Bg.then(a,b,c)};XA.prototype.gD=function(a){this.Bg||(this.Bg=a)};
var gB=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.ph(a,c);d&&(c=_.oh(c,d))&&_.qh(b,c,d,!0)}return b},hB={error:{code:-1,message:"A network error occurred and the request could not be completed."}},iB=function(a,b,c,d){_.Ay.call(this);this.Hd=a;this.kJ=b;this.Kd=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.ph(d,e),b!==void 0&&(e=_.nh(e,b))&&_.qh(a,e,b));d={};for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&(d[unescape(encodeURIComponent(f))]=
unescape(encodeURIComponent(a[f])));this.Wu=d;this.Bg=null};_.y(iB,_.Ay);
iB.prototype.then=function(a){this.Bg||(this.Bg=(new _.xk(function(b,c){this.na("error",(0,_.z)(function(){c(jB(this))},this));this.na("success",(0,_.z)(function(){b(jB(this))},this));this.send(this.Hd,this.kJ,this.Kd,this.Wu)},this)).then(function(b){b.headers=gB(b.headers);return b},function(b){return b.status?(b.headers=gB(b.headers),_.Ck(b)):_.Ck({result:hB,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.Bg.then.apply(this.Bg,arguments)};jB=function(a){var b=a.getStatus(),c=_.Jy(a);var d=b==204?!1:a.Qm==""?_.Qf(c):_.Ky(a);var e=a.getAllResponseHeaders();e=_.rh(e,!1);try{var f=_.Iy(a)>2?a.Va.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}};kB=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i;lB=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i;mB=/;\s*charset\s*=/i;nB=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g;
oB=function(a){var b=[];a=a.replace(nB,function(c){b.push(_.Az(c));return""});if(a.length)throw Error("va");return b.join("")};pB=function(a){var b=a.headers;if(b&&_.ph(b,"X-Goog-Safety-Encoding")==="base64"){var c=oB(a.body),d=_.ph(b,"X-Goog-Safety-Content-Type");b["Content-Type"]=d;if(d.match(kB)||d.match(lB)&&!d.match(mB))c=_.Ih(c),c=_.Fw(c);_.qh(b,"X-Goog-Safety-Encoding");_.qh(b,"X-Goog-Safety-Content-Type");a.body=c}};
qB=function(a,b,c){c||((c=_.Xe("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,"")||"/"),c=String(c||""));c||(c=_.Xe("googleapis.config/root"),b&&(c=_.Xe("googleapis.config/root-1p")||c),c=String(c||""));c=String(fB(b,c)||c);return a=_.yy(c,a)};
rB=function(a,b){var c=a.params||_.Ce();c.url=c.path;var d=c.root;d=qB("/",_.Ti(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Zi.Vm("makeHttpRequests",[a],function(e,f){e&&e.gapiRequest?(e.gapiRequest.data?pB(e.gapiRequest.data):pB(e),b(e,_.Rf(e))):b(e,f)})};
sB=function(a){var b=_.ru(a,"params","headers");b&&typeof b==="object"||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.ph(b,c);d&&(_.nh(c,d),_.qh(a,c,d))}c=(window.location.href.match(_.Sh)[1]||null)=="chrome-extension";a=_.Ti(a);return!(c&&a)&&eB()};
tB=function(a){return new _.xk(function(b,c){var d=function(e){e&&e.gapiRequest?e=e.gapiRequest.data||e:c(e);e={result:e.status!=204&&_.Qf(e.body),body:e.body,headers:e.headers||null,status:e.status||null,statusText:e.statusText||null};dB(e)?b(e):c(e)};try{rB(a,d)}catch(e){c(e)}})};uB=function(a){var b=!_.Xe("client/cors")||!!_.Xe("client/xd4"),c={};_.Qm(a,function(d,e){(d=_.nh(e,d))||b||(d=_.mh(e));d&&(e=_.ph(a,d))&&_.qh(c,d,e)});return c};
vB=function(a){var b=a.params||_.Ce();a=_.fk(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=encodeURIComponent("$unique");if(!(c==="POST"||_.wu(d,"$unique",d.search(_.xu))>=0||_.wu(d,e,d.search(_.xu))>=0)){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());f.sort();f.push(_.Ig(location.href));var h=f.join(":");f=_.mi();f.update(h);h=f.Ri().toLowerCase().substr(0,7);h=String(parseInt(h,16)%1E3+1E3).substr(1);d=_.xy(d,e,"gc"+h)}e=b.body||null;h=b.responseType||
null;b=_.Ti(a)||b.authType=="1p";f=!!_.Xe("googleapis.config/auth/useUberProxyAuth")||!!_.Xe("client/withCredentials");_.qh(a,"X-Referer");a=uB(a);var k=new iB(d,c,e,a);k.wh=b||f;h&&(k.Qm=h);return new _.xk(function(l,m){k.then(function(n){pB(n);l(n)},function(n){m(n)})})};wB=function(a,b){var c=function(d){d=_.fk(d);delete d.result;d={gapiRequest:{data:d}};b&&b(d,_.Rf(d))};vB(a).then(c,c)};
xB=function(a,b){(_.Xe("client/cors")||_.Xe("client/xd4"))&&sB(a)?(_.Hi(_.Gi(),12).rb(),wB(a,b)):(_.Hi(_.Gi(),11).rb(),rB(a,b))};_.yB={};var zB=function(a){this.jw=a;this.Wf=!1;this.promise={then:(0,_.z)(function(b,c,d){this.Wf||(this.Wf=!0);this.iw&&!this.gw?this.jw.resolve(this.iw):this.gw&&!this.iw&&this.jw.reject(this.gw);return this.jw.promise.then(b,c,d)},this)}};zB.prototype.resolve=function(a){this.Wf?this.jw.resolve(a):this.iw||this.gw||(this.iw=a)};zB.prototype.reject=function(a){this.Wf?this.jw.reject(a):this.iw||this.gw||(this.gw=a)};var AB=function(a){a=_.su(a.error);return{code:a.code,data:a.errors,message:a.message}},BB=function(a){throw Error("Aa`"+a);};var CB=function(a){XA.call(this,CB.prototype.Wo);if(!a||typeof a!="object"&&typeof a!="string")throw new WA("Missing required parameters");if(typeof a==="string"){var b={};b.path=a}else b=a;if(!b.path)throw new WA('Missing required parameter: "path"');this.ih={};this.ih.path=b.path;this.ih.method=b.method||"GET";this.ih.params=b.params||{};this.ih.headers=b.headers||{};this.ih.body=b.body;this.ih.root=b.root;this.ih.responseType=b.responseType;this.ih.apiId=b.apiId;this.rn=b.authType||"auto";this.Hca=
!!b.isXd4;this.mW=!1;this.Ij(this.rn);this.PZ=!1};_.y(CB,XA);CB.prototype.Ff=function(){return this.ih};CB.prototype.Ij=function(a){this.rn=a;this.mW=this.rn==="1p"};CB.prototype.Dq=function(){return this.mW};
CB.prototype.Dj=function(){if(!this.PZ){this.PZ=!0;var a=this.ih,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.ph(b,e);f&&(e=_.nh(e,f)||_.mh(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.qh(b,d[c][0],d[c][1]);if(this.Hca)d=this.rn=="1p";else{d=b;c=String(_.Xe("client/version","1.1.0"));h=String(_.Xe("client/name","google-api-javascript-client"));h=DB[h]===!0?h:"google-api-javascript-client";
e=String(_.Xe("client/appName",""));f=[];e&&(f.push(e),f.push(" "));f.push(h);c&&(f.push("/"),f.push(c));_.qh(d,"X-JavaScript-User-Agent",f.join(""));_.qh(b,"X-Requested-With","XMLHttpRequest");d=_.ph(b,"Content-Type");a.body&&!d&&_.qh(b,"Content-Type","application/json");_.Xe("client/allowExecutableResponse")||_.qh(b,"X-Goog-Encode-Response-If-Executable","base64");(d=_.ph(b,"Content-Type"))&&d.toLowerCase()=="application/json"&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.vb(d)&&(a.body=
_.Rf(d));a.key=a.id;b=_.Yi(b,void 0,this.rn);d=_.Ti(b);if((c=b)&&window.navigator){h=[];for(e=0;e<EB.length;e++)(f=window.navigator[EB[e]])&&h.push(encodeURIComponent(EB[e])+"="+encodeURIComponent(f));_.qh(c,"X-ClientDetails",h.join("&"))}(c=_.Xe("client/apiKey"))&&a.params.key===void 0&&(a.params.key=c);(c=_.Xe("client/trace"))&&!a.params.trace&&(a.params.trace=c)}this.rn=="auto"&&(d?this.Ij("1p"):(b=_.ph(b,"Authorization"))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Ij("oauth2"):this.Ij("none"));
if((b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root)if(a.root=String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/))a.root+="/_ah/api",a.path=a.path.substr(8);else{b=_.Xe("googleapis.config/root");d&&(b=_.Xe("googleapis.config/root-1p")||b);b=String(b||"");c=a.root+a.path;if(h=b&&c.substr(0,b.length)===b)h=_.Ru(b),e=_.Ru(c),h=(!h.Wd&&!e.Wd||h.Mg()==e.Mg())&&(h.Ag==null&&e.Ag==null||h.Ag==e.Ag);h&&(a.path=c.substr(b.length),a.root=b)}b=a.params;c=_.dh(a.path);
h=String(_.Xe("googleapis.config/xd3")||"");h.length>=18&&h.substring(h.length-18)=="/static/proxy.html"&&(h=h.substring(0,h.length-18));h||(h="/");e=_.dh(h);if(h!=e)throw Error("x");h.charAt(h.length-1)!="/"&&(h+="/");c=_.yy(h,c);_.Mj(c,"/")&&(c=c.substring(0,c.length-1));h=_.Ce();for(var k in b)Object.prototype.hasOwnProperty.call(b,k)&&(e=encodeURIComponent(k),h[e]=b[k]);c=_.Wh(c,h);a.path=c;a.root=fB(!!d,a.root);a.url=qB(a.path,!!d,a.root)}};
var FB=function(a){a.Dj();var b=a.ih;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.rn}}};_.g=CB.prototype;_.g.execute=function(a){var b=FB(this);xB(b,function(c,d){var e=c;c.gapiRequest&&(e=c.gapiRequest);e&&e.data&&(e=e.data);c=e;c=c instanceof Array?c[0]:c;if(c.status!=204&&c.body)try{var f=_.Qf(c.body)}catch(h){}a&&a(f,d)})};
_.g.Wo=function(){var a=FB(this);(_.Xe("client/cors")||_.Xe("client/xd4"))&&sB(a)?(_.Hi(_.Gi(),15).rb(),a=vB(a)):(_.Hi(_.Gi(),14).rb(),a=tB(a));return a};_.g.ej=function(){return this.Wo()};_.g.Be=function(){return this.ih.root};_.g.Hv=function(){console.log("makeJsonRpc is not supported for this request.");return{}};_.g.getFormat=function(){return 0};var EB=["appVersion","platform","userAgent"],DB={"google-api-gwt-client":!0,"google-api-javascript-client":!0};CB.prototype.execute=CB.prototype.execute;
CB.prototype.then=CB.prototype.then;CB.prototype.getPromise=CB.prototype.ej;var GB=function(a){if(!a||typeof a!="object")throw new WA("Missing rpc parameters");if(!a.method)throw new WA("Missing rpc method");this.xC=a};_.g=GB.prototype;_.g.Be=function(){var a=this.xC.transport;return a?a.root||null:null};_.g.execute=function(a){var b=aB();b.add(this,{id:"gapiRpc",callback:this.Fv(a)});b.execute()};
_.g.Hv=function(a){var b=this.xC.method,c=String,d;(d=this.xC.apiVersion)||(d=String(b).split(".")[0],d=_.Xe("googleapis.config/versions/"+b)||_.Xe("googleapis.config/versions/"+d)||"v1",d=String(d));a={jsonrpc:"2.0",id:a,method:b,apiVersion:c(d)};(b=this.xC.rpcParams)&&(a.params=b);return a};
_.g.Fv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;d.error==null&&(d.error=_.fk(b.error))}else d=b.result||b.data,_.vb(d)&&d.result==null&&(d.result=_.fk(b.result||b.data));else d=!1;a(d,c)}};_.g.then=function(){throw BB('The "then" method is not available on this object.');};_.g.gD=function(){};_.g.Ff=function(){};_.g.Dj=function(){};_.g.Ij=function(){};_.g.Dq=function(){};_.g.ej=function(){};GB.prototype.execute=GB.prototype.execute;var IB=function(a,b){this.We=b||0;this.We==2?(b=null,a!=null&&_.vb(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.Rb=new GB(b)):(this.We==0&&(b=a&&a.callback)&&(a.callback=HB(b)),b=null,a!=null&&(_.vb(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):typeof a==="string"&&(b=a)),
this.Rb=new CB(b))},HB=function(a){return function(b){if(b!=null&&_.vb(b)&&b.error){var c=AB(b);b=_.Rf([{id:"gapiRpc",error:c}]);c.error=_.su(c)}else b==null&&(b={}),c=_.su(b),c.result=_.su(b),b=_.Rf([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=IB.prototype;_.g.getFormat=function(){return this.We};_.g.execute=function(a){this.Rb.execute(a&&this.We==1?HB(a):a)};_.g.then=function(a,b,c){return this.Rb.then(a,b,c)};_.g.gD=function(a){this.Rb.gD(a)};_.g.Ff=function(){return this.Rb.Ff()};_.g.Dj=function(){this.Rb.Dj()};
_.g.Be=function(){return this.Rb.Be()};_.g.Hv=function(a){if(this.Rb.Hv)return this.Rb.Hv(a)};_.g.Ij=function(a){this.Rb.Ij(a)};_.g.Dq=function(){return!!this.Rb.Dq()};_.g.ej=function(){return this.Rb.ej()};IB.prototype.execute=IB.prototype.execute;IB.prototype.then=IB.prototype.then;IB.prototype.getPromise=IB.prototype.ej;var JB=/<response-(.*)>/,KB=/^application\/http(;.+$|$)/,LB=["clients6.google.com","content.googleapis.com","www.googleapis.com"],MB=function(a,b){a=_.ph(a,b);if(!a)throw new WA("Unable to retrieve header.");return a},NB=function(a){var b=void 0;a=_.Aa(a);for(var c=a.next();!c.done;c=a.next()){c=c.value.Ff().apiId;if(typeof c!=="string")return"batch";if(b===void 0)b=c;else if(b!=c)return"batch"}b=_.Xe("client/batchPath/"+b)||"batch/"+b.split(":").join("/");return String(b)},OB=function(a){a=a.map(function(b){return b.request});
return NB(a)},PB=function(a,b){var c=[];a=a.Ff();var d=function(f,h){_.Qm(f,function(k,l){h.push(l+": "+k)})},e={"Content-Type":"application/http","Content-Transfer-Encoding":"binary"};e["Content-ID"]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},SB=function(a,b){a=QB(a,b);var c={};_.Zb(a,function(d,e){c[e]=RB(d,e)});return c},RB=function(a,b){return{result:a.result||a.body,rawResult:_.Rf({id:b,result:a.result||a.body}),
id:b}},QB=function(a,b){a=_.zc(a);_.Mj(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.Ce();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.Mj(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&d[e]=="";)e++;for(f.outerHeaders=TB(d,e);e<d.length&&d[e]!="";)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=TB(d,e);e<d.length&&d[e]!="";)e++;e++;f.body=d.slice(e).join("\r\n");
pB(f);d=f}else d=null;e=_.Ce();f=MB(d.outerHeaders,"Content-Type");if(KB.exec(f)==null)throw new WA("Unexpected Content-Type <"+f+">");f=MB(d.outerHeaders,"Content-ID");f=JB.exec(f);if(!f)throw new WA("Unable to recognize Content-Id.");e.id=decodeURIComponent(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};d.status!=204&&(e.response.body=d.body,e.response.result=_.Qf(d.body));d=e}else d=null;d&&d.id&&(b[d.id]=d.response)}return b},TB=
function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.rh(c.join("\r\n"),!1)},UB=function(a,b,c){a=a||b;(b=!a)||(b=_.Ru(a).Bi!=="https");if(b&&(a=c?_.Xe("googleapis.config/root-1p"):_.Xe("googleapis.config/root"),!a))return!1;a=fB(c,String(a))||a;return LB.includes(_.Ru(a).Mg())};var VB=function(a){XA.call(this,VB.prototype.Wo);this.nk={};this.qy={};this.Om=[];this.Rd=a;this.hda=!!a;this.eV=this.AA=!1};_.y(VB,XA);var WB=function(a,b){a=_.Aa(Object.values(a.nk));for(var c=a.next();!c.done;c=a.next())if(c.value.map(function(d){return d.id}).includes(b))return!0;return!1};VB.prototype.Wp=function(a){(function(b){setTimeout(function(){throw b;})})(a)};
VB.prototype.add=function(a,b){var c=b||_.Ce();b=_.Ce();if(!a)throw new WA("Batch entry "+(_.De(c,"id")?'"'+c.id+'" ':"")+"is missing a request method");a.Dj();b.request=a;var d=_.Hk();d=new zB(d);b.qC=d;a.gD(b.qC.promise);d=a.Ff().headers;_.Ti(d)&&(this.AA=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.eV=!0);d=a.Ff().root;if(!this.hda){if(d&&this.Rd&&d!=this.Rd)throw new WA('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Rd=d||this.Rd}if(_.De(c,"id")){d=c.id;if(WB(this,d))throw new WA('Batch ID "'+d+'" already in use, please use another.');b.id=d}else{do b.id=String(Math.round(2147483647*_.Pi()));while(WB(this,b.id))}b.callback=c.callback;c="batch";UB(this.Rd,a.Ff().path,this.AA)&&(c=OB([b]));this.nk[c]=this.nk[c]||[];this.nk[c].push(b);this.qy[b.id]=b;return b.id};
var XB=function(a){var b=[],c=UB(a.Rd,void 0,a.AA);Object.entries(a.nk).length>1&&_.Vf.warn("Heterogeneous batch requests are deprecated. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");for(var d=_.Aa(Object.entries(a.nk)),e=d.next();!e.done;e=d.next()){e=_.Aa(e.value);var f=e.next().value;e=e.next().value;for(var h=!0,k=_.Aa(e),l=k.next();!l.done;l=k.next())l=l.value,l.request.Dj(),f==="batch"&&c&&(h=!1,l.zca=!0,l.request.Ff.root=a.Rd,b.push(l.request),
a.Om.push([l]));if(h){var m=e;f=a.Rd;h=a.AA;k=a.eV;l="batch"+String(Math.round(2147483647*_.Pi()))+String(Math.round(2147483647*_.Pi()));var n="--"+l;l="multipart/mixed; boundary="+l;for(var p={path:OB(m),method:"POST"},q=[],r=0;r<m.length;r++)q.push(PB(m[r].request,[n.substr(n.indexOf("--")+2),"+",encodeURIComponent(m[r].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));p.body=[n,q.join("\r\n"+n+"\r\n"),n+"--"].join("\r\n")+"\r\n";p.root=f||null;
_.Xe("client/xd4")&&eB()?(p.isXd4=!0,p.params={$ct:l},p.headers={},_.qh(p.headers,"Content-Type","text/plain; charset=UTF-8"),h?p.authType="1p":k&&(p.authType="oauth2"),f=new CB(p)):(p.headers={},_.qh(p.headers,"Content-Type",l),f=bB(p));b.push(f);a.Om.push(e)}}return b};
VB.prototype.execute=function(a){if(!(Object.keys(this.nk).length<1)){var b=this.Fv(a);a=XB(this);var c=[],d=a.map(function(e){return new _.xk(function(f){try{e.execute(function(h,k){return f({aQ:h,Nea:k})})}catch(h){c.push(h),f({aQ:{Bz:!1,reason:h}})}})});if(c.length>0&&c.length===a.length)throw c[0];_.Fk(d).then(function(e){var f=e.map(function(h){return h.Nea});e=e.map(function(h){return h.aQ});b(e,f)})}};
VB.prototype.Wo=function(){var a=this;if(Object.keys(this.nk).length<1)return _.Bk({});var b=XB(this).map(function(c){return new _.xk(function(d,e){return c.ej().then(d,e)})});return ZA(b).then(function(c){c=c.map(function(d){return d.Bz?d.value:d});return YB(a,c,!0)})};
VB.prototype.IY=function(a,b,c,d){var e={};if(c){e=b?QB:SB;b=MB(a.headers,"Content-Type").split("boundary=")[1];if(!b)throw new WA("Boundary not indicated in response.");e=e(a.body,"--"+b)}else b?(a.result=_.Qf(a.body),e[d]=a):e[d]=RB(a,d);a={};e=_.Aa(Object.entries(e));for(b=e.next();!b.done;b=e.next())if(c=_.Aa(b.value),b=c.next().value,c=c.next().value,a[b]=c,!this.qy[b])throw new WA("Could not find batch entry for id "+b+".");return a};
var YB=function(a,b,c,d,e){for(var f=!1,h={},k,l=0,m=0;m<b.length;m++){var n=b[m];if(n&&Object.keys(n).includes("fulfilled")&&n.Bz===!1){l++;b[m]=n.reason;n=ZB([b[m]]);for(var p=_.Aa(a.Om[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=n}else{if(a.Om[m].length<1)throw new WA("Error processing batch responses.");try{var r=!(a.Om[m].length===1&&a.Om[m][0].zca),w=a.Om[m][0].id;if(!c){p=n;q=r;var u=d[m],x=p;if(u&&(!x||!q)){var A=_.Qf(u);A&&(x=A.gapiRequest?A.gapiRequest.data:A,!q&&p&&(x.body=p))}if(!x)throw new WA("The batch response is missing.");
n=x}p=void 0;if(q=n){var D=q.headers;if(D){var E=_.Ce();for(p in D)if(Object.prototype.hasOwnProperty.call(D,p)){var N=_.ph(D,p);_.qh(E,p,N,!0)}q.headers=E}}if(r&&MB(n.headers,"Content-Type").indexOf("multipart/mixed")!=0)throw new WA("The response's Content-Type is not multipart/mixed.");k=k||_.su(n);var H=dB(n);H&&!dB(k)&&(k.status=n.status,k.statusText=n.statusText);if(H||c||!r)f=!0,h=Object.assign(h,a.IY(n,c,r,w))}catch(R){for(l++,b[m]=R,n=ZB([R]),p=_.Aa(a.Om[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=
n}}}if(l===b.length){d=ZB(b);h=_.Rf(d);k=0;a=Array.from(Object.values(a.nk)).flat();f=_.Aa(a);for(l=f.next();!l.done;l=f.next())if(l=l.value,c)l.qC.reject(d);else if(l.callback)try{k++,l.callback(d,h)}catch(R){VB.prototype.Wp(R)}if(e)try{e(d,h)}catch(R){VB.prototype.Wp(R)}else if(k!==a.length)throw b.length===1?b[0]:d;}else{if(f)for(f=_.Aa(Object.entries(h)),l=f.next();!l.done;l=f.next())if(l=_.Aa(l.value),m=l.next().value,l=l.next().value,c)m=a.qy[m],l&&dB(l)?m.qC.resolve(l):m.qC.reject(l);else if(m=
a.qy[m],m.callback){if(l&&l.rawResult)try{delete l.rawResult}catch(R){}try{m.callback(l||!1,_.Rf(l))}catch(R){VB.prototype.Wp(R)}}k.result=h||{};k.body=b.length===1?k.body:"";if(e)try{e(h||null,d.length===1?d[0]:null)}catch(R){VB.prototype.Wp(R)}return k}},ZB=function(a){var b={error:{code:0,message:"The batch request could not be fulfilled.  "}};a=_.Aa(a);for(var c=a.next();!c.done;c=a.next())(c=c.value)&&c.message||c instanceof Error&&c.message?b.error.message+=(c.message||c instanceof Error&&c.message)+
"  ":c&&c.error&&c.error.message&&(b.error.message+=c.error.message+"  ",b.error.code=c.error.code||b.error.code||0);b.error.message=b.error.message.trim();return{result:b,body:_.Rf(b),headers:null,status:null,statusText:null}};VB.prototype.Fv=function(a){var b=this;return function(c,d){b.XE(c,d,a)}};VB.prototype.XE=function(a,b,c){YB(this,a,!1,b,c)};VB.prototype.add=VB.prototype.add;VB.prototype.execute=VB.prototype.execute;VB.prototype.then=VB.prototype.then;var $B=function(){this.Ml=[];this.Rd=this.mf=null};
$B.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.hp=a;else throw new WA("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.Ml.length;d++)if(this.Ml[d].id==a)throw new WA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*_.Pi()|0);while(d.call(this.Ml,c.id))}c.callback=b.callback;this.Ml.push(c);return c.id};
var aC=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=b.length,f=0;f<e;++f)d[b[f].id]=b[f];a(d,c)}else a(b,c)}};
$B.prototype.execute=function(a){this.mf=[];for(var b,c,d=0;d<this.Ml.length;d++)b=this.Ml[d],c=b.hp,this.mf.push(c.Hv(b.id)),this.Rd=c.Be()||this.Rd;c=this.Fv(a);a={requests:this.mf,root:this.Rd};b={};d=a.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.ph(d,f);h&&(f=_.nh(f,h)||_.mh(f))&&_.qh(b,f,h)}}_.qh(b,"Content-Type","application/json");e=aC(c);bB({method:"POST",root:a.root||void 0,path:"/rpc",params:a.urlParams,headers:b,body:a.requests||[]}).then(e,
e)};$B.prototype.Fv=function(a){var b=this;return function(c,d){b.XE(c,d,a)}};$B.prototype.XE=function(a,b,c){a||(a={});for(var d=0;d<this.Ml.length;d++){var e=this.Ml[d];e.callback&&e.callback(a[e.id]||!1,b)}c&&c(a,b)};cB.fQ(function(){return new $B});$B.prototype.add=$B.prototype.add;$B.prototype.execute=$B.prototype.execute;var bC=function(a,b){this.hea=a;this.We=b||null;this.zf=null};bC.prototype.oI=function(a){this.We=a;this.zf=this.We==2?new $B:new VB(this.hea)};bC.prototype.add=function(a,b){if(!a)throw a=b||_.Ce(),new WA("Batch entry "+(_.De(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");this.We===null&&this.oI(a.getFormat());this.We!==a.getFormat()&&BB("Unable to add item to batch.");var c=b&&b.callback;this.We==1&&c&&(b.callback=function(d){d=cC(d);var e=_.Rf([d]);c(d,e)});return this.zf.add(a,b)};
bC.prototype.execute=function(a){var b=a&&this.We==1?function(c){var d=[];_.Qm(c,function(f,h){f=cC(f);c[h]=f;d.push(f)});var e=_.Rf(d);a(c,e)}:a;this.zf&&this.zf.execute(b)};var cC=function(a){var b=a?_.ru(a,"result"):null;_.vb(b)&&b.error!=null&&(b=AB(b),a={id:a.id,error:b});return a};bC.prototype.then=function(a,b,c){this.We==2&&BB('The "then" method is not available on this object.');return this.zf.then(a,b,c)};bC.prototype.add=bC.prototype.add;bC.prototype.execute=bC.prototype.execute;
bC.prototype.then=bC.prototype.then;var dC=function(a){XA.call(this,dC.prototype.Wo);this.Rb=a;this.vQ=!1};_.y(dC,XA);var eC=function(a){a.Rb.Dj();var b=a.Rb,c=b.Ff();return!(UB(c.root,c.path,a.Rb.Dq())?NB([b])!=="batch":1)};_.g=dC.prototype;
_.g.execute=function(a){var b=this;this.vQ=!0;if(eC(this))this.Rb.execute(a);else{_.Hi(_.Gi(),13).rb();var c=function(d){if(typeof a==="function"){var e={gapiRequest:{data:{status:d&&d.status,statusText:d&&d.statusText,headers:d&&d.headers,body:d&&d.body}}};if(b.getFormat()===1){a=HB(a);var f={}}var h=d?d.result:!1;d&&d.status==204&&(h=f,delete e.gapiRequest.data.body);a(h,_.Rf(e))}};this.ej().then(c,c)}};
_.g.Wo=function(){if(eC(this))return this.Rb.ej();this.vQ||_.Hi(_.Gi(),16).rb();return new _.xk(function(a,b){var c=$A(),d=c.add(this.Rb,{id:"gapiRequest"});c.then(function(e){var f=e.result;if(f&&(f=f[d])){Object.prototype.hasOwnProperty.call(f,"result")||(f.result=!1);Object.prototype.hasOwnProperty.call(f,"body")||(f.body="");dB(f)?a(f):b(f);return}b(e)},b)},this)};_.g.Ff=function(){if(this.Rb.Ff)return this.Rb.Ff()};_.g.Dj=function(){this.Rb.Dj&&this.Rb.Dj()};_.g.Be=function(){if(this.Rb.Be)return this.Rb.Be()};
_.g.Ij=function(a){this.Rb.Ij&&this.Rb.Ij(a)};_.g.Dq=function(){return this.Rb.Dq()};_.g.getFormat=function(){return this.Rb.getFormat?this.Rb.getFormat():0};_.g.ej=function(){return this.Wo()};dC.prototype.execute=dC.prototype.execute;dC.prototype.then=dC.prototype.then;dC.prototype.getPromise=dC.prototype.ej;var fC="/rest?fields="+encodeURIComponent("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",gC=function(a,b){return"/discovery/v1/apis/"+(encodeURIComponent(a)+"/"+encodeURIComponent(b)+fC)},iC=function(a,b,c,d){if(_.vb(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new WA("Missing required parameters.");var h=c||function(){},k=_.vb(d)?d:{};c=function(l){var m=l&&l.result;if(!m||m.error||!m.name||!l||l.error||l.message||l.message)h(m&&
m.error?m:l&&(l.error||l.message||l.message)?l:new WA("API discovery response missing required fields."));else{l=k.root;l=m.rootUrl!=null?String(m.rootUrl):l;l=typeof l==="string"?l.replace(/([^\/])\/$/,"$1"):void 0;k.root=l;m.name&&m.version&&!m.id&&(m.id=[m.name,m.version].join(":"));m.id&&(k.apiId=m.id,l="client/batchPath/"+m.id,m.batchPath&&!_.Xe(l)&&_.Ye(l,m.batchPath));var n=m.servicePath,p=m.parameters,q=function(w){_.Qm(w,function(u){if(!(u&&u.id&&u.path&&u.httpMethod))throw new WA("Missing required parameters");
var x=u.id.split("."),A=window.gapi.client,D;for(D=0;D<x.length-1;D++){var E=x[D];A[E]=A[E]||{};A=A[E]}var N,H;k&&(k.hasOwnProperty("root")&&(N=k.root),k.hasOwnProperty("apiId")&&(H=k.apiId));E=window.gapi.client[x[0]];E.hO||(E.hO={servicePath:n||"",parameters:p,apiId:H});x=x[D];A[x]||(A[x]=_.bb(hC,{path:typeof u.path==="string"?u.path:null,httpMethod:typeof u.httpMethod==="string"?u.httpMethod:null,parameters:u.parameters,parameterName:(u.request||{}).parameterName||"",request:u.request,root:N},
E.hO))})},r=function(w){_.Qm(w,function(u){q(u.methods);r(u.resources)})};r(m.resources);q(m.methods);h.call()}};e?c({result:e}):f.indexOf("://")>0?bB({path:f,params:{pp:0,fields:("/"+f).indexOf("/discovery/v1/apis/")>=0?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,c):bB({path:gC(f,
a),root:d&&d.root}).then(c,c)},hC=function(a,b,c,d,e){e=e===void 0?{}:e;var f=b.servicePath||"";_.wc(f,"/")||(f="/"+f);var h=jC(a.path,[a.parameters,b.parameters],c||{});c=h.Ed;var k=h.Jha;f=_.yy(f,h.path);h=k.root;delete k.root;var l=a.parameterName;!l&&_.vy(k)==1&&k.hasOwnProperty("resource")&&(l="resource");if(l){var m=k[l];delete k[l]}m==null&&(m=d);m==null&&a.request&&(_.Gh(k)&&(k=void 0),m=k);e=e||{};l=a.httpMethod;l=="GET"&&m!==void 0&&String(m)!=""&&(_.qh(e,"X-HTTP-Method-Override",l),l="POST");
if((m==null||d!=null)&&k)for(var n in k)typeof k[n]==="string"&&(c[n]=k[n]);return bB({path:f,method:l,params:c,headers:e,body:m,root:h||a.root,apiId:b.apiId},1)},jC=function(a,b,c){c=_.fk(c);var d={};_.Pm(b,function(e){_.Qm(e,function(f,h){var k=f.required;if(f.location=="path")if(Object.prototype.hasOwnProperty.call(c,h))_.yc(a,"{"+h+"}")?(f=encodeURIComponent(String(c[h])),a=a.replace("{"+h+"}",f)):_.yc(a,"{+"+h+"}")&&(f=encodeURI(String(c[h])),a=a.replace("{+"+h+"}",f)),delete c[h];else{if(k)throw new WA("Required path parameter "+
h+" is missing.");}else f.location=="query"&&Object.prototype.hasOwnProperty.call(c,h)&&(d[h]=c[h],delete c[h])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Ed:d,Jha:c}};var kC=function(a,b,c,d){var e=b||"v1",f=_.vb(d)?d:{root:d};if(c)iC(a,e,function(h){if(h)if(h.error)c(h);else{var k="API discovery was unsuccessful.";if(h.message||h.message)k=h.message||h.message;c({error:k,code:0})}else c()},f);else return new _.xk(function(h,k){var l=function(m){m?k(m):h()};try{iC(a,e,l,f)}catch(m){k(m)}})},lC=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),mC=function(a,b){if(!a)throw new WA("Missing required parameters");
var c=typeof a==="object"?a:{path:a};a=c.callback;delete c.callback;b=new IB(c,b);if(c=!!_.Xe("client/xd4")&&eB()){var d=b.Ff();c=d.path;(d=d.root)&&d.charAt(d.length-1)!=="/"&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(lC)}c&&(b=new dC(b));return a?(b.execute(a),null):b};cB.gQ(function(a){return mC.apply(null,arguments)});
var nC=function(a,b){if(!a)throw new WA("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(k){var l=typeof h=="string"?h:h.root;k&&k.root&&(l=k.root);return new IB({method:a,apiVersion:h.apiVersion,rpcParams:k,transport:{name:"googleapis",root:l}},2)}}},oC=function(a){return new bC(a)};cB.eQ(function(a){return oC.apply(null,arguments)});
var pC=function(a){if(_.yB.JSONRPC_ERROR_MOD)throw new WA(a+" is discontinued. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");_.Vf.log(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};_.t("gapi.client.init",function(a){a.apiKey&&_.Ye("client/apiKey",a.apiKey);var b=_.Ib(a.discoveryDocs||[],function(d){return kC(d)});if((a.clientId||a.client_id)&&a.scope){var c=new _.xk(function(d,e){var f=function(){_.Xa.gapi.auth2.init.call(_.Xa.gapi.auth2,a).then(function(){d()},e)};YA?f():_.Xa.gapi.load("auth2",{callback:function(){f()},onerror:function(h){e(h||Error("Ba"))}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Vf.log("client_id and scope must both be provided to initialize OAuth.");
return _.Fk(b).then(function(){})});_.t("gapi.client.load",kC);_.t("gapi.client.newBatch",oC);_.t("gapi.client.newRpcBatch",function(){pC("gapi.client.newRpcBatch");return oC()});_.t("gapi.client.newHttpBatch",function(a){pC("gapi.client.newHttpBatch");return new bC(a,0)});_.t("gapi.client.register",function(a,b){pC("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});nC(a,c)});_.t("gapi.client.request",mC);
_.t("gapi.client.rpcRequest",function(a,b,c){pC("gapi.client.rpcRequest");if(!a)throw new WA('Missing required parameter "method".');return new IB({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.t("gapi.client.setApiKey",function(a){_.Ye("client/apiKey",a);_.Ye("googleapis.config/developerKey",a)});_.t("gapi.client.setApiVersions",function(a){pC("gapi.client.setApiVersions");_.Ye("googleapis.config/versions",a)});_.t("gapi.client.getToken",function(a){return _.ui(a)});
_.t("gapi.client.setToken",function(a,b){a?_.Hw(a,b):_.Iw(b)});_.t("gapi.client.AuthType",{wia:"auto",NONE:"none",ema:"oauth2",qka:"1p"});_.t("gapi.client.AuthType.AUTO","auto");_.t("gapi.client.AuthType.NONE","none");_.t("gapi.client.AuthType.OAUTH2","oauth2");_.t("gapi.client.AuthType.FIRST_PARTY","1p");
});
// Google Inc.
