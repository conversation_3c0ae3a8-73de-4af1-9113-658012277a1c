import os
import requests
import json
import datetime
import time

# 1. 配置参数
GITHUB_TOKEN = '*********************************************************************************************'  # 替换为你的 GitHub Personal Access Token
FOLDER_TO_BACKUP = 'D:\\PortableApps\\BestSub_Windows_x86_64 (2)\\output'  # 替换为你想要备份的文件夹路径
GIST_ID = '361ad63b8ec7f0d92a81d0608111f917'  # 如果是更新现有 Gist，替换为你的 Gist ID，如果是新建，可以留空
GIST_PUBLIC = False
GIST_DESCRIPTION = 'allyml'
GIST_FILENAME = 'folder_backup.txt'  # Gist 中保存的文件名

# 2. 函数：读取文件内容
def read_file_content(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件内容出错: {e}")
        return None
# 3. 函数：更新 Gist
def update_gist(token, gist_id, description, files):
    api_url = f'https://api.github.com/gists/{gist_id}'
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    data = {
        'description': description,
        'public': GIST_PUBLIC,
        'files': files
    }
    try:
        response = requests.patch(api_url, headers=headers, data=json.dumps(data))
        response.raise_for_status()  # 检查是否有 HTTP 错误
        if response.status_code == 200:
            result = response.json()
            gist_url = result['html_url']
            print(f"Gist 更新成功！Gist URL: {gist_url}")
            return True
        else:
            print(f"Gist 更新失败，状态码: {response.status_code}, 响应内容: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Gist 更新网络错误: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}, 响应内容: {response.text if 'response' in locals() else 'N/A'}")
        return False
# 4. 主流程
def main():
    files_to_update = {}
    for filename in os.listdir(FOLDER_TO_BACKUP):
        filepath = os.path.join(FOLDER_TO_BACKUP, filename)
        if os.path.isfile(filepath):
            content = read_file_content(filepath)
            if content:
                files_to_update[filename] = {'content': content}
            else:
                print(f"无法读取文件 {filename} 的内容，跳过。")
    if files_to_update:
        if update_gist(GITHUB_TOKEN, GIST_ID, GIST_DESCRIPTION, files_to_update):
            print("Gist 更新完成！")
        else:
            print("Gist 更新失败。")
    else:
        print("没有需要更新的文件。")
# 5. 运行主流程
if __name__ == "__main__":
    main()