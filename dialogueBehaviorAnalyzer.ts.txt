// src/rpgSystem/utils/dialogueBehaviorAnalyzer.ts

import { CoreAttributes_RPG } from '../types_rpg';
import { behaviorConfigLoader } from './behaviorConfigLoader';
import { AIPromptTemplates, AIBehaviorAnalysisResult, AI_ANALYSIS_CONFIG, calculateAttributeEffects, BehaviorType } from '../config/ai_prompts';
import { AIMessage, AIResponse } from '../../services/aiProviders/types';
import { prebakedResultLoader, PrebakedResultLoader } from './prebakedResultLoader';
import { semanticCache, SemanticCache } from './semanticCache';

/**
 * 属性变化接口
 */
export interface AttributeChange {
  attribute: keyof CoreAttributes_RPG;
  change: number;
  reason?: string;
}

/**
 * 行为分析结果接口
 */
export interface BehaviorAnalysisResult {
  behaviorType: string;
  confidence: number;
  attributeChanges: AttributeChange[];
  justification?: string;
}

/**
 * 行为映射规则接口
 */
export interface BehaviorMappingRule {
  behavior: string;
  keywords: string[];
  effects: Partial<Record<keyof CoreAttributes_RPG, number>>;
  description?: string;
}

/**
 * 分析模式枚举
 */
export enum AnalysisMode {
  KEYWORD_ONLY = 'keyword_only',
  AI_ONLY = 'ai_only',
  HYBRID = 'hybrid'  // AI优先，关键词作为fallback
}

/**
 * 缓存条目接口
 */
interface CacheEntry {
  result: AttributeChange[];
  timestamp: number;
  confidence: number;
}

/**
 * 简单的LRU缓存实现
 */
class SimpleCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize: number = 100, ttl: number = 300000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: string): AttributeChange[] | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 检查是否过期
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    // LRU: 重新插入到末尾
    this.cache.delete(key);
    this.cache.set(key, entry);
    return entry.result;
  }

  set(key: string, result: AttributeChange[], confidence: number = 1.0): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, {
      result,
      timestamp: Date.now(),
      confidence
    });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

/**
 * 对话行为分析器
 * 升级版本：支持AI驱动的智能分析，关键词匹配作为fallback
 */
export class DialogueBehaviorAnalyzer {
  private behaviorRules: BehaviorMappingRule[] = [];
  private isInitialized = false;
  private analysisCache: SimpleCache;
  private analysisMode: AnalysisMode = AnalysisMode.HYBRID;
  private aiServiceManager: any = null; // 延迟注入
  private prebakedLoader: PrebakedResultLoader;
  private prebakedEnabled = true;
  private semanticCache: SemanticCache;
  private semanticCacheEnabled = true;

  constructor() {
    this.analysisCache = new SimpleCache(
      AI_ANALYSIS_CONFIG.cache.maxSize,
      AI_ANALYSIS_CONFIG.cache.ttl
    );
    this.prebakedLoader = prebakedResultLoader;
    this.semanticCache = semanticCache;
    this.initializeFromConfig();
  }

  /**
   * 注入AI服务管理器（避免循环依赖）
   */
  public setAIServiceManager(aiServiceManager: any): void {
    this.aiServiceManager = aiServiceManager;
    console.log('✅ [DialogueBehaviorAnalyzer] AI服务管理器已注入');
  }

  /**
   * 设置分析模式
   */
  public setAnalysisMode(mode: AnalysisMode): void {
    this.analysisMode = mode;
    console.log(`🔧 [DialogueBehaviorAnalyzer] 分析模式设置为: ${mode}`);
  }

  /**
   * 配置预烘焙功能
   */
  public configurePrebakedAnalysis(options: { enabled?: boolean }): void {
    this.prebakedEnabled = options.enabled ?? true;
    this.prebakedLoader.configure({ enabled: this.prebakedEnabled });
    console.log(`⚙️ [DialogueBehaviorAnalyzer] 预烘焙功能: ${this.prebakedEnabled ? '启用' : '禁用'}`);
  }

  /**
   * 预热预烘焙数据（可选的性能优化）
   */
  public async warmupPrebakedData(): Promise<boolean> {
    if (!this.prebakedEnabled) {
      return false;
    }

    try {
      const success = await this.prebakedLoader.warmup();
      if (success) {
        console.log('🔥 [DialogueBehaviorAnalyzer] 预烘焙数据预热完成');
      }
      return success;
    } catch (error) {
      console.error('❌ [DialogueBehaviorAnalyzer] 预烘焙数据预热失败:', error);
      return false;
    }
  }

  /**
   * 配置语义缓存功能
   */
  public configureSemanticCache(options: { enabled?: boolean; semanticThreshold?: number; fallbackThreshold?: number }): void {
    this.semanticCacheEnabled = options.enabled ?? true;

    if (options.semanticThreshold !== undefined || options.fallbackThreshold !== undefined) {
      this.semanticCache.configure({
        enabled: this.semanticCacheEnabled,
        semanticThreshold: options.semanticThreshold,
        fallbackThreshold: options.fallbackThreshold
      });
    } else {
      this.semanticCache.configure({ enabled: this.semanticCacheEnabled });
    }

    console.log(`⚙️ [DialogueBehaviorAnalyzer] 语义缓存功能: ${this.semanticCacheEnabled ? '启用' : '禁用'}`);
  }

  /**
   * 预热语义缓存（可选的性能优化）
   */
  public async warmupSemanticCache(): Promise<boolean> {
    if (!this.semanticCacheEnabled) {
      return false;
    }

    try {
      await this.semanticCache.initialize();
      console.log('🧠 [DialogueBehaviorAnalyzer] 语义缓存预热完成');
      return true;
    } catch (error) {
      console.error('❌ [DialogueBehaviorAnalyzer] 语义缓存预热失败:', error);
      return false;
    }
  }

  /**
   * 从配置文件初始化规则
   */
  private async initializeFromConfig(): Promise<void> {
    try {
      console.log('🔧 [DialogueBehaviorAnalyzer] 从配置加载行为规则...');
      this.behaviorRules = await behaviorConfigLoader.getBehaviorRules();
      this.isInitialized = true;
      console.log(`✅ [DialogueBehaviorAnalyzer] 成功加载 ${this.behaviorRules.length} 个行为规则`);
    } catch (error) {
      console.error('❌ [DialogueBehaviorAnalyzer] 配置加载失败，使用默认规则:', error);
      this.initializeFallbackRules();
    }
  }

  /**
   * 初始化备用规则（当配置加载失败时使用）
   */
  private initializeFallbackRules(): void {
    this.behaviorRules = [
      {
        behavior: 'Brave',
        keywords: ['brave', 'courage', 'face it', 'not afraid', 'handle this', '勇敢', '不怕', '面对'],
        effects: {
          strength: 1,
          sanity: 1
        },
        description: '表现出勇气和决心'
      },
      {
        behavior: 'Cunning',
        keywords: ['clever', 'trick', 'deceive', 'lie', 'cunning', '聪明', '欺骗', '谎言', '狡猾'],
        effects: {
          intelligence: 1,
          charisma: -1
        },
        description: '表现出狡猾和欺骗'
      }
    ];
    this.isInitialized = true;
  }

  /**
   * 核心分析方法（增强版：支持预烘焙结果）
   * @param playerInput 玩家的对话文本
   * @param context 可选的游戏情境描述
   * @returns 属性变化数组
   */
  public async analyze(playerInput: string, context?: string): Promise<AttributeChange[]> {
    console.log(`🔍 [DialogueBehaviorAnalyzer] 分析输入: "${playerInput}" (情境: ${context || '无'})`);

    // 确保初始化完成
    if (!this.isInitialized) {
      console.log('⏳ [DialogueBehaviorAnalyzer] 等待初始化完成...');
      await this.initializeFromConfig();
    }

    if (!this.isInitialized) {
      console.warn('⚠️ [DialogueBehaviorAnalyzer] 分析器初始化失败');
      return [];
    }

    // 🚀 第一优先级：检查预烘焙结果（零延迟）
    if (this.prebakedEnabled) {
      try {
        const prebakedResult = await this.prebakedLoader.findResult(playerInput, context);
        if (prebakedResult) {
          console.log('🎯 [DialogueBehaviorAnalyzer] 使用预烘焙结果（0ms响应）');
          return prebakedResult;
        }
      } catch (error) {
        console.warn('⚠️ [DialogueBehaviorAnalyzer] 预烘焙查找失败，降级到动态分析:', error);
      }
    }

    // 第二优先级：检查精确LRU缓存
    if (AI_ANALYSIS_CONFIG.cache.enabled) {
      const cacheKey = AIPromptTemplates.generateCacheKey(playerInput, context);
      const cachedResult = this.analysisCache.get(cacheKey);
      if (cachedResult) {
        console.log('💾 [DialogueBehaviorAnalyzer] 使用精确LRU缓存结果');
        return cachedResult;
      }
    }

    // 🧠 第三优先级：检查语义缓存（智能相似度匹配）
    if (this.semanticCacheEnabled) {
      try {
        const semanticResult = await this.semanticCache.find(playerInput, context);
        if (semanticResult) {
          console.log('🧠 [DialogueBehaviorAnalyzer] 使用语义缓存结果（智能匹配）');
          return semanticResult;
        }
      } catch (error) {
        console.warn('⚠️ [DialogueBehaviorAnalyzer] 语义缓存查找失败:', error);
      }
    }

    // 第三优先级：动态分析
    let result: AttributeChange[] = [];

    // 根据分析模式选择分析方法
    switch (this.analysisMode) {
      case AnalysisMode.AI_ONLY:
        result = await this.analyzeWithAI(playerInput, context);
        break;
      case AnalysisMode.KEYWORD_ONLY:
        result = await this.analyzeWithKeywords(playerInput);
        break;
      case AnalysisMode.HYBRID:
      default:
        result = await this.analyzeHybrid(playerInput, context);
        break;
    }

    // 缓存动态分析结果到多个缓存层
    if (result.length > 0) {
      // 添加到精确LRU缓存
      if (AI_ANALYSIS_CONFIG.cache.enabled) {
        const cacheKey = AIPromptTemplates.generateCacheKey(playerInput, context);
        this.analysisCache.set(cacheKey, result);
      }

      // 添加到语义缓存（用于未来的相似查询）
      if (this.semanticCacheEnabled) {
        try {
          await this.semanticCache.add(playerInput, result, context);
          console.log('🧠 [DialogueBehaviorAnalyzer] 结果已添加到语义缓存');
        } catch (error) {
          console.warn('⚠️ [DialogueBehaviorAnalyzer] 添加到语义缓存失败:', error);
        }
      }
    }

    console.log(`✅ [DialogueBehaviorAnalyzer] 分析完成，发现 ${result.length} 个属性变化:`, result);
    return result;
  }

  /**
   * AI驱动的行为分析
   */
  private async analyzeWithAI(playerInput: string, context?: string): Promise<AttributeChange[]> {
    if (!this.aiServiceManager) {
      console.warn('⚠️ [DialogueBehaviorAnalyzer] AI服务管理器未注入，降级到关键词匹配');
      return this.analyzeWithKeywords(playerInput);
    }

    try {
      console.log('🤖 [DialogueBehaviorAnalyzer] 使用AI分析...');

      const prompt = AIPromptTemplates.buildBehaviorAnalysisPrompt(playerInput, context);
      const messages: AIMessage[] = [
        { role: 'user', content: prompt }
      ];

      const startTime = Date.now();
      const response: AIResponse = await this.aiServiceManager.generateResponse(
        messages,
        AI_ANALYSIS_CONFIG.defaultGenerationConfig
      );
      const analysisTime = Date.now() - startTime;

      console.log(`⏱️ [DialogueBehaviorAnalyzer] AI分析耗时: ${analysisTime}ms`);

      // 验证和解析AI响应
      const aiResult = AIPromptTemplates.validateAIResponse(response.content);
      if (!aiResult) {
        console.warn('⚠️ [DialogueBehaviorAnalyzer] AI响应格式无效，降级到关键词匹配');
        return this.analyzeWithKeywords(playerInput);
      }

      // 转换AI结果为AttributeChange格式（使用本地映射配置）
      const attributeChanges: AttributeChange[] = [];
      for (const behavior of aiResult.behaviors) {
        if (behavior.confidence >= AI_ANALYSIS_CONFIG.quality.minConfidence) {
          // 使用本地映射配置计算属性效果
          const effects = calculateAttributeEffects(
            behavior.type as BehaviorType, // 类型已在验证中确认
            behavior.intensity
          );

          for (const [attribute, change] of Object.entries(effects)) {
            if (change !== 0) {
              attributeChanges.push({
                attribute: attribute as keyof CoreAttributes_RPG,
                change: change,
                reason: `${behavior.type}(强度${behavior.intensity}): ${behavior.description} (置信度: ${Math.round(behavior.confidence * 100)}%)`
              });
            }
          }
        }
      }

      console.log(`🎯 [DialogueBehaviorAnalyzer] AI分析识别出 ${attributeChanges.length} 个属性变化`);
      return attributeChanges;

    } catch (error) {
      console.error('❌ [DialogueBehaviorAnalyzer] AI分析失败:', error);
      console.log('🔄 [DialogueBehaviorAnalyzer] 降级到关键词匹配');
      return this.analyzeWithKeywords(playerInput);
    }
  }

  /**
   * 关键词匹配分析（原有方法重构）
   */
  private async analyzeWithKeywords(playerInput: string): Promise<AttributeChange[]> {
    console.log('🔤 [DialogueBehaviorAnalyzer] 使用关键词匹配分析...');
    console.log(`📝 [DialogueBehaviorAnalyzer] 可用规则数量: ${this.behaviorRules.length}`);

    // 输出所有可用规则用于调试
    if (this.behaviorRules.length > 0) {
      console.log('📋 [DialogueBehaviorAnalyzer] 可用行为规则:');
      this.behaviorRules.forEach((rule, index) => {
        console.log(`  ${index + 1}. ${rule.behavior}: [${rule.keywords.slice(0, 3).join(', ')}${rule.keywords.length > 3 ? '...' : ''}]`);
      });
    }

    const matchedRules = this.findMatchingRules(playerInput);

    if (matchedRules.length === 0) {
      console.log('📝 [DialogueBehaviorAnalyzer] 未找到匹配的行为规则');
      return [];
    }

    console.log(`✅ [DialogueBehaviorAnalyzer] 找到 ${matchedRules.length} 个匹配规则`);

    // 转换为AttributeChange格式
    const attributeChanges: AttributeChange[] = [];

    for (const rule of matchedRules) {
      for (const [attribute, change] of Object.entries(rule.effects)) {
        if (change !== 0) {
          attributeChanges.push({
            attribute: attribute as keyof CoreAttributes_RPG,
            change: change,
            reason: `${rule.behavior}: ${rule.description}`
          });
        }
      }
    }

    console.log(`🎯 [DialogueBehaviorAnalyzer] 生成 ${attributeChanges.length} 个属性变化`);
    return attributeChanges;
  }

  /**
   * 混合分析模式（AI优先，关键词作为fallback）
   */
  private async analyzeHybrid(playerInput: string, context?: string): Promise<AttributeChange[]> {
    console.log('🔀 [DialogueBehaviorAnalyzer] 使用混合分析模式...');

    // 首先尝试AI分析
    const aiResult = await this.analyzeWithAI(playerInput, context);

    // 如果AI分析有结果且置信度足够，直接返回
    if (aiResult.length > 0) {
      return aiResult;
    }

    // 否则使用关键词匹配作为fallback
    console.log('🔄 [DialogueBehaviorAnalyzer] AI分析无结果，使用关键词匹配');
    return this.analyzeWithKeywords(playerInput);
  }

  /**
   * 查找匹配的行为规则
   */
  private findMatchingRules(input: string): BehaviorMappingRule[] {
    const matchedRules: BehaviorMappingRule[] = [];
    const normalizedInput = input.toLowerCase();

    console.log(`🔍 [DialogueBehaviorAnalyzer] 分析输入: "${input.substring(0, 50)}..." (规则数量: ${this.behaviorRules.length})`);

    for (const rule of this.behaviorRules) {
      const matchedKeywords: string[] = [];

      for (const keyword of rule.keywords) {
        const normalizedKeyword = keyword.toLowerCase();
        if (normalizedInput.includes(normalizedKeyword)) {
          matchedKeywords.push(keyword);
        }
      }

      if (matchedKeywords.length > 0) {
        console.log(`🎯 [DialogueBehaviorAnalyzer] 匹配规则: ${rule.behavior} (关键词: ${matchedKeywords.join(', ')})`);
        matchedRules.push(rule);
      }
    }

    if (matchedRules.length === 0) {
      console.log(`❌ [DialogueBehaviorAnalyzer] 未找到匹配规则，输入: "${normalizedInput.substring(0, 30)}..."`);
    }

    return matchedRules;
  }

  /**
   * 获取所有可用的行为规则（用于调试）
   */
  public getBehaviorRules(): BehaviorMappingRule[] {
    return [...this.behaviorRules];
  }

  /**
   * 添加自定义行为规则
   */
  public addBehaviorRule(rule: BehaviorMappingRule): void {
    this.behaviorRules.push(rule);
    console.log(`➕ [DialogueBehaviorAnalyzer] 添加新规则: ${rule.behavior}`);
  }

  /**
   * 获取分析统计信息
   */
  public getAnalysisStats(): {
    cacheSize: number;
    analysisMode: AnalysisMode;
    rulesCount: number;
    aiServiceAvailable: boolean;
  } {
    return {
      cacheSize: this.analysisCache.size(),
      analysisMode: this.analysisMode,
      rulesCount: this.behaviorRules.length,
      aiServiceAvailable: !!this.aiServiceManager
    };
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.analysisCache.clear();
    console.log('🗑️ [DialogueBehaviorAnalyzer] 缓存已清空');
  }

  /**
   * 批量分析（用于性能优化）
   */
  public async batchAnalyze(inputs: { text: string; context?: string }[]): Promise<AttributeChange[][]> {
    console.log(`📦 [DialogueBehaviorAnalyzer] 批量分析 ${inputs.length} 个输入`);

    const results: AttributeChange[][] = [];

    // 简单的批量处理，可以后续优化为真正的批量AI调用
    for (const input of inputs) {
      const result = await this.analyze(input.text, input.context);
      results.push(result);
    }

    return results;
  }

  /**
   * 获取预烘焙性能统计
   */
  public getPrebakedPerformanceStats(): {
    enabled: boolean;
    hitRate: number;
    totalQueries: number;
    averageQueryTime: number;
    dataSize: number;
    isCompatible: boolean;
  } {
    const metrics = this.prebakedLoader.getPerformanceMetrics();
    const versionInfo = this.prebakedLoader.getVersionInfo();

    return {
      enabled: this.prebakedEnabled,
      hitRate: metrics.hitRate,
      totalQueries: metrics.totalQueries,
      averageQueryTime: metrics.averageQueryTime,
      dataSize: metrics.dataSize,
      isCompatible: versionInfo.isCompatible
    };
  }

  /**
   * 检查特定文本是否有预烘焙结果
   */
  public hasPrebakedResult(text: string, context?: string): boolean {
    return this.prebakedEnabled && this.prebakedLoader.hasResult(text, context);
  }

  /**
   * 获取所有可用的预烘焙选择（调试用）
   */
  public getAvailablePrebakedChoices(): string[] {
    if (!this.prebakedEnabled) {
      return [];
    }
    return this.prebakedLoader.getAvailableChoices();
  }

  /**
   * 获取语义缓存性能统计
   */
  public getSemanticCacheStats(): {
    enabled: boolean;
    totalQueries: number;
    exactHits: number;
    semanticHits: number;
    fallbackHits: number;
    hitRate: number;
    averageQueryTime: number;
    cacheSize: number;
  } {
    if (!this.semanticCacheEnabled) {
      return {
        enabled: false,
        totalQueries: 0,
        exactHits: 0,
        semanticHits: 0,
        fallbackHits: 0,
        hitRate: 0,
        averageQueryTime: 0,
        cacheSize: 0
      };
    }

    const stats = this.semanticCache.getStats();
    return {
      enabled: this.semanticCacheEnabled,
      totalQueries: stats.totalQueries,
      exactHits: stats.exactHits,
      semanticHits: stats.semanticHits,
      fallbackHits: stats.fallbackHits,
      hitRate: stats.hitRate,
      averageQueryTime: stats.averageQueryTime,
      cacheSize: stats.cacheSize
    };
  }

  /**
   * 获取语义缓存中的所有文本（调试用）
   */
  public getSemanticCachedTexts(): string[] {
    if (!this.semanticCacheEnabled) {
      return [];
    }
    return this.semanticCache.getCachedTexts();
  }

  /**
   * 清空所有缓存
   */
  public clearAllCaches(): void {
    this.analysisCache.clear();

    if (this.semanticCacheEnabled) {
      this.semanticCache.clear();
    }

    console.log('🗑️ [DialogueBehaviorAnalyzer] 所有缓存已清空');
  }

  /**
   * 获取综合缓存统计
   */
  public getComprehensiveCacheStats(): {
    prebakedCache: any;
    lruCache: { size: number };
    semanticCache: any;
    totalCacheSize: number;
  } {
    const prebakedStats = this.getPrebakedPerformanceStats();
    const semanticStats = this.getSemanticCacheStats();

    return {
      prebakedCache: prebakedStats,
      lruCache: { size: this.analysisCache.size() },
      semanticCache: semanticStats,
      totalCacheSize: prebakedStats.dataSize + this.analysisCache.size() + semanticStats.cacheSize
    };
  }
}

/**
 * 全局单例实例
 */
export const dialogueBehaviorAnalyzer = new DialogueBehaviorAnalyzer();
