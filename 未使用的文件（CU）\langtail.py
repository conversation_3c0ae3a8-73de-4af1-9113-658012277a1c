import time
import re
import uuid
import random
from DrissionPage.common import Settings
from DrissionPage import ChromiumOptions, Chromium
from temp_mail.mail_cx import MailCX
import asyncio

repeat = 3
keys = []


async def main():
    for i in range(0, repeat):
        tempmail = MailCX()
        co = ChromiumOptions()
        co.auto_port()
        Settings.set_language("zh_cn")
        tab = Chromium(addr_or_opts=co).latest_tab
        tab.get("https://app.langtail.com/sign-up")
        for i in range(0, 10):
            tab.actions.move(random.random()*100, random.random()*100)
        time.sleep(5)
        accept_all = tab.ele("@data-role=all", timeout=3)
        if accept_all:
            accept_all.click()
        first_name = tab.ele("#firstName")
        first_name.input("Yang")
        last_name = tab.ele("#lastName")
        last_name.input("Zhi")
        email = tab.ele("#email")
        email.input(await tempmail.get_email_address())
        submit = tab.ele("@name=submit")
        submit.click()
        code = ""
        max_wait_time = 30  # seconds, 增加等待时间以防邮件延迟
        start_time = time.time()
        attempts = 0
        time.sleep(3)
        print("Waiting for verification email...")
        while code == "" and (time.time() - start_time) < max_wait_time:
            attempts += 1
            print(f"Attempt {attempts}: Checking for emails...")
            try:
                emails = await tempmail.get_email_list()
                print(f"Received {len(emails)} email(s).")
                for mail in emails:
                    # 从 body_plain 或 body_html 中提取 6 位验证码
                    print(mail)
                    body = mail.subject
                    match = re.search(r"\b(\d{6})\b", body)  # 查找 6 位数字
                    if match:
                        code = match.group(1)
                        print(f"Extracted verification code: {code}")
                        break  # 找到验证码，退出内层循环

            except Exception as e:
                print(f"An unexpected error occurred: {e}")

            if not code:  # Only sleep if code not found yet
                wait_time = 3  # 检查间隔
                print(f"Waiting {wait_time} seconds before next check...")
                time.sleep(wait_time)

        if not code:
            print(
                f"Timeout: Did not find verification code within {max_wait_time} seconds."
            )
            # 可以选择退出或抛出异常
            exit()

        code_input = tab.ele("@name=codeInput")
        code_input.input(code)
        tab.wait.load_start()
        workspace = tab.ele("@placeholder=Workspace name")
        workspace.input(uuid.uuid4().hex)
        create = tab.ele("@type=submit")
        create.click()
        time.sleep(3)
        current_url = tab.url
        last_slash_index = current_url.rfind("/")
        if last_slash_index != -1:
            base_url = current_url[:last_slash_index]
            secrets_url = base_url + "/secrets"
            print(f"原始 URL: {current_url}")
            print(f"修改后的 URL: {secrets_url}")
            # 如果需要，可以在这里导航到新的 URL
            tab.get(secrets_url)
        tab.wait.load_start()
        create_key = tab.ele("text=New API Key")
        create_key.click()
        create_submit = tab.ele("@type=submit")
        create_submit.click()
        key = tab.ele("@readonly").attr("value")
        print(key)
        keys.append(key)
        with open("key.txt", "w") as f:
            for i in keys:
                f.write(f"{i}\n")
        tab.close()
asyncio.run(main())