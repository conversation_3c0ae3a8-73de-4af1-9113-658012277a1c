<!DOCTYPE html>
<!-- saved from url=(0012)about:srcdoc -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></head><body><input type="hidden" id="nonce" value="**************.**************.***************.************"><script nonce="">var g=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},h=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},k=h(this),l=function(a,b){if(b)a:{var c=k;a=a.split(".");for(var d=0;d<a.length-
1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&g(c,a,{configurable:!0,writable:!0,value:b})}},m=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};l("globalThis",function(a){return a||k});/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var n=globalThis.trustedTypes,p;function q(){var a=null;if(!n)return a;try{var b=function(c){return c};a=n.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};var r=function(a){this.g=a};r.prototype.toString=function(){return this.g+""};function t(a){var b;p===void 0&&(p=q());a=(b=p)?b.createScript(a):a;return new r(a)}function u(a){if(a instanceof r)return a.g;throw Error("");};function v(a,b){a.textContent=u(b);var c;b=a.ownerDocument;b=b===void 0?document:b;var d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,"script[nonce]");(c=b==null?"":b.nonce||b.getAttribute("nonce")||"")&&a.setAttribute("nonce",c)};var w=function(a){var b=m.apply(1,arguments),c,d;"evalCspCompatiblyData"in window||(window.evalCspCompatiblyData={index:0});var e=window.evalCspCompatiblyData.index++;window.evalCspCompatiblyData[e]={args:b,callback:function(f,x){c=f;d=x}};b=document.createElement("script");b.async=!1;b.setAttribute("data-index",e);e='"use strict";\v(function(){var idx=parseInt((document.currentScript||Array.prototype.slice.call(document.querySelectorAll("head script[data-index]"),-1)[0]).getAttribute("data-index"),10),curEvalData=evalCspCompatiblyData[idx];delete evalCspCompatiblyData[idx];(function(){var callback=curEvalData.callback;try{callback(true,('+
String(u(a))+"\n));}catch(ex){callback(false, ex);}}).apply(this, curEvalData.args);})()";e=t(e);v(b,e);e=function(f){c=!1;d=f.error||f};window.addEventListener("error",e);document.head.appendChild(b);window.removeEventListener("error",e);document.head.removeChild(b);if(!c)throw d;return d};var y=document.getElementById("nonce"),z=y?y.value:(new URL(location.href)).searchParams.get("nonce");function A(){if(self.origin)return self.origin=="null";if(location.host!="")return!1;try{return window.parent.escape(""),!1}catch(a){return!0}}
(function(){if(!A())throw"sandboxing error";var a=new MessageChannel;a.port1.onmessage=function(c){try{var d=w.apply(null,[t("(function("+c.data.paramNames.join(", ")+") {\n"+c.data.code+"\n}).apply(null, arguments)")].concat(c.data.values));c.ports[0].postMessage({result:d})}catch(e){c.ports[0].postMessage({error:e.toString()})}};var b="*";location.protocol=="chrome-extension:"&&(b=location.origin);window.parent.postMessage(z,b,[a.port2])})();
</script></body></html>