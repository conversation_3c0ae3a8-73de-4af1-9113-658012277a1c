<!DOCTYPE html>
<!-- saved from url=(0083)https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&gender=female -->
<html lang="en" class="fontawesome-i2svg-active fontawesome-i2svg-complete" imt-state="original" imt-trans-position="after"><plasmo-csui id="codebox-csui"><template shadowrootmode="open"><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-overlay-0" class="plasmo-csui-container" style="display: flex; position: absolute; top: 0px; left: 0px;"></div></div></template></plasmo-csui><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    	<title>Fake Name Generator | FauxID.com</title>
	<meta name="Description" content="Free Fake Name Generator. Create Fake Names, Addresses, Credit Cards, SSNs and Phone Numbers. A Better Fake Name Generator. Fake names for the US, UK, India, China, and more! FakeNameGenerator">
	
    <!-- Google Tag Manager -->
    <script async="" src="./Fake Name Generator _ FauxID.com_files/gtm.js.下载"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-KMHKK5M');</script>
    <!-- End Google Tag Manager -->

    <!-- CSS -->
    <style type="text/css">svg:not(:root).svg-inline--fa{overflow:visible}.svg-inline--fa{display:inline-block;font-size:inherit;height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-lg{vertical-align:-.225em}.svg-inline--fa.fa-w-1{width:.0625em}.svg-inline--fa.fa-w-2{width:.125em}.svg-inline--fa.fa-w-3{width:.1875em}.svg-inline--fa.fa-w-4{width:.25em}.svg-inline--fa.fa-w-5{width:.3125em}.svg-inline--fa.fa-w-6{width:.375em}.svg-inline--fa.fa-w-7{width:.4375em}.svg-inline--fa.fa-w-8{width:.5em}.svg-inline--fa.fa-w-9{width:.5625em}.svg-inline--fa.fa-w-10{width:.625em}.svg-inline--fa.fa-w-11{width:.6875em}.svg-inline--fa.fa-w-12{width:.75em}.svg-inline--fa.fa-w-13{width:.8125em}.svg-inline--fa.fa-w-14{width:.875em}.svg-inline--fa.fa-w-15{width:.9375em}.svg-inline--fa.fa-w-16{width:1em}.svg-inline--fa.fa-w-17{width:1.0625em}.svg-inline--fa.fa-w-18{width:1.125em}.svg-inline--fa.fa-w-19{width:1.1875em}.svg-inline--fa.fa-w-20{width:1.25em}.svg-inline--fa.fa-pull-left{margin-right:.3em;width:auto}.svg-inline--fa.fa-pull-right{margin-left:.3em;width:auto}.svg-inline--fa.fa-border{height:1.5em}.svg-inline--fa.fa-li{width:2em}.svg-inline--fa.fa-fw{width:1.25em}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers-text{left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-counter{background-color:#ff253a;border-radius:1em;color:#fff;height:1.5em;line-height:1;max-width:5em;min-width:1.5em;overflow:hidden;padding:.25em;right:0;text-overflow:ellipsis;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-bottom-right{bottom:0;right:0;top:auto;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:bottom right;transform-origin:bottom right}.fa-layers-bottom-left{bottom:0;left:0;right:auto;top:auto;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:bottom left;transform-origin:bottom left}.fa-layers-top-right{right:0;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-top-left{left:0;right:auto;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top left;transform-origin:top left}.fa-lg{font-size:1.33333em;line-height:.75em;vertical-align:-.0667em}.fa-xs{font-size:.75em}.fa-sm{font-size:.875em}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:2.5em;padding-left:0}.fa-ul>li{position:relative}.fa-li{left:-2em;position:absolute;text-align:center;width:2em;line-height:inherit}.fa-border{border:solid .08em #eee;border-radius:.1em;padding:.2em .25em .15em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left,.fab.fa-pull-left,.fal.fa-pull-left,.far.fa-pull-left,.fas.fa-pull-left{margin-right:.3em}.fa.fa-pull-right,.fab.fa-pull-right,.fal.fa-pull-right,.far.fa-pull-right,.fas.fa-pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.fa-rotate-90{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-webkit-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-webkit-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{-webkit-transform:scale(1,-1);transform:scale(1,-1)}.fa-flip-horizontal.fa-flip-vertical{-webkit-transform:scale(-1,-1);transform:scale(-1,-1)}:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-rotate-90{-webkit-filter:none;filter:none}.fa-stack{display:inline-block;height:2em;position:relative;width:2em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.svg-inline--fa.fa-stack-1x{height:1em;width:1em}.svg-inline--fa.fa-stack-2x{height:2em;width:2em}.fa-inverse{color:#fff}.sr-only{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.sr-only-focusable:active,.sr-only-focusable:focus{clip:auto;height:auto;margin:0;overflow:visible;position:static;width:auto}</style><link rel="stylesheet" href="./Fake Name Generator _ FauxID.com_files/bootstrap.min.css">
    <link rel="stylesheet" href="./Fake Name Generator _ FauxID.com_files/jquery.selectBoxIt.css" integrity="sha256-nZrlwceVn3A7PabpGVuo/Lrq1nxTpJFhTjJsPQB/I9c=" crossorigin="anonymous">

    <script defer="" src="./Fake Name Generator _ FauxID.com_files/solid.js.下载" integrity="sha384-+Ga2s7YBbhOD6nie0DzrZpJes+b2K1xkpKxTFFcx59QmVPaSA8c7pycsNaFwUK6l" crossorigin="anonymous"></script>
    <script defer="" src="./Fake Name Generator _ FauxID.com_files/regular.js.下载" integrity="sha384-t7yHmUlwFrLxHXNLstawVRBMeSLcXTbQ5hsd0ifzwGtN7ZF7RZ8ppM7Ldinuoiif" crossorigin="anonymous"></script>
    <script defer="" src="./Fake Name Generator _ FauxID.com_files/fontawesome.js.下载" integrity="sha384-7ox8Q2yzO/uWircfojVuCQOZl+ZZBg2D2J5nkpLqzH1HY0C1dHlTKIbpRz/LG23c" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="./Fake Name Generator _ FauxID.com_files/app.css">

    <!-- Favicon -->
	<link rel="apple-touch-icon" sizes="76x76" href="https://fauxid.com/apple-touch-icon.png">
	<link id="favicon" rel="icon" type="image/png" sizes="32x32" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIIAAACCCAIAAAAFYYeqAAAABnRSTlMAAAAAAABupgeRAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABc0lEQVR4nO3csQ1CMRAFQT4ipBnqI6Y+mqEAeoAXjGCngJOllR2dfJym7q/bduDj+lyNks92Xg3KN8pAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEI75Lls+0G0glIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFwmU/8k08Kt2frNhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyE/toj7Lf2ttg9u60eJUIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBsJ+eXK4Zyfrr70fVAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlILwB+ncX5X49rV4AAAAASUVORK5CYII=">
	<link rel="manifest" href="https://fauxid.com/site.webmanifest">
	<link rel="mask-icon" href="https://fauxid.com/safari-pinned-tab.svg" color="#b00e0e">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#B00E0E">

    <meta name="_token" content="jY4ndNv8aQAUN8ZFLTHGIsPXvwEiRlXL4iU5mlAy">
<style id="VMst0.9777436730319788">
            button.script-button {
                position: fixed;
                bottom: 50%;
                right: -50px;
                transform: translateY(50%);
                padding: 10px;
                font-size: 16px;
                border: none;
                border-radius: 4px;
                background-color: #1e90ff;
                color: #ffffff;
                cursor: pointer;
                transition: right 0.3s;
                z-index: 9999999999999999; /* 设置一个较高的 z-index 值 */
            }
            div.info-container {
                display: none;
                position: fixed;
                top: 10%;
                right: 100px;
                width: 650px;
                padding: 12px;
                background-color: #ffffff;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
                border-radius: 4px;
                opacity: 0;
                transition: opacity 0.3s;
                z-index: 9999;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            ul.info-list {
                list-style: none;
                margin: 0;
                padding: 0;
            }
            li.info-item {
                margin-bottom: 15px;
                padding: 12px;
                padding-bottom: 22px;
                display: flex;
                flex-direction: column;
                border: 1px solid #1e90ff;
                border-radius: 5px;
            }
            .div.script-container {
                display: flex;
                flex-direction: column;
            }
            a.script-link {
                font-size: 18px !important;
                font-weight: bold !important;
                margin-bottom: 5px !important;
                color: #1e90ff !important;
            }
            p.script-description {
                color: black !important;
                margin-top: 2px;
                margin-bottom: 5px;
                font-size: 16px;
            }
            div.details-container {
                font-size: 15px;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
                margin-bottom: 15px;
            }
            span.script-details {
                font: !important
                color: black !important;
                flex-grow: 1 !important;
                text-align: center !important;
                border: 1px solid #1e90ff !important;
                border-radius: 5px !important;
                margin: 4px !important;
            }
            div.table-header {
                color: #1e90ff !important;
                font-size: 25px;
                font-weight: bold;
            }
            input.script-search-input {
                width: 96% !important;
                padding: 10px !important;
                font-size: 18px !important;
                border: 1px solid #1e90ff !important;
                border-radius: 4px !important;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3) !important;
                margin-bottom: 15px !important;
                margin-top: 20px !important;
            }
            a.install-button {
                font-size: 20px;
                background-color: green;
                color: white;
                padding: 12px;
            }
            button.to-greasyfork {
                position: absolute; 
                top: 12px; 
                right: 12px;
                border-radius: 4px;
                padding: 8px;
                font-size: 16px;
                border: none;
                background-color: #1e90ff;
                color: #ffffff;
                cursor: pointer;
            }
            span.match-count {
                background-color: #1e90ff;
                font-size: 25px;
                font-weight: bold;
                color: white;
                padding: 6px;
                position: absolute;
                right: 50%;
                border-radius: 12px;
                top: 10px;
            }
            div.wait-loading {
                font-size: 20px;
                font-weight: bold;
                color: #1e90ff;
                animation: blink 1s infinite;
            }
            @keyframes fadeInOut {
                0% {
                    opacity: 0;
                }
                50% {
                    opacity: 1;
                }
                100% {
                    opacity: 0;
                }
            }
            @keyframes blink {
                0%, 100% {
                    opacity: 0;
                }
                50% {
                    opacity: 1;
                }
            }
            button.load-more {
                border-radius: 4px;
                padding: 8px;
                font-size: 16px;
                border: none;
                background-color: #1e90ff;
                color: #ffffff;
                cursor: pointer;
                position: relative;
                bottom: 5px;
                left: 50%;
                transform: translateX(-50%);
            }
            button.load-more:disabled {
                background-color: #cccccc;
                cursor: not-allowed;
            }
        </style><style id="VMst0.49260003975977473">
        .img-upload-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 300px;
            font-size: 14px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(20px);
        }
        .img-upload-notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        .img-upload-success {
            background-color: #4caf50;
            color: white;
        }
        .img-upload-error {
            background-color: #f44336;
            color: white;
        }
        .img-upload-info {
            background-color: #2196F3;
            color: white;
        }
        .img-upload-close {
            float: right;
            margin-left: 10px;
            cursor: pointer;
            opacity: 0.8;
        }
        .img-upload-close:hover {
            opacity: 1;
        }

        .img-upload-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .img-upload-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }
        .img-upload-modal h2 {
            margin: 0 0 20px;
            color: #333;
            font-size: 18px;
        }
        .img-upload-form-group {
            margin-bottom: 20px;
        }
        .img-upload-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        .img-upload-help-text {
            margin-top: 4px;
            color: #666;
            font-size: 12px;
        }
        .img-upload-form-group input[type="text"],
        .img-upload-form-group input[type="number"],
        .img-upload-form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .img-upload-form-group textarea {
            min-height: 100px;
            font-family: monospace;
        }
        .img-upload-form-group input[type="checkbox"] {
            margin-right: 8px;
        }
        .img-upload-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .img-upload-button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .img-upload-button-primary {
            background: #2196F3;
            color: white;
        }
        .img-upload-button-secondary {
            background: #e0e0e0;
            color: #333;
        }
        .img-upload-button:hover {
            opacity: 0.9;
        }
        .img-upload-error {
            color: #ffffff;
            font-size: 12px;
            margin-top: 4px;
        }
        .img-upload-info-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #2196F3;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            margin-left: 4px;
            cursor: help;
        }
        .img-upload-form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
        .img-upload-input-group {
            display: flex;
            align-items: center;
        }
        .img-upload-input-group input {
            flex: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        .img-upload-input-group-text {
            padding: 8px 12px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-left: none;
            border-radius: 0 4px 4px 0;
            color: #666;
        }
        .img-upload-checkbox-label {
            display: flex !important;
            align-items: center;
            font-weight: normal !important;
        }
        .img-upload-checkbox-label input {
            margin-right: 8px;
        }

        .img-upload-dropzone {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(33, 150, 243, 0.2);
            border: 3px dashed #2196F3;
            z-index: 9998;
            box-sizing: border-box;
        }
        .img-upload-dropzone.active {
            display: block;
        }
        .img-upload-dropzone-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px 40px;
            border-radius: 8px;
            font-size: 18px;
            color: #2196F3;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style><style lucid-mode-video="">video { filter: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg"> <filter id="sharpen"> <feConvolveMatrix order="3" preserveAlpha="true" kernelMatrix="1 -1 1 -1 -1 -1 1 -1 1"/> </filter> </svg>#sharpen'); }</style><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border: 1px solid #888;
  border-radius: 10px;
  width: 80%;
  max-width: 270px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  word-break: break-all;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 16px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style><style data-id="immersive-translate-default-injected-css">:root {
  --immersive-translate-theme-underline-borderColor: #72ece9;
  --immersive-translate-theme-nativeUnderline-borderColor: #72ece9;
  --immersive-translate-theme-nativeDashed-borderColor: #72ece9;
  --immersive-translate-theme-nativeDotted-borderColor: #72ece9;
  --immersive-translate-theme-highlight-backgroundColor: #ffff00;
  --immersive-translate-theme-dashed-borderColor: #59c1bd;
  --immersive-translate-theme-blockquote-borderColor: #cc3355;
  --immersive-translate-theme-thinDashed-borderColor: #ff374f;
  --immersive-translate-theme-dashedBorder-borderColor: #94a3b8;
  --immersive-translate-theme-dashedBorder-borderRadius: 0;
  --immersive-translate-theme-solidBorder-borderColor: #94a3b8;
  --immersive-translate-theme-solidBorder-borderRadius: 0;
  --immersive-translate-theme-dotted-borderColor: #94a3b8;
  --immersive-translate-theme-wavy-borderColor: #72ece9;
  --immersive-translate-theme-dividingLine-borderColor: #94a3b8;
  --immersive-translate-theme-grey-textColor: #2f4f4f;
  --immersive-translate-theme-marker-backgroundColor: #fbda41;
  --immersive-translate-theme-marker-backgroundColor-rgb: 251, 218, 65;
  --immersive-translate-theme-marker2-backgroundColor: #ffff00;
  --immersive-translate-theme-background-backgroundColor: #dbafaf;
  --immersive-translate-theme-background-backgroundColor-rgb: 219, 175, 175;
  --immersive-translate-theme-background-backgroundOpacity: 12;
  --immersive-translate-theme-opacity-opacity: 10;
}

[imt-state="dual"] .immersive-translate-target-translation-pre-whitespace {
  white-space: pre-wrap !important;
}

[imt-state="dual"] .immersive-translate-pdf-target-container {
  position: absolute;
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    sans-serif;
  top: 0;
  width: 600px;
  height: 100%;
  z-index: 2;
  line-height: 1.3;
  font-size: 16px;
}
[imt-state="dual"] .immersive-translate-target-wrapper[dir="rtl"] {
  text-align: right;
}

[imt-state="dual"]
  .immersive-translate-pdf-target-container
  .immersive-translate-target-wrapper {
  color: rgb(0, 0, 0);
  white-space: normal;
  position: absolute;
}

[imt-state="dual"]
  .immersive-translate-pdf-target-container
  .immersive-translate-target-wrapper
  font {
  color: inherit;
  white-space: inherit;
  position: unset;
}

[imt-state="translation"] .immersive-translate-target-wrapper > br {
  display: none;
}

[imt-state="translation"]
  .immersive-translate-target-translation-block-wrapper {
  margin: 0 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-block-wrapper {
  margin: 8px 0 !important;
  display: inline-block;
}

[imt-trans-position="before"]
  .immersive-translate-target-translation-block-wrapper {
  display: block;
}

[imt-trans-position="before"]
  .immersive-translate-target-translation-block-wrapper {
  margin-top: 0 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-pdf-block-wrapper {
  margin: 0 !important;
  display: inline-block;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-grey-inner {
  color: var(--immersive-translate-theme-grey-textColor);
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-underline-inner {
  border-bottom: 1px solid
    var(--immersive-translate-theme-underline-borderColor) !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeUnderline-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeUnderline-borderColor
  ) !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-dashedBorder-borderRadius
  ) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-dashedBorder-borderRadius
  ) !important;
  padding: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-solidBorder-borderRadius
  ) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-solidBorder-borderRadius
  ) !important;
  padding: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeDashed-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeDashed-borderColor
  ) !important;
  text-decoration-style: dashed !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-thinDashed-inner {
  border-bottom: 1px dashed
    var(--immersive-translate-theme-thinDashed-borderColor) !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-dotted-inner {
  background-image: linear-gradient(
    to right,
    var(--immersive-translate-theme-dotted-borderColor) 30%,
    rgba(255, 255, 255, 0) 0%
  );
  background-position: bottom;
  background-size: 5px 1px;
  background-repeat: repeat-x;
  padding-bottom: 3px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeDotted-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeDotted-borderColor
  ) !important;
  text-decoration-style: dotted !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-wavy-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-wavy-borderColor
  ) !important;
  text-decoration-style: wavy !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-dashed-inner {
  background: linear-gradient(
      to right,
      var(--immersive-translate-theme-dashed-borderColor) 0%,
      var(--immersive-translate-theme-dashed-borderColor) 50%,
      transparent 50%,
      transparent 100%
    )
    repeat-x left bottom;
  background-size: 8px 2px;
  padding-bottom: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-dividingLine::before {
  content: "";
  display: block;
  max-width: 80px;
  width: 10%;
  border-top: 1px dashed
    var(--immersive-translate-theme-dividingLine-borderColor);
  padding-top: 8px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-dividingLine::before {
  content: "";
  border-left: 1px dashed
    var(--immersive-translate-theme-dividingLine-borderColor);
  max-height: 16px;
  height: 16px;
  padding-left: 8px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-highlight-inner {
  background: var(--immersive-translate-theme-highlight-backgroundColor);
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-marker {
  line-height: 1.5em;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-marker2-inner {
  font-weight: bold;
  text-shadow: 10px 0px 3px
      var(--immersive-translate-theme-marker2-backgroundColor),
    16px 3px 9px var(--immersive-translate-theme-marker2-backgroundColor),
    2px 0px 6px var(--immersive-translate-theme-marker2-backgroundColor),
    -12px 0px 12px var(--immersive-translate-theme-marker2-backgroundColor) !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-marker-inner {
  /* TODO: add more texture */
  background: linear-gradient(
    to right,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.1),
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 3%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 35%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 70%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.8) 95%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.3)
  );
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-weakening {
  opacity: 0.618 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-italic {
  font-style: italic !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-bold {
  font-weight: bold !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-paper {
  margin: 8px 0;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  padding: 16px 32px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-blockquote {
  border-left: 4px solid var(--immersive-translate-theme-blockquote-borderColor) !important;
  padding-left: 12px !important;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  display: inline-block;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-mask-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[data-immersive-translate-root-translation-theme="none"]
  .immersive-translate-target-translation-theme-mask-inner {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"]
  .immersive-translate-target-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

/* opacity theme start */

[imt-state="dual"] .immersive-translate-target-translation-theme-opacity-inner {
  filter: opacity(
    calc(var(--immersive-translate-theme-opacity-opacity) * 1%)
  ) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[data-immersive-translate-root-translation-theme="none"]
  .immersive-translate-target-translation-theme-opacity-inner {
  filter: none !important;
}
[data-immersive-translate-root-translation-theme="opacity"]
  .immersive-translate-target-inner,
[imt-state="dual"]
  .immersive-translate-target-translation-theme-opacity-inner:hover {
  filter: opacity(
    calc(var(--immersive-translate-theme-opacity-opacity) * 1%)
  ) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-opacity-inner:hover {
  filter: none !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-mask-inner:hover {
  filter: none !important;
}
[data-immersive-translate-root-translation-theme="opacity"]
  .immersive-translate-target-inner:hover {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"]
  .immersive-translate-target-inner:hover {
  filter: none !important;
}

/* opacity theme end */

/* background theme start */
[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-background {
  margin: 8px 0;
  background: rgba(
    var(--immersive-translate-theme-background-backgroundColor-rgb),
    calc(var(--immersive-translate-theme-background-backgroundOpacity) * 1%)
  );
  border-radius: 4px;
  box-shadow: unset !important;
  padding: 12px;
  display: inline-block;
}
[imt-state="dual"]
  .immersive-translate-target-translation-theme-background-inner {
  background: rgba(
    var(--immersive-translate-theme-background-backgroundColor-rgb),
    calc(var(--immersive-translate-theme-background-backgroundOpacity) * 1%)
  );
  padding-left: 6px;
  padding-right: 6px;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}
[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper
  .immersive-translate-target-translation-theme-background-inner {
  background: unset;
  padding-left: unset;
  padding-right: unset;
}
/* background theme end */

/* vertical css , please remain it in the last one. */
.immersive-translate-target-translation-vertical-block-wrapper {
  margin: 0px 8px !important;
}

.immersive-translate-text {
  font-size: 15px !important;
}

.immersive-translate-error-toast {
  position: fixed;
  top: 5%;
  z-index: 99999999;
  left: 0;
  right: 0;
  margin: auto;
  max-width: 300px;
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

@media all and (min-width: 750px) {
  .immersive-translate-error-toast {
    max-width: 400px;
  }
}

.immersive-translate-clickable-button {
  cursor: pointer;
}

.immersive-translate-help-button {
  cursor: pointer;
}

.immersive-translate-loading-text:before {
  content: "...";
}

/* dark mode for loading */

@media only screen and (prefers-color-scheme: dark) {
  .immersive-translate-loading {
    border: 2px rgba(255, 255, 255, 0.25) solid !important;
    border-top: 2px rgba(255, 255, 255, 1) solid !important;
  }
}

.immersive-translate-error-wrapper {
  position: relative;
  display: inline-flex;
  padding: 6px;
  margin: 0 12px;
  white-space: nowrap;
  font-size: 0.9em;
}
[lang="zh-CN"] .immersive-translate-error-wrapper {
  font-size: 0.75em;
}
[lang="zh-TW"] .immersive-translate-error-wrapper {
  font-size: 0.75em;
}

.immersive-translate-tooltip {
  position: relative;
  display: inline-flex;
  /* little indicater to indicate it's hoverable */
}

.immersive-translate-tooltip-content {
  /* here's the magic */
  position: absolute;
  z-index: 100000000000;

  left: 50%;
  bottom: 0;
  transform: translate(-50%, 110%);
  line-height: 1;
  /* and add a small left margin */

  /* basic styles */
  width: max-content;
  max-width: 250px;
  word-wrap: break-word;
  white-space: pre-line;
  padding: 10px;
  border-radius: 10px;
  background: #000c;
  color: #fff;
  text-align: center;
  font-size: 14px;
  display: none;
  /* hide by default */
}

.immersive-translate-tooltip:hover .immersive-translate-tooltip-content {
  display: inline-block;
}

.immersive-translate-tooltip:hover + .immersive-translate-tooltip-content {
  display: inline-block;
}

.immersive-translate-tooltip-content-table {
  left: unset !important;
  bottom: unset !important;
  transform: translate(-10%, 50%) !important;
}

.immersive-translate-tooltip:hover:before {
  display: inline-block;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--bg-2, #fff);
  font-size: 14px;
}
</style><style data-id="immersive-translate-user-custom-style">:root {

.immersive-translate-target-inner { font-family: inherit; }


.immersive-translate-target-inner { font-family: inherit; }
}
</style><style data-id="immersive-translate-dynamic-injected-css">.immersive-translate-target-wrapper[dir='rtl'] {text-align: right;}
.immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}
.immersive-translate-target-wrapper {word-break:break-word; user-select:text;}
[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block;}
[dir='rtl'] .immersive-translate-target-wrapper:not([dir]) {text-align:left;}
[imt-state=dual] .immersive-translate-target-translation-block-wrapper-theme-dividingLine::before {display:block;}
[imt-trans-position=before] .immersive-translate-target-translation-block-wrapper {display:block!important;}
</style></head>

	<body data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
      	<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KMHKK5M"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->

		<div class="navbar navbar-expand-lg navbar-dark bg-primary accent-underline">
	<div class="container">
		<a href="https://fauxid.com/" class="navbar-brand"><img src="./Fake Name Generator _ FauxID.com_files/logo_small_white.png" height="25" title="A Better Fake Name Generator"></a>
		<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
			<span class="navbar-toggler-icon"></span>
		</button>
		<div class="collapse navbar-collapse" id="navbarResponsive">
			<ul class="navbar-nav">
				<li class="nav-item">
					<a class="nav-link active" href="https://fauxid.com/fake-name-generator" title="Fake Name Generator">Fake Name</a>
				</li>


				<li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" data-toggle="dropdown" href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" id="tools">More Tools <span class="caret"></span></a>
					<div class="dropdown-menu" aria-labelledby="tools">
						<a class="dropdown-item" href="https://fauxid.com/tools" title="Fake Identity Generators">View all Tools</a>
						<div class="dropdown-divider"></div>
						<a class="dropdown-item" href="https://fauxid.com/tools/random-text-generator" title="Generate Random Lorem Ipsom Text">Random Text</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-email-list" title="Fake Email List Generator">Fake Email List</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-credit-card-generator" title="Fake Credit Card Generator">Fake Credit Card Generator</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-credit-card-validator" title="Fake Credit Card Validator">Fake Credit Card Validator</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-drivers-license" title="Fake Driver&#39;s License">Fake Driver's License</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-company-generator" title="Fake Company Generator">Fake Company</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-phone-number" title="Fake Phone Number Generator">Fake Phone Number</a>
						<a class="dropdown-item" href="https://fauxid.com/tools/fake-ssn-generator" title="Fake Social Security Number Generator">Fake SSN</a>

					</div>
				</li>
			</ul>

			<ul class="nav navbar-nav ml-auto">
				<li class="nav-item">
					<a class="nav-link" href="https://fauxid.com/about">About</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="https://fauxid.com/contact">Contact Us</a>
				</li>
			</ul>

		</div>
	</div>
</div>

		<div class="container" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

	            <div id="navbar-left" class="col-md-3 d-none d-lg-block" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	                <div id="side-nav" style="height: 100%;" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	                    	                        <!-- navbar side -->

<div class="card big-bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

		<div class="text-center">
			<a href="https://fauxid.com/" title="Fake Identity Generator"><img src="./Fake Name Generator _ FauxID.com_files/logo_small.png"></a>
		</div>

		<nav class="nav flex-column nav-pills" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

			<hr class="nav-divider" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

			<b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Tools:</b>

				<a class="nav-link active" href="https://fauxid.com/identity" title="Fake Identity Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Identity Generator</b></a>
				<a class="nav-link" href="https://fauxid.com/tools/random-text-generator" title="Generate Random Lorem Ipsom Text" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Random Text</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-email-list" title="Fake Email List Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Fake Email</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-credit-card-generator" title="Fake Credit Card Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Credit Card Generator</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-credit-card-validator" title="Fake Credit Card Validator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Credit Card Validator</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-drivers-license" title="Fake Driver&#39;s License" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Driver's License</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-company-generator" title="Fake Company Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Company</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-phone-number" title="Fake Phone Number Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Phone Number</a>
				<a class="nav-link" href="https://fauxid.com/tools/fake-ssn-generator" title="Fake Social Security Number Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Social Security Number</a>



			<hr class="nav-divider" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

				<a class="nav-link" href="https://fauxid.com/tools" title="All Fake Identity Tools" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><i data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">View All Tools »</i></a>

			<hr class="nav-divider" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

			<b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More:</b>

				<a class="nav-link" href="https://fauxid.com/about" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">About</a>
				<a class="nav-link" href="https://fauxid.com/contact" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Contact Us</a>

							<hr class="nav-divider" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Your Fake Identities:</b>

	
		<a class="nav-link" href="https://fauxid.com/identity/769a615f-c986-4e7a-8409-13132136046e" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
			<div class="color-box not-left-spaced right-spaced" style="background: #7bf431" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></div>
			Ms. Candace Carroll
		</a>

	
		<a class="nav-link" href="https://fauxid.com/identity/e7315a7d-0e15-467c-805f-248cf9820704" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
			<div class="color-box not-left-spaced right-spaced" style="background: #aefa1d" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></div>
			Madisyn Lebsack
		</a>

	
		<a class="nav-link" href="https://fauxid.com/identity/75f5a98a-3da2-42c9-a6a2-b06c667f5bb3" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
			<div class="color-box not-left-spaced right-spaced" style="background: #a923fb" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></div>
			Ana Cremin
		</a>

	
	<hr class="nav-divider" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	
	<a class="nav-link" href="https://fauxid.com/recent-identities" title="Recently generated fake identities" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><i data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">View All Identities »</i></a>			
		</nav>
	</div>
</div>
		<div class="sticky-top" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<script async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>
<!-- Faux ID - Side Nav - Responsive -->
<script>
document.write('\
<ins class="adsbygoogle"\
     style="display:block"\
     data-ad-client="ca-p'+'ub-05830478'+'430128'+'66"\
     data-ad-slot="8728336524"\
     data-ad-format="auto"></ins>');
(adsbygoogle = window.adsbygoogle || []).push({});
</script><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-0583047843012866" data-ad-slot="8728336524" data-ad-format="auto" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></ins>
	</div>
	<script>
		setTimeout(function() {
			$('#side-nav').css({height:'100%'});
		}, 3000);
	</script>
	                    	                </div>
	            </div>
				<div class="col-lg-9" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

					
												<script async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>
<!-- Faux ID - Main Unit - Responsive -->
<script>
document.write('\
<ins class="adsbygoogle"\
     style="display:block"\
     data-ad-client="ca-p'+'ub-05830478'+'430128'+'66"\
     data-ad-slot="3991015168"\
     data-ad-format="auto"></ins>');
(adsbygoogle = window.adsbygoogle || []).push({});
</script><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-0583047843012866" data-ad-slot="3991015168" data-ad-format="auto" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></ins>
					
					<div class="alert alert-danger text-center alert-dismissible fade show" role="alert" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">   <button type="button" class="close" data-dismiss="alert" aria-label="Close" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span aria-hidden="true" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">×</span></button>FauxID.com is a free service and <b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">we rely on ad revenue</b>. Please consider disabling your ad blocker on this site.</div><div class="content" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
													<h1 class="heading" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"> 	<img src="./Fake Name Generator _ FauxID.com_files/logo_no_com.png" title="FauxID.com logo" alt="FauxID.com" width="123.375" height="42" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			Fake U.S. Name Generator
	 </h1>
						
						


<p class="lead" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><strong data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">FauxID.com</strong> is a <u data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">free tool</u> to generate <i data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">full fake identities</i> with random First and Last Name, Address, Social Security Number, Credit Card, Phone Number, and more!</p>
<p class="lead" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Protect your privacy by only using your true identity when <u data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">absolutely</u> necessary.</p>

<div class="card bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<div class="card-body bg-light" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<form action="https://fauxid.com/identity" method="get" id="options-form" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="form-row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div class="form-group col-md-2 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<label for="gender" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Gender</label>
					<select id="gender" name="gender" class="form-control" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<option value="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Random</option>
						<option value="male" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Male</option>
						<option value="female" selected="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Female</option>
					</select>
				</div>
				<div class="form-group col-md-2 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<label for="age" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Age</label>
					<select id="age" name="age" class="form-control" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<option value="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Random</option>
						<option value="18-24" selected="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">18 to 24</option>
						<option value="25-34" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">25 to 34</option>
						<option value="35-44" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">35 to 44</option>
						<option value="45-54" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">45 to 54</option>
						<option value="55-64" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">55 to 64</option>
						<option value="65-74" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">65 to 74</option>
						<option value="75-85" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">75+</option>
					</select>
				</div>
				<div class="form-group col-md-3 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<label for="country" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Country</label>
					<select id="country" name="country" class="form-control" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<option value="united-states" selected="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">United States</option>
						<option value="united-kingdom" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">United Kingdom</option>
						<option value="france" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">France</option>
						<option value="germany" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Germany</option>
						<option value="china" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">China</option>
						<option value="india" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">India</option>
						<option value="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">---</option>
												<option value="argentina" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Argentina</option>
												<option value="armenia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Armenia</option>
												<option value="australia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Australia</option>
												<option value="austria" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Austria</option>
												<option value="bangladesh" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Bangladesh</option>
												<option value="belgium-dutch" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Belgium (Dutch)</option>
												<option value="belgium-french" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Belgium (French)</option>
												<option value="brazil" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Brazil</option>
												<option value="bulgaria" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Bulgaria</option>
												<option value="canada-english" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Canada (English)</option>
												<option value="canada-french" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Canada (French)</option>
												<option value="china" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">China</option>
												<option value="croatia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Croatia</option>
												<option value="cyprus" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Cyprus</option>
												<option value="czech-republic" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Czech Republic</option>
												<option value="denmark" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Denmark</option>
												<option value="finland" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Finland</option>
												<option value="france" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">France</option>
												<option value="georgia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Georgia</option>
												<option value="germany" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Germany</option>
												<option value="greece" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Greece</option>
												<option value="hong-kong" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Hong Kong (English)</option>
												<option value="hungary" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Hungary</option>
												<option value="iceland" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Iceland</option>
												<option value="india" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">India</option>
												<option value="indonesia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Indonesia</option>
												<option value="iran" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Iran</option>
												<option value="israel" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Israel</option>
												<option value="italy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Italy</option>
												<option value="japan" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Japan</option>
												<option value="jordan" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Jordan</option>
												<option value="kazakhstan" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Kazakhstan</option>
												<option value="latvia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Latvia</option>
												<option value="lithuania" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Lithuania</option>
												<option value="malaysia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Malaysia</option>
												<option value="moldova" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Moldova</option>
												<option value="mongolia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Mongolia</option>
												<option value="nepal" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Nepal</option>
												<option value="netherlands" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Netherlands</option>
												<option value="new-zealand" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">New Zealand</option>
												<option value="nigeria" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Nigeria</option>
												<option value="norway" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Norway</option>
												<option value="peru" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Peru</option>
												<option value="philippines" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Philippines</option>
												<option value="poland" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Poland</option>
												<option value="portugal" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Portugal</option>
												<option value="romania" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Romania</option>
												<option value="russia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Russia</option>
												<option value="saudi-arabia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Saudi Arabia</option>
												<option value="serbia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Serbia</option>
												<option value="singapore" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Singapore</option>
												<option value="slovakia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Slovakia</option>
												<option value="slovenia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Slovenia</option>
												<option value="south-africa" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">South Africa</option>
												<option value="south-korea" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">South Korea</option>
												<option value="spain" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Spain</option>
												<option value="sweden" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Sweden</option>
												<option value="switzerland-french" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Switzerland (French)</option>
												<option value="switzerland-german" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Switzerland (German)</option>
												<option value="switzerland-italian" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Switzerland (Italian)</option>
												<option value="taiwan" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Taiwan</option>
												<option value="thailand" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Thailand</option>
												<option value="uganda" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Uganda</option>
												<option value="ukraine" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Ukraine</option>
												<option value="united-kingdom" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">United Kingdom</option>
												<option value="united-states" selected="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">United States</option>
												<option value="venezuela-english" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Venezuela (English)</option>
												<option value="venezuela-spanish" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Venezuela (Spanish)</option>
												<option value="vietnam" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Vietnam</option>
											</select>
				</div>
				<div class="form-group col-md-3 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<label for="state" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">State</label>
					<select id="state" name="state" class="form-control" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<option value="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Random</option>
												<option value="alabama" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Alabama</option>
												<option value="alaska" selected="" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Alaska</option>
												<option value="arizona" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Arizona</option>
												<option value="arkansas" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Arkansas</option>
												<option value="california" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">California</option>
												<option value="colorado" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Colorado</option>
												<option value="connecticut" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Connecticut</option>
												<option value="delaware" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Delaware</option>
												<option value="district-of-columbia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">District Of Columbia</option>
												<option value="florida" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Florida</option>
												<option value="georgia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Georgia</option>
												<option value="hawaii" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Hawaii</option>
												<option value="idaho" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Idaho</option>
												<option value="illinois" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Illinois</option>
												<option value="indiana" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Indiana</option>
												<option value="iowa" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Iowa</option>
												<option value="kansas" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Kansas</option>
												<option value="kentucky" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Kentucky</option>
												<option value="louisiana" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Louisiana</option>
												<option value="maine" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Maine</option>
												<option value="maryland" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Maryland</option>
												<option value="massachusetts" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Massachusetts</option>
												<option value="michigan" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Michigan</option>
												<option value="minnesota" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Minnesota</option>
												<option value="mississippi" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Mississippi</option>
												<option value="missouri" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Missouri</option>
												<option value="montana" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Montana</option>
												<option value="nebraska" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Nebraska</option>
												<option value="nevada" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Nevada</option>
												<option value="new-hampshire" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">New Hampshire</option>
												<option value="new-jersey" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">New Jersey</option>
												<option value="new-mexico" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">New Mexico</option>
												<option value="new-york" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">New York</option>
												<option value="north-carolina" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">North Carolina</option>
												<option value="north-dakota" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">North Dakota</option>
												<option value="ohio" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Ohio</option>
												<option value="oklahoma" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Oklahoma</option>
												<option value="oregon" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Oregon</option>
												<option value="pennsylvania" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Pennsylvania</option>
												<option value="rhode-island" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Rhode Island</option>
												<option value="south-carolina" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">South Carolina</option>
												<option value="south-dakota" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">South Dakota</option>
												<option value="tennessee" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Tennessee</option>
												<option value="texas" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Texas</option>
												<option value="utah" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Utah</option>
												<option value="vermont" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Vermont</option>
												<option value="virginia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Virginia</option>
												<option value="washington" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Washington</option>
												<option value="west-virginia" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">West Virginia</option>
												<option value="wisconsin" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Wisconsin</option>
												<option value="wyoming" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Wyoming</option>
											</select>
				</div>
				<div class="form-group col-md-2" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<label for="submit" class="d-none d-md-block" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">&nbsp;</label>
					<button id="submit" class="btn btn-success btn-block" title="Generate Fake Name" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Generate <svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></button>
				</div>
			</div> <!-- // form-row -->
		</form>
		<div class="label-row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
		<span class="text-muted small" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">FAKENAMEGENERATOR:</b></span>
							<span class="badge badge-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Generate fake female name" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">FEMALE</span>
										<span class="badge badge-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Generate fake identity age 18-24" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">AGE 18-24</span>
										<span class="badge badge-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Generate Fake U.S. Name" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">UNITED STATES</span>
										<span class="badge badge-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Generate Fake Address in Alaska" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">ALASKA</span>
					</div>
	</div>
</div><!-- // search card -->

<div class="left-spaced bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<script async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>
<!-- Faux ID - Generic - Link Unit -->
<script>
document.write('\
<ins class="adsbygoogle"\
     style="display:block"\
     data-ad-client="ca-p'+'ub-05830478'+'430128'+'66"\
     data-ad-slot="8290665909"\
     data-ad-format="link"></ins>');
(adsbygoogle = window.adsbygoogle || []).push({});
</script><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-0583047843012866" data-ad-slot="8290665909" data-ad-format="link" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></ins>
</div>

<div class="card" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-sm-8" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 class="not-bottom-spaced not-top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Name:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="id_name can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Ms. Candace Carroll</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Address:</h5>
					<address class="can-copy not-bottom-spaced left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
													92714 Weimann Junctions<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
														Fairbanks, AK 99707
											</address>
					<img src="./Fake Name Generator _ FauxID.com_files/blank.gif" class="left-spaced flag flag-us" title="United States" width="32" height="32" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
											<img class="left-spaced" src="./Fake Name Generator _ FauxID.com_files/AK.png" title="Alaska state flag" width="40" height="24" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
										<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#map" class="left-spaced small" data-toggle="modal" title="See address on map" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">See Map <svg class="svg-inline--fa fa-map-marker-alt fa-w-12" aria-hidden="true" data-prefix="fas" data-icon="map-marker-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"></path></svg><!-- <i class="fas fa-map-marker-alt"></i> --></a>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Latitude &amp; longitude:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">26.725696, -116.007561</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><svg class="svg-inline--fa fa-phone fa-w-16 small" aria-hidden="true" data-prefix="fas" data-icon="phone" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M493.397 24.615l-104-23.997c-11.314-2.611-22.879 3.252-27.456 13.931l-48 111.997a24 24 0 0 0 6.862 28.029l60.617 49.596c-35.973 76.675-98.938 140.508-177.249 177.248l-49.596-60.616a24 24 0 0 0-28.029-6.862l-111.997 48C3.873 366.516-1.994 378.08.618 389.397l23.997 104C27.109 504.204 36.748 512 48 512c256.087 0 464-207.532 464-464 0-11.176-7.714-20.873-18.603-23.385z"></path></svg><!-- <i class="fas fa-phone small"></i> --> Phone:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">************</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a class="small left-spaced" href="https://fauxid.com/tools/fake-phone-number" title="Fake Phone Number Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More Phone Options »</a>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Social Security Number:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">***********</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a class="small left-spaced" href="https://fauxid.com/tools/fake-ssn-generator" title="Fake Social Security Number Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More SSN Options »</a>
				</div>
			</div>
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Image provided by ThisPersonDoesNotExist.com" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-question-circle fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="question-circle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"></path></svg><!-- <i class="fas fa-question-circle"></i> --></a>
				<h5 class="not-top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Random Face:</h5><br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<figure class="figure" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<img data-src="https://thispersondoesnotexist.com/image.jpg?time=1745294388" title="This person does not exist!" class="img-lazy img-thumbnail img-fluid" style="" src="./Fake Name Generator _ FauxID.com_files/image.jpg" width="193.5703125" height="31" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<figcaption class="figure-caption text-center" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<a href="./Fake Name Generator _ FauxID.com_files/image.jpg" class="small" target="_blank" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><svg class="svg-inline--fa fa-download fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="download" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"></path></svg><!-- <i class="fas fa-download"></i> --> Download Image</a>
					</figcaption>
				</figure>

				<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<div class="col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Random Avatar:</h5><br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<figure class="figure" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
							<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIIAAACCCAIAAAAFYYeqAAAABnRSTlMAAAAAAABupgeRAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABc0lEQVR4nO3csQ1CMRAFQT4ipBnqI6Y+mqEAeoAXjGCngJOllR2dfJym7q/bduDj+lyNks92Xg3KN8pAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEI75Lls+0G0glIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFwmU/8k08Kt2frNhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyE/toj7Lf2ttg9u60eJUIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBsJ+eXK4Zyfrr70fVAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlILwB+ncX5X49rV4AAAAASUVORK5CYII=" title="Avatar color: #7bf431" class="img-thumbnail img-fluid" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
							<figcaption class="figure-caption text-center" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
								<a href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIIAAACCCAIAAAAFYYeqAAAABnRSTlMAAAAAAABupgeRAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABc0lEQVR4nO3csQ1CMRAFQT4ipBnqI6Y+mqEAeoAXjGCngJOllR2dfJym7q/bduDj+lyNks92Xg3KN8pAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEI75Lls+0G0glIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFwmU/8k08Kt2frNhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyE/toj7Lf2ttg9u60eJUIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBsJ+eXK4Zyfrr70fVAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlILwB+ncX5X49rV4AAAAASUVORK5CYII=" class="small" download="FauxID.com Avatar" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><svg class="svg-inline--fa fa-download fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="download" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"></path></svg><!-- <i class="fas fa-download"></i> --> Download Image</a>
							</figcaption>
						</figure>
					</div>
					<div class="col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" data-toggle="tooltip" data-placement="top" title="" data-original-title="QR Codes are type of two-dimensional bar code. Scan this code with your phone to return to this exact identity later." data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-question-circle fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="question-circle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"></path></svg><!-- <i class="fas fa-question-circle"></i> --></a>
						<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">QR Code:</h5><br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<figure class="figure" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
							<img src="./Fake Name Generator _ FauxID.com_files/chart" title="QR Code links to is identity" class="img-thumbnail img-fluid img-muted" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
							<figcaption class="figure-caption text-center" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
								<span class="small" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">QR Code links to this Identity!</span>
							</figcaption>
						</figure>
					</div>
				</div>

			</div>
		</div>
	</div>
</div><!-- // overview -->

<div class="bottom-spaced top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<script async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>
<!-- FauxId - In Article -->
<script>
document.write('\
<ins class="adsbygoogle"\
     style="display:block; text-align:center;"\
     data-ad-layout="in-article"\
     data-ad-format="fluid"\
     data-ad-client="c'+'a-pub-0583'+'047'+'843012'+'866"\
     data-ad-slot="6461014845"></ins>');
     (adsbygoogle = window.adsbygoogle || []).push({});
</script><ins class="adsbygoogle" style="display:block; text-align:center;" data-ad-layout="in-article" data-ad-format="fluid" data-ad-client="ca-pub-0583047843012866" data-ad-slot="6461014845" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></ins>
</div>


<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><svg class="svg-inline--fa fa-calendar-alt fa-w-14" aria-hidden="true" data-prefix="fas" data-icon="calendar-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M436 160H12c-6.6 0-12-5.4-12-12v-36c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48v36c0 6.6-5.4 12-12 12zM12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm116 204c0-6.6-5.4-12-12-12H76c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40zm0-128c0-6.6-5.4-12-12-12H76c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40zm128 128c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40zm0-128c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40zm128 128c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40zm0-128c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-40z"></path></svg><!-- <i class="fas fa-calendar-alt"></i> --> Date of Birth:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">June 17, 2005</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced small text-muted" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">19 years 10 months 5 days old</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a href="https://date-check.org/2005/june-17" target="_blank noopener" class="small left-spaced" title="About June 17, 2005" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">About this date »</a>
				</div>
			</div>
			<div class="col-md-4 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Height:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">5" 2' (62 inches)</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">1.6 meters</span>
				</div>
			</div>
			<div class="col-md-4 col-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Weight:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">116.9lbs</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">53kg</span>
				</div>
			</div>
		</div>
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Gender:</h5>
					<svg class="svg-inline--fa fa-venus fa-w-9" aria-hidden="true" data-prefix="fas" data-icon="venus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 288 512" data-fa-i2svg=""><path fill="currentColor" d="M288 176c0-79.5-64.5-144-144-144S0 96.5 0 176c0 68.5 47.9 125.9 112 140.4V368H76c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h36v36c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-36h36c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-36v-51.6c64.1-14.5 112-71.9 112-140.4zm-224 0c0-44.1 35.9-80 80-80s80 35.9 80 80-35.9 80-80 80-80-35.9-80-80z"></path></svg><!-- <i class="fas fa-venus"></i> --> <span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Female</span>
				</div>
			</div>
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Hair Color:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Blond</span>
				</div>
			</div>
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Eye Color:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Brown</span>
				</div>
			</div>
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Ethnicity:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Black</span>
				</div>
			</div>
			<div class="col-sm-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Blood Type:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">O+</span>
				</div>
			</div>
					</div>
	</div>
</div><!-- // demographics -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-sm-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<h3 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Financial &amp; Banking Numbers </h3>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" data-toggle="tooltip" data-placement="top" title="" data-original-title="Credit Card Number passes the Luhn test. Click below for more options." data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-credit-card fa-w-18" aria-hidden="true" data-prefix="fas" data-icon="credit-card" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M0 432c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V256H0v176zm192-68c0-6.6 5.4-12 12-12h136c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H204c-6.6 0-12-5.4-12-12v-40zm-128 0c0-6.6 5.4-12 12-12h72c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM576 80v48H0V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48z"></path></svg><!-- <i class="fas fa-credit-card"></i> --></a>
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Credit Card Number:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">379-2202-0135-6935</code></span>
					<img class="left-spaced" title="MasterCard" src="./Fake Name Generator _ FauxID.com_files/mastercard.png" width="45" height="28" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Exp Date:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">1/26</span>
					<h5 class="left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">CVV:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">667</span>
				</div>
				<a href="https://fauxid.com/tools/fake-credit-card-generator" class="small left-spaced" title="Credit Card Number Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More Credit Card Options »</a>
			</div>
			<div class="col-sm-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-university fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="university" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M496 128v16a8 8 0 0 1-8 8h-24v12c0 6.627-5.373 12-12 12H60c-6.627 0-12-5.373-12-12v-12H24a8 8 0 0 1-8-8v-16a8 8 0 0 1 4.941-7.392l232-88a7.996 7.996 0 0 1 6.118 0l232 88A8 8 0 0 1 496 128zm-24 304H40c-13.255 0-24 10.745-24 24v16a8 8 0 0 0 8 8h464a8 8 0 0 0 8-8v-16c0-13.255-10.745-24-24-24zM96 192v192H60c-6.627 0-12 5.373-12 12v20h416v-20c0-6.627-5.373-12-12-12h-36V192h-64v192h-64V192h-64v192h-64V192H96z"></path></svg><!-- <i class="fas fa-university"></i> --> Bank:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">ALASKA USA FEDERAL CREDIT UNION</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Bank Account Number:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">***********</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Routing Number:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">*********</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">IBAN:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">****************************</code></span>
					<img src="./Fake Name Generator _ FauxID.com_files/blank.gif" class="left-spaced flag flag-us" title="United States" width="32" height="32" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				</div>
			</div>
		</div>
	</div>
</div><!-- // banking -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<h3 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Cryptocurrency Addresses</h3>
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
						<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" data-toggle="tooltip" data-placement="top" title="" data-original-title="This address can be used as a Bitcoin Classic, Bitcoin Cash or Bitcoin Gold address." data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-question-circle fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="question-circle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"></path></svg><!-- <i class="fas fa-question-circle"></i> --></a>
						Bitcoin Address:
					</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><img src="./Fake Name Generator _ FauxID.com_files/bitcoin.svg" class="icon" title="Bitcoin" width="18.1953125" height="18.1953125" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">**********************************</code></span>
				</div>
			</div>
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
						<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" data-toggle="tooltip" data-placement="top" title="" data-original-title="This address be used for Ether of any ERC-20 Token." data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-question-circle fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="question-circle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"></path></svg><!-- <i class="fas fa-question-circle"></i> --></a>
						Ethereum Address:
					</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><img src="./Fake Name Generator _ FauxID.com_files/ethereum.svg" class="icon" title="Ethereum" width="11.3671875" height="18.1953125" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">******************************************</code></span>
				</div>
			</div>
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Ripple Address:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><img src="./Fake Name Generator _ FauxID.com_files/ripple.svg" class="icon" title="Ripple" width="18.1953125" height="18.1953125" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">rLBoWtuq4oretrDnLNDs4Z5jhpS2HnkFMC</code></span>
				</div>
			</div>
			<div class="col-md-12" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Monero Address:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><img src="./Fake Name Generator _ FauxID.com_files/monero.svg" class="icon" title="Monero" width="17.625" height="18.1953125" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<span class="can-copy left-spaced small" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">45qXP5kbEru8NcLsrw7ZK788ojo4jikWfgCp775vYgJSKoKpHCRsJGb3MiobecnbQ5jpQ1jPFHCCWVdP3JrYHYvkMyFUxyk</code></span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a class="small big-left-spaced" href="https://fauxid.com/crypto" title="Generate Bitcoin, Ethereum and Monero Addresses" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More Cryptocurrency Options »</a>
				</div>
			</div>
		</div>

		<hr data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">

		<p class="text-warning text-center not-bottom-spaced top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
			<b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Note:</b> Do not send funds to any of these addresses. You will lose your money.
		</p>

	</div>
</div><!-- // cryptocurrency -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<h3 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Internet Details</h3>
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Username:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">pgleason</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Password:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">D&gt;J%cTD-a1</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Email Address:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><EMAIL></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Unique User Identifier (UUID):</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">769a615f-c986-4e7a-8409-13132136046e</code></span>
				</div>
				<a href="https://www.uuidtools.com/docs" class="small left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">UUIDs Provided by UUIDTools.com »</a>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Website:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">legros.com</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Color:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">#7bf431</code></span>
					<div class="color-box" style="background-color: #7bf431;" title="#7bf431" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></div>
				</div>
			</div>
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">IP Address (IPv4):</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">**************</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">IP Address (Local):</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">**************</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">MAC Address:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">D7:8C:D2:BE:74:84</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">IP Address (IPv6):</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">5973:5346:dc6:10b7:e48e:d127:ff38:670d</code></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Random Emoji:</h5>
					<span class="can-copy" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">😡</span>
				</div>
			</div>
		</div>
		<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">User Agent:</h5>
			<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Mozilla/5.0 (Windows; U; Windows 95) AppleWebKit/535.19.2 (KHTML, like Gecko) Version/5.0.2 Safari/535.19.2</code></span>
			<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><a class="small left-spaced" href="https://fauxid.com/tools/fake-internet-identifiers" title="Generate online identity with IP Address, User Agent, and more!" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More Internet Number Options »</a>
		</div>

	</div>
</div><!-- // Internet -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<h3 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Education</h3>
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Education Level:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Bachelor's Degree</span>
				</div>
			</div>
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">University:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Alaska Christian College</span>
				</div>
			</div>
		</div>
	</div>
</div><!-- // education -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg class="svg-inline--fa fa-sync-alt fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="sync-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M370.72 133.28C339.458 104.008 298.888 87.962 255.848 88c-77.458.068-144.328 53.178-162.791 126.85-1.344 5.363-6.122 9.15-11.651 9.15H24.103c-7.498 0-13.194-6.807-11.807-14.176C33.933 94.924 134.813 8 256 8c66.448 0 126.791 26.136 171.315 68.685L463.03 40.97C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.749zM32 296h134.059c21.382 0 32.09 25.851 16.971 40.971l-41.75 41.75c31.262 29.273 71.835 45.319 114.876 45.28 77.418-.07 144.315-53.144 162.787-126.849 1.344-5.363 6.122-9.15 11.651-9.15h57.304c7.498 0 13.194 6.807 11.807 14.176C478.067 417.076 377.*********** 504c-66.448 0-126.791-26.136-171.315-68.685L48.97 471.03C33.851 486.149 8 475.441 8 454.059V320c0-13.255 10.745-24 24-24z"></path></svg><!-- <i class="fas fa-sync-alt"></i> --></a>
	<div class="card-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<h3 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Company &amp; Employee</h3>
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Fake Company Name:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Schinner Group LLC</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced text-muted" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><i data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Cross-Platform Web-Enabled Strategy</i></span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Company Description:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Drive Extensible Vortals</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Company EIN:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><code data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">68-6678385</code></span>
				</div>
			</div>
			<div class="col-md-6" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Salary:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">$111,300 per year</span>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced text-muted" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">$53.51 per hour</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Employee Title:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Highway Patrol Pilot</span>
				</div>
				<div data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<h5 data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Company Email:</h5>
					<br data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><span class="can-copy left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><EMAIL></span>
				</div>
			</div>
		</div>

		<a class="small left-spaced" href="https://fauxid.com/tools/fake-company-generator" title="Fake Company Name Generator" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">More Company &amp; Employee Options »</a>
	</div>
</div><!-- // employment -->

<!-- <div class="card top-spaced">
	<a href="#" class="fixed-refresh" onclick="document.location.reload(true); return false;" title="generate new identity"><i class="fas fa-sync-alt"></i></a>
	<div class="card-body">
		<h3>Automotive</h3>
	</div>
</div> -->
<!-- // automotive -->

<div class="card top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<div class="card-body bg-light" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<div class="row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="col-md-4" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<img src="./Fake Name Generator _ FauxID.com_files/logo.png" class="img-fluid" title="A Fake Name Generator that doesn&#39;t suck." width="230.9921875" height="43.4140625" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			</div>
			<div class="col-md-8" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<p class="lead text-center" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
					<b data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">Save this Identity</b>—Use the link below to save this page or download this identity for later use.
				</p>
			</div>
		</div>
		<div class="form-row" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<div class="form-group col-md-6 not-bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<label id="save-this-page-label" for="id-link" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Save this page:</label>
				<input id="id-link" class="form-control sm-bottom-spaced" onclick="saveThisPageCopy(this);" value="https://fauxid.com/identity/769a615f-c986-4e7a-8409-13132136046e" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			</div>
			<div class="form-group col-md-3 not-bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<label class="d-none d-md-block" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">&nbsp;</label>
				<div class="dropdown sm-bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
					<button type="button" class="btn-block btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">
						<svg class="svg-inline--fa fa-download fa-w-16" aria-hidden="true" data-prefix="fas" data-icon="download" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"></path></svg><!-- <i class="fas fa-download"></i> --> Download Identity
					</button>
					<div class="dropdown-menu" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
						<a href="https://fauxid.com/api/download/769a615f-c986-4e7a-8409-13132136046e.json" class="dropdown-item" download="">Download as JSON</a>
						<a href="https://fauxid.com/api/download/769a615f-c986-4e7a-8409-13132136046e.csv" class="dropdown-item" download="">Download as CSV</a>
					</div>
				</div>
			</div>
			<div class="form-group col-md-3 not-bottom-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<label class="d-none d-md-block" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">&nbsp;</label>
				<a href="https://fauxid.com/tools/bulk-identity-generator" class="btn btn-success btn-block" title="Bulk Identity Generator" target="_blank" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1"><svg class="svg-inline--fa fa-users fa-w-20" aria-hidden="true" data-prefix="fas" data-icon="users" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" data-fa-i2svg=""><path fill="currentColor" d="M320 64c57.99 0 105 47.01 105 105s-47.01 105-105 105-105-47.01-105-105S262.01 64 320 64zm113.463 217.366l-39.982-9.996c-49.168 35.365-108.766 27.473-146.961 0l-39.982 9.996C174.485 289.379 152 318.177 152 351.216V412c0 19.882 16.118 36 36 36h264c19.882 0 36-16.118 36-36v-60.784c0-33.039-22.485-61.837-54.537-69.85zM528 300c38.66 0 70-31.34 70-70s-31.34-70-70-70-70 31.34-70 70 31.34 70 70 70zm-416 0c38.66 0 70-31.34 70-70s-31.34-70-70-70-70 31.34-70 70 31.34 70 70 70zm24 112v-60.784c0-16.551 4.593-32.204 12.703-45.599-29.988 14.72-63.336 8.708-85.69-7.37l-26.655 6.664C14.99 310.252 0 329.452 0 351.477V392c0 13.255 10.745 24 24 24h112.169a52.417 52.417 0 0 1-.169-4zm467.642-107.09l-26.655-6.664c-27.925 20.086-60.89 19.233-85.786 7.218C499.369 318.893 504 334.601 504 351.216V412c0 1.347-.068 2.678-.169 4H616c13.255 0 24-10.745 24-24v-40.523c0-22.025-14.99-41.225-36.358-46.567z"></path></svg><!-- <i class="fas fa-users"></i> --> Bulk Generator</a>
			</div>
		</div>

	</div>
</div><!-- // share this page -->

<div class="row top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<div class="col-12" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<div class="left-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
			<script async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>
<!-- Faux ID - Generic - Link Unit -->
<script>
document.write('\
<ins class="adsbygoogle"\
     style="display:block"\
     data-ad-client="ca-p'+'ub-05830478'+'430128'+'66"\
     data-ad-slot="8290665909"\
     data-ad-format="link"></ins>');
(adsbygoogle = window.adsbygoogle || []).push({});
</script><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-0583047843012866" data-ad-slot="8290665909" data-ad-format="link" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"></ins>
		</div>
	</div>
</div>

<p class="text-right top-spaced" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<a href="https://fauxid.com/fake-name-generator/united-states/alaska?age=18-24&amp;gender=female#top" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Top <svg class="svg-inline--fa fa-arrow-up fa-w-14" aria-hidden="true" data-prefix="fas" data-icon="arrow-up" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M34.9 289.5l-22.2-22.2c-9.4-9.4-9.4-24.6 0-33.9L207 39c9.4-9.4 24.6-9.4 33.9 0l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9L413 289.4c-9.5 9.5-25 9.3-34.3-.4L264 168.6V456c0 13.3-10.7 24-24 24h-32c-13.3 0-24-10.7-24-24V168.6L69.2 289.1c-9.3 9.8-24.8 10-34.3.4z"></path></svg><!-- <i class="fas fa-arrow-up"></i> --></a>
</p>

<!-- Map Modal -->
<div class="modal fade" id="map" tabindex="-1" role="dialog" aria-labelledby="map" aria-hidden="true" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
  <div class="modal-dialog modal-lg " data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	<div class="modal-content" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
	  <div class="modal-header" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<h5 class="modal-title">Fairbanks, AK 99707</h5>
		<button type="button" class="close" data-dismiss="modal" aria-label="Close" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		  <span aria-hidden="true">×</span>
		</button>
	  </div>
	  <div class="modal-body" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
				<iframe height="450" frameborder="0" style="border:0; width: 100%" src="./Fake Name Generator _ FauxID.com_files/place.html" allowfullscreen=""></iframe>
	  </div>
	  <div class="modal-footer" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02">
		<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
	  </div>
	</div>
  </div>
</div><!-- // map modal -->

					</div>

				</div>
			</div>
		</div><!-- /.container -->

		<textarea id="copy-target" class="offscreen">Ms. Candace Carroll</textarea>
<footer class="footer" default-translate="no">
	<div class="container">
		<p>
			<a href="https://fauxid.com/identity" title="Generate Random Identity by Country">Fake Identity</a> •
			<a href="https://fauxid.com/fake-name-generator" title="Generate Random Name">Fake Name</a> •
			<a href="https://fauxid.com/tools/fake-ssn-generator" title="Generate Random Social Security Number (SSN)">Fake SSN</a> •
			<a href="https://fauxid.com/tools/fake-address-generator" title="Generate Random Address">Fake Address</a>  •
			<a href="https://fauxid.com/tools/fake-email-list" title="Generate Random Email Address">Fake Email</a>
			<br>
			<a href="https://fauxid.com/tools/fake-phone-number" title="Generate Random Phone Numbers">Fake Phone Number</a> •
			<a href="https://fauxid.com/tools/fake-credit-card-generator" title="Generate Random Credit Card Number">Fake Credit Card</a> •
			<a href="https://fauxid.com/tools/fake-company-generator" title="Generate Ranome Company Name">Fake Company</a> •
			<a href="https://fauxid.com/tools/random-text-generator" title="Ipsom Lorem Text Generator">Random Text</a> •
			<a href="https://fauxid.com/tools/fake-internet-identifiers" title="Generate Fake IP Address, User Agent, and more">Internet Numbers</a>
		</p>
		<p>
			© 2025. All rights reserved.
			<b class="copyright">FauxID.com</b> – <em>A Better Fake Name Generator</em>.
		</p>
		<p>
			<a href="https://fauxid.com/sitemap">View Sitemap</a>.
			Usage is subject to our <a href="https://fauxid.com/legal">Terms and Privacy Policy</a>.
			Questions or Comments? <a href="https://fauxid.com/contact">Contact Us</a>.
		</p>
		<a href="https://fauxid.com/">
			<img src="./Fake Name Generator _ FauxID.com_files/logo.png" height="60" title="FauxID.com – A Better Fake Name Generator">
		</a>
	</div>
</footer>

<!-- Include JS files -->
<script src="./Fake Name Generator _ FauxID.com_files/jquery-3.2.1.min.js.下载"></script>
<script src="./Fake Name Generator _ FauxID.com_files/jquery-ui.min.js.下载" integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
<script src="./Fake Name Generator _ FauxID.com_files/jquery.selectBoxIt.min.js.下载" integrity="sha256-+wQ8KMvKqZLm5uje1dVt+/gClUGWc4oZkNdD2Q4Mzfs=" crossorigin="anonymous"></script>

<script src="./Fake Name Generator _ FauxID.com_files/popper.min.js.下载"></script>
<script src="./Fake Name Generator _ FauxID.com_files/bootstrap.min.js.下载"></script>
<script src="./Fake Name Generator _ FauxID.com_files/app.js.下载"></script>

<script>
$( document ).ready(function() {
	var href = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIIAAACCCAIAAAAFYYeqAAAABnRSTlMAAAAAAABupgeRAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABc0lEQVR4nO3csQ1CMRAFQT4ipBnqI6Y+mqEAeoAXjGCngJOllR2dfJym7q/bduDj+lyNks92Xg3KN8pAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEMpAKAOhDIQyEI75Lls+0G0glIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFwmU/8k08Kt2frNhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyEMhDKQCgDoQyE/toj7Lf2ttg9u60eJUIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBkIZCGUglIFQBsJ+eXK4Zyfrr70fVAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlIJSBUAZCGQhlILwB+ncX5X49rV4AAAAASUVORK5CYII=';
	$("#favicon").attr("href",href);
});

function saveThisPageCopy(element) {
	element.select();
	document.execCommand('Copy');
	$('#save-this-page-label').after('<span class="copied left-spaced">Copied</span>');
	$('.copied').fadeOut(600, function() {
		$(this).remove();
	});
}
</script>

<script>
	// lazy load images
	$( document ).ready(function() {
		console.log('called');
		$('.img-lazy').each(function(index, element) {
			$(element).attr('src', $(element).data('src'));
			$(element).show();
		});
	})
</script>

<script>
	$("select").keypress(function(event) {
		if (event.which == 13) {
			event.preventDefault();
			$("#options-form").click();
		}
	});

	$("#submit").click(function(event) {

		url = "https://fauxid.com/fake-name-generator";
		if ($('#country').val() != '') {
			url += "/" + $('#country').val();

			if ($('#state').val() != '') {
				url += "/" + $('#state').val();
			}
		}

		options = [];
		if ($('#age').val() != '') {
			options.push('age=' + $('#age').val())
		}
		if ($('#gender').val() != '') {
			options.push('gender=' + $('#gender').val())
		}
		console.log(options.length);
		if (options.length > 0) {
			url += "?" + options.join("&")
		}

		window.location.href = url;
		return false;
	})

	$("#country").change(function() {
		checkStateSelectActive();
	});

	function checkStateSelectActive() {
		if ($("#country").val() != "en_US" && $("#country").val() != "united-states")
		{
			$("#state").prop("disabled", true);
			$("#state").addClass("disabled");
		}
		else
		{
			$("#state").prop("disabled", false);
			$("#state").removeClass("disabled");
		}
	}

	$( document ).ready(function() {
		checkStateSelectActive();
	})
</script>


<script data-ad-client="ca-pub-0583047843012866" async="" src="./Fake Name Generator _ FauxID.com_files/f.txt"></script>

<!-- Global site tag (gtag.js) - Google Analytics -->
<script async="" src="./Fake Name Generator _ FauxID.com_files/js"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-11'+'669'+'290'+'6-1');
</script>

	

<button class="script-button" style="right: -50px;" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02" data-immersive-translate-paragraph="1">Scripts</button><div class="info-container" style="opacity: 0; display: none;" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><div class="table-header">Script Finder<span class="match-count">0</span><input type="text" placeholder="Search scripts..." class="script-search-input"><button class="to-greasyfork">View on Greasyfork</button></div><ul class="info-list" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><div class="wait-loading">Loading scripts...</div><button class="load-more" style="display: none;">Load more</button></ul></div><div class="img-upload-dropzone" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><div class="img-upload-dropzone-message">释放鼠标上传图片</div></div><div id="_qk_menu" class="left right bottom" style="inset: auto -12.875px 227.109px auto;" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><div class="quicker_button" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><svg style="enable-background:new 0 0 32 32;" viewBox="0 0 32 32" x="0px" y="0px" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M5.8,29.5l8.4-13c0.2-0.2,0.1-0.6-0.1-0.7l-8.2-5.6c-0.3-0.2-0.3-0.6,0-0.8l10.8-8.8v0l6,6.5 c0.2,0.3,0.2,0.7-0.1,0.8l-3.3,1.5c-0.4,0.2-0.4,0.7-0.1,0.9l7.8,5.3c0.3,0.2,0.3,0.7,0,0.9L6.5,30.2C6,30.5,5.5,30,5.8,29.5z  M4.5,31.5" fill="#FFFFFF"></path></svg></div><div class="dropdown-content" data-immersive-translate-walked="f1abc04d-1a60-4e77-85ab-795bb2afba02"><button class="qk_action_button" title=""><div class="btn_content_wrapper"><div class="qk_action_icon"><img src="https://files.getquicker.net/_icons/07ECC0FAA8E1DA2DB072FBCB0C46D9C189328933.png" class="icon_img"></div><div class="qk_action_label">pake打包</div></div></button><button class="qk_action_button" title=""><div class="btn_content_wrapper"><div class="qk_action_icon"><img src="https://files.getquicker.net/_icons/715CE7C94259D6951813E67F8AC0DD4F7817D4DC.png" class="icon_img"></div><div class="qk_action_label">仓库</div></div></button><button class="qk_action_button" title="浏览器扩展 | 选中多个标签页后，执行该动作，即可批量获取页面标题及链接（MD格式）"><div class="btn_content_wrapper"><div class="qk_action_icon"><img src="https://files.getquicker.net/_icons/0EBE5E37D14F0FBAE3707B829F6599E39309C2C5.png" class="icon_img"></div><div class="qk_action_label">获取标签页</div></div></button><button class="qk_action_button" title="5秒完成抠图"><div class="btn_content_wrapper"><div class="qk_action_icon"><img src="https://files.getquicker.net/_icons/7F27A5C485FCD262DBD0327614805F4F49A94CBE.svg" class="icon_img"></div><div class="qk_action_label">一键抠图</div></div></button></div></div></body><div id="immersive-translate-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 4);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 3px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --background-light-green: #F5F7F9;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  --float-ball-more-button-border-color: #F6F6F6;
  --float-ball-more-button-background-color: #FCFCFC;
  --float-ball-more-button-svg-color: #6C6F73;
  color-scheme: light;
  --service-bg-hover:#F7FAFF;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --background-light-green: #141e26;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
    color-scheme: dark;
    --service-bg-hover:#22292F;
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --background-light-green: #141e26;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  color-scheme: dark;
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 1130px;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}

@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 992px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
  --switch-checked-background-color: var(--primary);
}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 13px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

html {
  font-size: 16px;
  --font-size: 16px;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #F9FBFC;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.20);
  --service-select-border-color: #FAFAFA;
  --service-select-selected-background-color: #F3F5F6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.90);
    --service-select-border-color: #2C2C2C;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.90);
  --service-select-border-color: #2C2C2C;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

#mount {
  min-width: 268px;
}

.main-button {
  font-size: 15px;
  vertical-align: middle;
  border-radius: 12px;
  padding: unset;
  height: 44px;
  line-height: 44px;
}

.pt-4 {
  padding-top: 16px;
}

.p-2 {
  padding: 8px;
}

.pl-5 {
  padding-left: 48px;
}

.p-0 {
  padding: 0;
}

.pl-2 {
  padding-left: 8px;
}

.pl-4 {
  padding-left: 24px;
}

.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pr-5 {
  padding-right: 48px;
}

.text-sm {
  font-size: 13px;
}

.text-base {
  font-size: 16px;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.justify-between {
  justify-content: space-between;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 24px;
}

.ml-3 {
  margin-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.pl-3 {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.p-3 {
  padding: 12px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.px-6 {
  padding-left: 18px;
  padding-right: 18px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.pt-6 {
  padding-top: 20px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-30 {
  max-height: 120px;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-2 {
  flex: 2;
}

.items-center {
  align-items: center;
}

.max-content {
  width: max-content;
}

.justify-center {
  justify-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {

  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

select.min-select-secondary {
  color: var(--color);
}

select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}

select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.popup-footer {
  background-color: var(--popup-footer-background-color);
  height: 40px;
}

.text-right {
  text-align: right;
}

.clickable {
  cursor: pointer;
}

.close {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}

.text-label {
  color: #666;
}

.display-none {
  display: none;
}

/* dark use #18232c */
@media (prefers-color-scheme: dark) {
  .text-label {
    color: #9ca3af;
  }
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --text-decoration: none !important;
  background-color: transparent !important;
}

.language-select-container {
  position: relative;
  width: 100%;
  background-color: var(--popup-item-background-color);
  height: 55px;
  border-radius: 12px;
}

select.language-select {
  color: var(--text-black-2);
  font-size: 14px;
  padding: 8px 24px 24px 16px;
  position: absolute;
  border-radius: 12px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

select.text-gray-6 {
  color: var(--text-gray-6);
}

.language-select-container label {
  position: absolute;
  bottom: 10px;
  left: 16px;
  font-size: 12px;
  color: var(--text-gray-9);
  line-height: 12px;
  margin: 0;
}

.translation-service-container {
  background-color: var(--popup-item-background-color);
  border-radius: 12px;
}

.min-select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  background-color: var(--popup-item-background-color);
  padding-left: 16px;
}

.min-select-container:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.min-select-container:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.min-select-container:only-child {
  border-radius: 10px;
}

.translate-mode {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: var(--popup-item-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.translate-mode svg {
  fill: var(--text-gray-2);
}

.widgets-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.widgets-container> :not(:last-child) {
  margin-right: 8px;
}

.widget-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--popup-item-background-color);
  font-size: 12px;
  height: 44px;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
}

.widget-item svg {
  fill: var(--text-gray-2);
}

.setting svg {
  fill: var(--text-gray-6);
}

.share-button-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 3px 0 8px;
}

.share-button-container svg {
  fill: var(--text-gray-9);
}

.min-select-container:hover,
.language-select-container:hover,
.widget-item:hover,
.translate-mode:hover {
  background-color: var(--popup-item-hover-background-color);
}

.main-button:hover {
  background-color: #f5508f;
}

.share-button-container:hover {
  background-color: var(--popup-item-background-color);
  border-radius: 6px;
}

.error-boundary {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  display: flex;
  padding: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  word-break: break-all;
  margin: 12px;
  border-radius: 12px;
  flex-direction: column;
}


.upgrade-pro {
  border-radius: 11px;
  background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
  padding: 2px 8px;
  transform: scale(0.85);
}

.upgrade-pro span {
  background: linear-gradient(180deg, #FFEAB4 17.65%, #F8C235 85.29%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  margin-left: 4px;
}


.upgrade-pro svg {
  margin-top: -2px;
}

.upgrade-pro:hover {
  background: linear-gradient(57deg, #3D3D3D 19.8%, #949494 82.2%);
}

.border-bottom-radius-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.trial-pro-container {
  border-radius: 0px 0px 12px 12px;
  background: var(--popup-trial-pro-background-color);
  display: flex;
  align-items: center;
  height: 44px;
  padding-left: 16px;
  padding-right: 12px;
  font-size: 12px;
}

.trial-pro-container label {
  line-height: 13px;
  color: var(--text-black-2);
}

.trial-pro-container img {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.upgrade-pro-discount-act {
  height: 25px;
  display: flex;
  padding: 0 4px;
  align-items: center;
  border-radius: 15px;
  background: linear-gradient(90deg, #CEFBFA 11.33%, #D7F56F 63.75%, #FCCD5E 100%);
  transform: scale(0.9);
  box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.10);
  cursor: pointer;
}

.upgrade-pro-discount-act span {
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  color: #222222;
}

.upgrade-pro-discount-act:hover {
  text-decoration: unset;
  background: linear-gradient(90deg, #E2FFFE 11.33%, #E6FF91 63.75%, #FFDF93 100%);
}


.custom-select-container {
  width: 200px;
  position: relative;
  flex: 1;
}

.custom-select-content {
  border-radius: 12px;
  background: var(--popup-content-background-color);
  box-shadow: var(--service-select-content-shadow);
  border: 1px solid var(--service-select-border-color);
  padding: 4px 5px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  overflow-y: auto;
}

.custom-select-item {
  font-size: 13px;
  padding: 5px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-black-2);
  width: 100%;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}


.custom-select-content .custom-select-item.selected, .custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin:  0 3px;
  width: 20px;
}
.more-container {
  position: relative;
}
.new-menu-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #EF3434;
  border-radius: 50%;
  right: 18px;
  top: 4px;
}

html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  z-index: 2147483647;
  top: 335px;
  width: 56px;
  display: flex;
  flex-direction: column;
  display: none;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: linear-gradient(320.9deg, #db3b7b 26.47%, #ffcee2 88.86%);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: linear-gradient(140.91deg, #ff87b7 12.61%, #ec4c8c 76.89%);
  height: 34px;
  width: 54px;
  margin: 1px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-content {
  padding: 22px;
  width: 320px;
}

.imt-fb-close-title {
  font-weight: 500;
  color: var(--h2-color);
}

.imt-fb-close-radio-content {
  background-color: var(--background-light-green);
  padding: 8px 20px;
}

.imt-fb-radio-sel,
.imt-fb-radio-nor {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  flex-shrink: 0;
}

.imt-fb-radio-sel {
  border: 2px solid var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-radio-sel div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: var(--primary);
}

.imt-fb-radio-nor {
  border: 2px solid #d3d4d6;
}

.imt-fb-primary-btn {
  background-color: var(--primary);
  width: 72px;
  height: 32px;
  color: white;
  border-radius: 8px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-default-btn {
  border: 1px solid var(--primary);
  width: 72px;
  height: 32px;
  border-radius: 8px;
  color: var(--primary);
  line-height: 32px;
  text-align: center;
  font-size: 16px;
}

.imt-fb-guide-container {
  width: 312px;
  transform: translateY(-50%);
}

.imt-fb-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
}

.imt-fb-guide-bg.left {
  transform: scaleX(-1);
}

.imt-fb-guide-content {
  margin: 16px 32px 60px 21px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imt-fb-guide-content.left {
  margin: 16px 21px 60px 32px;
}

.imt-fb-guide-img {
  width: 235px;
  height: 171px;
  margin-top: 16px;
}

.imt-fb-guide-message {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  white-space: pre-wrap;
  text-align: center;
  font-weight: 700;
  margin-top: 10px;
}

.imt-fb-guide-button {
  margin-top: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 20px;
  width: unset;
}

.imt-fb-more-buttons {
  box-shadow: 0px 2px 10px 0px #00000014;
  border: 1px solid var(--float-ball-more-button-border-color);
  background: var(--float-ball-more-button-background-color);
  width: 36px;
  display: flex;
  flex-direction: column;
  border-radius: 18px;
  margin-right: 8px;
}

.imt-fb-more-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Sheet.css */
.immersive-translate-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Start off screen */
  left: 0;
  right: 0;
  background-color: white;
  transition: transform 0.3s ease-out;
  /* Smooth slide transition */
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it's above other content */
  bottom: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.immersive-translate-sheet.visible {
  transform: translateY(0);
}

.immersive-translate-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.immersive-translate-sheet-backdrop.visible {
  opacity: 1;
}

.popup-container-sheet {
  max-width: 100vw;
  width: 100vw;
}

.imt-no-events svg * {
  pointer-events: none !important;
}

.imt-manga-button {
  width: 36px;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: all;
  margin: 12px 0 0 0;
  background-color: white;
  border-radius: 18px;
  filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
  opacity: 0.5;
  right: 8px;
}

.imt-manga-feedback {
  cursor: pointer;
  margin: 10px 9px 12px 9px;
}

.imt-manga-button:hover {
  opacity: 1;
}

.imt-manga-translated {
  position: absolute;
  left: 24px;
  top: 20px;
}

.imt-float-ball-loading {
  animation: imt-loading-animation 0.6s infinite linear !important;
}

.imt-manga-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  width: 372px;
  transform: translateY(-50%);
}
.imt-manga-guide-content {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 40px 0;
}

.img-manga-guide-button {
  width: fit-content;
  margin: 16px auto;
}

.img-manga-close {
  position: absolute;
  bottom: -200px;
  width: 32px;
  height: 32px;
  left: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

@-webkit-keyframes imt-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes imt-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
</style><div id="mount" style="display: block;"><div class="imt-fb-container right notranslate" dir="ltr" style="z-index: 2147483647; pointer-events: none; top: 614px; display: flex;"><div title="关闭悬浮球" class="btn-animate" style="padding: 4px; cursor: pointer; transform: translateX(100%);"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div><div style="position: relative; pointer-events: all; display: inline-block;"><div><div class="imt-fb-btn  right btn-animate " dir="ltr" style="opacity: 0.5; transform: translateX(20px);"><div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" class="imt-fb-logo-img"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 15v2a2 2 0 0 0 1.85 1.995L7 19h3v2H7a4 4 0 0 1-4-4v-2h2zm13-5l4.4 11h-2.155l-1.201-3h-4.09l-1.199 3h-2.154L16 10h2zm-1 2.885L15.753 16h2.492L17 12.885zM8 2v2h4v7H8v3H6v-3H2V4h4V2h2zm9 1a4 4 0 0 1 4 4v2h-2V7a2 2 0 0 0-2-2h-3V3h3zM6 6H4v3h2V6zm4 0H8v3h2V6z" fill="rgba(255,255,255,1)"></path></svg><svg class="imt-float-ball-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg" hidden="true"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg></div></div></div></div><div hidden="" class="imt-manga-button imt-no-events btn-animate " id="manga-button" style="transform: translateX(8px);"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="manhua"><path id="Vector" d="M14.8853 4.92364C14.8853 4.92364 16.3905 10.4362 22.6668 4C22.6668 4 20.3381 10.8907 25.3364 10.0843C25.3364 10.0843 22.0563 15.6994 29 18.0599C29 18.0599 22.9934 19.306 21.1617 28C21.1617 28 17.7679 24.54 14.8853 27.3549C14.8853 27.3549 13.3233 23.5724 7.33097 26.27C7.33097 26.27 10.1141 20.6549 4.83179 21.0507C4.83179 21.0507 7.16057 18.8955 3 15.9047C3 15.9047 7.50137 16.1833 6.33697 11.7117C6.33697 11.7117 10.0005 12.3421 8.66576 6.82957C8.65156 6.81491 12.4855 9.80574 14.8853 4.92364Z" fill="#EA4C89"></path><path id="Vector_2" d="M20.8599 13.7022C20.885 13.1361 20.9543 12.5713 20.9959 12.0052C21.0337 11.568 20.8107 11.2794 20.3876 11.18C20.0759 11.1013 19.7508 11.0867 19.433 11.137C19.1951 11.1945 18.9542 11.2396 18.7113 11.2721C18.2403 11.3028 17.9973 11.5275 17.9796 11.988C17.977 12.0833 17.9596 12.1777 17.928 12.268C17.3034 13.9102 16.6774 15.5499 16.0503 17.1873C16.0301 17.2401 16.0062 17.2904 15.9671 17.3776C15.7291 16.8975 15.4281 16.4898 15.2745 15.9986C14.8073 14.5152 14.3186 13.033 13.8312 11.5594C13.6826 11.1112 13.3489 10.9344 12.8754 11.0216C12.7889 11.0365 12.7008 11.0398 12.6134 11.0314C12.2241 10.9938 11.8311 11.0404 11.4623 11.1677C11.0946 11.2991 10.9498 11.557 11.0152 11.9254C11.0428 12.0371 11.0643 12.1503 11.0795 12.2643C11.1223 13.1902 11.1777 14.1087 11.2054 15.0321C11.257 16.7992 11.2117 18.5651 11.0858 20.3284C11.0644 20.6354 11.0304 20.9424 11.0228 21.2494C11.0115 21.6092 11.1613 21.7811 11.5266 21.8143C11.9976 21.8573 12.4711 21.8708 12.9421 21.9088C13.0309 21.9201 13.121 21.9003 13.1962 21.8528C13.2714 21.8053 13.3268 21.7334 13.3527 21.6497C13.3996 21.5394 13.4252 21.4216 13.4282 21.3022C13.4295 20.8258 13.4207 20.3493 13.4081 19.8741C13.393 19.3264 13.3917 18.7763 13.3438 18.231C13.2857 17.5839 13.266 16.934 13.2847 16.2847C13.2847 16.2466 13.291 16.2073 13.2985 16.1312C13.3338 16.2024 13.3514 16.2356 13.3665 16.2712C13.9017 17.5228 14.3617 18.8037 14.7443 20.1074C14.7928 20.2421 14.7928 20.3889 14.7443 20.5237C14.6322 20.8196 14.7141 21.037 14.9659 21.1377C15.4445 21.3268 15.9331 21.4926 16.4155 21.6731C16.4865 21.7033 16.566 21.7091 16.6408 21.6895C16.7157 21.6698 16.7815 21.6259 16.8273 21.565C16.9085 21.4643 16.9743 21.3526 17.0225 21.2335C17.0537 21.1374 17.0798 21.0399 17.1006 20.9412C17.3185 20.2425 17.5653 19.5499 17.7517 18.8438C17.9785 17.9723 18.2624 17.1158 18.6018 16.2798C18.6201 16.2439 18.6411 16.2094 18.6647 16.1766C18.6761 16.2319 18.6761 16.254 18.6761 16.2761C18.6345 17.59 18.5955 18.8978 18.5501 20.2056C18.5363 20.5949 18.491 20.9829 18.4809 21.3722C18.4721 21.705 18.6207 21.8708 18.9557 21.9002C19.4355 21.9432 19.9191 21.9592 20.4002 21.9973C20.4888 22.0079 20.5784 21.9875 20.653 21.9399C20.7277 21.8922 20.7827 21.8203 20.8082 21.7369C20.8531 21.6305 20.8766 21.5167 20.8775 21.4017C20.88 20.7668 20.8674 20.132 20.8674 19.4971C20.8662 19.2846 20.8687 19.0722 20.8523 18.8622C20.8158 18.3968 20.7264 17.9314 20.7339 17.4685C20.7515 16.2122 20.8044 14.9572 20.8599 13.7022Z" fill="white"></path></g></svg><svg hidden="true" class="imt-manga-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg><svg class="imt-float-ball-loading" hidden="true" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 9px;"><path d="M9.42859 0C9.84288 0 10.1929 0.387143 10.1929 0.847143V3.99429C10.1929 4.45429 9.84431 4.84143 9.42859 4.84143C9.01431 4.84143 8.66431 4.45571 8.66431 3.99429V0.847143C8.66431 0.387143 9.01288 0 9.42859 0Z" fill="#E9E9E9"></path><path d="M14.1301 1.38877C14.5158 1.62591 14.6301 2.12163 14.4258 2.52305L12.9515 5.19448C12.901 5.28714 12.8325 5.36876 12.75 5.43455C12.6675 5.50035 12.5727 5.54898 12.4712 5.5776C12.3696 5.60621 12.2634 5.61424 12.1586 5.60119C12.0539 5.58814 11.9529 5.55429 11.8615 5.50163C11.6787 5.38432 11.5468 5.20237 11.4923 4.9921C11.4377 4.78184 11.4645 4.55874 11.5672 4.36734L13.0415 1.69591C13.2686 1.29448 13.7443 1.15305 14.1301 1.38877Z" fill="#989697"></path><path d="M17.4685 4.75707C17.5813 4.95451 17.6123 5.18824 17.5549 5.40825C17.4975 5.62826 17.3563 5.81705 17.1614 5.93422L14.4971 7.52564C14.0971 7.76993 13.6014 7.62422 13.3657 7.20707C13.2532 7.00994 13.2222 6.77667 13.2793 6.55702C13.3365 6.33737 13.4771 6.14874 13.6714 6.03136L16.3357 4.43993C16.7371 4.21993 17.2557 4.34136 17.4685 4.7585V4.75707Z" fill="#9B999A"></path><path d="M18.8572 9.42835C18.8572 9.84263 18.47 10.1926 18.01 10.1926H14.8629C14.4029 10.1926 14.0157 9.84406 14.0157 9.42835C14.0157 9.01406 14.4029 8.66406 14.8629 8.66406H18.01C18.47 8.66406 18.8572 9.01263 18.8572 9.42835Z" fill="#A3A1A2"></path><path d="M17.4686 14.1303C17.3515 14.3134 17.1697 14.4455 16.9594 14.5003C16.7491 14.5552 16.5259 14.5286 16.3343 14.426L13.6629 12.9517C13.5702 12.9012 13.4886 12.8327 13.4228 12.7503C13.357 12.6678 13.3084 12.573 13.2798 12.4714C13.2512 12.3698 13.2431 12.2636 13.2562 12.1589C13.2692 12.0542 13.3031 11.9532 13.3558 11.8617C13.4731 11.6789 13.655 11.547 13.8653 11.4925C14.0755 11.4379 14.2986 11.4647 14.49 11.5674L17.1615 13.0417C17.5629 13.2689 17.7043 13.7446 17.4686 14.1303Z" fill="#ABA9AA"></path><path opacity="0.7" d="M14.1 17.4686C13.9026 17.5814 13.6689 17.6124 13.4489 17.555C13.2288 17.4976 13.04 17.3564 12.9229 17.1615L11.3315 14.4972C11.0872 14.0972 11.2329 13.6015 11.65 13.3658C11.8472 13.2533 12.0804 13.2224 12.3001 13.2795C12.5197 13.3366 12.7084 13.4773 12.8257 13.6715L14.4172 16.3358C14.6372 16.7372 14.5157 17.2558 14.0986 17.4686H14.1Z" fill="#B2B2B2"></path><path opacity="0.6" d="M9.42859 18.8571C9.01431 18.8571 8.66431 18.4699 8.66431 18.0099V14.8628C8.66431 14.4028 9.01288 14.0156 9.42859 14.0156C9.84288 14.0156 10.1929 14.4028 10.1929 14.8628V18.0099C10.1929 18.4699 9.84431 18.8571 9.42859 18.8571Z" fill="#BAB8B9"></path><path opacity="0.5" d="M4.72717 17.4685C4.5441 17.3514 4.41195 17.1696 4.35713 16.9593C4.30231 16.749 4.32885 16.5258 4.43145 16.3342L5.90574 13.6628C5.95622 13.5701 6.02472 13.4885 6.1072 13.4227C6.18969 13.3569 6.2845 13.3083 6.38606 13.2797C6.48762 13.251 6.59387 13.243 6.69857 13.2561C6.80327 13.2691 6.90431 13.303 6.99574 13.3556C7.38145 13.5914 7.49431 14.0885 7.29002 14.4899L5.81574 17.1614C5.5886 17.5628 5.11288 17.7042 4.72717 17.4685Z" fill="#C2C0C1"></path><path opacity="0.4" d="M1.38862 14.1002C1.27584 13.9027 1.24483 13.669 1.30223 13.449C1.35964 13.229 1.50089 13.0402 1.69576 12.923L4.36004 11.3316C4.76004 11.0873 5.25576 11.233 5.49147 11.6502C5.60393 11.8473 5.63491 12.0806 5.5778 12.3002C5.52069 12.5199 5.38 12.7085 5.18576 12.8259L2.52004 14.4173C2.12004 14.6373 1.60004 14.5159 1.38862 14.0987V14.1002Z" fill="#CBCBCB"></path><path d="M0 9.42835C0 9.01406 0.387143 8.66406 0.847143 8.66406H3.99429C4.45429 8.66406 4.84143 9.01263 4.84143 9.42835C4.84143 9.84263 4.45571 10.1926 3.99429 10.1926H0.847143C0.387143 10.1926 0 9.84406 0 9.42835Z" fill="#D2D2D2"></path><path opacity="0.2" d="M1.38852 4.72705C1.50561 4.54398 1.68746 4.41183 1.89774 4.35701C2.10803 4.30219 2.33125 4.32873 2.52281 4.43133L5.19424 5.90562C5.28689 5.9561 5.36851 6.0246 5.43431 6.10708C5.5001 6.18957 5.54874 6.28438 5.57735 6.38594C5.60597 6.48749 5.61399 6.59375 5.60094 6.69845C5.5879 6.80315 5.55405 6.90419 5.50138 6.99562C5.38407 7.17844 5.20212 7.31029 4.99186 7.36484C4.78159 7.4194 4.55849 7.39263 4.3671 7.2899L1.69567 5.81562C1.29424 5.58847 1.15281 5.11276 1.38852 4.72705Z" fill="#DADADA"></path><path d="M4.75719 1.38849C4.95463 1.27571 5.18837 1.24471 5.40838 1.30211C5.62838 1.35952 5.81718 1.50077 5.93434 1.69564L7.52577 4.35992C7.77005 4.75992 7.62434 5.25564 7.20719 5.49135C7.01006 5.60381 6.77679 5.63479 6.55714 5.57768C6.33749 5.52056 6.14886 5.37988 6.03148 5.18564L4.44005 2.51992C4.22005 2.11992 4.34148 1.59992 4.75862 1.38849H4.75719Z" fill="#E2E2E2"></path></svg><div style="position: relative; pointer-events: all; display: inline-block;"><div><svg class="imt-manga-feedback" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.9999 3C15.1688 3 15.336 3.03326 15.492 3.09787C15.648 3.16248 15.7897 3.25719 15.9091 3.37658C16.0285 3.49597 16.1232 3.6377 16.1878 3.79369C16.2524 3.94968 16.2857 4.11687 16.2857 4.28571V12.8571C16.2857 13.026 16.2524 13.1932 16.1878 13.3492C16.1232 13.5052 16.0285 13.6469 15.9091 13.7663C15.7897 13.8857 15.648 13.9804 15.492 14.045C15.336 14.1096 15.1688 14.1429 14.9999 14.1429H8.32366L5.37766 16.0736C5.313 16.1159 5.23809 16.14 5.16086 16.1433C5.08363 16.1465 5.00696 16.1288 4.93897 16.0921C4.87097 16.0553 4.81418 16.0008 4.77462 15.9344C4.73506 15.868 4.71419 15.7922 4.71423 15.7149V14.1429H2.99995C2.83111 14.1429 2.66392 14.1096 2.50793 14.045C2.35194 13.9804 2.2102 13.8857 2.09081 13.7663C1.97142 13.6469 1.87672 13.5052 1.8121 13.3492C1.74749 13.1932 1.71423 13.026 1.71423 12.8571V4.28571C1.71423 3.94472 1.84969 3.61769 2.09081 3.37658C2.33193 3.13546 2.65896 3 2.99995 3H14.9999ZM14.9999 4.28571H2.99995V12.8571H5.99995V14.1287L7.94009 12.8571H14.9999V4.28571ZM9.54852 8.57143V9.85714H5.99995V8.57143H9.54852ZM11.9999 6V7.28571H5.99995V6H11.9999Z" fill="#6C6F73"></path></svg></div></div></div><div class="imt-fb-more-buttons btn-animate" style="margin-top: 12px; transform: translateX(60px);"><div class="btn-animate" style="position: relative; pointer-events: all; display: inline-block;"><div><div class="imt-fb-more-button"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.6861 1L15.2353 4.54635V7.11765V14.6471V15.5882C15.2353 15.9627 15.0866 16.3217 14.8218 16.5865C14.557 16.8513 14.198 17 13.8235 17H4.41176C4.03734 17 3.67825 16.8513 3.4135 16.5865C3.14874 16.3217 3 15.9627 3 15.5882V14.6471V7.11765V2.41176C3 2.03734 3.14874 1.67825 3.4135 1.4135C3.67825 1.14874 4.03734 1 4.41176 1H11.6861ZM11.8692 3.17882V4.74212H13.4334L11.8692 3.17882ZM4.41171 15.5882V14.647V2.41176H10.4574L10.4578 6.15341H13.8235V14.647V15.5882H4.41171ZM12.7739 7.51746H5.46094V8.6155H12.7739V7.51746ZM5.46094 9.98805H12.7739V11.0861H5.46094V9.98805ZM9.5127 12.36H5.46094V13.458H9.5127V12.36Z" fill="#6C6F73"></path></svg></div></div></div><div class="btn-animate" style="position: relative; pointer-events: all; display: inline-block;"><div><div class="imt-fb-more-button"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.55741 0L9.06847 0.00329403C9.84824 0.00470579 10.4802 0.636235 10.4812 1.41647L10.4821 1.82588C10.9687 2.0278 11.4297 2.28671 11.8553 2.59718L12.1913 2.40329C12.516 2.21676 12.9013 2.1665 13.2629 2.26352C13.6246 2.36055 13.933 2.59695 14.1207 2.92094L15.3795 5.09365C15.5601 5.40546 15.6149 5.7744 15.5328 6.12523C15.4507 6.47606 15.2378 6.78235 14.9376 6.98165L14.8609 7.02871L14.5235 7.22353C14.5819 7.76273 14.5736 8.30708 14.4986 8.84424L14.7372 8.98259C15.0496 9.16307 15.2812 9.45606 15.3848 9.80165C15.4884 10.1472 15.456 10.5193 15.2944 10.8419L15.2553 10.9153L14.076 12.9576C13.8955 13.27 13.6025 13.5017 13.2569 13.6053C12.9113 13.7088 12.5392 13.6765 12.2167 13.5148L12.1433 13.4753L11.8172 13.2871C11.4074 13.5817 10.9651 13.8283 10.4991 14.0221L10.4995 14.5831C10.5 14.9434 10.3629 15.2904 10.1163 15.5532C9.86972 15.816 9.53215 15.9748 9.17247 15.9972L9.08306 16L6.57153 15.9967C6.19697 15.9961 5.83793 15.847 5.57312 15.5821C5.30831 15.3172 5.15932 14.9581 5.15883 14.5835L5.15788 13.9073C4.76852 13.7244 4.39771 13.5044 4.05059 13.2504L3.44918 13.5967C3.12448 13.7834 2.73902 13.8337 2.37726 13.7367C2.01551 13.6397 1.70698 13.4032 1.5193 13.0791L0.260473 10.9064C0.0799611 10.5945 0.0252226 10.2255 0.107423 9.87467C0.189623 9.52384 0.402569 9.21757 0.702826 9.01835L0.779062 8.97129L1.3913 8.61835C1.34424 8.17129 1.34188 7.71765 1.38706 7.26494L0.707532 6.87247C0.395061 6.69207 0.163305 6.39911 0.0596515 6.05351C-0.0440025 5.70791 -0.0117246 5.33577 0.149885 5.01318L0.189415 4.93976L1.36871 2.89741C1.54919 2.58502 1.84218 2.35337 2.18777 2.2498C2.53336 2.14624 2.90547 2.17859 3.228 2.34023L3.30141 2.37976L3.89436 2.72188C4.28027 2.42082 4.69854 2.1637 5.14141 1.95529L5.14047 1.41694C5.14001 1.05657 5.27707 0.709596 5.52367 0.446813C5.77028 0.184029 6.10786 0.0252343 6.46753 0.00282354L6.55741 0ZM6.55553 1.41506L6.55694 2.85271L5.74377 3.23576C5.39553 3.39906 5.06706 3.60094 4.764 3.83718L4.01247 4.424L2.62941 3.62494L2.59365 3.60518L1.41483 5.64753L2.88636 6.49694L2.79506 7.40612C2.75968 7.7598 2.76078 8.11619 2.79836 8.46965L2.8953 9.38541L1.48494 10.1976L2.7433 12.3704L4.14377 11.5647L4.88636 12.1087C5.15997 12.309 5.45231 12.4823 5.7593 12.6264L6.57106 13.008L6.57388 14.5816L9.08447 14.5849L9.08306 13.0791L9.95553 12.7158C10.3216 12.5635 10.6689 12.3698 10.9908 12.1384L11.7329 11.6047L12.8506 12.2499L14.0289 10.2075L12.9654 9.592L13.0972 8.64847C13.1561 8.22659 13.1628 7.79904 13.1169 7.37553L13.0181 6.45882L14.1555 5.80235L12.8967 3.62965L11.7645 4.28235L11.0214 3.74024C10.686 3.4956 10.3229 3.29152 9.93953 3.13224L9.06894 2.77082L9.06659 1.41835L6.55553 1.41506ZM9.37153 5.47624C10.0214 5.85201 10.4955 6.47036 10.6898 7.19547C10.8841 7.92058 10.7827 8.69316 10.4078 9.34353C10.2223 9.66543 9.97517 9.9476 9.68053 10.1739C9.38589 10.4002 9.04953 10.5662 8.69068 10.6623C8.33183 10.7585 7.95754 10.7829 7.58923 10.7343C7.22092 10.6856 6.86582 10.5648 6.54424 10.3788C5.89445 10.003 5.4204 9.38458 5.2262 8.65948C5.032 7.93438 5.13352 7.16184 5.50847 6.51153C5.69395 6.18963 5.94107 5.90746 6.23571 5.68117C6.53034 5.45488 6.86671 5.28891 7.22556 5.19275C7.58441 5.09659 7.9587 5.07213 8.32701 5.12077C8.69532 5.16942 9.05042 5.29021 9.372 5.47624H9.37153ZM6.73388 7.21835C6.54638 7.54388 6.49567 7.9305 6.5929 8.29336C6.69012 8.65623 6.92733 8.96571 7.25247 9.15388C7.41305 9.24679 7.59037 9.30712 7.77429 9.33143C7.9582 9.35574 8.14511 9.34355 8.32431 9.29556C8.50351 9.24757 8.67149 9.16472 8.81864 9.05174C8.96579 8.93877 9.08923 8.7979 9.18188 8.63718C9.55883 7.98353 9.356 7.15435 8.73435 6.74494L8.66377 6.70118L8.59035 6.66165C8.26834 6.49988 7.89663 6.46742 7.55145 6.57093C7.20626 6.67444 6.91375 6.90608 6.73388 7.21835Z" fill="#6C6F73"></path></svg></div></div></div></div><div class="imt-fb-more-buttons btn-animate" style="margin-top: 12px; transform: translateX(60px);" hidden=""><div class="btn-animate" style="position: relative; pointer-events: all; display: inline-block;"><div><svg class="imt-manga-feedback" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.9999 3C15.1688 3 15.336 3.03326 15.492 3.09787C15.648 3.16248 15.7897 3.25719 15.9091 3.37658C16.0285 3.49597 16.1232 3.6377 16.1878 3.79369C16.2524 3.94968 16.2857 4.11687 16.2857 4.28571V12.8571C16.2857 13.026 16.2524 13.1932 16.1878 13.3492C16.1232 13.5052 16.0285 13.6469 15.9091 13.7663C15.7897 13.8857 15.648 13.9804 15.492 14.045C15.336 14.1096 15.1688 14.1429 14.9999 14.1429H8.32366L5.37766 16.0736C5.313 16.1159 5.23809 16.14 5.16086 16.1433C5.08363 16.1465 5.00696 16.1288 4.93897 16.0921C4.87097 16.0553 4.81418 16.0008 4.77462 15.9344C4.73506 15.868 4.71419 15.7922 4.71423 15.7149V14.1429H2.99995C2.83111 14.1429 2.66392 14.1096 2.50793 14.045C2.35194 13.9804 2.2102 13.8857 2.09081 13.7663C1.97142 13.6469 1.87672 13.5052 1.8121 13.3492C1.74749 13.1932 1.71423 13.026 1.71423 12.8571V4.28571C1.71423 3.94472 1.84969 3.61769 2.09081 3.37658C2.33193 3.13546 2.65896 3 2.99995 3H14.9999ZM14.9999 4.28571H2.99995V12.8571H5.99995V14.1287L7.94009 12.8571H14.9999V4.28571ZM9.54852 8.57143V9.85714H5.99995V8.57143H9.54852ZM11.9999 6V7.28571H5.99995V6H11.9999Z" fill="#6C6F73"></path></svg></div></div></div></div></div></template></div></html>