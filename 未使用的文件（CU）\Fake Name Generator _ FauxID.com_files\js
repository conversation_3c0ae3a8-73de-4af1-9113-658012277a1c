
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__cid"}],
  "tags":[{"function":"__rep","once_per_event":true,"vtp_containerId":["macro",1],"tag_id":1}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"}],
  "rules":[[["if",0],["add",0]]]
},
"runtime":[ [50,"__cid",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"containerId"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 
]
,"entities":{
"__cid":{"2":true,"4":true,"3":true}
,
"__e":{"2":true,"4":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__cid":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}


}



,"security_groups":{
"google":[
"__cid"
,
"__e"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),fa=function(a,b){if(b)a:{for(var c=ea,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ha=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if(typeof Object.setPrototypeOf=="function")ka=Object.setPrototypeOf;else{var ma;a:{var na={a:!0},oa={};try{oa.__proto__=na;ma=oa.a;break a}catch(a){}ma=!1}ka=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var pa=ka,qa=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Yo=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ra=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},sa=function(a){return a instanceof Array?a:ra(l(a))},ua=function(a){return ta(a,a)},ta=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},va=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};fa("Object.assign",function(a){return a||va});
var wa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var xa=this||self;var ya=function(a,b){this.type=a;this.data=b};var za=function(){this.map={};this.C={}};za.prototype.get=function(a){return this.map["dust."+a]};za.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};za.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};za.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Aa=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};za.prototype.qa=function(){return Aa(this,1)};za.prototype.bc=function(){return Aa(this,2)};za.prototype.Gb=function(){return Aa(this,3)};var Ba=function(){};Ba.prototype.reset=function(){};var Da=function(a,b){this.O=a;this.parent=b;this.C=this.H=void 0;this.Ac=!1;this.N=function(c,d,e){return c.apply(d,e)};this.values=new za};Da.prototype.add=function(a,b){Ea(this,a,b,!1)};var Ea=function(a,b,c,d){if(!a.Ac)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};Da.prototype.set=function(a,b){this.Ac||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Da.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Da.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Fa=function(a){var b=new Da(a.O,a);a.H&&(b.H=a.H);b.N=a.N;b.C=a.C;return b};Da.prototype.Id=function(){return this.O};Da.prototype.Qa=function(){this.Ac=!0};var Ga=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.yk=a;this.kk=c===void 0?!1:c;this.debugInfo=[];this.C=b};qa(Ga,Error);var Ha=function(a){return a instanceof Ga?a:new Ga(a,void 0,!0)};function Ja(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=La(a,e.value),c instanceof ya);e=d.next());return c}function La(a,b){try{var c=l(b),d=c.next().value,e=ra(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ha(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(sa(e)))}catch(h){var g=a.H;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Ma=function(){this.H=new Ba;this.C=new Da(this.H)};k=Ma.prototype;k.Id=function(){return this.H};k.execute=function(a){return this.Ai([a].concat(sa(wa.apply(1,arguments))))};k.Ai=function(){for(var a,b=l(wa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=La(this.C,c.value);return a};k.Zl=function(a){var b=wa.apply(1,arguments),c=Fa(this.C);c.C=a;for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=La(c,f.value);return d};k.Qa=function(){this.C.Qa()};var Na=function(){this.sa=!1;this.W=new za};k=Na.prototype;k.get=function(a){return this.W.get(a)};k.set=function(a,b){this.sa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.sa||this.W.remove(a)};k.qa=function(){return this.W.qa()};k.bc=function(){return this.W.bc()};k.Gb=function(){return this.W.Gb()};k.Qa=function(){this.sa=!0};k.Ac=function(){return this.sa};function Oa(){for(var a=Pa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Qa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Pa,Ra;function Sa(a){Pa=Pa||Qa();Ra=Ra||Oa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Pa[m],Pa[n],Pa[p],Pa[q])}return b.join("")}
function Ta(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ra[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Pa=Pa||Qa();Ra=Ra||Oa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Ua={};function Va(a,b){Ua[a]=Ua[a]||[];Ua[a][b]=!0}function Xa(a){var b=Ua[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Sa(c.join("")).replace(/\.+$/,"")}function Ya(){for(var a=[],b=Ua.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function $a(){}function ab(a){return typeof a==="function"}function bb(a){return typeof a==="string"}function cb(a){return typeof a==="number"&&!isNaN(a)}function db(a){return Array.isArray(a)?a:[a]}function eb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function fb(a,b){if(!cb(a)||!cb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function gb(a,b){for(var c=new hb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function ib(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function jb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function kb(a){return Math.round(Number(a))||0}function lb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function mb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function nb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function ob(){return new Date(Date.now())}function pb(){return ob().getTime()}var hb=function(){this.prefix="gtm.";this.values={}};hb.prototype.set=function(a,b){this.values[this.prefix+a]=b};hb.prototype.get=function(a){return this.values[this.prefix+a]};hb.prototype.contains=function(a){return this.get(a)!==void 0};
function qb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function rb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function sb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function tb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function ub(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function vb(a,b){var c=z;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function wb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var xb=/^\w{1,9}$/;function yb(a,b){a=a||{};b=b||",";var c=[];ib(a,function(d,e){xb.test(d)&&e&&c.push(d)});return c.join(b)}function zb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Ab(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Bb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Cb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Db=globalThis.trustedTypes,Eb;function Fb(){var a=null;if(!Db)return a;try{var b=function(c){return c};a=Db.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Gb(){Eb===void 0&&(Eb=Fb());return Eb};var Hb=function(a){this.C=a};Hb.prototype.toString=function(){return this.C+""};function Ib(a){var b=a,c=Gb(),d=c?c.createScriptURL(b):b;return new Hb(d)}function Jb(a){if(a instanceof Hb)return a.C;throw Error("");};var Kb=ua([""]),Lb=ta(["\x00"],["\\0"]),Mb=ta(["\n"],["\\n"]),Nb=ta(["\x00"],["\\u0000"]);function Pb(a){return a.toString().indexOf("`")===-1}Pb(function(a){return a(Kb)})||Pb(function(a){return a(Lb)})||Pb(function(a){return a(Mb)})||Pb(function(a){return a(Nb)});var Qb=function(a){this.C=a};Qb.prototype.toString=function(){return this.C};var Rb=function(a){this.Bn=a};function Sb(a){return new Rb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Tb=[Sb("data"),Sb("http"),Sb("https"),Sb("mailto"),Sb("ftp"),new Rb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Ub(a){var b;b=b===void 0?Tb:b;if(a instanceof Qb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Rb&&d.Bn(a))return new Qb(a)}}var Vb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Wb(a){var b;if(a instanceof Qb)if(a instanceof Qb)b=a.C;else throw Error("");else b=Vb.test(a)?a:void 0;return b};function Xb(a,b){var c=Wb(b);c!==void 0&&(a.action=c)};function Yb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var Zb=function(a){this.C=a};Zb.prototype.toString=function(){return this.C+""};var ac=function(){this.C=$b[0].toLowerCase()};ac.prototype.toString=function(){return this.C};function bc(a,b){var c=[new ac];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof ac)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var cc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function dc(a){return a===null?"null":a===void 0?"undefined":a};var z=window,ec=window.history,A=document,fc=navigator;function gc(){var a;try{a=fc.serviceWorker}catch(b){return}return a}var hc=A.currentScript,ic=hc&&hc.src;function jc(a,b){var c=z[a];z[a]=c===void 0?b:c;return z[a]}function kc(a){return(fc.userAgent||"").indexOf(a)!==-1}function lc(){return kc("Firefox")||kc("FxiOS")}function mc(){return(kc("GSA")||kc("GoogleApp"))&&(kc("iPhone")||kc("iPad"))}function nc(){return kc("Edg/")||kc("EdgA/")||kc("EdgiOS/")}
var oc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},pc={onload:1,src:1,width:1,height:1,style:1};function qc(a,b,c){b&&ib(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function rc(a,b,c,d,e){var f=A.createElement("script");qc(f,d,oc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ib(dc(a));f.src=Jb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function sc(){if(ic){var a=ic.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function tc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);qc(g,c,pc);d&&ib(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function uc(a,b,c,d){return vc(a,b,c,d)}function wc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function xc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function D(a){z.setTimeout(a,0)}function yc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function zc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Ac(a){var b=A.createElement("div"),c=b,d,e=dc("A<div>"+a+"</div>"),f=Gb(),g=f?f.createHTML(e):e;d=new Zb(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof Zb)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Bc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Cc(a,b,c){var d;try{d=fc.sendBeacon&&fc.sendBeacon(a)}catch(e){Va("TAGGING",15)}d?b==null||b():vc(a,b,c)}function Dc(a,b){try{return fc.sendBeacon(a,b)}catch(c){Va("TAGGING",15)}return!1}var Ec={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Fc(a,b,c,d,e){if(Gc()){var f=Object.assign({},Ec);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=z.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.ji)return e==null||e(),!1;if(b){var h=
Dc(a,b);h?d==null||d():e==null||e();return h}Hc(a,d,e);return!0}function Gc(){return typeof z.fetch==="function"}function Ic(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Jc(){var a=z.performance;if(a&&ab(a.now))return a.now()}
function Kc(){var a,b=z.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Lc(){return z.performance||void 0}function Mc(){var a=z.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var vc=function(a,b,c,d){var e=new Image(1,1);qc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Hc=Cc;function Nc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Oc(a,b){return this.evaluate(a)===this.evaluate(b)}function Pc(a,b){return this.evaluate(a)||this.evaluate(b)}function Qc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Rc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Sc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=z.location.href;d instanceof Na&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Tc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Uc=function(a){if(a==null)return String(a);var b=Tc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Vc=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Wc=function(a){if(!a||Uc(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Vc(a,"constructor")&&!Vc(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
Vc(a,b)},Xc=function(a,b){var c=b||(Uc(a)=="array"?[]:{}),d;for(d in a)if(Vc(a,d)){var e=a[d];Uc(e)=="array"?(Uc(c[d])!="array"&&(c[d]=[]),c[d]=Xc(e,c[d])):Wc(e)?(Wc(c[d])||(c[d]={}),c[d]=Xc(e,c[d])):c[d]=e}return c};function Yc(a){if(a==void 0||Array.isArray(a)||Wc(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function Zc(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var $c=function(a){a=a===void 0?[]:a;this.W=new za;this.values=[];this.sa=!1;for(var b in a)a.hasOwnProperty(b)&&(Zc(b)?this.values[Number(b)]=a[Number(b)]:this.W.set(b,a[b]))};k=$c.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof $c?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.sa)if(a==="length"){if(!Zc(b))throw Ha(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else Zc(a)?this.values[Number(a)]=b:this.W.set(a,b)};k.get=function(a){return a==="length"?this.length():Zc(a)?this.values[Number(a)]:this.W.get(a)};k.length=function(){return this.values.length};k.qa=function(){for(var a=this.W.qa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.bc=function(){for(var a=this.W.bc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Gb=function(){for(var a=this.W.Gb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){Zc(a)?delete this.values[Number(a)]:this.sa||this.W.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,sa(wa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=wa.apply(2,arguments);return b===void 0&&c.length===0?new $c(this.values.splice(a)):new $c(this.values.splice.apply(this.values,[a,b||0].concat(sa(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,sa(wa.apply(0,arguments)))};k.has=function(a){return Zc(a)&&this.values.hasOwnProperty(a)||this.W.has(a)};k.Qa=function(){this.sa=!0;Object.freeze(this.values)};k.Ac=function(){return this.sa};
function ad(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var bd=function(a,b){this.functionName=a;this.Hd=b;this.W=new za;this.sa=!1};k=bd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new $c(this.qa())};k.invoke=function(a){return this.Hd.call.apply(this.Hd,[new cd(this,a)].concat(sa(wa.apply(1,arguments))))};k.kb=function(a){var b=wa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(sa(b)))}catch(c){}};k.get=function(a){return this.W.get(a)};
k.set=function(a,b){this.sa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.sa||this.W.remove(a)};k.qa=function(){return this.W.qa()};k.bc=function(){return this.W.bc()};k.Gb=function(){return this.W.Gb()};k.Qa=function(){this.sa=!0};k.Ac=function(){return this.sa};var dd=function(a,b){bd.call(this,a,b)};qa(dd,bd);var ed=function(a,b){bd.call(this,a,b)};qa(ed,bd);var cd=function(a,b){this.Hd=a;this.J=b};
cd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?La(b,a):a};cd.prototype.getName=function(){return this.Hd.getName()};cd.prototype.Id=function(){return this.J.Id()};var fd=function(){this.map=new Map};fd.prototype.set=function(a,b){this.map.set(a,b)};fd.prototype.get=function(a){return this.map.get(a)};var gd=function(){this.keys=[];this.values=[]};gd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};gd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function hd(){try{return Map?new fd:new gd}catch(a){return new gd}};var id=function(a){if(a instanceof id)return a;if(Yc(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};id.prototype.getValue=function(){return this.value};id.prototype.toString=function(){return String(this.value)};var kd=function(a){this.promise=a;this.sa=!1;this.W=new za;this.W.set("then",jd(this));this.W.set("catch",jd(this,!0));this.W.set("finally",jd(this,!1,!0))};k=kd.prototype;k.get=function(a){return this.W.get(a)};k.set=function(a,b){this.sa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.sa||this.W.remove(a)};k.qa=function(){return this.W.qa()};k.bc=function(){return this.W.bc()};k.Gb=function(){return this.W.Gb()};
var jd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new dd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof dd||(d=void 0);e instanceof dd||(e=void 0);var f=Fa(this.J),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new id(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new kd(h)})};kd.prototype.Qa=function(){this.sa=!0};kd.prototype.Ac=function(){return this.sa};function ld(a,b,c){var d=hd(),e=function(g,h){for(var m=g.qa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof $c){var m=[];d.set(g,m);for(var n=g.qa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof kd)return g.promise.then(function(u){return ld(u,b,1)},function(u){return Promise.reject(ld(u,b,1))});if(g instanceof Na){var q={};d.set(g,q);e(g,q);return q}if(g instanceof dd){var r=function(){for(var u=
wa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=md(u[w],b,c);var x=new Da(b?b.Id():new Ba);b&&(x.C=b.C);return f(g.invoke.apply(g,[x].concat(sa(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof id&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function md(a,b,c){var d=hd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||jb(g)){var m=new $c;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(Wc(g)){var p=new Na;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new dd("",function(){for(var u=wa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=ld(this.evaluate(u[w]),b,c);return f((0,this.J.N)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new id(g)};return f(a)};var nd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof $c)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new $c(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new $c(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new $c(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
sa(wa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ha(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ha(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ha(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ha(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=ad(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new $c(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=ad(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(sa(wa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,sa(wa.apply(1,arguments)))}};var od={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},pd=new ya("break"),qd=new ya("continue");function rd(a,b){return this.evaluate(a)+this.evaluate(b)}function sd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function td(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof $c))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ha(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=ld(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ha(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(od.hasOwnProperty(e)){var m=2;m=1;var n=ld(f,void 0,m);return md(d[e].apply(d,n),this.J)}throw Ha(Error("TypeError: "+e+" is not a function"));}if(d instanceof $c){if(d.has(e)){var p=d.get(String(e));if(p instanceof dd){var q=ad(f);return p.invoke.apply(p,[this.J].concat(sa(q)))}throw Ha(Error("TypeError: "+e+" is not a function"));}if(nd.supportedMethods.indexOf(e)>=
0){var r=ad(f);return nd[e].call.apply(nd[e],[d,this.J].concat(sa(r)))}}if(d instanceof dd||d instanceof Na||d instanceof kd){if(d.has(e)){var t=d.get(e);if(t instanceof dd){var u=ad(f);return t.invoke.apply(t,[this.J].concat(sa(u)))}throw Ha(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof dd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof id&&e==="toString")return d.toString();throw Ha(Error("TypeError: Object has no '"+
e+"' property."));}function ud(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function vd(){var a=wa.apply(0,arguments),b=Fa(this.J),c=Ja(b,a);if(c instanceof ya)return c}function wd(){return pd}function xd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof ya)return d}}
function yd(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ea(a,c,d,!0)}}}function zd(){return qd}function Ad(a,b){return new ya(a,this.evaluate(b))}function Bd(a,b){for(var c=wa.apply(2,arguments),d=new $c,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(sa(c));this.J.add(a,this.evaluate(g))}function Cd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Dd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof id,f=d instanceof id;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Ed(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Fd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ja(f,d);if(g instanceof ya){if(g.type==="break")break;if(g.type==="return")return g}}}
function Gd(a,b,c){if(typeof b==="string")return Fd(a,function(){return b.length},function(f){return f},c);if(b instanceof Na||b instanceof kd||b instanceof $c||b instanceof dd){var d=b.qa(),e=d.length;return Fd(a,function(){return e},function(f){return d[f]},c)}}function Hd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Gd(function(h){g.set(d,h);return g},e,f)}
function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Gd(function(h){var m=Fa(g);Ea(m,d,h,!0);return m},e,f)}function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Gd(function(h){var m=Fa(g);m.add(d,h);return m},e,f)}function Kd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){g.set(d,h);return g},e,f)}
function Nd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){var m=Fa(g);Ea(m,d,h,!0);return m},e,f)}function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){var m=Fa(g);m.add(d,h);return m},e,f)}
function Md(a,b,c){if(typeof b==="string")return Fd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof $c)return Fd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ha(Error("The value is not iterable."));}
function Pd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof $c))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),m=Fa(g);for(e(g,m);La(m,b);){var n=Ja(m,h);if(n instanceof ya){if(n.type==="break")break;if(n.type==="return")return n}var p=Fa(g);e(m,p);La(p,c);m=p}}
function Qd(a,b){var c=wa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof $c))throw Error("Error: non-List value given for Fn argument names.");return new dd(a,function(){return function(){var f=wa.apply(0,arguments),g=Fa(d);g.C===void 0&&(g.C=this.J.C);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new $c(h));var r=Ja(g,c);if(r instanceof ya)return r.type===
"return"?r.data:r}}())}function Rd(a){var b=this.evaluate(a),c=this.J;if(Sd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Td(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ha(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Na||d instanceof kd||d instanceof $c||d instanceof dd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:Zc(e)&&(c=d[e]);else if(d instanceof id)return;return c}function Ud(a,b){return this.evaluate(a)>this.evaluate(b)}function Vd(a,b){return this.evaluate(a)>=this.evaluate(b)}
function Wd(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof id&&(c=c.getValue());d instanceof id&&(d=d.getValue());return c===d}function Xd(a,b){return!Wd.call(this,a,b)}function Yd(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ja(this.J,d);if(e instanceof ya)return e}var Sd=!1;
function Zd(a,b){return this.evaluate(a)<this.evaluate(b)}function $d(a,b){return this.evaluate(a)<=this.evaluate(b)}function ae(){for(var a=new $c,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function be(){for(var a=new Na,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ce(a,b){return this.evaluate(a)%this.evaluate(b)}
function de(a,b){return this.evaluate(a)*this.evaluate(b)}function ee(a){return-this.evaluate(a)}function fe(a){return!this.evaluate(a)}function ge(a,b){return!Dd.call(this,a,b)}function he(){return null}function ie(a,b){return this.evaluate(a)||this.evaluate(b)}function je(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ke(a){return this.evaluate(a)}function le(){return wa.apply(0,arguments)}function me(a){return new ya("return",this.evaluate(a))}
function ne(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ha(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof dd||d instanceof $c||d instanceof Na)&&d.set(String(e),f);return f}function oe(a,b){return this.evaluate(a)-this.evaluate(b)}
function pe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof ya){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof ya&&(g.type==="return"||g.type==="continue")))return g}
function qe(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function re(a){var b=this.evaluate(a);return b instanceof dd?"function":typeof b}function se(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function te(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ja(this.J,e);if(f instanceof ya){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ja(this.J,e);if(g instanceof ya){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function ue(a){return~Number(this.evaluate(a))}function ve(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function we(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function xe(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function ye(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function ze(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ae(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Be(){}
function Ce(a,b,c){try{var d=this.evaluate(b);if(d instanceof ya)return d}catch(h){if(!(h instanceof Ga&&h.kk))throw h;var e=Fa(this.J);a!==""&&(h instanceof Ga&&(h=h.yk),e.add(a,new id(h)));var f=this.evaluate(c),g=Ja(e,f);if(g instanceof ya)return g}}function De(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ga&&f.kk))throw f;c=f}var e=this.evaluate(b);if(e instanceof ya)return e;if(c)throw c;if(d instanceof ya)return d};var Fe=function(){this.C=new Ma;Ee(this)};Fe.prototype.execute=function(a){return this.C.Ai(a)};var Ee=function(a){var b=function(c,d){var e=new ed(String(c),d);e.Qa();a.C.C.set(String(c),e)};b("map",be);b("and",Nc);b("contains",Qc);b("equals",Oc);b("or",Pc);b("startsWith",Rc);b("variable",Sc)};var He=function(){this.H=!1;this.C=new Ma;Ge(this);this.H=!0};He.prototype.execute=function(a){return Ie(this.C.Ai(a))};var Je=function(a,b,c){return Ie(a.C.Zl(b,c))};He.prototype.Qa=function(){this.C.Qa()};
var Ge=function(a){var b=function(c,d){var e=String(c),f=new ed(e,d);f.Qa();a.C.C.set(e,f)};b(0,rd);b(1,sd);b(2,td);b(3,ud);b(56,ye);b(57,ve);b(58,ue);b(59,Ae);b(60,we);b(61,xe);b(62,ze);b(53,vd);b(4,wd);b(5,xd);b(68,Ce);b(52,yd);b(6,zd);b(49,Ad);b(7,ae);b(8,be);b(9,xd);b(50,Bd);b(10,Cd);b(12,Dd);b(13,Ed);b(67,De);b(51,Qd);b(47,Hd);b(54,Id);b(55,Jd);b(63,Pd);b(64,Kd);b(65,Nd);b(66,Od);b(15,Rd);b(16,Td);b(17,Td);b(18,Ud);b(19,Vd);b(20,Wd);b(21,Xd);b(22,Yd);b(23,Zd);b(24,$d);b(25,ce);b(26,de);b(27,
ee);b(28,fe);b(29,ge);b(45,he);b(30,ie);b(32,je);b(33,je);b(34,ke);b(35,ke);b(46,le);b(36,me);b(43,ne);b(37,oe);b(38,pe);b(39,qe);b(40,re);b(44,Be);b(41,se);b(42,te)};He.prototype.Id=function(){return this.C.Id()};function Ie(a){if(a instanceof ya||a instanceof dd||a instanceof $c||a instanceof Na||a instanceof kd||a instanceof id||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ke=function(a){this.message=a};function Le(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ke("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Me(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Ne=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Oe(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Le(e)+c}a<<=2;d||(a|=32);return c=""+Le(a|b)+c};var Pe=function(){function a(b){return{toString:function(){return b}}}return{Vk:a("consent"),Ki:a("convert_case_to"),Li:a("convert_false_to"),Mi:a("convert_null_to"),Ni:a("convert_true_to"),Oi:a("convert_undefined_to"),vo:a("debug_mode_metadata"),za:a("function"),sh:a("instance_name"),dm:a("live_only"),fm:a("malware_disabled"),METADATA:a("metadata"),im:a("original_activity_id"),Ho:a("original_vendor_template_id"),Go:a("once_on_load"),hm:a("once_per_event"),Sj:a("once_per_load"),Io:a("priority_override"),
Jo:a("respected_consent_types"),Zj:a("setup_tags"),og:a("tag_id"),dk:a("teardown_tags")}}();var nf;var of=[],pf=[],qf=[],rf=[],sf=[],tf,uf,vf;function wf(a){vf=vf||a}
function xf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)of.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)rf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)qf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||yf(p[r])}pf.push(p)}}
function yf(a){}var zf,Af=[],Bf=[];function Cf(a,b){var c={};c[Pe.za]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Df(a,b,c){try{return uf(Ef(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Ff(a){var b=a[Pe.za];if(!b)throw Error("Error: No function name given for function call.");return!!tf[b]}
var Ef=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Gf(a[e],b,c));return d},Gf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Gf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=of[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Pe.sh]);try{var m=Ef(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=If(m,{event:b,index:f,type:2,
name:h});zf&&(d=zf.Cm(d,m))}catch(y){b.logMacroError&&b.logMacroError(y,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Gf(a[n],b,c)]=Gf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Gf(a[q],b,c);vf&&(p=p||vf.yn(r));d.push(r)}return vf&&p?vf.Hm(d):d.join("");case "escape":d=Gf(a[1],b,c);if(vf&&Array.isArray(a[1])&&a[1][0]==="macro"&&vf.zn(a))return vf.Sn(d);d=String(d);for(var t=2;t<a.length;t++)Ye[a[t]]&&(d=Ye[a[t]](d));return d;
case "tag":var u=a[1];if(!rf[u])throw Error("Unable to resolve tag reference "+u+".");return{pk:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Pe.za]=a[1];var w=Df(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},If=function(a,b){var c=a[Pe.za],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=tf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Af.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&ub(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=of[q];break;case 1:r=rf[q];break;default:n="";break a}var t=r&&r[Pe.sh];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Bf.indexOf(c)===-1){Bf.push(c);
var x=pb();u=e(g);var y=pb()-x,B=pb();v=nf(c,h,b);w=y-(pb()-B)}else if(e&&(u=e(g)),!e||f)v=nf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),Yc(u)?(Array.isArray(u)?Array.isArray(v):Wc(u)?Wc(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Jf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};qa(Jf,Error);Jf.prototype.getMessage=function(){return this.message};function Kf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Kf(a[c],b[c])}};function Lf(){return function(a,b){var c;var d=Mf;a instanceof Ga?(a.C=d,c=a):c=new Ga(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Mf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)cb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Nf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Of(a),f=0;f<pf.length;f++){var g=pf[f],h=Pf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<rf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Pf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Of(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Df(qf[c],a));return b[c]}};function Qf(a,b){b[Pe.Ki]&&typeof a==="string"&&(a=b[Pe.Ki]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Pe.Mi)&&a===null&&(a=b[Pe.Mi]);b.hasOwnProperty(Pe.Oi)&&a===void 0&&(a=b[Pe.Oi]);b.hasOwnProperty(Pe.Ni)&&a===!0&&(a=b[Pe.Ni]);b.hasOwnProperty(Pe.Li)&&a===!1&&(a=b[Pe.Li]);return a};var Rf=function(){this.C={}},Tf=function(a,b){var c=Sf.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,sa(wa.apply(0,arguments)))})};function Uf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Jf(c,d,g);}}
function Vf(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(sa(wa.apply(1,arguments))));Uf(e,b,d,g);Uf(f,b,d,g)}}}};var Zf=function(){var a=data.permissions||{},b=Wf.ctid,c=this;this.H={};this.C=new Rf;var d={},e={},f=Vf(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(sa(wa.apply(1,arguments)))):{}});ib(a,function(g,h){function m(p){var q=wa.apply(1,arguments);if(!n[p])throw Xf(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(sa(q)))}var n={};ib(h,function(p,q){var r=Yf(p,q);n[p]=r.assert;d[p]||(d[p]=r.P);r.hk&&!e[p]&&(e[p]=r.hk)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw Xf(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(sa(t.slice(1))))}})},$f=function(a){return Sf.H[a]||function(){}};
function Yf(a,b){var c=Cf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=Xf;try{return If(c)}catch(d){return{assert:function(e){throw new Jf(e,{},"Permission "+e+" is unknown.");},P:function(){throw new Jf(a,{},"Permission "+a+" is unknown.");}}}}function Xf(a,b,c){return new Jf(a,b,c)};var ag=!1;var bg={};bg.Nk=lb('');bg.Nm=lb('');function gg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var hg=[],ig={};function jg(a){return hg[a]===void 0?!1:hg[a]};var kg=[];function lg(a){switch(a){case 0:return 0;case 38:return 14;case 50:return 10;case 51:return 11;case 52:return 1;case 53:return 2;case 54:return 7;case 73:return 3;case 74:return 12;case 113:return 13;case 114:return 4;case 116:return 5;case 134:return 9;case 135:return 6}}function mg(a,b){kg[a]=b;var c=lg(a);c!==void 0&&(hg[c]=b)}function G(a){mg(a,!0)}G(39);G(34);G(35);G(36);
G(56);G(109);G(145);G(18);
G(153);G(144);G(75);G(119);
G(58);G(6);G(110);
G(139);G(101);G(90);G(74);
G(115);G(159);G(131);
G(20);G(71);G(112);
G(154);G(116);
mg(23,!1),G(24);
ig[1]=gg('1',6E4);ig[3]=gg('10',1);ig[2]=gg('',50);
G(29);
G(10);G(89);
G(140);G(122);
G(157);
G(135);G(137);
G(126);G(27);
G(68);G(69);G(134);
G(51);G(50);G(93);
G(100);G(111);
G(152);G(99);

G(133);G(114);G(94);G(31);G(22);G(55);G(14);
G(150);G(151);
G(95);G(12);
G(15);G(148);
G(86);
G(96);G(76);G(77);G(28);
G(80);G(88);G(117);function H(a){return!!kg[a]}function ng(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?G(b):G(a)};var pg={},qg=(pg.uaa=!0,pg.uab=!0,pg.uafvl=!0,pg.uamb=!0,pg.uam=!0,pg.uap=!0,pg.uapv=!0,pg.uaw=!0,pg);
var yg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!wg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!xg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?ub(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},xg=/^[a-z$_][\w-$]*$/i,wg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var zg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ag(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Bg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Cg=new hb;function Dg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Cg.get(e);f||(f=new RegExp(b,d),Cg.set(e,f));return f.test(a)}catch(g){return!1}}function Eg(a,b){return String(a).indexOf(String(b))>=0}
function Fg(a,b){return String(a)===String(b)}function Gg(a,b){return Number(a)>=Number(b)}function Hg(a,b){return Number(a)<=Number(b)}function Ig(a,b){return Number(a)>Number(b)}function Jg(a,b){return Number(a)<Number(b)}function Kg(a,b){return ub(String(a),String(b))};var Rg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Sg={Fn:"function",PixieMap:"Object",List:"Array"};
function Tg(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Rg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof dd?n="Fn":m instanceof $c?n="List":m instanceof Na?n="PixieMap":m instanceof kd?n="PixiePromise":m instanceof id&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Sg[n]||n)+", which does not match required type ")+
((Sg[h]||h)+"."));}}}function I(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof dd?d.push("function"):g instanceof $c?d.push("Array"):g instanceof Na?d.push("Object"):g instanceof kd?d.push("Promise"):g instanceof id?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Ug(a){return a instanceof Na}function Vg(a){return Ug(a)||a===null||Wg(a)}
function Xg(a){return a instanceof dd}function Yg(a){return Xg(a)||a===null||Wg(a)}function Zg(a){return a instanceof $c}function $g(a){return a instanceof id}function ah(a){return typeof a==="string"}function bh(a){return ah(a)||a===null||Wg(a)}function ch(a){return typeof a==="boolean"}function dh(a){return ch(a)||Wg(a)}function eh(a){return ch(a)||a===null||Wg(a)}function fh(a){return typeof a==="number"}function Wg(a){return a===void 0};function gh(a){return""+a}
function hh(a,b){var c=[];return c};function ih(a,b){var c=new dd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ha(g);}});c.Qa();return c}
function jh(a,b){var c=new Na,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ab(e)?c.set(d,ih(a+"_"+d,e)):Wc(e)?c.set(d,jh(a+"_"+d,e)):(cb(e)||bb(e)||typeof e==="boolean")&&c.set(d,e)}c.Qa();return c};function kh(a,b){if(!ah(a))throw I(this.getName(),["string"],arguments);if(!bh(b))throw I(this.getName(),["string","undefined"],arguments);var c={},d=new Na;return d=jh("AssertApiSubject",
c)};function lh(a,b){if(!bh(b))throw I(this.getName(),["string","undefined"],arguments);if(a instanceof kd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Na;return d=jh("AssertThatSubject",c)};function mh(a){return function(){for(var b=wa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(ld(b[e],d));return md(a.apply(null,c))}}function nh(){for(var a=Math,b=oh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=mh(a[e].bind(a)))}return c};function ph(a){return a!=null&&ub(a,"__cvt_")};function qh(a){var b;return b};function rh(a){var b;return b};function sh(a){try{return encodeURI(a)}catch(b){}};function th(a){try{return encodeURIComponent(String(a))}catch(b){}};function yh(a){if(!bh(a))throw I(this.getName(),["string|undefined"],arguments);};function zh(a,b){if(!fh(a)||!fh(b))throw I(this.getName(),["number","number"],arguments);return fb(a,b)};function Ah(){return(new Date).getTime()};function Bh(a){if(a===null)return"null";if(a instanceof $c)return"array";if(a instanceof dd)return"function";if(a instanceof id){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ch(a){function b(c){return function(d){try{return c(d)}catch(e){(ag||bg.Nk)&&a.call(this,e.message)}}}return{parse:b(function(c){return md(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(ld(c))}),publicName:"JSON"}};function Dh(a){return kb(ld(a,this.J))};function Eh(a){return Number(ld(a,this.J))};function Fh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Gh(a,b,c){var d=null,e=!1;return e?d:null};var oh="floor ceil round max min abs pow sqrt".split(" ");function Hh(){var a={};return{Zm:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Kk:function(b,c){a[b]=c},reset:function(){a={}}}}function Ih(a,b){return function(){return dd.prototype.invoke.apply(a,[b].concat(sa(wa.apply(0,arguments))))}}
function Jh(a,b){if(!ah(a))throw I(this.getName(),["string","any"],arguments);}
function Kh(a,b){if(!ah(a)||!Ug(b))throw I(this.getName(),["string","PixieMap"],arguments);};var Lh={};
Lh.keys=function(a){return new $c};
Lh.values=function(a){return new $c};
Lh.entries=function(a){return new $c};
Lh.freeze=function(a){return a};Lh.delete=function(a,b){return!1};function M(a,b){var c=wa.apply(2,arguments),d=a.J.C;if(!d)throw Error("Missing program state.");if(d.Yn){try{d.ik.apply(null,[b].concat(sa(c)))}catch(e){throw Va("TAGGING",21),e;}return}d.ik.apply(null,[b].concat(sa(c)))};var Nh=function(){this.H={};this.C={};this.N=!0;};Nh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Nh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Nh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:ab(b)?ih(a,b):jh(a,b)};function Oh(a,b){var c=void 0;return c};function Ph(a,b){}Ph.K="internal.safeInvoke";function Qh(){var a={};
return a};var N={m:{Ca:"ad_personalization",T:"ad_storage",U:"ad_user_data",Z:"analytics_storage",Mb:"region",kc:"consent_updated",Cf:"wait_for_update",al:"app_remove",bl:"app_store_refund",fl:"app_store_subscription_cancel",il:"app_store_subscription_convert",jl:"app_store_subscription_renew",kl:"consent_update",Ri:"add_payment_info",Si:"add_shipping_info",hd:"add_to_cart",jd:"remove_from_cart",Ti:"view_cart",Cc:"begin_checkout",kd:"select_item",Ob:"view_item_list",mc:"select_promotion",Pb:"view_promotion",
Va:"purchase",ld:"refund",hb:"view_item",Ui:"add_to_wishlist",ml:"exception",nl:"first_open",ol:"first_visit",ka:"gtag.config",mb:"gtag.get",pl:"in_app_purchase",Dc:"page_view",ql:"screen_view",rl:"session_start",sl:"source_update",tl:"timing_complete",vl:"track_social",md:"user_engagement",wl:"user_id_update",Ud:"gclid_link_decoration_source",Vd:"gclid_storage_source",Qb:"gclgb",Wa:"gclid",Vi:"gclid_len",nd:"gclgs",od:"gcllp",pd:"gclst",na:"ads_data_redaction",Wd:"gad_source",Xd:"gad_source_src",
Ec:"gclid_url",Wi:"gclsrc",Yd:"gbraid",rd:"wbraid",xa:"allow_ad_personalization_signals",Gf:"allow_custom_scripts",Zd:"allow_direct_google_requests",Hf:"allow_display_features",If:"allow_enhanced_conversions",nb:"allow_google_signals",Ia:"allow_interest_groups",xl:"app_id",yl:"app_installer_id",zl:"app_name",Al:"app_version",Rb:"auid",Bl:"auto_detection_enabled",Fc:"aw_remarketing",Ng:"aw_remarketing_only",Jf:"discount",Kf:"aw_feed_country",Lf:"aw_feed_language",la:"items",Mf:"aw_merchant_id",Xi:"aw_basket_type",
ae:"campaign_content",be:"campaign_id",ce:"campaign_medium",de:"campaign_name",ee:"campaign",fe:"campaign_source",he:"campaign_term",wb:"client_id",Yi:"rnd",Og:"consent_update_type",Cl:"content_group",Dl:"content_type",xb:"conversion_cookie_prefix",ie:"conversion_id",Fa:"conversion_linker",Pg:"conversion_linker_disabled",Gc:"conversion_api",Nf:"cookie_deprecation",Xa:"cookie_domain",Ya:"cookie_expires",ib:"cookie_flags",Hc:"cookie_name",yb:"cookie_path",Ta:"cookie_prefix",nc:"cookie_update",sd:"country",
Ja:"currency",Qg:"customer_buyer_stage",je:"customer_lifetime_value",Rg:"customer_loyalty",Sg:"customer_ltv_bucket",ke:"custom_map",Tg:"gcldc",Ic:"dclid",Zi:"debug_mode",oa:"developer_id",El:"disable_merchant_reported_purchases",Jc:"dc_custom_params",Fl:"dc_natural_search",aj:"dynamic_event_settings",bj:"affiliation",Of:"checkout_option",Ug:"checkout_step",cj:"coupon",me:"item_list_name",Vg:"list_name",Gl:"promotions",ne:"shipping",Wg:"tax",Pf:"engagement_time_msec",Qf:"enhanced_client_id",Rf:"enhanced_conversions",
dj:"enhanced_conversions_automatic_settings",Sf:"estimated_delivery_date",Xg:"euid_logged_in_state",oe:"event_callback",Hl:"event_category",zb:"event_developer_id_string",Il:"event_label",Kc:"event",Tf:"event_settings",Uf:"event_timeout",Jl:"description",Kl:"fatal",Ll:"experiments",Yg:"firebase_id",ud:"first_party_collection",Vf:"_x_20",Tb:"_x_19",ej:"fledge_drop_reason",fj:"fledge",gj:"flight_error_code",ij:"flight_error_message",jj:"fl_activity_category",kj:"fl_activity_group",Zg:"fl_advertiser_id",
lj:"fl_ar_dedupe",pe:"match_id",mj:"fl_random_number",nj:"tran",oj:"u",Wf:"gac_gclid",vd:"gac_wbraid",pj:"gac_wbraid_multiple_conversions",qj:"ga_restrict_domain",rj:"ga_temp_client_id",Ml:"ga_temp_ecid",Lc:"gdpr_applies",sj:"geo_granularity",oc:"value_callback",Ub:"value_key",Mc:"google_analysis_params",wd:"_google_ng",xd:"google_signals",tj:"google_tld",qe:"gpp_sid",se:"gpp_string",Xf:"groups",uj:"gsa_experiment_id",te:"gtag_event_feature_usage",vj:"gtm_up",qc:"iframe_state",ue:"ignore_referrer",
ah:"internal_traffic_results",rc:"is_legacy_converted",sc:"is_legacy_loaded",Yf:"is_passthrough",Nc:"_lps",jb:"language",Zf:"legacy_developer_id_string",Ga:"linker",yd:"accept_incoming",Vb:"decorate_forms",da:"domains",uc:"url_position",cg:"merchant_feed_label",dg:"merchant_feed_language",eg:"merchant_id",wj:"method",Nl:"name",xj:"navigation_type",ve:"new_customer",fg:"non_interaction",Ol:"optimize_id",yj:"page_hostname",we:"page_path",Ka:"page_referrer",ob:"page_title",zj:"passengers",Aj:"phone_conversion_callback",
Pl:"phone_conversion_country_code",Bj:"phone_conversion_css_class",Ql:"phone_conversion_ids",Cj:"phone_conversion_number",Dj:"phone_conversion_options",Rl:"_platinum_request_status",bh:"_protected_audience_enabled",xe:"quantity",gg:"redact_device_info",eh:"referral_exclusion_definition",xo:"_request_start_time",Bb:"restricted_data_processing",Sl:"retoken",Tl:"sample_rate",fh:"screen_name",vc:"screen_resolution",Ej:"_script_source",Ul:"search_term",Za:"send_page_view",Oc:"send_to",Pc:"server_container_url",
ye:"session_duration",hg:"session_engaged",gh:"session_engaged_time",Wb:"session_id",ig:"session_number",ze:"_shared_user_id",Ae:"delivery_postal_code",yo:"_tag_firing_delay",zo:"_tag_firing_time",Ao:"temporary_client_id",hh:"_timezone",ih:"topmost_url",Vl:"tracking_id",jh:"traffic_type",La:"transaction_id",Xb:"transport_url",Fj:"trip_type",Rc:"update",pb:"url_passthrough",Gj:"uptgs",Be:"_user_agent_architecture",Ce:"_user_agent_bitness",De:"_user_agent_full_version_list",Ee:"_user_agent_mobile",
Fe:"_user_agent_model",Ge:"_user_agent_platform",He:"_user_agent_platform_version",Ie:"_user_agent_wow64",Ma:"user_data",kh:"user_data_auto_latency",lh:"user_data_auto_meta",mh:"user_data_auto_multi",nh:"user_data_auto_selectors",oh:"user_data_auto_status",Cb:"user_data_mode",jg:"user_data_settings",Ha:"user_id",Db:"user_properties",Hj:"_user_region",Je:"us_privacy_string",ya:"value",Ij:"wbraid_multiple_conversions",zd:"_fpm_parameters",qh:"_host_name",Pj:"_in_page_command",Qj:"_ip_override",Rj:"_is_passthrough_cid",
Yb:"non_personalized_ads",Qe:"_sst_parameters",Sb:"conversion_label",ra:"page_location",Ab:"global_developer_id_string",Qc:"tc_privacy_string"}};var Rh={},Sh=Object.freeze((Rh[N.m.xa]=1,Rh[N.m.Hf]=1,Rh[N.m.If]=1,Rh[N.m.nb]=1,Rh[N.m.la]=1,Rh[N.m.Xa]=1,Rh[N.m.Ya]=1,Rh[N.m.ib]=1,Rh[N.m.Hc]=1,Rh[N.m.yb]=1,Rh[N.m.Ta]=1,Rh[N.m.nc]=1,Rh[N.m.ke]=1,Rh[N.m.oa]=1,Rh[N.m.aj]=1,Rh[N.m.oe]=1,Rh[N.m.Tf]=1,Rh[N.m.Uf]=1,Rh[N.m.ud]=1,Rh[N.m.qj]=1,Rh[N.m.Mc]=1,Rh[N.m.xd]=1,Rh[N.m.tj]=1,Rh[N.m.Xf]=1,Rh[N.m.ah]=1,Rh[N.m.rc]=1,Rh[N.m.sc]=1,Rh[N.m.Ga]=1,Rh[N.m.eh]=1,Rh[N.m.Bb]=1,Rh[N.m.Za]=1,Rh[N.m.Oc]=1,Rh[N.m.Pc]=1,Rh[N.m.ye]=1,Rh[N.m.gh]=1,Rh[N.m.Ae]=1,Rh[N.m.Xb]=
1,Rh[N.m.Rc]=1,Rh[N.m.jg]=1,Rh[N.m.Db]=1,Rh[N.m.Qe]=1,Rh));Object.freeze([N.m.ra,N.m.Ka,N.m.ob,N.m.jb,N.m.fh,N.m.Ha,N.m.Yg,N.m.Cl]);
var Th={},Uh=Object.freeze((Th[N.m.al]=1,Th[N.m.bl]=1,Th[N.m.fl]=1,Th[N.m.il]=1,Th[N.m.jl]=1,Th[N.m.nl]=1,Th[N.m.ol]=1,Th[N.m.pl]=1,Th[N.m.rl]=1,Th[N.m.md]=1,Th)),Vh={},Wh=Object.freeze((Vh[N.m.Ri]=1,Vh[N.m.Si]=1,Vh[N.m.hd]=1,Vh[N.m.jd]=1,Vh[N.m.Ti]=1,Vh[N.m.Cc]=1,Vh[N.m.kd]=1,Vh[N.m.Ob]=1,Vh[N.m.mc]=1,Vh[N.m.Pb]=1,Vh[N.m.Va]=1,Vh[N.m.ld]=1,Vh[N.m.hb]=1,Vh[N.m.Ui]=1,Vh)),Xh=Object.freeze([N.m.xa,N.m.Zd,N.m.nb,N.m.nc,N.m.ud,N.m.ue,N.m.Za,N.m.Rc]),Yh=Object.freeze([].concat(sa(Xh))),Zh=Object.freeze([N.m.Ya,
N.m.Uf,N.m.ye,N.m.gh,N.m.Pf]),$h=Object.freeze([].concat(sa(Zh))),ai={},bi=(ai[N.m.T]="1",ai[N.m.Z]="2",ai[N.m.U]="3",ai[N.m.Ca]="4",ai),ci={},di=Object.freeze((ci.search="s",ci.youtube="y",ci.playstore="p",ci.shopping="h",ci.ads="a",ci.maps="m",ci));Object.freeze(N.m);var ei={},fi=(ei[N.m.kc]="gcu",ei[N.m.Qb]="gclgb",ei[N.m.Wa]="gclaw",ei[N.m.Vi]="gclid_len",ei[N.m.nd]="gclgs",ei[N.m.od]="gcllp",ei[N.m.pd]="gclst",ei[N.m.Rb]="auid",ei[N.m.Jf]="dscnt",ei[N.m.Kf]="fcntr",ei[N.m.Lf]="flng",ei[N.m.Mf]="mid",ei[N.m.Xi]="bttype",ei[N.m.wb]="gacid",ei[N.m.Sb]="label",ei[N.m.Gc]="capi",ei[N.m.Nf]="pscdl",ei[N.m.Ja]="currency_code",ei[N.m.Qg]="clobs",ei[N.m.je]="vdltv",ei[N.m.Rg]="clolo",ei[N.m.Sg]="clolb",ei[N.m.Zi]="_dbg",ei[N.m.Sf]="oedeld",ei[N.m.zb]="edid",ei[N.m.ej]=
"fdr",ei[N.m.fj]="fledge",ei[N.m.Wf]="gac",ei[N.m.vd]="gacgb",ei[N.m.pj]="gacmcov",ei[N.m.Lc]="gdpr",ei[N.m.Ab]="gdid",ei[N.m.wd]="_ng",ei[N.m.qe]="gpp_sid",ei[N.m.se]="gpp",ei[N.m.uj]="gsaexp",ei[N.m.te]="_tu",ei[N.m.qc]="frm",ei[N.m.Yf]="gtm_up",ei[N.m.Nc]="lps",ei[N.m.Zf]="did",ei[N.m.cg]="fcntr",ei[N.m.dg]="flng",ei[N.m.eg]="mid",ei[N.m.ve]=void 0,ei[N.m.ob]="tiba",ei[N.m.Bb]="rdp",ei[N.m.Wb]="ecsid",ei[N.m.ze]="ga_uid",ei[N.m.Ae]="delopc",ei[N.m.Qc]="gdpr_consent",ei[N.m.La]="oid",ei[N.m.Gj]=
"uptgs",ei[N.m.Be]="uaa",ei[N.m.Ce]="uab",ei[N.m.De]="uafvl",ei[N.m.Ee]="uamb",ei[N.m.Fe]="uam",ei[N.m.Ge]="uap",ei[N.m.He]="uapv",ei[N.m.Ie]="uaw",ei[N.m.kh]="ec_lat",ei[N.m.lh]="ec_meta",ei[N.m.mh]="ec_m",ei[N.m.nh]="ec_sel",ei[N.m.oh]="ec_s",ei[N.m.Cb]="ec_mode",ei[N.m.Ha]="userId",ei[N.m.Je]="us_privacy",ei[N.m.ya]="value",ei[N.m.Ij]="mcov",ei[N.m.qh]="hn",ei[N.m.Pj]="gtm_ee",ei[N.m.Yb]="npa",ei[N.m.ie]=null,ei[N.m.vc]=null,ei[N.m.jb]=null,ei[N.m.la]=null,ei[N.m.ra]=null,ei[N.m.Ka]=null,ei[N.m.ih]=
null,ei[N.m.zd]=null,ei[N.m.Ud]=null,ei[N.m.Vd]=null,ei[N.m.Mc]=null,ei);function gi(a,b){if(a){var c=a.split("x");c.length===2&&(hi(b,"u_w",c[0]),hi(b,"u_h",c[1]))}}
function ii(a){var b=ji;b=b===void 0?ki:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(li(q.value)),r.push(li(q.quantity)),r.push(li(q.item_id)),r.push(li(q.start_date)),r.push(li(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ki(a){return mi(a.item_id,a.id,a.item_name)}function mi(){for(var a=l(wa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ni(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function hi(a,b,c){c===void 0||c===null||c===""&&!qg[b]||(a[b]=c)}function li(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};function qi(a){return ri?A.querySelectorAll(a):null}
function si(a,b){if(!ri)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ti=!1;
if(A.querySelectorAll)try{var ui=A.querySelectorAll(":root");ui&&ui.length==1&&ui[0]==A.documentElement&&(ti=!0)}catch(a){}var ri=ti;function vi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var xi=/^[0-9A-Fa-f]{64}$/;function yi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function zi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=z.crypto)==null?0:b.subtle){if(xi.test(a))return Promise.resolve(a);try{var c=yi(a);return z.crypto.subtle.digest("SHA-256",c).then(function(d){var e=Array.from(new Uint8Array(d)).map(function(f){return String.fromCharCode(f)}).join("");return z.btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")};var Ai={Xk:'100',Yk:'100',Zk:'1000',qm:'102803279~102813109~102887800~102926062~103027016~103051953~103055465~103077950~103106314~103106316'},Bi={Rh:Number(Ai.Xk)||0,Ye:Number(Ai.Yk)||0,Mm:Number(Ai.Zk)||0,ro:Ai.qm};function O(a){Va("GTM",a)};var hj={},ij=(hj[N.m.Ia]=1,hj[N.m.Pc]=2,hj[N.m.Xb]=2,hj[N.m.na]=3,hj[N.m.je]=4,hj[N.m.Gf]=5,hj[N.m.nc]=6,hj[N.m.Ta]=6,hj[N.m.Xa]=6,hj[N.m.Hc]=6,hj[N.m.yb]=6,hj[N.m.ib]=6,hj[N.m.Ya]=7,hj[N.m.Bb]=9,hj[N.m.Hf]=10,hj[N.m.nb]=11,hj),jj={},kj=(jj.unknown=13,jj.standard=14,jj.unique=15,jj.per_session=16,jj.transactions=17,jj.items_sold=18,jj);var lj=[];function mj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(ij)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=ij[f],h=b;h=h===void 0?!1:h;Va("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(lj[g]=!0)}}};var nj=function(){this.C=new Set},pj=function(a){var b=oj.Da;a=a===void 0?[]:a;return Array.from(b.C).concat(a)},qj=function(){var a=oj.Da,b=Bi.ro;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var rj={xh:"54l0"};rj.wh=Number("2")||0;rj.vb="dataLayer";rj.uo="ChAI8NmXwAYQlr7Tqtmj9cFBEiUAdDX9wH9Wq9rLZfkZy5mNFy7iBtxm1BF8c1uYfTZxaLNObWRdGgIwUg\x3d\x3d";var sj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},tj={__paused:1,__tg:1},uj;for(uj in sj)sj.hasOwnProperty(uj)&&(tj[uj]=1);var vj=lb(""),wj=!1,xj,yj=!1;yj=!0;xj=yj;var zj,Aj=!1;zj=Aj;var Bj,Cj=!1;Bj=Cj;rj.Ff="www.googletagmanager.com";var Dj=""+rj.Ff+(xj?"/gtag/js":"/gtm.js"),Ej=null,Fj=null,Gj={},Hj={};rj.Wk="";var Ij="";rj.yh=Ij;
var oj=new function(){this.Da=new nj;this.C=!1;this.H=0;this.fa=this.ia=this.ab=this.O="";this.R=this.N=!1};function Jj(){var a;a=a===void 0?[]:a;return pj(a).join("~")}function Kj(){var a=oj.O.length;return oj.O[a-1]==="/"?oj.O.substring(0,a-1):oj.O}function Lj(){return oj.C?H(84)?oj.H===0:oj.H!==1:!1}function Mj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Nj=new hb,Oj={},Pj={},Sj={name:rj.vb,set:function(a,b){Xc(wb(a,b),Oj);Qj()},get:function(a){return Rj(a,2)},reset:function(){Nj=new hb;Oj={};Qj()}};function Rj(a,b){return b!=2?Nj.get(a):Tj(a)}function Tj(a,b){var c=a.split(".");b=b||[];for(var d=Oj,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Uj(a,b){Pj.hasOwnProperty(a)||(Nj.set(a,b),Xc(wb(a,b),Oj),Qj())}
function Vj(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Rj(c,1);if(Array.isArray(d)||Wc(d))d=Xc(d,null);Pj[c]=d}}function Qj(a){ib(Pj,function(b,c){Nj.set(b,c);Xc(wb(b),Oj);Xc(wb(b,c),Oj);a&&delete Pj[b]})}function Wj(a,b){var c,d=(b===void 0?2:b)!==1?Tj(a):Nj.get(a);Uc(d)==="array"||Uc(d)==="object"?c=Xc(d,null):c=d;return c};var bk=/:[0-9]+$/,ck=/^\d+\.fls\.doubleclick\.net$/;function dk(a,b,c,d){for(var e=[],f=l(a.split("&")),g=f.next();!g.done;g=f.next()){var h=l(g.value.split("=")),m=h.next().value,n=ra(h);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function ek(a){try{return decodeURIComponent(a)}catch(b){}}
function fk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=gk(a.protocol)||gk(z.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:z.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||z.location.hostname).replace(bk,"").toLowerCase());return hk(a,b,c,d,e)}
function hk(a,b,c,d,e){var f,g=gk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=ik(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(bk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Va("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=dk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function gk(a){return a?a.replace(":","").toLowerCase():""}function ik(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var jk={},kk=0;
function lk(a){var b=jk[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Va("TAGGING",1),d="/"+d);var e=c.hostname.replace(bk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};kk<5&&(jk[a]=b,kk++)}return b}function mk(a,b,c){var d=lk(a);return Bb(b,d,c)}
function nk(a){var b=lk(z.location.href),c=fk(b,"host",!1);if(c&&c.match(ck)){var d=fk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var ok={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},pk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function qk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return lk(""+c+b).href}}function rk(a,b){if(Lj()||zj)return qk(a,b)}
function sk(){return!!rj.yh&&rj.yh.split("@@").join("")!=="SGTM_TOKEN"}function tk(a){for(var b=l([N.m.Pc,N.m.Xb]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function uk(a,b,c){c=c===void 0?"":c;if(!Lj())return a;var d=b?ok[a]||"":"";d==="/gs"&&(c="");return""+Kj()+d+c}function vk(a){if(!Lj())return a;for(var b=l(pk),c=b.next();!c.done;c=b.next())if(ub(a,""+Kj()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function wk(a){var b=String(a[Pe.za]||"").replace(/_/g,"");return ub(b,"cvt")?"cvt":b}var xk=z.location.search.indexOf("?gtm_latency=")>=0||z.location.search.indexOf("&gtm_latency=")>=0;var yk={sampleRate:"0.005000",Sk:"",qo:"0.01"},zk=Math.random(),Ak;if(!(Ak=xk)){var Bk=yk.sampleRate;Ak=zk<Number(Bk)}var Ck=Ak,Dk=(ic==null?void 0:ic.includes("gtm_debug=d"))||xk||zk>=1-Number(yk.qo);var Ek=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Fk=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var Gk=function(a,b,c){return a.addEventListener?(a.addEventListener(b,c,!1),!0):!1},Hk=function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)};var Ik,Jk;a:{for(var Lk=["CLOSURE_FLAGS"],Mk=xa,Nk=0;Nk<Lk.length;Nk++)if(Mk=Mk[Lk[Nk]],Mk==null){Jk=null;break a}Jk=Mk}var Ok=Jk&&Jk[610401301];Ik=Ok!=null?Ok:!1;function Pk(){var a=xa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Qk,Rk=xa.navigator;Qk=Rk?Rk.userAgentData||null:null;function Sk(a){if(!Ik||!Qk)return!1;for(var b=0;b<Qk.brands.length;b++){var c=Qk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Tk(a){return Pk().indexOf(a)!=-1};function Uk(){return Ik?!!Qk&&Qk.brands.length>0:!1}function Vk(){return Uk()?!1:Tk("Opera")}function Wk(){return Tk("Firefox")||Tk("FxiOS")}function Xk(){return Uk()?Sk("Chromium"):(Tk("Chrome")||Tk("CriOS"))&&!(Uk()?0:Tk("Edge"))||Tk("Silk")};var Yk=function(a){Yk[" "](a);return a};Yk[" "]=function(){};var Zk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var $k=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},al=/#|$/,bl=function(a,b){var c=a.search(al),d=$k(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Zk(a.slice(d,e!==-1?e:0))},cl=/[?&]($|#)/,dl=function(a,b,c){for(var d,e=a.search(al),f=0,g,h=[];(g=$k(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(cl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function el(){return Ik?!!Qk&&!!Qk.platform:!1}function fl(){return Tk("iPhone")&&!Tk("iPod")&&!Tk("iPad")}function gl(){fl()||Tk("iPad")||Tk("iPod")};Vk();Uk()||Tk("Trident")||Tk("MSIE");Tk("Edge");!Tk("Gecko")||Pk().toLowerCase().indexOf("webkit")!=-1&&!Tk("Edge")||Tk("Trident")||Tk("MSIE")||Tk("Edge");Pk().toLowerCase().indexOf("webkit")!=-1&&!Tk("Edge")&&Tk("Mobile");el()||Tk("Macintosh");el()||Tk("Windows");(el()?Qk.platform==="Linux":Tk("Linux"))||el()||Tk("CrOS");el()||Tk("Android");fl();Tk("iPad");Tk("iPod");gl();Pk().toLowerCase().indexOf("kaios");var hl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Yk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},il=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},jl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},kl=function(a){if(z.top==z)return 0;if(a===void 0?0:a){var b=z.location.ancestorOrigins;
if(b)return b[b.length-1]==z.location.origin?1:2}return hl(z.top)?1:2},ll=function(a){a=a===void 0?document:a;return a.createElement("img")},ml=function(){for(var a=z,b=a;a&&a!=a.parent;)a=a.parent,hl(a)&&(b=a);return b};function nl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function ol(){return nl("join-ad-interest-group")&&ab(fc.joinAdInterestGroup)}
function pl(a,b,c){var d=ig[3]===void 0?1:ig[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(ig[2]===void 0?50:ig[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&pb()-q<(ig[1]===void 0?6E4:ig[1])?(Va("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)ql(f[0]);else{if(n)return Va("TAGGING",10),!1}else f.length>=d?ql(f[0]):n&&ql(m[0]);tc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:pb()});return!0}function ql(a){try{a.parentNode.removeChild(a)}catch(b){}}function rl(){return"https://td.doubleclick.net"};function sl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var tl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Wk();fl()||Tk("iPod");Tk("iPad");!Tk("Android")||Xk()||Wk()||Vk()||Tk("Silk");Xk();!Tk("Safari")||Xk()||(Uk()?0:Tk("Coast"))||Vk()||(Uk()?0:Tk("Edge"))||(Uk()?Sk("Microsoft Edge"):Tk("Edg/"))||(Uk()?Sk("Opera"):Tk("OPR"))||Wk()||Tk("Silk")||Tk("Android")||gl();var ul={},vl=null,wl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!vl){vl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));ul[m]=n;for(var p=0;p<n.length;p++){var q=n[p];vl[q]===void 0&&(vl[q]=p)}}}for(var r=ul[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
y=b[v+1],B=b[v+2],C=r[x>>2],E=r[(x&3)<<4|y>>4],F=r[(y&15)<<2|B>>6],K=r[B&63];t[w++]=""+C+E+F+K}var L=0,T=u;switch(b.length-v){case 2:L=b[v+1],T=r[(L&15)<<2]||u;case 1:var J=b[v];t[w]=""+r[J>>2]+r[(J&3)<<4|L>>4]+T+u}return t.join("")};function xl(a,b,c,d,e,f){var g=bl(c,"fmt");if(d){var h=bl(c,"random"),m=bl(c,"label")||"";if(!h)return!1;var n=wl(Zk(m)+":"+Zk(h));if(!sl(a,n,d))return!1}g&&Number(g)!==4&&(c=dl(c,"rfmt",g));var p=dl(c,"fmt",4);rc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var yl={},zl=(yl[1]={},yl[2]={},yl[3]={},yl[4]={},yl);function Al(a,b,c){var d=Bl(b,c);if(d){var e=zl[b][d];e||(e=zl[b][d]=[]);e.push(Object.assign({},a))}}function Cl(a,b){var c=Bl(a,b);if(c){var d=zl[a][c];d&&(zl[a][c]=d.filter(function(e){return!e.Fk}))}}function Dl(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Bl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=z.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function El(a){var b=wa.apply(1,arguments);H(55)&&Dk&&(Al(a,2,b[0]),Al(a,3,b[0]));Cc.apply(null,sa(b))}function Fl(a){var b=wa.apply(1,arguments);H(55)&&Dk&&Al(a,2,b[0]);return Dc.apply(null,sa(b))}function Gl(a){var b=wa.apply(1,arguments);H(55)&&Dk&&Al(a,3,b[0]);uc.apply(null,sa(b))}
function Hl(a){var b=wa.apply(1,arguments),c=b[0];H(55)&&Dk&&(Al(a,2,c),Al(a,3,c));return Fc.apply(null,sa(b))}function Il(a){var b=wa.apply(1,arguments);H(55)&&Dk&&Al(a,1,b[0]);rc.apply(null,sa(b))}function Jl(a){var b=wa.apply(1,arguments);b[0]&&H(55)&&Dk&&Al(a,4,b[0]);tc.apply(null,sa(b))}function Kl(a){var b=wa.apply(1,arguments);H(55)&&Dk&&Al(a,1,b[2]);return xl.apply(null,sa(b))}function Ll(a){var b=wa.apply(1,arguments);H(55)&&Dk&&Al(a,4,b[0]);pl.apply(null,sa(b))};var Ml=/gtag[.\/]js/,Nl=/gtm[.\/]js/,Ol=!1;function Pl(a){if(Ol)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(Ml.test(c))return"3";if(Nl.test(c))return"2"}return"0"};function Ql(a,b){var c=Rl();c.pending||(c.pending=[]);eb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Sl(){var a=z.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Tl=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Sl()};
function Rl(){var a=jc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Tl,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Sl());return c};var Ul={},Vl=!1,Wl=void 0,Wf={ctid:"UA-123456789",canonicalContainerId:"",zk:"UA-123456789",Ak:"UA-123456789"};Ul.Le=lb("");function Xl(){return Ul.Le&&Yl().some(function(a){return a===Wf.ctid})}function Zl(){var a=$l();return Vl?a.map(am):a}function bm(){var a=Yl();return Vl?a.map(am):a}
function cm(){var a=bm();if(!Vl)for(var b=l([].concat(sa(a))),c=b.next();!c.done;c=b.next()){var d=am(c.value),e=Rl().destination[d];e&&e.state!==0||a.push(d)}return a}function dm(){return em(Wf.ctid)}function fm(){return em(Wf.canonicalContainerId||"_"+Wf.ctid)}function $l(){return Wf.zk?Wf.zk.split("|"):[Wf.ctid]}function Yl(){return Wf.Ak?Wf.Ak.split("|"):[]}function gm(){var a=hm(im()),b=a&&a.parent;if(b)return hm(b)}
function hm(a){var b=Rl();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function em(a){return Vl?am(a):a}function am(a){return"siloed_"+a}function jm(a){a=String(a);return ub(a,"siloed_")?a.substring(7):a}function km(){if(oj.N){var a=Rl();if(a.siloed){for(var b=[],c=$l().map(am),d=Yl().map(am),e={},f=0;f<a.siloed.length;e={sg:void 0},f++)e.sg=a.siloed[f],!Vl&&eb(e.sg.isDestination?d:c,function(g){return function(h){return h===g.sg.ctid}}(e))?Vl=!0:b.push(e.sg);a.siloed=b}}}
function lm(){var a=Rl();if(a.pending){for(var b,c=[],d=!1,e=Zl(),f=Wl?Wl:cm(),g={},h=0;h<a.pending.length;g={uf:void 0},h++)g.uf=a.pending[h],eb(g.uf.target.isDestination?f:e,function(m){return function(n){return n===m.uf.target.ctid}}(g))?d||(b=g.uf.onLoad,d=!0):c.push(g.uf);a.pending=c;if(b)try{b(fm())}catch(m){}}}
function mm(){var a=Wf.ctid,b=Zl(),c=cm();Wl=c;for(var d=function(n,p){var q={canonicalContainerId:Wf.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};hc&&(q.scriptElement=hc);ic&&(q.scriptSource=ic);if(gm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=oj.C,x=lk(v),y=w?x.pathname:""+x.hostname+x.pathname,B=A.scripts,C="",E=0;E<B.length;++E){var F=B[E];if(!(F.innerHTML.length===
0||!w&&F.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||F.innerHTML.indexOf(y)<0)){if(F.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(E);break b}C=String(E)}}if(C){t=C;break b}}t=void 0}var K=t;if(K){Ol=!0;r=K;break a}}var L=[].slice.call(A.scripts);r=q.scriptElement?String(L.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Pl(q)}var T=p?e.destination:e.container,J=T[n];J?(p&&J.state===0&&O(93),Object.assign(J,q)):T[n]=q},e=Rl(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[fm()]={};lm()}function nm(){var a=fm();return!!Rl().canonical[a]}function om(a){return!!Rl().container[a]}function pm(a){var b=Rl().destination[a];return!!b&&!!b.state}function im(){return{ctid:dm(),isDestination:Ul.Le}}function qm(a,b,c){b.siloed&&rm({ctid:a,isDestination:!1});var d=im();Rl().container[a]={state:1,context:b,parent:d};Ql({ctid:a,isDestination:!1},c)}
function rm(a){var b=Rl();(b.siloed=b.siloed||[]).push(a)}function sm(){var a=Rl().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function tm(){var a={};ib(Rl().destination,function(b,c){c.state===0&&(a[jm(b)]=c)});return a}function um(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function vm(){for(var a=Rl(),b=l(Zl()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function wm(a){var b=Rl();return b.destination[a]?1:b.destination[am(a)]?2:0};var xm={Aa:{Bd:0,Cd:1,uh:2}};xm.Aa[xm.Aa.Bd]="FULL_TRANSMISSION";xm.Aa[xm.Aa.Cd]="LIMITED_TRANSMISSION";xm.Aa[xm.Aa.uh]="NO_TRANSMISSION";var ym={V:{qb:0,wa:1,jc:2,wc:3}};ym.V[ym.V.qb]="NO_QUEUE";ym.V[ym.V.wa]="ADS";ym.V[ym.V.jc]="ANALYTICS";ym.V[ym.V.wc]="MONITORING";function zm(){var a=jc("google_tag_data",{});return a.ics=a.ics||new Am}var Am=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Am.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Va("TAGGING",19);b==null?Va("TAGGING",18):Bm(this,a,b==="granted",c,d,e,f,g)};Am.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Bm(this,a[d],void 0,void 0,"","",b,c)};
var Bm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&bb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&z.setTimeout(function(){m[b]===t&&t.quiet&&(Va("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Am.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Cm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Cm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&bb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Hd:b})};var Cm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Bk=!0)}};Am.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Bk){d.Bk=!1;try{d.Hd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Dm=!1,Em=!1,Fm={},Gm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Fm.ad_storage=1,Fm.analytics_storage=1,Fm.ad_user_data=1,Fm.ad_personalization=1,Fm),usedContainerScopedDefaults:!1};function Hm(a){var b=zm();b.accessedAny=!0;return(bb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Gm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Im(a){var b=zm();b.accessedAny=!0;return b.getConsentState(a,Gm)}function Jm(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Gm.corePlatformServices[e]!==!1}return b}function Km(a){var b=zm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}
function Lm(){if(!jg(8))return!1;var a=zm();a.accessedAny=!0;if(a.active)return!0;if(!Gm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Gm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Gm.containerScopedDefaults[c.value]!==1)return!0;return!1}function Mm(a,b){zm().addListener(a,b)}function Nm(a,b){zm().notifyListeners(a,b)}
function Om(a,b){function c(){for(var e=0;e<b.length;e++)if(!Km(b[e]))return!0;return!1}if(c()){var d=!1;Mm(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function Pm(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Hm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=bb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Mm(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):z.setTimeout(function(){m(c())},500)}}))};var Qm={},Rm=(Qm[ym.V.qb]=xm.Aa.Bd,Qm[ym.V.wa]=xm.Aa.Bd,Qm[ym.V.jc]=xm.Aa.Bd,Qm[ym.V.wc]=xm.Aa.Bd,Qm),Sm=function(a,b){this.C=a;this.consentTypes=b};Sm.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Hm(a)});case 1:return this.consentTypes.some(function(a){return Hm(a)});default:Yb(this.C,"consentsRequired had an unknown type")}};
var Tm={},Um=(Tm[ym.V.qb]=new Sm(0,[]),Tm[ym.V.wa]=new Sm(0,["ad_storage"]),Tm[ym.V.jc]=new Sm(0,["analytics_storage"]),Tm[ym.V.wc]=new Sm(1,["ad_storage","analytics_storage"]),Tm);var Wm=function(a){var b=this;this.type=a;this.C=[];Mm(Um[a].consentTypes,function(){Vm(b)||b.flush()})};Wm.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Vm=function(a){return Rm[a.type]===xm.Aa.uh&&!Um[a.type].isConsentGranted()},Xm=function(a,b){Vm(a)?a.C.push(b):b()},Ym=new Map;function Zm(a){Ym.has(a)||Ym.set(a,new Wm(a));return Ym.get(a)};var $m="/td?id="+Wf.ctid,an="v t pid dl tdp exp".split(" "),bn=["mcc"],cn={},dn={},en=!1;function fn(a,b,c){dn[a]=b;(c===void 0||c)&&gn(a)}function gn(a,b){if(cn[a]===void 0||(b===void 0?0:b))cn[a]=!0}function hn(a){a=a===void 0?!1:a;var b=Object.keys(cn).filter(function(c){return cn[c]===!0&&dn[c]!==void 0&&(a||!bn.includes(c))}).map(function(c){var d=dn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+uk("https://www.googletagmanager.com")+$m+(""+b+"&z=0")}
function jn(){Object.keys(cn).forEach(function(a){an.indexOf(a)<0&&(cn[a]=!1)})}function kn(a){a=a===void 0?!1:a;if(oj.R&&Dk&&Wf.ctid){var b=Zm(ym.V.wc);if(Vm(b))en||(en=!0,Xm(b,kn));else{var c=hn(a),d={destinationId:Wf.ctid,endpoint:56};a?Hl(d,c):Gl(d,c);jn();en=!1}}}var ln={};function mn(){Object.keys(cn).filter(function(a){return cn[a]&&!an.includes(a)}).length>0&&kn(!0)}var nn=fb();function on(){nn=fb()}
function pn(){fn("v","3");fn("t","t");fn("pid",function(){return String(nn)});fn("exp",Jj());wc(z,"pagehide",mn);z.setInterval(on,864E5)};var qn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],rn=[N.m.Pc,N.m.Xb,N.m.ud,N.m.wb,N.m.Wb,N.m.Ha,N.m.Ga,N.m.Ta,N.m.Xa,N.m.yb],sn=!1,tn=!1,un={},vn={};function wn(){!tn&&sn&&(qn.some(function(a){return Gm.containerScopedDefaults[a]!==1})||xn("mbc"));tn=!0}function xn(a){Dk&&(fn(a,"1"),kn())}function yn(a,b){if(!un[b]&&(un[b]=!0,vn[b]))for(var c=l(rn),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){xn("erc");break}};function zn(a){Va("HEALTH",a)};var An={Yj:"service_worker_endpoint",zh:"shared_user_id",Ah:"shared_user_id_requested",Re:"shared_user_id_source",Ef:"cookie_deprecation_label",Tk:"aw_user_data_cache",Xl:"ga4_user_data_cache",Wl:"fl_user_data_cache",Tj:"pt_listener_set",Pe:"pt_data",th:"ip_geo_fetch_in_progress",Ke:"ip_geo_data_cache"},Bn;function Cn(a){if(!Bn){Bn={};for(var b=l(Object.keys(An)),c=b.next();!c.done;c=b.next())Bn[An[c.value]]=!0}return!!Bn[a]}
function Dn(a,b){b=b===void 0?!1:b;if(Cn(a)){var c,d,e=(d=(c=jc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function En(a,b){var c=Dn(a,!0);c&&c.set(b)}function Fn(a){var b;return(b=Dn(a))==null?void 0:b.get()}function Gn(a,b){if(typeof b==="function"){var c;return(c=Dn(a,!0))==null?void 0:c.subscribe(b)}}function Hn(a,b){var c=Dn(a);return c?c.unsubscribe(b):!1};var In={Ym:"eyIwIjoiVVMiLCIxIjoiVVMtQVoiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9"},Jn={},Kn=!1;function Ln(){function a(){c!==void 0&&Hn(An.Ke,c);try{var e=Fn(An.Ke);Jn=JSON.parse(e)}catch(f){O(123),zn(2),Jn={}}Kn=!0;b()}var b=Mn,c=void 0,d=Fn(An.Ke);d?a(d):(c=Gn(An.Ke,a),Nn())}
function Nn(){function a(c){En(An.Ke,c||"{}");En(An.th,!1)}if(!Fn(An.th)){En(An.th,!0);var b="";try{z.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function On(){var a=In.Ym;try{return JSON.parse(Ta(a))}catch(b){return O(123),zn(2),{}}}function Pn(){return Jn["0"]||""}function Qn(){return Jn["1"]||""}function Rn(){var a=!1;return a}function Sn(){return Jn["6"]!==!1}function Tn(){var a="";return a}
function Un(){var a=!1;return a}function Vn(){var a="";return a};function Wn(a){return a&&a.indexOf("pending:")===0?Xn(a.substr(8)):!1}function Xn(a){if(a==null||a.length===0)return!1;var b=Number(a),c=pb();return b<c+3E5&&b>c-9E5};var Yn=!1,Zn=!1,$n=!1,ao=0,bo=!1,co=[];function eo(a){if(ao===0)bo&&co&&(co.length>=100&&co.shift(),co.push(a));else if(fo()){var b=jc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function go(){ho();xc(A,"TAProdDebugSignal",go)}function ho(){if(!Zn){Zn=!0;io();var a=co;co=void 0;a==null||a.forEach(function(b){eo(b)})}}
function io(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Xn(a)?ao=1:!Wn(a)||Yn||$n?ao=2:($n=!0,wc(A,"TAProdDebugSignal",go,!1),z.setTimeout(function(){ho();Yn=!0},200))}function fo(){if(!bo)return!1;switch(ao){case 1:case 0:return!0;case 2:return!1;default:return!1}};var jo=!1;function ko(a,b){var c=$l(),d=Yl();if(fo()){var e=lo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;eo(e)}}function mo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Na;e=a.isBatched;if(fo()){var f=lo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});f.target=b;f.url=c.url;c.postBody&&(f.postBody=c.postBody);f.parameterEncoding=c.parameterEncoding;f.endpoint=c.endpoint;e!==void 0&&(f.isBatched=e);eo(f)}}
function no(a){fo()&&mo(a())}function lo(a,b){b=b===void 0?{}:b;b.groupId=oo;var c,d=b,e={publicId:po};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=jo?"OGT":"GTM";c.key.targetRef=qo;return c}var po="",qo={ctid:"",isDestination:!1},oo;
function ro(a){var b=Wf.ctid,c=Xl();ao=0;bo=!0;io();oo=a;po=b;jo=xj;qo={ctid:b,isDestination:c}};var so=[N.m.T,N.m.Z,N.m.U,N.m.Ca],to,uo;function vo(a){for(var b=a[N.m.Mb],c=Array.isArray(b)?b:[b],d={ff:0};d.ff<c.length;d={ff:d.ff},++d.ff)ib(a,function(e){return function(f,g){if(f!==N.m.Mb){var h=c[e.ff],m=Pn(),n=Qn();Em=!0;Dm&&Va("TAGGING",20);zm().declare(f,g,h,m,n)}}}(d))}
function wo(a){wn();!uo&&to&&xn("crc");uo=!0;var b=a[N.m.Mb];b&&O(40);var c=a[N.m.Cf];c&&O(41);for(var d=Array.isArray(b)?b:[b],e={hf:0};e.hf<d.length;e={hf:e.hf},++e.hf)ib(a,function(f){return function(g,h){if(g!==N.m.Mb&&g!==N.m.Cf){var m=d[f.hf],n=Number(c),p=Pn(),q=Qn();n=n===void 0?0:n;Dm=!0;Em&&Va("TAGGING",20);zm().default(g,h,m,p,q,n,Gm)}}}(e))}
function xo(a){Gm.usedContainerScopedDefaults=!0;var b=a[N.m.Mb];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(Qn())&&!c.includes(Pn()))return}ib(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Gm.usedContainerScopedDefaults=!0;Gm.containerScopedDefaults[d]=e==="granted"?3:2})}function yo(a,b){wn();to=!0;ib(a,function(c,d){Dm=!0;Em&&Va("TAGGING",20);zm().update(c,d,Gm)});Nm(b.eventId,b.priorityId)}
function zo(a){a.hasOwnProperty("all")&&(Gm.selectedAllCorePlatformServices=!0,ib(di,function(b){Gm.corePlatformServices[b]=a.all==="granted";Gm.usedCorePlatformServices=!0}));ib(a,function(b,c){b!=="all"&&(Gm.corePlatformServices[b]=c==="granted",Gm.usedCorePlatformServices=!0)})}function Ao(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Hm(b)})}function Bo(a,b){Mm(a,b)}function Co(a,b){Pm(a,b)}function Do(a,b){Om(a,b)}
function Eo(){var a=[N.m.T,N.m.Ca,N.m.U];zm().waitForUpdate(a,500,Gm)}function Fo(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;zm().clearTimeout(d,void 0,Gm)}Nm()}function Go(){if(!Bj)for(var a=Sn()?Mj(oj.ia):Mj(oj.ab),b=0;b<so.length;b++){var c=so[b],d=c,e=a[c]?"granted":"denied";zm().implicit(d,e)}};var Ho=!1,Io=[];function Jo(){if(!Ho){Ho=!0;for(var a=Io.length-1;a>=0;a--)Io[a]();Io=[]}};var Ko=z.google_tag_manager=z.google_tag_manager||{};function Lo(a,b){return Ko[a]=Ko[a]||b()}function Mo(){var a=dm(),b=No;Ko[a]=Ko[a]||b}function Oo(){var a=rj.vb;return Ko[a]=Ko[a]||{}}function Po(){var a=Ko.sequence||1;Ko.sequence=a+1;return a};function Qo(){if(Ko.pscdl!==void 0)Fn(An.Ef)===void 0&&En(An.Ef,Ko.pscdl);else{var a=function(c){Ko.pscdl=c;En(An.Ef,c)},b=function(){a("error")};try{fc.cookieDeprecationLabel?(a("pending"),fc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};function Ro(a,b){b&&ib(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var So=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,To=/\s/;
function Uo(a,b){if(bb(a)){a=nb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(So.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||To.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Vo(a,b){for(var c={},d=0;d<a.length;++d){var e=Uo(a[d],b);e&&(c[e.id]=e)}Wo(c);var f=[];ib(c,function(g,h){f.push(h)});return f}function Wo(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];d.prefix==="AW"&&d.ids[Xo[1]]&&b.push(d.destinationId)}for(var e=0;e<b.length;++e)delete a[b[e]]}var Yo={},Xo=(Yo[0]=0,Yo[1]=1,Yo[2]=2,Yo[3]=0,Yo[4]=1,Yo[5]=0,Yo[6]=0,Yo[7]=0,Yo);var Zo=Number('')||500,$o={},dp={},ep={initialized:11,complete:12,interactive:13},fp={},gp=Object.freeze((fp[N.m.Za]=!0,fp)),hp=void 0;function ip(a,b){if(b.length&&Dk){var c;(c=$o)[a]!=null||(c[a]=[]);dp[a]!=null||(dp[a]=[]);var d=b.filter(function(e){return!dp[a].includes(e)});$o[a].push.apply($o[a],sa(d));dp[a].push.apply(dp[a],sa(d));!hp&&d.length>0&&(gn("tdc",!0),hp=z.setTimeout(function(){kn();$o={};hp=void 0},Zo))}}
function jp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function kp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;Uc(t)==="object"?u=t[r]:Uc(t)==="array"&&(u=t[r]);return u===void 0?gp[r]:u},f=jp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=Uc(m)==="object"||Uc(m)==="array",q=Uc(n)==="object"||Uc(n)==="array";if(p&&q)kp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function lp(){fn("tdc",function(){hp&&(z.clearTimeout(hp),hp=void 0);var a=[],b;for(b in $o)$o.hasOwnProperty(b)&&a.push(b+"*"+$o[b].join("."));return a.length?a.join("!"):void 0},!1)};var mp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.O=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},np=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.O);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.O);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.O)}return c},P=function(a,b,c,d){for(var e=l(np(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},op=function(a){for(var b={},c=np(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)},pp=function(a,b,c){function d(n){Wc(n)&&ib(n,function(p,q){f=!0;e[p]=q})}var e={},f=!1,g=np(a,c===void 0?3:c);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[b]);return f?e:void 0},qp=function(a){for(var b=[N.m.ee,N.m.ae,
N.m.be,N.m.ce,N.m.de,N.m.fe,N.m.he],c=np(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},rp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.O={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},sp=function(a,b){a.H=b;return a},tp=function(a,b){a.R=b;
return a},up=function(a,b){a.C=b;return a},vp=function(a,b){a.N=b;return a},wp=function(a,b){a.fa=b;return a},xp=function(a,b){a.O=b;return a},yp=function(a,b){a.eventMetadata=b||{};return a},zp=function(a,b){a.onSuccess=b;return a},Ap=function(a,b){a.onFailure=b;return a},Bp=function(a,b){a.isGtmEvent=b;return a},Cp=function(a){return new mp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.O,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Dp={Rk:Number("5"),Zo:Number("")},Ep=[],Fp=!1;function Gp(a){Ep.push(a)}var Hp="?id="+Wf.ctid,Ip=void 0,Jp={},Kp=void 0,Lp=new function(){var a=5;Dp.Rk>0&&(a=Dp.Rk);this.H=a;this.C=0;this.N=[]},Mp=1E3;
function Np(a,b){var c=Ip;if(c===void 0)if(b)c=Po();else return"";for(var d=[uk("https://www.googletagmanager.com"),"/a",Hp],e=l(Ep),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,gd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Op(){if(oj.R&&(Kp&&(z.clearTimeout(Kp),Kp=void 0),Ip!==void 0&&Pp)){var a=Zm(ym.V.wc);if(Vm(a))Fp||(Fp=!0,Xm(a,Op));else{var b;if(!(b=Jp[Ip])){var c=Lp;b=c.C<c.H?!1:pb()-c.N[c.C%c.H]<1E3}if(b||Mp--<=0)O(1),Jp[Ip]=!0;else{var d=Lp,e=d.C++%d.H;d.N[e]=pb();var f=Np(!0);Gl({destinationId:Wf.ctid,endpoint:56,eventId:Ip},f);Fp=Pp=!1}}}}function Qp(){if(Ck&&oj.R){var a=Np(!0,!0);Gl({destinationId:Wf.ctid,endpoint:56,eventId:Ip},a)}}var Pp=!1;
function Rp(a){Jp[a]||(a!==Ip&&(Op(),Ip=a),Pp=!0,Kp||(Kp=z.setTimeout(Op,500)),Np().length>=2022&&Op())}var Sp=fb();function Tp(){Sp=fb()}function Up(){return[["v","3"],["t","t"],["pid",String(Sp)]]};var Vp={};function Wp(a,b,c){Ck&&a!==void 0&&(Vp[a]=Vp[a]||[],Vp[a].push(c+b),Rp(a))}function Xp(a){var b=a.eventId,c=a.gd,d=[],e=Vp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Vp[b];return d};function Yp(a,b,c){var d=Uo(em(a),!0);d&&Zp.register(d,b,c)}function $p(a,b,c,d){var e=Uo(c,d.isGtmEvent);e&&(wj&&(d.deferrable=!0),Zp.push("event",[b,a],e,d))}function aq(a,b,c,d){var e=Uo(c,d.isGtmEvent);e&&Zp.push("get",[a,b],e,d)}function bq(a){var b=Uo(em(a),!0),c;b?c=cq(Zp,b).C:c={};return c}function dq(a,b){var c=Uo(em(a),!0);if(c){var d=Zp,e=Xc(b,null);Xc(cq(d,c).C,e);cq(d,c).C=e}}
var eq=function(){this.R={};this.C={};this.H={};this.fa=null;this.O={};this.N=!1;this.status=1},fq=function(a,b,c,d){this.H=pb();this.C=b;this.args=c;this.messageContext=d;this.type=a},gq=function(){this.destinations={};this.C={};this.commands=[]},cq=function(a,b){var c=b.destinationId;Vl||(c=jm(c));return a.destinations[c]=a.destinations[c]||new eq},hq=function(a,b,c,d){if(d.C){var e=cq(a,d.C),f=e.fa;if(f){var g=d.C.id;Vl||(g=jm(g));var h=Xc(c,null),m=Xc(e.R[g],null),n=Xc(e.O,null),p=Xc(e.C,null),
q=Xc(a.C,null),r={};if(Ck)try{r=Xc(Oj,null)}catch(x){O(72)}var t=d.C.prefix,u=function(x){Wp(d.messageContext.eventId,t,x)},v=Cp(Bp(Ap(zp(yp(wp(vp(xp(up(tp(sp(new rp(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Wp(d.messageContext.eventId,t,"1");var x=d.type,y=d.C.id;if(Dk&&x==="config"){var B,C=(B=Uo(y))==null?void 0:B.ids;if(!(C&&C.length>1)){var E,F=jc("google_tag_data",{});F.td||(F.td={});E=F.td;var K=Xc(v.O);Xc(v.C,K);var L=[],T;for(T in E)E.hasOwnProperty(T)&&kp(E[T],K).length&&L.push(T);L.length&&(ip(y,L),Va("TAGGING",ep[A.readyState]||14));E[y]=K}}f(d.C.id,b,d.H,v)}catch(J){Wp(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():Xm(e.ia,w)}}};
gq.prototype.register=function(a,b,c){var d=cq(this,a);d.status!==3&&(d.fa=b,d.status=3,d.ia=Zm(c),this.flush())};
gq.prototype.push=function(a,b,c,d){c!==void 0&&(cq(this,c).status===1&&(cq(this,c).status=2,this.push("require",[{}],c,{})),cq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata.send_to_destinations||(d.eventMetadata.send_to_destinations=[c.destinationId]),d.eventMetadata.send_to_targets||(d.eventMetadata.send_to_targets=[c.id]));this.commands.push(new fq(a,c,b,d));d.deferrable||this.flush()};
gq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Zb:void 0,ug:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||cq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(cq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];ib(h,function(u,v){Xc(wb(u,v),b.C)});mj(h,!0);break;case "config":var m=cq(this,g);
e.Zb={};ib(f.args[0],function(u){return function(v,w){Xc(wb(v,w),u.Zb)}}(e));var n=!!e.Zb[N.m.Rc];delete e.Zb[N.m.Rc];var p=g.destinationId===g.id;mj(e.Zb,!0);n||(p?m.O={}:m.R[g.id]={});m.N&&n||hq(this,N.m.ka,e.Zb,f);m.N=!0;p?Xc(e.Zb,m.O):(Xc(e.Zb,m.R[g.id]),O(70));d=!0;yn(e.Zb,g.id);sn=!0;break;case "event":e.ug={};ib(f.args[0],function(u){return function(v,w){Xc(wb(v,w),u.ug)}}(e));mj(e.ug);hq(this,f.args[1],e.ug,f);var q=void 0;!f.C||((q=f.messageContext.eventMetadata)==null?0:q.em_event)||(vn[f.C.id]=
!0);sn=!0;break;case "get":var r={},t=(r[N.m.Ub]=f.args[0],r[N.m.oc]=f.args[1],r);hq(this,N.m.mb,t,f);sn=!0}this.commands.shift();iq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var iq=function(a,b){if(b.type!=="require")if(b.C)for(var c=cq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Zp=new gq;function jq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=ll(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=cc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Hk(e,"load",f);Hk(e,"error",f)};Gk(e,"load",f);Gk(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function kq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";il(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});lq(c,b)}
function lq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else jq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var mq=function(){this.fa=this.fa;this.O=this.O};mq.prototype.fa=!1;mq.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};mq.prototype[Symbol.dispose]=function(){this.dispose()};mq.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.O||(this.O=[]),b&&(a=a.bind(b)),this.O.push(a))};mq.prototype.N=function(){if(this.O)for(;this.O.length;)this.O.shift()()};function nq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var oq=function(a,b){b=b===void 0?{}:b;mq.call(this);this.C=null;this.ia={};this.xc=0;this.R=null;this.H=a;var c;this.ab=(c=b.timeoutMs)!=null?c:500;var d;this.Da=(d=b.Po)!=null?d:!1};qa(oq,mq);oq.prototype.N=function(){this.ia={};this.R&&(Hk(this.H,"message",this.R),delete this.R);delete this.ia;delete this.H;delete this.C;mq.prototype.N.call(this)};var qq=function(a){return typeof a.H.__tcfapi==="function"||pq(a)!=null};
oq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Da},d=Fk(function(){return a(c)}),e=0;this.ab!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.ab));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=nq(c),c.internalBlockOnErrors=b.Da,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{rq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};oq.prototype.removeEventListener=function(a){a&&a.listenerId&&rq(this,"removeEventListener",null,a.listenerId)};
var tq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=sq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&sq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?sq(a.purpose.legitimateInterests,
b)&&sq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},sq=function(a,b){return!(!a||!a[b])},rq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(pq(a)){uq(a);var g=++a.xc;a.ia[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},pq=function(a){if(a.C)return a.C;a.C=jl(a.H,"__tcfapiLocator");return a.C},uq=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ia[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Gk(a.H,"message",b)}},vq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=nq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(kq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var wq={1:0,3:0,4:0,7:3,9:3,10:3};function xq(){return Lo("tcf",function(){return{}})}var yq=function(){return new oq(z,{timeoutMs:-1})};
function zq(){var a=xq(),b=yq();qq(b)&&!Aq()&&!Bq()&&O(124);if(!a.active&&qq(b)){Aq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,zm().active=!0,a.tcString="tcunavailable");Eo();try{b.addEventListener(function(c){if(c.internalErrorState!==0)Cq(a),Fo([N.m.T,N.m.Ca,N.m.U]),zm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,Bq()&&(a.active=!0),!Dq(c)||Aq()||Bq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in wq)wq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(Dq(c)){var g={},h;for(h in wq)if(wq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={Xm:!0};p=p===void 0?{}:p;m=vq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.Xm)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?tq(n,"1",0):!0:!1;g["1"]=m}else g[h]=tq(c,h,wq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.T]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Fo([N.m.T,N.m.Ca,N.m.U]),zm().active=!0):(r[N.m.Ca]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.U]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Fo([N.m.U]),yo(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:Eq()||""}))}}else Fo([N.m.T,N.m.Ca,N.m.U])})}catch(c){Cq(a),Fo([N.m.T,N.m.Ca,N.m.U]),zm().active=!0}}}
function Cq(a){a.type="e";a.tcString="tcunavailable"}function Dq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function Aq(){return z.gtag_enable_tcf_support===!0}function Bq(){return xq().enableAdvertiserConsentMode===!0}function Eq(){var a=xq();if(a.active)return a.tcString}function Fq(){var a=xq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Gq(a){if(!wq.hasOwnProperty(String(a)))return!0;var b=xq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Hq=[N.m.T,N.m.Z,N.m.U,N.m.Ca],Iq={},Jq=(Iq[N.m.T]=1,Iq[N.m.Z]=2,Iq);function Kq(a){if(a===void 0)return 0;switch(P(a,N.m.xa)){case void 0:return 1;case !1:return 3;default:return 2}}function Lq(a){if(Qn()==="US-CO"&&fc.globalPrivacyControl===!0)return!1;var b=Kq(a);if(b===3)return!1;switch(Im(N.m.Ca)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Mq(){return Lm()||!Hm(N.m.T)||!Hm(N.m.Z)}
function Nq(){var a={},b;for(b in Jq)Jq.hasOwnProperty(b)&&(a[Jq[b]]=Im(b));return"G1"+Me(a[1]||0)+Me(a[2]||0)}var Oq={},Pq=(Oq[N.m.T]=0,Oq[N.m.Z]=1,Oq[N.m.U]=2,Oq[N.m.Ca]=3,Oq);function Qq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Rq(a){for(var b="1",c=0;c<Hq.length;c++){var d=b,e,f=Hq[c],g=Gm.delegatedConsentTypes[f];e=g===void 0?0:Pq.hasOwnProperty(g)?12|Pq[g]:8;var h=zm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Qq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Qq(m.declare)<<4|Qq(m.default)<<2|Qq(m.update)])}var n=b,p=(Qn()==="US-CO"&&fc.globalPrivacyControl===!0?1:0)<<3,q=(Lm()?1:0)<<2,r=Kq(a);b=
n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Gm.containerScopedDefaults.ad_storage<<4|Gm.containerScopedDefaults.analytics_storage<<2|Gm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Gm.usedContainerScopedDefaults?1:0)<<2|Gm.containerScopedDefaults.ad_personalization]}
function Sq(){if(!Hm(N.m.U))return"-";for(var a=Object.keys(di),b=Jm(a),c="",d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;b[f]&&(c+=di[f])}(Gm.usedCorePlatformServices?Gm.selectedAllCorePlatformServices:1)&&(c+="o");return c||"-"}function Tq(){return Sn()||(Aq()||Bq())&&Fq()==="1"?"1":"0"}function Uq(){return(Sn()?!0:!(!Aq()&&!Bq())&&Fq()==="1")||!Hm(N.m.U)}
function Vq(){var a="0",b="0",c;var d=xq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=xq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;Sn()&&(h|=1);Fq()==="1"&&(h|=2);Aq()&&(h|=4);var m;var n=xq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);zm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Wq(){return Qn()==="US-CO"};function Xq(){var a=!1;return a};var Yq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Zq(a){a=a===void 0?{}:a;var b=Wf.ctid.split("-")[0].toUpperCase(),c={ctid:Wf.ctid,Xn:rj.wh,Zn:rj.xh,Cn:Ul.Le?2:1,ho:a.Jk,Ve:Wf.canonicalContainerId};c.Ve!==a.Ea&&(c.Ea=a.Ea);var d=gm();c.Ln=d?d.canonicalContainerId:void 0;xj?(c.Fg=Yq[b],c.Fg||(c.Fg=0)):c.Fg=Bj?13:10;oj.C?(c.Cg=0,c.ym=2):zj?c.Cg=1:Xq()?c.Cg=2:c.Cg=3;var e={};e[6]=Vl;oj.H===2?e[7]=!0:oj.H===1&&(e[2]=!0);if(ic){var f=fk(lk(ic),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Bm=e;var g=a.pg,h;var m=c.Fg,
n=c.Cg;m===void 0?h="":(n||(n=0),h=""+Oe(1,1)+Le(m<<2|n));var p=c.ym,q="4"+h+(p?""+Oe(2,1)+Le(p):""),r,t=c.Zn;r=t&&Ne.test(t)?""+Oe(3,2)+t:"";var u,v=c.Xn;u=v?""+Oe(4,1)+Le(v):"";var w;var x=c.ctid;if(x&&g){var y=x.split("-"),B=y[0].toUpperCase();if(B!=="GTM"&&B!=="OPT")w="";else{var C=y[1];w=""+Oe(5,3)+Le(1+C.length)+(c.Cn||0)+C}}else w="";var E=c.ho,F=c.Ve,K=c.Ea,L=c.Wo,T=q+r+u+w+(E?""+Oe(6,1)+Le(E):"")+(F?""+Oe(7,3)+Le(F.length)+F:"")+(K?""+Oe(8,3)+Le(K.length)+K:"")+(L?""+Oe(9,3)+Le(L.length)+
L:""),J;var Z=c.Bm;Z=Z===void 0?{}:Z;for(var X=[],da=l(Object.keys(Z)),U=da.next();!U.done;U=da.next()){var Q=U.value;X[Number(Q)]=Z[Q]}if(X.length){var ja=Oe(10,3),ia;if(X.length===0)ia=Le(0);else{for(var la=[],Ia=0,Ka=!1,Ca=0;Ca<X.length;Ca++){Ka=!0;var Wa=Ca%6;X[Ca]&&(Ia|=1<<Wa);Wa===5&&(la.push(Le(Ia)),Ia=0,Ka=!1)}Ka&&la.push(Le(Ia));ia=la.join("")}var Za=ia;J=""+ja+Le(Za.length)+Za}else J="";var Ob=c.Ln;return T+J+(Ob?""+Oe(11,3)+Le(Ob.length)+Ob:"")};function $q(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var ar={M:{km:0,Fi:1,Df:2,Ii:3,Jg:4,Gi:5,Hi:6,Ji:7,Kg:8,Kj:9,Jj:10,ph:11,Lj:12,kg:13,Nj:14,Ne:15,jm:16,Dd:17,Dh:18,Eh:19,Fh:20,bk:21,Gh:22,Lg:23,Qi:24}};ar.M[ar.M.km]="RESERVED_ZERO";ar.M[ar.M.Fi]="ADS_CONVERSION_HIT";ar.M[ar.M.Df]="CONTAINER_EXECUTE_START";ar.M[ar.M.Ii]="CONTAINER_SETUP_END";ar.M[ar.M.Jg]="CONTAINER_SETUP_START";ar.M[ar.M.Gi]="CONTAINER_BLOCKING_END";ar.M[ar.M.Hi]="CONTAINER_EXECUTE_END";ar.M[ar.M.Ji]="CONTAINER_YIELD_END";ar.M[ar.M.Kg]="CONTAINER_YIELD_START";ar.M[ar.M.Kj]="EVENT_EXECUTE_END";
ar.M[ar.M.Jj]="EVENT_EVALUATION_END";ar.M[ar.M.ph]="EVENT_EVALUATION_START";ar.M[ar.M.Lj]="EVENT_SETUP_END";ar.M[ar.M.kg]="EVENT_SETUP_START";ar.M[ar.M.Nj]="GA4_CONVERSION_HIT";ar.M[ar.M.Ne]="PAGE_LOAD";ar.M[ar.M.jm]="PAGEVIEW";ar.M[ar.M.Dd]="SNIPPET_LOAD";ar.M[ar.M.Dh]="TAG_CALLBACK_ERROR";ar.M[ar.M.Eh]="TAG_CALLBACK_FAILURE";ar.M[ar.M.Fh]="TAG_CALLBACK_SUCCESS";ar.M[ar.M.bk]="TAG_EXECUTE_END";ar.M[ar.M.Gh]="TAG_EXECUTE_START";ar.M[ar.M.Lg]="CUSTOM_PERFORMANCE_START";ar.M[ar.M.Qi]="CUSTOM_PERFORMANCE_END";var br=[],cr={},dr={};var er=["1"];function fr(a){return a.origin!=="null"};function gr(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return jg(13)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function hr(a,b,c,d){if(!ir(d))return[];if(br.includes("1")){var e;(e=Lc())==null||e.mark("1-"+ar.M.Lg+"-"+(dr["1"]||0))}var f=gr(a,String(b||jr()),c);if(br.includes("1")){var g="1-"+ar.M.Qi+"-"+(dr["1"]||0),h={start:"1-"+ar.M.Lg+"-"+(dr["1"]||0),end:g},m;(m=Lc())==null||m.mark(g);var n,p,q=(p=(n=Lc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(dr["1"]=(dr["1"]||0)+1,cr["1"]=q+(cr["1"]||0))}return f}
function kr(a,b,c,d,e){if(ir(e)){var f=lr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=mr(f,function(g){return g.Km},b);if(f.length===1)return f[0];f=mr(f,function(g){return g.Nn},c);return f[0]}}}function nr(a,b,c,d){var e=jr(),f=window;fr(f)&&(f.document.cookie=a);var g=jr();return e!==g||c!==void 0&&hr(b,g,!1,d).indexOf(c)>=0}
function or(a,b,c,d){function e(w,x,y){if(y==null)return delete h[x],w;h[x]=y;return w+"; "+x+"="+y}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!ir(c.Kb))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=pr(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Hn);g=e(g,"samesite",c.ao);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=qr(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!rr(u,c.path)&&nr(v,a,b,c.Kb))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return rr(n,c.path)?1:nr(g,a,b,c.Kb)?0:1}function sr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return or(a,b,c)}
function mr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function lr(a,b,c){for(var d=[],e=hr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Dm:e[f],Em:g.join("."),Km:Number(n[0])||1,Nn:Number(n[1])||1})}}}return d}function pr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var tr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,ur=/(^|\.)doubleclick\.net$/i;function rr(a,b){return a!==void 0&&(ur.test(window.document.location.hostname)||b==="/"&&tr.test(a))}function vr(a){if(!a)return 1;var b=a;jg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function wr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function xr(a,b){var c=""+vr(a),d=wr(b);d>1&&(c+="-"+d);return c}
var jr=function(){return fr(window)?window.document.cookie:""},ir=function(a){return a&&jg(8)?(Array.isArray(a)?a:[a]).every(function(b){return Km(b)&&Hm(b)}):!0},qr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;ur.test(e)||tr.test(e)||a.push("none");return a};function yr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^$q(a)&2147483647):String(b)}function zr(a){return[yr(a),Math.round(pb()/1E3)].join(".")}function Ar(a,b,c,d,e){var f=vr(b),g;return(g=kr(a,f,wr(c),d,e))==null?void 0:g.Em}function Br(a,b,c,d){return[b,xr(c,d),a].join(".")};function Cr(a,b,c,d){var e,f=Number(a.Jb!=null?a.Jb:void 0);f!==0&&(e=new Date((b||pb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Kb:d}};var Dr=["ad_storage","ad_user_data"];function Er(a,b){if(!a)return 10;if(b===null||b===void 0||b==="")return 11;var c=Fr(!1);if(c.error!==0)return c.error;if(!c.value)return 2;c.value[a]=b;return Gr(c)}function Hr(a){if(!a)return{error:10};var b=Fr();if(b.error!==0)return b;if(!b.value)return{error:2};if(!(a in b.value))return{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?{value:void 0,error:11}:{value:c,error:0}}
function Fr(a){a=a===void 0?!0:a;if(!Hm(Dr))return{error:3};try{if(!z.localStorage)return{error:1}}catch(f){return{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=z.localStorage.getItem("_gcl_ls")}catch(f){return{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return{error:12}}}catch(f){return{error:8}}if(b.schema!=="gcl")return{error:4};if(b.version!==1)return{error:5};try{var e=Ir(b);a&&e&&Gr({value:b,error:0})}catch(f){return{error:8}}return{value:b,error:0}}
function Ir(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Ir(a[e.value])||c;return c}return!1}
function Gr(a){if(a.error)return a.error;if(!a.value)return 2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return 6}try{z.localStorage.setItem("_gcl_ls",c)}catch(d){return 7}return 0};function Jr(){if(!Kr())return-1;var a=Lr();return a!==-1&&Mr(a+1)?a+1:-1}function Lr(){if(!Kr())return-1;var a=Hr("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Kr(){return Hm(["ad_storage","ad_user_data"])?jg(11):!1}
function Mr(a,b){b=b||{};var c=pb();return Er("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(Cr(b,c,!0).expires)})===0?!0:!1};var Nr;function Or(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Pr,d=Qr,e=Rr();if(!e.init){wc(A,"mousedown",a);wc(A,"keyup",a);wc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Sr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Rr().decorators.push(f)}
function Tr(a,b,c){for(var d=Rr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&sb(e,g.callback())}}return e}
function Rr(){var a=jc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ur=/(.*?)\*(.*?)\*(.*)/,Vr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Wr=/^(?:www\.|m\.|amp\.)+/,Xr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Yr(a){var b=Xr.exec(a);if(b)return{oi:b[1],query:b[2],fragment:b[3]}}function Zr(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function $r(a,b){var c=[fc.userAgent,(new Date).getTimezoneOffset(),fc.userLanguage||fc.language,Math.floor(pb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Nr)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Nr=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Nr[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function as(a){return function(b){var c=lk(z.location.href),d=c.search.replace("?",""),e=dk(d,"_gl",!1,!0)||"";b.query=bs(e)||{};var f=fk(c,"fragment"),g;var h=-1;if(ub(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=bs(g||"")||{};a&&cs(c,d,f)}}function ds(a,b){var c=Zr(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function cs(a,b,c){function d(g,h){var m=ds("_gl",g);m.length&&(m=h+m);return m}if(ec&&ec.replaceState){var e=Zr("_gl");if(e.test(b)||e.test(c)){var f=fk(a,"path");b=d(b,"?");c=d(c,"#");ec.replaceState({},"",""+f+b+c)}}}function es(a,b){var c=as(!!b),d=Rr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(sb(e,f.query),a&&sb(e,f.fragment));return e}
var bs=function(a){try{var b=fs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Ta(d[e+1]);c[f]=g}Va("TAGGING",6);return c}}catch(h){Va("TAGGING",8)}};function fs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ur.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===$r(h,p)){m=!0;break a}m=!1}if(m)return h;Va("TAGGING",7)}}}
function gs(a,b,c,d,e){function f(p){p=ds(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Yr(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.oi+h+m}
function hs(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Sa(String(x))))}var y=v.join("*");u=["1",$r(y),y].join("*");d?(jg(3)||jg(1)||!p)&&is("_gl",u,a,p,q):js("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Tr(b,1,d),f=Tr(b,2,d),g=Tr(b,4,d),h=Tr(b,3,d);c(e,!1,!1);c(f,!0,!1);jg(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ks(m,h[m],a)}function ks(a,b,c){c.tagName.toLowerCase()==="a"?js(a,b,c):c.tagName.toLowerCase()==="form"&&is(a,b,c)}function js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!jg(5)||d)){var h=z.location.href,m=Yr(c.href),n=Yr(h);g=!(m&&n&&m.oi===n.oi&&m.query===n.query&&m.fragment)}f=g}if(f){var p=gs(a,b,c.href,d,e);Vb.test(p)&&(c.href=p)}}
function is(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=(jg(12)?c.getAttribute("action"):c.action)||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=gs(a,b,f,d,e);Vb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Pr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||hs(e,e.hostname)}}catch(g){}}function Qr(a){try{var b;if(b=jg(12)?a.getAttribute("action"):a.action){var c=fk(lk(b),"host");hs(a,c)}}catch(d){}}function ls(a,b,c,d){Or();var e=c==="fragment"?2:1;d=!!d;Sr(a,b,e,d,!1);e===2&&Va("TAGGING",23);d&&Va("TAGGING",24)}
function ms(a,b){Or();Sr(a,[hk(z.location,"host",!0)],b,!0,!0)}function ns(){var a=A.location.hostname,b=Vr.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Wr,""),m=e.replace(Wr,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function os(a,b){return a===!1?!1:a||b||ns()};var ps=["1"],qs={},rs={};function ss(a,b){b=b===void 0?!0:b;var c=ts(a.prefix);if(qs[c])us(a);else if(vs(c,a.path,a.domain)){var d=rs[ts(a.prefix)]||{id:void 0,Bg:void 0};b&&ws(a,d.id,d.Bg);us(a)}else{var e=nk("auiddc");if(e)Va("TAGGING",17),qs[c]=e;else if(b){var f=ts(a.prefix),g=zr();xs(f,g,a);vs(c,a.path,a.domain);us(a,!0)}}}
function us(a,b){if((b===void 0?0:b)&&Kr()){var c=Fr(!1);c.error===0&&c.value&&"gcl_ctr"in c.value&&(delete c.value.gcl_ctr,Gr(c))}Hm(["ad_storage","ad_user_data"])&&jg(10)&&Lr()===-1&&Mr(0,a)}function ws(a,b,c){var d=ts(a.prefix),e=qs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(pb()/1E3)));xs(d,h,a,g*1E3)}}}}function xs(a,b,c,d){var e=Br(b,"1",c.domain,c.path),f=Cr(c,d);f.Kb=ys();sr(a,e,f)}
function vs(a,b,c){var d=Ar(a,b,c,ps,ys());if(!d)return!1;zs(a,d);return!0}function zs(a,b){var c=b.split(".");c.length===5?(qs[a]=c.slice(0,2).join("."),rs[a]={id:c.slice(2,4).join("."),Bg:Number(c[4])||0}):c.length===3?rs[a]={id:c.slice(0,2).join("."),Bg:Number(c[2])||0}:qs[a]=b}function ts(a){return(a||"_gcl")+"_au"}function As(a){function b(){Hm(c)&&a()}var c=ys();Om(function(){b();Hm(c)||Pm(b,c)},c)}
function Bs(a){var b=es(!0),c=ts(a.prefix);As(function(){var d=b[c];if(d){zs(c,d);var e=Number(qs[c].split(".")[1])*1E3;if(e){Va("TAGGING",16);var f=Cr(a,e);f.Kb=ys();var g=Br(d,"1",a.domain,a.path);sr(c,g,f)}}})}function Cs(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Ar(a,e.path,e.domain,ps,ys());h&&(g[a]=h);return g};As(function(){ls(f,b,c,d)})}function ys(){return["ad_storage","ad_user_data"]};var Ds={},Es=(Ds.k={X:/^[\w-]+$/},Ds.b={X:/^[\w-]+$/,yi:!0},Ds.i={X:/^[1-9]\d*$/},Ds.h={X:/^\d+$/},Ds.t={X:/^[1-9]\d*$/},Ds.d={X:/^[A-Za-z0-9_-]+$/},Ds.j={X:/^\d+$/},Ds.u={X:/^[1-9]\d*$/},Ds.l={X:/^[01]$/},Ds.o={X:/^[1-9]\d*$/},Ds.g={X:/^[01]$/},Ds.s={X:/^.+$/},Ds);var Fs={},Js=(Fs[5]={Hg:{2:Gs},hi:"2",qg:["k","i","b","u"]},Fs[4]={Hg:{2:Gs,GCL:Hs},hi:"2",qg:["k","i","b"]},Fs[2]={Hg:{GS2:Gs,GS1:Is},hi:"GS2",qg:"sogtjlhd".split("")},Fs);function Ks(a,b,c){var d=Js[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hg[e];if(f)return f(a,b)}}}
function Gs(a,b){var c=a.split(".");if(c.length===3){var d={},e=Js[b];if(e){for(var f=e.qg,g=l(c[2].split("$")),h=g.next();!h.done;h=g.next()){var m=h.value,n=m[0];if(f.indexOf(n)!==-1)try{var p=decodeURIComponent(m.substring(1)),q=Es[n];q&&(q.yi?(d[n]=d[n]||[],d[n].push(p)):d[n]=p)}catch(r){}}return d}}}function Ls(a,b,c){var d=Js[b];if(d)return[d.hi,c||"1",Ms(a,b)].join(".")}
function Ms(a,b){var c=Js[b];if(c){for(var d=[],e=l(c.qg),f=e.next();!f.done;f=e.next()){var g=f.value,h=Es[g];if(h){var m=a[g];if(m!==void 0)if(h.yi&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Hs(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Is(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Ns=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Os(a,b,c){if(Js[b]){for(var d=[],e=hr(a,void 0,void 0,Ns.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Ks(g.value,b,c);h&&d.push(Ps(h))}return d}}function Qs(a,b,c,d,e){d=d||{};var f=xr(d.domain,d.path),g=Ls(b,c,f);if(!g)return 1;var h=Cr(d,e,void 0,Ns.get(c));return sr(a,g,h)}function Rs(a,b){var c=b.X;return typeof c==="function"?c(a):c.test(a)}
function Ps(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Xe:void 0},c=b.next()){var e=c.value,f=a[e];d.Xe=Es[e];d.Xe?d.Xe.yi?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Rs(h,g.Xe)}}(d)):void 0:typeof f==="string"&&Rs(f,d.Xe)||(a[e]=void 0):a[e]=void 0}return a};function Ss(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Di:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Ts(a,b){var c=Ss(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Di]||(d[c[e].Di]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,aa:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Di].push(g)}}return d};function Us(){var a=String,b=z.location.hostname,c=z.location.pathname,d=b=Cb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Cb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a($q((""+b+e).toLowerCase()))};var Vs=/^\w+$/,Ws=/^[\w-]+$/,Xs={},Ys=(Xs.aw="_aw",Xs.dc="_dc",Xs.gf="_gf",Xs.gp="_gp",Xs.gs="_gs",Xs.ha="_ha",Xs.ag="_ag",Xs.gb="_gb",Xs);function Zs(){return["ad_storage","ad_user_data"]}function $s(a){return!jg(8)||Hm(a)}function at(a,b){function c(){var d=$s(b);d&&a();return d}Om(function(){c()||Pm(c,b)},b)}function bt(a){return ct(a).map(function(b){return b.aa})}function dt(a){return et(a).filter(function(b){return b.aa}).map(function(b){return b.aa})}
function et(a){var b=ft(a.prefix),c=gt("gb",b),d=gt("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=ct(c).map(e("gb")),g=ht(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}function it(a,b,c,d,e,f){var g=eb(a,function(h){return h.aa===c});g?(g.timestamp<d&&(g.timestamp=d,g.Md=f),g.labels=jt(g.labels||[],e||[])):a.push({version:b,aa:c,timestamp:d,labels:e,Md:f})}
function ht(a){for(var b=Os(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=kt(f);if(n){var p=void 0;jg(9)&&(p=f.u);it(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function ct(a){for(var b=[],c=hr(a,A.cookie,void 0,Zs()),d=l(c),e=d.next();!e.done;e=d.next()){var f=lt(e.value);if(f!=null){var g=f;it(b,g.version,g.aa,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return mt(b)}
function nt(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}function ot(a,b){var c=eb(a,function(d){return d.aa===b.aa});c?(c.Pa=c.Pa?b.Pa?c.timestamp<b.timestamp?b.Pa:c.Pa:c.Pa||0:b.Pa||0,c.timestamp<b.timestamp&&(c.timestamp=b.timestamp,c.Md=b.Md),c.labels=nt(c.labels||[],b.labels||[]),c.Bc=nt(c.Bc||[],b.Bc||[])):a.push(b)}
function pt(){var a=Hr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;return d&&d.match(Ws)?{version:"",aa:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Pa:c.linkDecorationSource||0,Bc:[2]}:null}catch(e){return null}}
function qt(a){for(var b=[],c=hr(a,A.cookie,void 0,Zs()),d=l(c),e=d.next();!e.done;e=d.next()){var f=lt(e.value);f!=null&&(f.Md=void 0,f.Pa=0,f.Bc=[1],ot(b,f))}var g=pt();g&&(g.Md=void 0,g.Pa=g.Pa||0,g.Bc=g.Bc||[2],ot(b,g));b.sort(function(h,m){return m.timestamp-h.timestamp});return mt(b)}function jt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}
function ft(a){return a&&typeof a==="string"&&a.match(Vs)?a:"_gcl"}function rt(a,b,c){var d=lk(a),e=fk(d,"query",!1,void 0,"gclsrc"),f={value:fk(d,"query",!1,void 0,"gclid"),Pa:c?4:2};if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=dk(g,"gclid",!1),f.Pa=3);e||(e=dk(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function st(a,b){var c=lk(a),d=fk(c,"query",!1,void 0,"gclid"),e=fk(c,"query",!1,void 0,"gclsrc"),f=fk(c,"query",!1,void 0,"wbraid");f=Ab(f);var g=fk(c,"query",!1,void 0,"gbraid"),h=fk(c,"query",!1,void 0,"gad_source"),m=fk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||dk(n,"gclid",!1);e=e||dk(n,"gclsrc",!1);f=f||dk(n,"wbraid",!1);g=g||dk(n,"gbraid",!1);h=h||dk(n,"gad_source",!1)}return tt(d,e,m,f,g,h)}function ut(){return st(z.location.href,!0)}
function tt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Ws))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Ws.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Ws.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Ws.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function vt(a){for(var b=ut(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=st(z.document.referrer,!1),b.gad_source=void 0);wt(b,!1,a)}
function xt(a){vt(a);var b=rt(z.location.href,!0,!1);b.length||(b=rt(z.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=pb(),e=Cr(a,d,!0),f=Zs(),g=function(){$s(f)&&e.expires!==void 0&&Er("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSource:c.Pa},expires:Number(e.expires)})};Om(function(){g();$s(f)||Pm(g,f)},f)}}
function wt(a,b,c,d,e){c=c||{};e=e||[];var f=ft(c.prefix),g=d||pb(),h=Math.round(g/1E3),m=Zs(),n=!1,p=!1,q=function(){if($s(m)){var r=Cr(c,g,!0);r.Kb=m;for(var t=function(L,T){var J=gt(L,f);J&&(sr(J,T,r),L!=="gb"&&(n=!0))},u=function(L){var T=["GCL",h,L];e.length>0&&T.push(e.join("."));return T.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var y=a.gb[0],B=gt("gb",f);!b&&ct(B).some(function(L){return L.aa===y&&L.labels&&L.labels.length>
0})||t("gb",u(y))}}if(!p&&a.gbraid&&$s("ad_storage")&&(p=!0,!n)){var C=a.gbraid,E=gt("ag",f);if(b||!ht(E).some(function(L){return L.aa===C&&L.labels&&L.labels.length>0})){var F={},K=(F.k=C,F.i=""+h,F.b=e,F);Qs(E,K,5,c,g)}}zt(a,f,g,c)};Om(function(){q();$s(m)||Pm(q,m)},m)}
function zt(a,b,c,d){if(a.gad_source!==void 0&&$s("ad_storage")){if(jg(4)){var e=Kc();if(e==="r"||e==="h")return}var f=a.gad_source,g=gt("gs",b);if(g){var h=Math.floor((pb()-(Jc()||0))/1E3),m;if(jg(9)){var n=Us(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Qs(g,m,5,d,c)}}}
function At(a,b){var c=es(!0);at(function(){for(var d=ft(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Ys[f]!==void 0){var g=gt(f,d),h=c[g];if(h){var m=Math.min(Bt(h),pb()),n;b:{for(var p=m,q=hr(g,A.cookie,void 0,Zs()),r=0;r<q.length;++r)if(Bt(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Cr(b,m,!0);t.Kb=Zs();sr(g,h,t)}}}}wt(tt(c.gclid,c.gclsrc),!1,b)},Zs())}
function Ct(a){var b=["ag"],c=es(!0),d=ft(a.prefix);at(function(){for(var e=0;e<b.length;++e){var f=gt(b[e],d);if(f){var g=c[f];if(g){var h=Ks(g,5);if(h){var m=kt(h);m||(m=pb());var n;a:{for(var p=m,q=Os(f,5),r=0;r<q.length;++r)if(kt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Qs(f,h,5,a,m)}}}}},["ad_storage"])}function gt(a,b){var c=Ys[a];if(c!==void 0)return b+c}function Bt(a){return Dt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function kt(a){return a?(Number(a.i)||0)*1E3:0}function lt(a){var b=Dt(a.split("."));return b.length===0?null:{version:b[0],aa:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Dt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Ws.test(a[2])?[]:a}
function Et(a,b,c,d,e){if(Array.isArray(b)&&fr(z)){var f=ft(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=gt(a[m],f);if(n){var p=hr(n,A.cookie,void 0,Zs());p.length&&(h[n]=p.sort()[p.length-1])}}return h};at(function(){ls(g,b,c,d)},Zs())}}
function Ft(a,b,c,d){if(Array.isArray(a)&&fr(z)){var e=["ag"],f=ft(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=gt(e[m],f);if(!n)return{};var p=Os(n,5);if(p.length){var q=p.sort(function(r,t){return kt(t)-kt(r)})[0];h[n]=Ls(q,5)}}return h};at(function(){ls(g,a,b,c)},["ad_storage"])}}function mt(a){return a.filter(function(b){return Ws.test(b.aa)})}
function Gt(a,b){if(fr(z)){for(var c=ft(b.prefix),d={},e=0;e<a.length;e++)Ys[a[e]]&&(d[a[e]]=Ys[a[e]]);at(function(){ib(d,function(f,g){var h=hr(c+g,A.cookie,void 0,Zs());h.sort(function(t,u){return Bt(u)-Bt(t)});if(h.length){var m=h[0],n=Bt(m),p=Dt(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Dt(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];wt(q,!0,b,n,p)}})},Zs())}}
function Ht(a){var b=["ag"],c=["gbraid"];at(function(){for(var d=ft(a.prefix),e=0;e<b.length;++e){var f=gt(b[e],d);if(!f)break;var g=Os(f,5);if(g.length){var h=g.sort(function(q,r){return kt(r)-kt(q)})[0],m=kt(h),n=h.b,p={};p[c[e]]=h.k;wt(p,!0,a,m,n)}}},["ad_storage"])}function It(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Jt(a){function b(h,m,n){n&&(h[m]=n)}if(Lm()){var c=ut(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:es(!1)._gs);if(It(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ms(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ms(function(){return g},1)}}}
function Kt(a){if(!jg(1))return null;var b=es(!0).gad_source;if(b!=null)return z.location.hash="",b;if(jg(2)){var c=lk(z.location.href);b=fk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=ut();if(It(d,a))return"0"}return null}function Lt(a){var b=Kt(a);b!=null&&ms(function(){var c={};return c.gad_source=b,c},4)}
function Mt(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Nt(a,b,c,d){var e=[];c=c||{};if(!$s(Zs()))return e;var f=ct(a),g=Mt(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.aa].concat(n.labels||[],[b]).join("."),r=Cr(c,p,!0);r.Kb=Zs();sr(a,q,r)}return e}
function Ot(a,b){var c=[];b=b||{};var d=et(b),e=Mt(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ft(b.prefix),n=gt(h.type,m);if(!n)break;var p=h,q=p.version,r=p.aa,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Qs(n,x,5,b,u)}else if(h.type==="gb"){var y=[q,v,r].concat(t||[],[a]).join("."),B=Cr(b,u,!0);B.Kb=Zs();sr(n,y,B)}}return c}
function Pt(a,b){var c=ft(b),d=gt(a,c);if(!d)return 0;var e;e=a==="ag"?ht(d):ct(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Qt(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Rt(a){var b=Math.max(Pt("aw",a),Qt($s(Zs())?Ts():{})),c=Math.max(Pt("gb",a),Qt($s(Zs())?Ts("_gac_gb",!0):{}));c=Math.max(c,Pt("ag",a));return c>b};function gu(){return Lo("dedupe_gclid",function(){return zr()})};var hu=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,iu=/^www.googleadservices.com$/;function ju(a){a||(a=ku());return a.po?!1:a.nn||a.on||a.rn||a.pn||a.df||a.Wm||a.qn||a.dn?!0:!1}function ku(){var a={},b=es(!0);a.po=!!b._up;var c=ut();a.nn=c.aw!==void 0;a.on=c.dc!==void 0;a.rn=c.wbraid!==void 0;a.pn=c.gbraid!==void 0;a.qn=c.gclsrc==="aw.ds";a.df=Ut().df;var d=A.referrer?fk(lk(A.referrer),"host"):"";a.dn=hu.test(d);a.Wm=iu.test(d);return a};var lu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];
function mu(){if(H(118)){if(Fn(An.Pe))return O(176),An.Pe;if(Fn(An.Tj))return O(170),An.Pe;var a=ml();if(!a)O(171);else if(a.opener){var b=function(e){if(lu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?En(An.Pe,{gadSource:e.data.gadSource}):O(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);Hk(a,"message",b)}else O(172)};if(Gk(a,"message",b)){En(An.Tj,!0);for(var c=l(lu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);O(174);return An.Pe}O(175)}}}
;var nu=function(){this.C=this.gppString=void 0};nu.prototype.reset=function(){this.C=this.gppString=void 0};var ou=new nu;var pu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),qu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,ru=/^\d+\.fls\.doubleclick\.net$/,su=/;gac=([^;?]+)/,tu=/;gacgb=([^;?]+)/;
function uu(a,b){if(ru.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(pu)?ek(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].aa);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function vu(a,b,c){for(var d=$s(Zs())?Ts("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Nt("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Vm:f?e.join(";"):"",Um:uu(d,tu)}}function wu(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(qu)?b[1]:void 0}
function xu(a){var b=jg(9),c={},d,e,f;ru.test(A.location.host)&&(d=wu("gclgs"),e=wu("gclst"),b&&(f=wu("gcllp")));if(d&&e&&(!b||f))c.vg=d,c.xg=e,c.wg=f;else{var g=pb(),h=ht((a||"_gcl")+"_gs"),m=h.map(function(q){return q.aa}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Md}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.vg=m.join("."),c.xg=n.join("."),b&&p.length>0&&(c.wg=p.join(".")))}return c}
function yu(a,b,c,d){d=d===void 0?!1:d;if(ru.test(A.location.host)){var e=wu(c);if(e)return e.split(".").map(function(g){return{aa:g}})}else{if(b==="gclid"){var f=(a||"_gcl")+"_aw";return d?qt(f):ct(f)}if(b==="wbraid")return ct((a||"_gcl")+"_gb");if(b==="braids")return et({prefix:a})}return[]}function zu(a){return ru.test(A.location.host)?!(wu("gclaw")||wu("gac")):Rt(a)}
function Au(a,b,c){var d;d=c?Ot(a,b):Nt((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Bu(){var a=z.__uspapi;if(ab(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
function Ou(a){var b=P(a.D,N.m.sc),c=P(a.D,N.m.rc);b&&!c?(a.eventName!==N.m.ka&&a.eventName!==N.m.md&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function Pu(a){var b=Ao(N.m.T)?Ko.pscdl:"denied";b!=null&&V(a,N.m.Nf,b)}function Qu(a){var b=kl(!0);V(a,N.m.qc,b)}function Ru(a){Wq()&&V(a,N.m.wd,1)}function Fu(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&ek(a.substring(0,b))===void 0;)b--;return ek(a.substring(0,b))||""}
function Su(a){Tu(a,"ce",P(a.D,N.m.Ya))}function Tu(a,b,c){Eu(a,N.m.zd)||V(a,N.m.zd,{});Eu(a,N.m.zd)[b]=c}function Uu(a){S(a,"transmission_type",ym.V.wa)}function Vu(a){var b=Xa("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,N.m.te,b),Ua.GTAG_EVENT_FEATURE_CHANNEL=lj)}function Wu(a){if(H(86)){var b=pp(a.D,N.m.Mc);b&&V(a,N.m.Mc,b)}}
function Xu(a,b){b=b===void 0?!1:b;if(H(107)){var c=R(a,"send_to_destinations");if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,"accept_by_default",!1),b||!Yu(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,"accept_by_default",!0)}};function iv(a,b,c,d){var e=sc(),f;if(e===1)a:{var g=Dj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==z.location.protocol?a:b)+c};function jv(a){return typeof a!=="object"||a===null?{}:a}function kv(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function lv(a){if(a!==void 0&&a!==null)return kv(a)}function mv(a){return typeof a==="number"?a:lv(a)};
var rv=function(a,b){if(a)if(Xq()){}else if(a=bb(a)?Uo(jm(a)):Uo(jm(a.id))){var c=void 0,d=!1,e=P(b,N.m.Ql);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Uo(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=P(b,N.m.Cj),m;if(h){m=Array.isArray(h)?h:[h];var n=P(b,N.m.Aj),p=P(b,N.m.Bj),q=P(b,N.m.Dj),r=lv(P(b,N.m.Pl)),t=n||p,u=1;a.prefix!==
"UA"||c||(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)nv(c,m[v],r,b,{hc:t,options:q});else if(a.prefix==="AW"&&a.ids[Xo[1]])H(155)?nv([a],m[v],r||"US",b,{hc:t,options:q}):ov(a.ids[Xo[0]],a.ids[Xo[1]],m[v],b,{hc:t,options:q});else if(a.prefix==="UA")if(H(155))nv([a],m[v],r||"US",b,{hc:t});else{var w=a.destinationId,x=m[v],y={hc:t};O(23);if(x){y=y||{};var B=pv(qv,y,w),C={};y.hc!==void 0?C.receiver=y.hc:C.replace=x;C.ga_wpid=w;C.destination=x;B(2,ob(),C)}}}}}},nv=function(a,b,c,d,e){O(21);if(b&&c){e=
e||{};for(var f={countryNameCode:c,destinationNumber:b,retrievalTime:ob()},g=0;g<a.length;g++){var h=a[g];sv[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Xo[0]],cl:h.ids[Xo[1]]},tv(f.adData,d),sv[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},sv[h.id]=!0))}(f.gaData||f.adData)&&pv(uv,e,void 0,d)(e.hc,f,e.options)}},ov=function(a,b,c,d,e){O(22);if(c){e=e||{};var f=pv(vv,e,a,d),g={ak:a,cl:b};e.hc===void 0&&(g.autoreplace=c);tv(g,d);f(2,e.hc,
g,c,0,ob(),e.options)}},tv=function(a,b){a.dma=Tq();Uq()&&(a.dmaCps=Sq());Lq(b)?a.npa="0":a.npa="1"},pv=function(a,b,c,d){if(z[a.functionName])return b.ni&&D(b.ni),z[a.functionName];var e=wv();z[a.functionName]=e;if(a.additionalQueues)for(var f=0;f<a.additionalQueues.length;f++)z[a.additionalQueues[f]]=z[a.additionalQueues[f]]||wv();a.idKey&&z[a.idKey]===void 0&&(z[a.idKey]=c);Il({destinationId:Wf.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},iv("https://",
"http://",a.scriptUrl),b.ni,b.Kn);return e},wv=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},vv={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},qv={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},xv={Uk:"9",mm:"5"},uv={functionName:"_googCallTrackingImpl",additionalQueues:[qv.functionName,vv.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+
(xv.Uk||xv.mm)+".js"},sv={};function yv(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Eu(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){Eu(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.D,b)},ac:function(){return a},getHitKeys:function(){return Object.keys(a.C)}}};function Fv(a,b){return arguments.length===1?Gv("set",a):Gv("set",a,b)}function Hv(a,b){return arguments.length===1?Gv("config",a):Gv("config",a,b)}function Iv(a,b,c){c=c||{};c[N.m.Oc]=a;return Gv("event",b,c)}function Gv(){return arguments};var Kv=function(){this.messages=[];this.C=[]};Kv.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Kv.prototype.listen=function(a){this.C.push(a)};
Kv.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Kv.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Lv(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata.source_canonical_id=Wf.canonicalContainerId;Mv().enqueue(a,b,c)}
function Nv(){var a=Ov;Mv().listen(a)}function Mv(){return Lo("mb",function(){return new Kv})};var Pv,Qv=!1;function Rv(){Qv=!0;Pv=Pv||{}}function Sv(a){Qv||Rv();return Pv[a]};function Tv(){var a=z.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Uv(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!z.getComputedStyle)return!0;var c=z.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=z.getComputedStyle(d,null))}return!1}var Sf;var sx=Number('')||5,tx=Number('')||50,ux=fb();
var wx=function(a,b){a&&(vx("sid",a.targetId,b),vx("cc",a.clientCount,b),vx("tl",a.totalLifeMs,b),vx("hc",a.heartbeatCount,b),vx("cl",a.clientLifeMs,b))},vx=function(a,b,c){b!=null&&c.push(a+"="+b)},xx=function(){var a=A.referrer;if(a){var b;return fk(lk(a),"host")===((b=z.location)==null?void 0:b.host)?1:2}return 0},zx=function(){this.R=yx;this.N=0};zx.prototype.H=function(a,b,c,d){var e=xx(),f,g=[];f=z===z.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&vx("si",a.lf,g);vx("m",0,g);vx("iss",f,g);vx("if",c,g);wx(b,g);d&&vx("fm",encodeURIComponent(d.substring(0,tx)),g);this.O(g);};zx.prototype.C=function(a,b,c,d,e){var f=[];vx("m",1,f);vx("s",a,f);vx("po",xx(),f);b&&(vx("st",b.state,f),vx("si",b.lf,f),vx("sm",b.yf,f));wx(c,f);vx("c",d,f);e&&vx("fm",encodeURIComponent(e.substring(0,tx)),f);this.O(f);};
zx.prototype.O=function(a){a=a===void 0?[]:a;!Ck||this.N>=sx||(vx("pid",ux,a),vx("bc",++this.N,a),a.unshift("ctid="+Wf.ctid+"&t=s"),this.R("https://www.googletagmanager.com/a?"+a.join("&")))};var Ax=Number('')||500,Bx=Number('')||5E3,Cx=Number('20')||10,Dx=Number('')||5E3;function Ex(a){return a.performance&&a.performance.now()||Date.now()}
var Fx=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{wk:function(){},xk:function(){},vk:function(){},onFailure:function(){}}:g;this.sm=e;this.C=f;this.N=g;this.fa=this.ia=this.heartbeatCount=this.rm=0;this.mg=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.lf=Ex(this.C);this.yf=Ex(this.C);this.R=10};d.prototype.init=function(){this.O(1);this.Da()};d.prototype.getState=function(){return{state:this.state,
lf:Math.round(Ex(this.C)-this.lf),yf:Math.round(Ex(this.C)-this.yf)}};d.prototype.O=function(e){this.state!==e&&(this.state=e,this.yf=Ex(this.C))};d.prototype.ek=function(){return String(this.rm++)};d.prototype.Da=function(){var e=this;this.heartbeatCount++;this.ab({type:0,clientId:this.id,requestId:this.ek(),maxDelay:this.ng()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.fa++,f.isDead||e.fa>Cx){var h=f.isDead&&f.failure.failureType;
e.R=h||10;e.O(4);e.om();var m,n;(n=(m=e.N).vk)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.O(3),e.fk();else{if(e.heartbeatCount>f.stats.heartbeatCount+Cx){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.N).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.O(2);if(r!==2)if(e.mg){var t,u;(u=(t=e.N).xk)==null||u.call(t)}else{e.mg=!0;var v,w;(w=(v=e.N).wk)==null||w.call(v)}e.fa=0;e.tm();e.fk()}}})};d.prototype.ng=function(){return this.state===2?
Bx:Ax};d.prototype.fk=function(){var e=this;this.C.setTimeout(function(){e.Da()},Math.max(0,this.ng()-(Ex(this.C)-this.ia)))};d.prototype.xm=function(e,f,g){var h=this;this.ab({type:1,clientId:this.id,requestId:this.ek(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.N).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.ab=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.R},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.C.setTimeout(function(){var r=g.H[m];r&&g.Me(r,7)},(n=e.maxDelay)!=null?n:Dx),q={request:e,Hk:f,Ck:h,Gn:p};this.H[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.ia=Ex(this.C);e.Ck=!1;this.sm(e.request)};d.prototype.tm=function(){for(var e=l(Object.keys(this.H)),f=e.next();!f.done;f=e.next()){var g=this.H[f.value];g.Ck&&this.sendRequest(g)}};d.prototype.om=function(){for(var e=
l(Object.keys(this.H)),f=e.next();!f.done;f=e.next())this.Me(this.H[f.value],this.R)};d.prototype.Me=function(e,f){this.xc(e);var g=e.request;g.failure={failureType:f};e.Hk(g)};d.prototype.xc=function(e){delete this.H[e.request.requestId];this.C.clearTimeout(e.Gn)};d.prototype.ln=function(e){this.ia=Ex(this.C);var f=this.H[e.requestId];if(f)this.xc(f),f.Hk(e);else{var g,h;(h=(g=this.N).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,z,b);return c};var Gx;
var Hx=function(){Gx||(Gx=new zx);return Gx},yx=function(a){Xm(Zm(ym.V.wc),function(){vc(a)})},Ix=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Jx=function(a){var b=a,c=oj.fa;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Kx=function(a){var b=Fn(An.Yj);return b&&b[a]},Lx=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.O=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.Gm(a);z.setTimeout(function(){f.initialize()},1E3);D(function(){f.vn(a,b,e)})};k=Lx.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),lf:this.initTime,yf:Math.round(pb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.xm(a,b,c)};k.getState=function(){return this.N.getState().state};k.vn=function(a,b,c){var d=z.location.origin,e=this,
f=tc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Ix(h):"",p;H(132)&&(p={sandbox:"allow-same-origin allow-scripts"});tc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.ln(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Gm=function(a){var b=this,c=Fx(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{wk:function(){b.O=!0;b.H.H(c.getState(),c.stats)},xk:function(){},vk:function(d){b.O?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Mx(){var a=Vf(Sf.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Nx(a,b,c){c=c===void 0?!1:c;var d=z.location.origin;if(!d||!Mx())return;Lj()&&(a=""+d+Kj()+"/_/service_worker");var e=Jx(a);if(e===null||Kx(e.origin))return;if(!gc()){Hx().H(void 0,void 0,6);return}var f=new Lx(e,!!a,b||Math.round(pb()),Hx(),c),g;a:{var h=An.Yj,m={},n=Dn(h);if(!n){n=Dn(h,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var Ox=function(a,b,c,d){var e;if((e=Kx(a))==null||!e.delegate){var f=gc()?16:6;Hx().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Kx(a).delegate(b,c,d);};
function Px(a,b,c,d,e){var f=Jx();if(f===null){d(gc()?16:6);return}var g,h=(g=Kx(f.origin))==null?void 0:g.initTime,m=Math.round(pb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Ox(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Qx(a,b,c,d){var e=Jx(a);if(e===null){d("_is_sw=f"+(gc()?16:6)+"te");return}var f=b?1:0,g=Math.round(pb()),h,m=(h=Kx(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0;Ox(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,sinceInit:n,attributionReporting:!0,referer:z.location.href}},function(){},function(p){var q="_is_sw=f"+p.failureType,r,t=(r=Kx(e.origin))==null?void 0:r.getState();t!==void 0&&(q+="s"+
t);d(n?q+("t"+n):q+"te")});};var Rx="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Sx(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Tx(){var a=z.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Ux(){var a,b;return(b=(a=z.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Vx(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Wx(){var a=z;if(!Vx(a))return null;var b=Sx(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Rx).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function cy(a){var b=a.location.href;if(a===a.top)return{url:b,An:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,An:c}};function Uy(a,b){var c=!!Lj();switch(a){case 45:return c&&!H(76)?Kj()+"/g/ccm/collect":"https://www.google.com/ccm/collect";case 46:return c?Kj()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return c&&!H(80)?Kj()+"/travel/flights/click/conversion":"https://www.google.com/travel/flights/click/conversion";case 9:return!H(77)&&c?Kj()+"/pagead/viewthroughconversion":"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c&&!H(82)?(H(88)?Tn():
"").toLowerCase()==="region1"?""+Kj()+"/r1ag/g/c":""+Kj()+"/ag/g/c":Sy();case 16:return c?""+Kj()+(H(15)?"/ga/g/c":"/g/collect"):Ty();case 1:return!H(81)&&c?Kj()+"/activity;":"https://ad.doubleclick.net/activity;";case 2:return c?Kj()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return!H(81)&&c?Kj()+"/activity;register_conversion=1;":"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?H(79)?Kj()+"/d/pagead/form-data":Kj()+"/pagead/form-data":
H(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return!H(81)&&c?Kj()+"/activityi/"+b+";":"https://"+b+".fls.doubleclick.net/activityi;";case 5:case 6:case 7:case 8:case 12:case 13:case 14:case 15:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 0:throw Error("Unsupported endpoint");
default:Yb(a,"Unknown endpoint")}};function Vy(a){a=a===void 0?[]:a;return pj(a).join("~")}function Wy(){if(!H(117))return"";var a,b;return(((a=hm(im()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var dz={};dz.M=ar.M;var ez={Fo:"L",lm:"S",Mo:"Y",so:"B",Co:"E",Eo:"I",Lo:"TC",Do:"HTC"},fz={lm:"S",Bo:"V",wo:"E",Ko:"tag"},gz={},hz=(gz[dz.M.Eh]="6",gz[dz.M.Fh]="5",gz[dz.M.Dh]="7",gz);function iz(){function a(c,d){var e=Xa(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var jz=!1;function zz(a){}
function Az(a){}function Bz(){}
function Cz(a){}function Dz(a){}
function Ez(a){}
function Fz(){}
function Gz(a,b){}
function Hz(a,b,c){}
function Iz(){};var Jz=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Kz(a,b,c,d,e,f,g){var h=Object.assign({},Jz);c&&(h.body=c,h.method="POST");Object.assign(h,e);z.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});Lz(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():H(127)&&(b+="&_z=retryFetch",c?Fl(a,b,c):El(a,b))})};var Mz=function(a){this.O=a;this.C=""},Nz=function(a,b){a.H=b;return a},Oz=function(a,b){a.N=b;return a},Lz=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}Pz(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Qz=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};Pz(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},Pz=function(a,b){b&&(Rz(b.send_pixel,b.options,a.O),Rz(b.create_iframe,b.options,a.H),Rz(b.fetch,b.options,a.N))};function Sz(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Rz(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=Wc(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function AA(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function BA(a,b,c){c=c===void 0?!1:c;CA().addRestriction(0,a,b,c)}function DA(a,b,c){c=c===void 0?!1:c;CA().addRestriction(1,a,b,c)}function EA(){var a=fm();return CA().getRestrictions(1,a)}var FA=function(){this.container={};this.C={}},GA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
FA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=GA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
FA.prototype.getRestrictions=function(a,b){var c=GA(this,b);if(a===0){var d,e;return[].concat(sa((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),sa((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(sa((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),sa((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
FA.prototype.getExternalRestrictions=function(a,b){var c=GA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};FA.prototype.removeExternalRestrictions=function(a){var b=GA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function CA(){return Lo("r",function(){return new FA})};var HA=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),IA={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},JA={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},KA="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function LA(){var a=Rj("gtm.allowlist")||Rj("gtm.whitelist");a&&O(9);xj&&(a=["google","gtagfl","lcl","zone"],H(48)&&a.push("cmpPartners"));HA.test(z.location&&z.location.hostname)&&(xj?O(116):(O(117),MA&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&tb(mb(a),IA),c=Rj("gtm.blocklist")||Rj("gtm.blacklist");c||(c=Rj("tagTypeBlacklist"))&&O(3);c?O(8):c=[];HA.test(z.location&&z.location.hostname)&&(c=mb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));mb(c).indexOf("google")>=0&&O(2);var d=c&&tb(mb(c),JA),e={};return function(f){var g=f&&f[Pe.za];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Hj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(H(48)&&xj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=gb(d,h||
[]);t&&O(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:H(48)&&xj&&h.indexOf("cmpPartners")>=0?!NA():b&&b.indexOf("sandboxedScripts")!==-1?0:gb(d,KA))&&(u=!0);return e[g]=u}}function NA(){var a=Vf(Sf.C,dm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var MA=!1;MA=!0;
function OA(){Vl&&BA(fm(),function(a){var b=Cf(a.entityId),c;if(Ff(b)){var d=b[Pe.za];if(!d)throw Error("Error: No function name given for function call.");var e=tf[d];c=!!e&&!!e.runInSiloedMode}else c=!!AA(b[Pe.za],4);return c})};function PA(a,b,c,d,e){if(!QA()){var f=d.siloed?am(a):a;if(!om(f)){d.loadExperiments=pj();qm(f,d,e);var g=RA(a),h=function(){Rl().container[f]&&(Rl().container[f].state=3);SA()},m={destinationId:f,endpoint:0};if(Lj())Il(m,Kj()+"/"+g,void 0,h);else{var n=ub(a,"GTM-"),p=sk(),q=c?"/gtag/js":"/gtm.js",r=rk(b,q+g);if(!r){var t=rj.Ff+q;p&&ic&&n&&(t=ic.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=iv("https://","http://",t+g)}Il(m,r,void 0,h)}}}}
function SA(){sm()||ib(tm(),function(a,b){TA(a,b.transportUrl,b.context);O(92)})}
function TA(a,b,c,d){if(!QA()){var e=c.siloed?am(a):a;if(!pm(e))if(c.loadExperiments||(c.loadExperiments=pj()),sm())Rl().destination[e]={state:0,transportUrl:b,context:c,parent:im()},Ql({ctid:e,isDestination:!0},d),O(91);else{c.siloed&&rm({ctid:e,isDestination:!0});Rl().destination[e]={state:1,context:c,parent:im()};Ql({ctid:e,isDestination:!0},d);var f={destinationId:e,endpoint:0};if(Lj())Il(f,Kj()+("/gtd"+RA(a,!0)));else{var g="/gtag/destination"+RA(a,!0),h=rk(b,g);h||(h=iv("https://","http://",
rj.Ff+g));Il(f,h)}}}}function RA(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);H(123)&&rj.vb==="dataLayer"||(c+="&l="+rj.vb);if(!ub(a,"GTM-")||b)c=H(129)?c+(Lj()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Zq();sk()&&(c+="&sign="+rj.yh);var d=oj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");H(69)&&Jj()&&(c+="&tag_exp="+Jj());return c}function QA(){if(Xq()){return!0}return!1};var UA=function(){this.H=0;this.C={}};UA.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Lb:c};return d};UA.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var WA=function(a,b){var c=[];ib(VA.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Lb===void 0||b.indexOf(e.Lb)>=0)&&c.push(e.listener)});return c};function XA(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:dm()}};var ZA=function(a,b){this.C=!1;this.O=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;YA(this,a,b)},$A=function(a,b,c,d){if(tj.hasOwnProperty(b)||b==="__zone")return-1;var e={};Wc(d)&&(e=Xc(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},aB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},bB=function(a){if(!a.C){for(var b=a.O,c=0;c<b.length;c++)b[c]();a.C=!0;a.O.length=0}},YA=function(a,b,c){b!==void 0&&a.Se(b);c&&z.setTimeout(function(){bB(a)},
Number(c))};ZA.prototype.Se=function(a){var b=this,c=rb(function(){D(function(){a(dm(),b.eventData)})});this.C?c():this.O.push(c)};var cB=function(a){a.N++;return rb(function(){a.H++;a.R&&a.H>=a.N&&bB(a)})},dB=function(a){a.R=!0;a.H>=a.N&&bB(a)};var eB={};function fB(){return z[gB()]}var hB=function(a){if(Lm()){var b=fB();b(a+"require","linker");b(a+"linker:passthrough",!0)}},iB=function(a){z.GoogleAnalyticsObject||(z.GoogleAnalyticsObject=a||"ga");var b=z.GoogleAnalyticsObject;if(z[b])z.hasOwnProperty(b);else{var c=function(){var d=wa.apply(0,arguments);c.q=c.q||[];c.q.push(d)};c.l=Number(ob());z[b]=c}return z[b]};
function gB(){return z.GoogleAnalyticsObject||"ga"}function jB(){var a=dm();}
function kB(a,b){return function(){var c=fB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var qB=["es","1"],rB={},sB={};function tB(a,b){if(Ck){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";rB[a]=[["e",c],["eid",a]];Rp(a)}}function uB(a){var b=a.eventId,c=a.gd;if(!rB[b])return[];var d=[];sB[b]||d.push(qB);d.push.apply(d,sa(rB[b]));c&&(sB[b]=!0);return d};var vB={},wB={},xB={};function yB(a,b,c,d){Ck&&H(119)&&((d===void 0?0:d)?(xB[b]=xB[b]||0,++xB[b]):c!==void 0?(wB[a]=wB[a]||{},wB[a][b]=Math.round(c)):(vB[a]=vB[a]||{},vB[a][b]=(vB[a][b]||0)+1))}function zB(a){var b=a.eventId,c=a.gd,d=vB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete vB[b];return e.length?[["md",e.join(".")]]:[]}
function AB(a){var b=a.eventId,c=a.gd,d=wB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete wB[b];return e.length?[["mtd",e.join(".")]]:[]}function BB(){for(var a=[],b=l(Object.keys(xB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+xB[d])}return a.length?[["mec",a.join(".")]]:[]};var CB={},DB={};function EB(a,b,c){if(Ck&&b){var d=wk(b);CB[a]=CB[a]||[];CB[a].push(c+d);var e=(Ff(b)?"1":"2")+d;DB[a]=DB[a]||[];DB[a].push(e);Rp(a)}}function FB(a){var b=a.eventId,c=a.gd,d=[],e=CB[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=DB[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete CB[b],delete DB[b]);return d};function GB(a,b,c,d){var e=rf[a],f=HB(a,b,c,d);if(!f)return null;var g=Gf(e[Pe.Zj],c,[]);if(g&&g.length){var h=g[0];f=GB(h.index,{onSuccess:f,onFailure:h.pk===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function HB(a,b,c,d){function e(){function w(){zn(3);var K=pb()-F;EB(c.id,f,"7");aB(c.yc,C,"exception",K);H(108)&&Hz(c,f,dz.M.Dh);E||(E=!0,h())}if(f[Pe.fm])h();else{var x=Ef(f,c,[]),y=x[Pe.Vk];if(y!=null)for(var B=0;B<y.length;B++)if(!Ao(y[B])){h();return}var C=$A(c.yc,String(f[Pe.za]),Number(f[Pe.og]),x[Pe.METADATA]),E=!1;x.vtp_gtmOnSuccess=function(){if(!E){E=!0;var K=pb()-F;EB(c.id,rf[a],"5");aB(c.yc,C,"success",K);H(108)&&Hz(c,f,dz.M.Fh);g()}};x.vtp_gtmOnFailure=function(){if(!E){E=!0;var K=pb()-
F;EB(c.id,rf[a],"6");aB(c.yc,C,"failure",K);H(108)&&Hz(c,f,dz.M.Eh);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);EB(c.id,f,"1");H(108)&&Gz(c,f);var F=pb();try{If(x,{event:c,index:a,type:1})}catch(K){w(K)}H(108)&&Hz(c,f,dz.M.bk)}}var f=rf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Gf(f[Pe.dk],c,[]);if(n&&n.length){var p=n[0],q=GB(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.pk===
2?m:q}if(f[Pe.Sj]||f[Pe.hm]){var r=f[Pe.Sj]?sf:c.io,t=g,u=h;if(!r[a]){var v=IB(a,r,rb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function IB(a,b,c){var d=[],e=[];b[a]=JB(d,e,c);return{onSuccess:function(){b[a]=KB;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=LB;for(var f=0;f<e.length;f++)e[f]()}}}function JB(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function KB(a){a()}function LB(a,b){b()};var OB=function(a,b){for(var c=[],d=0;d<rf.length;d++)if(a[d]){var e=rf[d];var f=cB(b.yc);try{var g=GB(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Pe.za];if(!h)throw Error("Error: No function name given for function call.");var m=tf[h];c.push({Mk:d,priorityOverride:(m?m.priorityOverride||0:0)||AA(e[Pe.za],1)||0,execute:g})}else MB(d,b),f()}catch(p){f()}}c.sort(NB);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function PB(a,b){if(!VA)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=WA(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=cB(b);try{d[e](a,f)}catch(g){f()}}return!0}function NB(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Mk,h=b.Mk;f=g>h?1:g<h?-1:0}return f}
function MB(a,b){if(Ck){var c=function(d){var e=b.isBlocked(rf[d])?"3":"4",f=Gf(rf[d][Pe.Zj],b,[]);f&&f.length&&c(f[0].index);EB(b.id,rf[d],e);var g=Gf(rf[d][Pe.dk],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var QB=!1,VA;function RB(){VA||(VA=new UA);return VA}
function SB(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(H(108)){}if(d==="gtm.js"){if(QB)return!1;QB=!0}var e=!1,f=EA(),g=Xc(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}tB(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:TB(g,e),io:[],logMacroError:function(){O(6);zn(0)},cachedModelValues:UB(),yc:new ZA(function(){if(H(108)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};H(119)&&Ck&&(n.reportMacroDiscrepancy=yB);H(108)&&Dz(n.id);var p=Nf(n);H(108)&&Ez(n.id);e&&(p=VB(p));H(108)&&Cz(b);var q=OB(p,n),r=PB(a,n.yc);dB(n.yc);d!=="gtm.js"&&d!=="gtm.sync"||jB();return WB(p,q)||r}function UB(){var a={};a.event=Wj("event",1);a.ecommerce=Wj("ecommerce",1);a.gtm=Wj("gtm");a.eventModel=Wj("eventModel");return a}
function TB(a,b){var c=LA();return function(d){if(c(d))return!0;var e=d&&d[Pe.za];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=fm();f=CA().getRestrictions(0,g);var h=a;b&&(h=Xc(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Hj[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function VB(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(rf[c][Pe.za]);if(sj[d]||rf[c][Pe.im]!==void 0||AA(d,2))b[c]=!0}return b}function WB(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&rf[c]&&!tj[String(rf[c][Pe.za])])return!0;return!1};function XB(){RB().addListener("gtm.init",function(a,b){oj.R=!0;kn();b()})};var YB=!1,ZB=0,$B=[];function aC(a){if(!YB){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){YB=!0;for(var e=0;e<$B.length;e++)D($B[e])}$B.push=function(){for(var f=wa.apply(0,arguments),g=0;g<f.length;g++)D(f[g]);return 0}}}function bC(){if(!YB&&ZB<140){ZB++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");aC()}catch(c){z.setTimeout(bC,50)}}}
function cC(){YB=!1;ZB=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")aC();else{wc(A,"DOMContentLoaded",aC);wc(A,"readystatechange",aC);if(A.createEventObject&&A.documentElement.doScroll){var a=!0;try{a=!z.frameElement}catch(b){}a&&bC()}wc(z,"load",aC)}}function dC(a){YB?a():$B.push(a)};var eC=0;var fC={},gC={};function hC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={si:void 0,Xh:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.si=Uo(g,b),e.si){var h=Wl?Wl:cm();eb(h,function(r){return function(t){return r.si.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=fC[g]||[];e.Xh={};m.forEach(function(r){return function(t){r.Xh[t]=!0}}(e));for(var n=Zl(),p=0;p<n.length;p++)if(e.Xh[n[p]]){c=c.concat(bm());break}var q=gC[g]||[];q.length&&(c=c.concat(q))}}return{En:c,In:d}}
function iC(a){ib(fC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function jC(a){ib(gC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var kC=!1,lC=!1;function mC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=Xc(b,null),b[N.m.oe]&&(d.eventCallback=b[N.m.oe]),b[N.m.Uf]&&(d.eventTimeout=b[N.m.Uf]));return d}function nC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Po()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function oC(a,b){var c=a&&a[N.m.Oc];c===void 0&&(c=Rj(N.m.Oc,2),c===void 0&&(c="default"));if(bb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?bb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=hC(d,b.isGtmEvent),f=e.En,g=e.In;if(g.length)for(var h=pC(a),m=0;m<g.length;m++){var n=Uo(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=ub(p,"siloed_"))){var r=n.destinationId,t=Rl().destination[r];q=!!t&&t.state===0}q||TA(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}return Vo(f,
b.isGtmEvent)}}var qC=void 0,rC=void 0;function sC(a,b,c){var d=Xc(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=Xc(b,null);Xc(c,e);Lv(Hv(Zl()[0],e),a.eventId,d)}function pC(a){for(var b=l([N.m.Pc,N.m.Xb]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Zp.C[d];if(e)return e}}
var tC={config:function(a,b){var c=nC(a,b);if(!(a.length<2)&&bb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!Wc(a[2])||a.length>3)return;d=a[2]}var e=Uo(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Ul.Le){var m=hm(im());if(um(m)){var n=m.parent,p=n.isDestination;h={Mn:hm(n),Dn:p};break a}}h=void 0}var q=h;q&&(f=q.Mn,g=q.Dn);tB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?bm().indexOf(r)===-1:Zl().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.sc]){var u=pC(d);if(t)TA(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;qC?sC(b,v,qC):rC||(rC=Xc(v,null))}else PA(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;rC?(sC(b,rC,x),w=!1):(!x[N.m.Rc]&&vj&&qC||(qC=Xc(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Dk&&(eC===1&&(cn.mcc=!1),eC=2);if(vj&&!t&&!d[N.m.Rc]){var y=lC;lC=!0;if(y)return}kC||O(43);if(!b.noTargetGroup)if(t){jC(e.id);
var B=e.id,C=d[N.m.Xf]||"default";C=String(C).split(",");for(var E=0;E<C.length;E++){var F=gC[C[E]]||[];gC[C[E]]=F;F.indexOf(B)<0&&F.push(B)}}else{iC(e.id);var K=e.id,L=d[N.m.Xf]||"default";L=L.toString().split(",");for(var T=0;T<L.length;T++){var J=fC[L[T]]||[];fC[L[T]]=J;J.indexOf(K)<0&&J.push(K)}}delete d[N.m.Xf];var Z=b.eventMetadata||{};Z.hasOwnProperty("is_external_event")||(Z.is_external_event=!b.fromContainerExecution);b.eventMetadata=Z;delete d[N.m.oe];for(var X=t?[e.id]:bm(),da=0;da<X.length;da++){var U=
d,Q=X[da],ja=Xc(b,null),ia=Uo(Q,ja.isGtmEvent);ia&&Zp.push("config",[U],ia,ja)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=nC(a,b),d=a[1],e;if(H(137)){var f={},g=jv(a[2]),h;for(h in g)if(g.hasOwnProperty(h)){var m=g[h];f[h]=h===N.m.Cf?Array.isArray(m)?NaN:Number(m):h===N.m.Mb?(Array.isArray(m)?m:[m]).map(kv):lv(m)}e=f}else e=a[2];var n=e;b.fromContainerExecution||(n[N.m.U]&&O(139),n[N.m.Ca]&&O(140));d==="default"?wo(n):d==="update"?yo(n,c):d==="declare"&&b.fromContainerExecution&&vo(n)}},
event:function(a,b){var c=a[1];if(!(a.length<2)&&bb(c)){var d=void 0;if(a.length>2){if(!Wc(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=mC(c,d),f=nC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=oC(d,b);if(m){tB(g,c);var n=m.map(function(E){return E.id}),p=m.map(function(E){return E.destinationId}),q=n;if(!Vl&&H(107)){q=n.slice();for(var r=l(Wl?Wl:cm()),t=r.next();!t.done;t=r.next()){var u=
t.value;!ub(u,"siloed_")&&p.indexOf(u)<0&&q.push(u)}}for(var v=l(q),w=v.next();!w.done;w=v.next()){var x=w.value,y=Xc(b,null),B=Xc(d,null);delete B[N.m.oe];var C=y.eventMetadata||{};C.hasOwnProperty("is_external_event")||(C.is_external_event=!y.fromContainerExecution);C.send_to_targets=n.slice();C.send_to_destinations=p.slice();y.eventMetadata=C;$p(c,B,x,y);Dk&&C.source_canonical_id===void 0&&eC===0&&(fn("mcc","1"),eC=1)}e.eventModel=e.eventModel||{};n.length>0?e.eventModel[N.m.Oc]=n.join(","):delete e.eventModel[N.m.Oc];
kC||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata.syn_or_mod&&(b.noGtmEvent=!0);e.eventModel[N.m.rc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&bb(a[1])&&bb(a[2])&&ab(a[3])){var c=Uo(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){kC||O(43);var f=pC();if(eb(bm(),function(h){return c.destinationId===h})){nC(a,b);var g={};Xc((g[N.m.Ub]=d,g[N.m.oc]=e,g),null);aq(d,function(h){D(function(){e(h)})},c.id,b)}else TA(c.destinationId,f,{source:4,
fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){kC=!0;var c=nC(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&bb(a[1])&&ab(a[2])){if(Tf(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](dm(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&Wc(a[1])?c=Xc(a[1],null):a.length===
3&&bb(a[1])&&(c={},Wc(a[2])||Array.isArray(a[2])?c[a[1]]=Xc(a[2],null):c[a[1]]=a[2]);if(c){var d=nC(a,b),e=d.eventId,f=d.priorityId;Xc(c,null);var g=Xc(c,null);Zp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},uC={policy:!0};var wC=function(a){if(vC(a))return a;this.value=a};wC.prototype.getUntrustedMessageValue=function(){return this.value};var vC=function(a){return!a||Uc(a)!=="object"||Wc(a)?!1:"getUntrustedMessageValue"in a};wC.prototype.getUntrustedMessageValue=wC.prototype.getUntrustedMessageValue;var xC=!1,yC=[];function zC(){if(!xC){xC=!0;for(var a=0;a<yC.length;a++)D(yC[a])}}function AC(a){xC?D(a):yC.push(a)};var BC=0,CC={},DC=[],EC=[],FC=!1,GC=!1;function HC(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function IC(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return JC(a)}function KC(a,b){if(!cb(b)||b<0)b=0;var c=Ko[rj.vb],d=0,e=!1,f=void 0;f=z.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(z.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function LC(a,b){var c=a._clear||b.overwriteModelFields;ib(a,function(e,f){e!=="_clear"&&(c&&Uj(e),Uj(e,f))});Ej||(Ej=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Po(),a["gtm.uniqueEventId"]=d,Uj("gtm.uniqueEventId",d));return SB(a)}function MC(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(jb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function NC(){var a;if(EC.length)a=EC.shift();else if(DC.length)a=DC.shift();else return;var b;var c=a;if(FC||!MC(c.message))b=c;else{FC=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Po(),f=Po(),c.message["gtm.uniqueEventId"]=Po());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};DC.unshift(n,c);b=h}return b}
function OC(){for(var a=!1,b;!GC&&(b=NC());){GC=!0;delete Oj.eventModel;Qj();var c=b,d=c.message,e=c.messageContext;if(d==null)GC=!1;else{e.fromContainerExecution&&Vj();try{if(ab(d))try{d.call(Sj)}catch(u){}else if(Array.isArray(d)){if(bb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Rj(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(jb(d))a:{if(d.length&&bb(d[0])){var p=tC[d[0]];if(p&&(!e.fromContainerExecution||!uC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=LC(n,e)||a)}}finally{e.fromContainerExecution&&Qj(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=CC[String(q)]||[],t=0;t<r.length;t++)EC.push(PC(r[t]));r.length&&EC.sort(HC);delete CC[String(q)];q>BC&&(BC=q)}GC=!1}}}return!a}
function QC(){if(H(108)){var a=!oj.N;}var c=OC();if(H(108)){}try{var e=dm(),f=z[rj.vb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Ov(a){if(BC<a.notBeforeEventId){var b=String(a.notBeforeEventId);CC[b]=CC[b]||[];CC[b].push(a)}else EC.push(PC(a)),EC.sort(HC),D(function(){GC||OC()})}function PC(a){return{message:a.message,messageContext:a.messageContext}}
function RC(){function a(f){var g={};if(vC(f)){var h=f;f=vC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=jc(rj.vb,[]),c=Oo();c.pruned===!0&&O(83);CC=Mv().get();Nv();dC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});AC(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Ko.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new wC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});DC.push.apply(DC,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return OC()&&p};var e=b.slice(0).map(function(f){return a(f)});DC.push.apply(DC,e);if(!oj.N){if(H(108)){}D(QC)}}var JC=function(a){return z[rj.vb].push(a)};function SC(a){JC(a)};function TC(){var a,b=lk(z.location.href);(a=b.hostname+b.pathname)&&fn("dl",encodeURIComponent(a));var c;var d=Wf.ctid;if(d){var e=Ul.Le?1:0,f,g=hm(im());f=g&&g.context;c=d+";"+Wf.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&fn("tdp",h);var m=kl(!0);m!==void 0&&fn("frm",String(m))};function UC(){H(55)&&Dk&&z.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){var b=Dl(a.effectiveDirective);if(b){var c;var d=Bl(b,a.blockedURI);c=d?zl[b][d]:void 0;var e;if(e=c)a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(q){}e=void 0}if(e){for(var h=l(c),m=h.next();!m.done;m=h.next()){var n=m.value;if(!n.Fk){n.Fk=!0;var p=String(n.endpoint);ln.hasOwnProperty(p)||(ln[p]=
!0,fn("csp",Object.keys(ln).join("~")))}}Cl(b,a.blockedURI)}}}})};function VC(){var a;var b=gm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&fn("pcid",e)};var WC=/^(https?:)?\/\//;
function XC(){var a;var b=hm(im());if(b){for(;b.parent;){var c=hm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Lc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=l(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(WC,"")===g.replace(WC,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
fn("rtg",String(d.canonicalContainerId)),fn("slo",String(t)),fn("hlo",d.htmlLoadOrder||"-1"),fn("lst",String(d.loadScriptType||"0")))}else O(144)};
function rD(){};var sD=function(){};sD.prototype.toString=function(){return"undefined"};var tD=new sD;function AD(a,b){function c(g){var h=lk(g),m=fk(h,"protocol"),n=fk(h,"host",!0),p=fk(h,"port"),q=fk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function BD(a){return CD(a)?1:0}
function CD(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=Xc(a,{});Xc({arg1:c[d],any_of:void 0},e);if(BD(e))return!0}return!1}switch(a["function"]){case "_cn":return Eg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<zg.length;g++){var h=zg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ag(b,c);case "_eq":return Fg(b,c);case "_ge":return Gg(b,c);case "_gt":return Ig(b,c);case "_lc":return Bg(b,c);case "_le":return Hg(b,
c);case "_lt":return Jg(b,c);case "_re":return Dg(b,c,a.ignore_case);case "_sw":return Kg(b,c);case "_um":return AD(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var DD=function(a,b,c,d){mq.call(this);this.mg=b;this.Me=c;this.xc=d;this.ab=new Map;this.ng=0;this.ia=new Map;this.Da=new Map;this.R=void 0;this.H=a};qa(DD,mq);DD.prototype.N=function(){delete this.C;this.ab.clear();this.ia.clear();this.Da.clear();this.R&&(Hk(this.H,"message",this.R),delete this.R);delete this.H;delete this.xc;mq.prototype.N.call(this)};
var ED=function(a){if(a.C)return a.C;a.Me&&a.Me(a.H)?a.C=a.H:a.C=jl(a.H,a.mg);var b;return(b=a.C)!=null?b:null},GD=function(a,b,c){if(ED(a))if(a.C===a.H){var d=a.ab.get(b);d&&d(a.C,c)}else{var e=a.ia.get(b);if(e&&e.ii){FD(a);var f=++a.ng;a.Da.set(f,{Eg:e.Eg,Jm:e.sk(c),persistent:b==="addEventListener"});a.C.postMessage(e.ii(c,f),"*")}}},FD=function(a){a.R||(a.R=function(b){try{var c;c=a.xc?a.xc(b):void 0;if(c){var d=c.Qn,e=a.Da.get(d);if(e){e.persistent||a.Da.delete(d);var f;(f=e.Eg)==null||f.call(e,
e.Jm,c.On)}}}catch(g){}},Gk(a.H,"message",a.R))};var HD=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},ID=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},JD={sk:function(a){return a.listener},ii:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eg:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},KD={sk:function(a){return a.listener},ii:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eg:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function LD(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{On:b,Qn:b.__gppReturn.callId}}
var MD=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;mq.call(this);this.caller=new DD(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},LD);this.caller.ab.set("addEventListener",HD);this.caller.ia.set("addEventListener",JD);this.caller.ab.set("removeEventListener",ID);this.caller.ia.set("removeEventListener",KD);this.timeoutMs=c!=null?c:500};qa(MD,mq);MD.prototype.N=function(){this.caller.dispose();mq.prototype.N.call(this)};
MD.prototype.addEventListener=function(a){var b=this,c=Fk(function(){a(ND,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);GD(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(OD,!0);return}a(PD,!0)}}})};
MD.prototype.removeEventListener=function(a){GD(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var PD={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},ND={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},OD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function QD(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){ou.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");ou.C=d}}function RD(){try{if(H(105)){var a=new MD(z,{timeoutMs:-1});ED(a.caller)&&a.addEventListener(QD)}}catch(b){}};function SD(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function TD(){var a=[["cv",H(140)?SD():"1"],["rv",rj.xh],["tc",rf.filter(function(b){return b}).length]];rj.wh&&a.push(["x",rj.wh]);Jj()&&a.push(["tag_exp",Jj()]);return a};var UD={},VD={};function WD(a){var b=a.eventId,c=a.gd,d=[],e=UD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=VD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete UD[b],delete VD[b]);return d};function XD(){return!1}function YD(){var a={};return function(b,c,d){}};function ZD(){var a=$D;return function(b,c,d){var e=d&&d.event;aE(c);var f=ph(b)?void 0:1,g=new Na;ib(c,function(r,t){var u=md(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.C.C.H=Lf();var h={ik:$f(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Se:e!==void 0?function(r){e.yc.Se(r)}:void 0,sb:function(){return b},log:function(){},Rm:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Yn:!!AA(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(XD()){var m=YD(),n,p;h.fb={Ci:[],Te:{},Hb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Dg:Hh()};h.log=function(r){var t=wa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Je(a,h,[b,g]);a.C.C.H=void 0;q instanceof ya&&(q.type==="return"?q=q.data:q=void 0);return ld(q,void 0,f)}}function aE(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ab(b)&&(a.gtmOnSuccess=function(){D(b)});ab(c)&&(a.gtmOnFailure=function(){D(c)})};function bE(a){}bE.K="internal.addAdsClickIds";function cE(a,b){var c=this;}cE.publicName="addConsentListener";var dE=!1;function eE(a){for(var b=0;b<a.length;++b)if(dE)try{a[b]()}catch(c){O(77)}else a[b]()}function fE(a,b,c){var d=this,e;return e}fE.K="internal.addDataLayerEventListener";function gE(a,b,c){}gE.publicName="addDocumentEventListener";function hE(a,b,c,d){}hE.publicName="addElementEventListener";function iE(a){return a.J.C};function jE(a){}jE.publicName="addEventCallback";
function zE(a){}zE.K="internal.addFormAbandonmentListener";function AE(a,b,c,d){}
AE.K="internal.addFormData";var BE={},CE=[],DE={},EE=0,FE=0;
function ME(a,b){}ME.K="internal.addFormInteractionListener";
function TE(a,b){}TE.K="internal.addFormSubmitListener";
function YE(a){}YE.K="internal.addGaSendListener";function ZE(a){if(!a)return{};var b=a.Rm;return XA(b.type,b.index,b.name)}function $E(a){return a?{originatingEntity:ZE(a)}:{}};function hF(a){var b=Ko.zones;return b?b.getIsAllowedFn(Zl(),a):function(){return!0}}function iF(){var a=Ko.zones;a&&a.unregisterChild(Zl())}
function jF(){DA(fm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Ko.zones;return c?c.isActive(Zl(),b):!0});BA(fm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return hF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var kF=function(a,b){this.tagId=a;this.Ve=b};
function lF(a,b){var c=this,d=void 0;
return d}lF.K="internal.loadGoogleTag";function mF(a){return new dd("",function(b){var c=this.evaluate(b);if(c instanceof dd)return new dd("",function(){var d=wa.apply(0,arguments),e=this,f=Xc(iE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Fa(this.J);h.C=f;return c.kb.apply(c,[h].concat(sa(g)))})})};function nF(a,b,c){var d=this;}nF.K="internal.addGoogleTagRestriction";var oF={},pF=[];
function wF(a,b){}
wF.K="internal.addHistoryChangeListener";function xF(a,b,c){}xF.publicName="addWindowEventListener";function yF(a,b){return!0}yF.publicName="aliasInWindow";function zF(a,b,c){}zF.K="internal.appendRemoteConfigParameter";function AF(a){var b;return b}
AF.publicName="callInWindow";function BF(a){}BF.publicName="callLater";function CF(a){}CF.K="callOnDomReady";function DF(a){}DF.K="callOnWindowLoad";function EF(a,b){var c;return c}EF.K="internal.computeGtmParameter";function FF(a,b){var c=this;}FF.K="internal.consentScheduleFirstTry";function GF(a,b){var c=this;}GF.K="internal.consentScheduleRetry";function HF(a){var b;return b}HF.K="internal.copyFromCrossContainerData";function IF(a,b){var c;var d=md(c,this.J,ph(iE(this).sb())?2:1);d===void 0&&c!==void 0&&O(45);return d}IF.publicName="copyFromDataLayer";
function JF(a){var b=void 0;return b}JF.K="internal.copyFromDataLayerCache";function KF(a){var b;return b}KF.publicName="copyFromWindow";function LF(a){var b=void 0;return md(b,this.J,1)}LF.K="internal.copyKeyFromWindow";var MF=function(a){this.C=a},NF=function(a,b,c,d){var e;return(e=a.C[b])!=null&&e[c]?a.C[b][c].some(function(f){return f(d)}):!1},OF=function(a){return a===ym.V.wa&&Rm[a]===xm.Aa.Cd&&!Ao(N.m.T)};var PF=function(){return"0"},QF=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];H(102)&&b.push("gbraid");return mk(a,b,"0")};var RF={},SF={},TF={},UF={},VF={},WF={},XF={},YF={},ZF={},$F={},aG={},bG={},cG={},dG={},eG={},fG={},gG={},hG={},iG={},jG={},kG={},lG={},mG={},nG={},oG={},pG={},qG=(pG[N.m.Ha]=(RF[2]=[OF],RF),pG[N.m.ze]=(SF[2]=[OF],SF),pG[N.m.pe]=(TF[2]=[OF],TF),pG[N.m.kh]=(UF[2]=[OF],UF),pG[N.m.lh]=(VF[2]=[OF],VF),pG[N.m.mh]=(WF[2]=[OF],WF),pG[N.m.nh]=(XF[2]=[OF],XF),pG[N.m.oh]=(YF[2]=[OF],YF),pG[N.m.Cb]=(ZF[2]=[OF],ZF),pG[N.m.Be]=($F[2]=[OF],$F),pG[N.m.Ce]=(aG[2]=[OF],aG),pG[N.m.De]=(bG[2]=[OF],bG),pG[N.m.Ee]=(cG[2]=
[OF],cG),pG[N.m.Fe]=(dG[2]=[OF],dG),pG[N.m.Ge]=(eG[2]=[OF],eG),pG[N.m.He]=(fG[2]=[OF],fG),pG[N.m.Ie]=(gG[2]=[OF],gG),pG[N.m.Wa]=(hG[1]=[OF],hG),pG[N.m.Ec]=(iG[1]=[OF],iG),pG[N.m.Ic]=(jG[1]=[OF],jG),pG[N.m.rd]=(kG[1]=[OF],kG),pG[N.m.Yd]=(lG[1]=[function(a){return H(102)&&OF(a)}],lG),pG[N.m.Jc]=(mG[1]=[OF],mG),pG[N.m.ra]=(nG[1]=[OF],nG),pG[N.m.Ka]=(oG[1]=[OF],oG),pG),rG={},sG=(rG[N.m.Wa]=PF,rG[N.m.Ec]=PF,rG[N.m.Ic]=PF,rG[N.m.rd]=PF,rG[N.m.Yd]=PF,rG[N.m.Jc]=function(a){if(!Wc(a))return{};var b=Xc(a,
null);delete b.match_id;return b},rG[N.m.ra]=QF,rG[N.m.Ka]=QF,rG),tG={},uG={},vG=(uG.user_data=(tG[2]=[OF],tG),uG),wG={};var xG=function(a,b){this.conditions=a;this.C=b},yG=function(a,b,c,d){return NF(a.conditions,b,2,d)?{status:2}:NF(a.conditions,b,1,d)?a.C[b]===void 0?{status:2}:{status:1,value:a.C[b](c,d)}:{status:0,value:c}},zG=new xG(new MF(qG),sG),AG=new xG(new MF(vG),wG);function BG(a,b,c){return yG(zG,a,b,c)}function CG(a,b,c){return yG(AG,a,b,c)}var DG=function(a,b,c,d){this.C=a;this.N=b;this.O=c;this.R=d};
DG.prototype.getValue=function(a){a=a===void 0?ym.V.qb:a;if(!this.N.some(function(b){return b(a)}))return this.O.some(function(b){return b(a)})?this.R(this.C):this.C};DG.prototype.H=function(){return Uc(this.C)==="array"||Wc(this.C)?Xc(this.C,null):this.C};var EG=function(){},FG=function(a,b){this.conditions=a;this.C=b},GG=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new DG(c,e,g,a.C[b]||EG)},HG,IG;function JG(a,b,c,d,e){if(b===void 0)c[a]=b;else{var f=d(a,b,e);f.status===2?delete c[a]:c[a]=f.value}}
var KG=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;if(H(57)){this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}}else this.metadata=Xc(c.eventMetadata||{},{})},Eu=function(a,b){if(H(57)){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,"transmission_type"))}return a.C[b]},V=function(a,b,c){if(H(57)){var d=a.C,e;c===void 0?e=void 0:(HG!=null||(HG=new FG(qG,
sG)),e=GG(HG,b,c));d[b]=e}else JG(b,c,a.C,BG,R(a,"transmission_type"))},LG=function(a,b){b=b===void 0?{}:b;if(H(57)){for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b}return Xc(a.C,b)};KG.prototype.copyToHitData=function(a,b,c){var d=P(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&bb(d)&&H(90))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var R=function(a,b){if(H(57)){var c=a.metadata[b];if(b==="transmission_type"){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,"transmission_type"))}return a.metadata[b]},S=function(a,b,c){if(H(57)){var d=a.metadata,e;c===void 0?e=void 0:(IG!=null||(IG=new FG(vG,wG)),e=GG(IG,b,c));d[b]=e}else if(JG(b,c,a.metadata,CG,R(a,"transmission_type")),b==="transmission_type"){for(var f=l(Object.keys(a.metadata)),g=f.next();!g.done;g=
f.next()){var h=g.value;h!=="transmission_type"&&S(a,h,R(a,h))}for(var m=l(Object.keys(a.C)),n=m.next();!n.done;n=m.next()){var p=n.value;V(a,p,Eu(a,p))}}},MG=function(a,b){b=b===void 0?{}:b;if(H(57)){for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b}return Xc(a.metadata,b)},Yu=function(a,b,c){var d=a.target.destinationId;Vl||(d=jm(d));var e=Sv(d);return e&&e[b]!==
void 0?e[b]:c};function NG(a,b){var c;return c}NG.K="internal.copyPreHit";function OG(a,b){var c=null;return md(c,this.J,2)}OG.publicName="createArgumentsQueue";function PG(a){return md(function(c){var d=fB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
fB(),n=m&&m.getByName&&m.getByName(f);return(new z.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}PG.K="internal.createGaCommandQueue";function QG(a){return md(function(){if(!ab(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
ph(iE(this).sb())?2:1)}QG.publicName="createQueue";function RG(a,b){var c=null;return c}RG.K="internal.createRegex";function SG(){var a={};return a};function TG(a){}TG.K="internal.declareConsentState";function UG(a){var b="";return b}UG.K="internal.decodeUrlHtmlEntities";function VG(a,b,c){var d;return d}VG.K="internal.decorateUrlWithGaCookies";function WG(){}WG.K="internal.deferCustomEvents";function XG(a){var b;return b}XG.K="internal.detectUserProvidedData";
function bH(a,b){return f}bH.K="internal.enableAutoEventOnClick";
function jH(a,b){return p}jH.K="internal.enableAutoEventOnElementVisibility";function kH(){}kH.K="internal.enableAutoEventOnError";var lH={},mH=[],nH={},oH=0,pH=0;
function vH(a,b){var c=this;return d}vH.K="internal.enableAutoEventOnFormInteraction";
function AH(a,b){var c=this;return f}AH.K="internal.enableAutoEventOnFormSubmit";
function FH(){var a=this;}FH.K="internal.enableAutoEventOnGaSend";var GH={},HH=[];
function OH(a,b){var c=this;return f}OH.K="internal.enableAutoEventOnHistoryChange";var PH=["http://","https://","javascript:","file://"];
function TH(a,b){var c=this;return h}TH.K="internal.enableAutoEventOnLinkClick";var UH,VH;
function fI(a,b){var c=this;return d}fI.K="internal.enableAutoEventOnScroll";function gI(a){return function(){if(a.limit&&a.li>=a.limit)a.Ag&&z.clearInterval(a.Ag);else{a.li++;var b=pb();JC({event:a.eventName,"gtm.timerId":a.Ag,"gtm.timerEventNumber":a.li,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Lk,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Lk,"gtm.triggers":a.no})}}}
function hI(a,b){
return f}hI.K="internal.enableAutoEventOnTimer";var $b=ua(["data-gtm-yt-inspected-"]),jI=["www.youtube.com","www.youtube-nocookie.com"],kI,lI=!1;
function vI(a,b){var c=this;return e}vI.K="internal.enableAutoEventOnYouTubeActivity";lI=!1;function wI(a,b){if(!ah(a)||!Vg(b))throw I(this.getName(),["string","Object|undefined"],arguments);var c=b?ld(b):{},d=a,e=!1;return e}wI.K="internal.evaluateBooleanExpression";var xI;function yI(a){var b=!1;return b}yI.K="internal.evaluateMatchingRules";function hJ(){return Gq(7)&&Gq(9)&&Gq(10)};
var lJ=function(a,b){if(!b.isGtmEvent){var c=P(b,N.m.Ub),d=P(b,N.m.oc),e=P(b,c);if(e===void 0){var f=void 0;iJ.hasOwnProperty(c)?f=iJ[c]:jJ.hasOwnProperty(c)&&(f=jJ[c]);f===1&&(f=kJ(c));bb(f)?fB()(function(){var g,h,m,n=(m=(g=fB())==null?void 0:(h=g.getByName)==null?void 0:h.call(g,a))==null?void 0:m.get(f);d(n)}):d(void 0)}else d(e)}},mJ=function(a,b){var c=a[N.m.uc],d=b+".",e=a[N.m.da]||"",f=c===void 0?!!a.use_anchor:c==="fragment",g=!!a[N.m.Vb];e=String(e).replace(/\s+/g,"").split(",");var h=fB();
h(d+"require","linker");h(d+"linker:autoLink",e,f,g)},qJ=function(a,b,c){if(!c.isGtmEvent||!nJ[a]){var d=!Ao(N.m.Z),e=function(f){var g="gtm"+String(Po()),h,m=fB(),n=oJ(b,"",c),p,q=n.createOnlyFields._useUp;if(c.isGtmEvent||pJ(b,n.createOnlyFields)){c.isGtmEvent&&(h=n.createOnlyFields,n.gtmTrackerName&&(h.name=g));m(function(){var t,u=m==null?void 0:(t=m.getByName)==null?void 0:t.call(m,b);u&&(p=u.get("clientId"));if(!c.isGtmEvent){var v;m==null||(v=m.remove)==null||v.call(m,b)}});m("create",a,c.isGtmEvent?
h:n.createOnlyFields);d&&Ao(N.m.Z)&&(d=!1,m(function(){var t,u,v=(t=fB())==null?void 0:(u=t.getByName)==null?void 0:u.call(t,c.isGtmEvent?g:b);!v||v.get("clientId")==p&&q||(c.isGtmEvent?(n.fieldsToSet["&gcu"]="1",n.fieldsToSet["&sst.gcut"]=bi[f]):(n.fieldsToSend["&gcu"]="1",n.fieldsToSend["&sst.gcut"]=bi[f]),v.set(n.fieldsToSet),
c.isGtmEvent?v.send("pageview"):v.send("pageview",n.fieldsToSend))}));c.isGtmEvent&&m(function(){var t;m==null||(t=m.remove)==null||t.call(m,g)})}};Co(function(){return void e(N.m.Z)},N.m.Z);Co(function(){return void e(N.m.T)},N.m.T);Co(function(){return void e(N.m.U)},N.m.U);c.isGtmEvent&&(nJ[a]=!0)}},rJ=function(a,b){sk()&&b&&(a[N.m.Tb]=b)},AJ=function(a,b,c){function d(){var X=wa.apply(0,arguments);X[0]=w?w+"."+X[0]:""+X[0];u.apply(window,X)}function e(X){function da(Ia,Ka){for(var Ca=0;Ka&&Ca<
Ka.length;Ca++)d(Ia,Ka[Ca])}var U=c.isGtmEvent,Q=U?sJ(x):tJ(b,c);if(Q){var ja={};rJ(ja,X);d("require","ec","ec.js",ja);U&&Q.Nh&&d("set","&cu",Q.Nh);var ia=Q.action;if(U||ia==="impressions")if(da("ec:addImpression",Q.rk),!U)return;if(ia==="promo_click"||ia==="promo_view"||U&&Q.wf){var la=Q.wf;da("ec:addPromo",la);if(la&&la.length>0&&ia==="promo_click"){U?d("ec:setAction",ia,Q.Eb):d("ec:setAction",ia);return}if(!U)return}ia!=="promo_view"&&ia!=="impressions"&&(da("ec:addProduct",Q.ed),d("ec:setAction",
ia,Q.Eb))}}function f(X){if(X){var da={};if(Wc(X))for(var U in uJ)uJ.hasOwnProperty(U)&&vJ(uJ[U],U,X[U],da);rJ(da,C);d("require","linkid",da)}}function g(){if(Xq()){}else{var X=P(c,N.m.Ol);X&&(d("require",X,{dataLayer:rj.vb}),d("require","render"))}}function h(){var X=P(c,N.m.ke);u(function(){if(!c.isGtmEvent&&Wc(X)){var da=x.fieldsToSend,U,Q,ja=(U=v())==null?void 0:(Q=U.getByName)==null?void 0:Q.call(U,w),ia;for(ia in X)if(X[ia]!=
null&&/^(dimension|metric)\d+$/.test(ia)){var la=void 0,Ia=(la=ja)==null?void 0:la.get(kJ(X[ia]));wJ(da,ia,Ia)}}})}function m(X,da,U){U&&(da=String(da));x.fieldsToSend[X]=da}function n(){if(x.displayfeatures){var X="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","displayfeatures",void 0,{cookieName:X})}}var p=a;if(H(107)){var q=Uo(a),r=c.eventMetadata.send_to_destinations;if(q&&r&&r.indexOf(q.destinationId)<0)return}var t,u=c.isGtmEvent?iB(P(c,"gaFunctionName")):iB();if(ab(u)){var v=fB,w;w=
c.isGtmEvent?P(c,"name")||P(c,"gtmTrackerName"):"gtag_"+p.split("-").join("_");var x=oJ(w,b,c);!c.isGtmEvent&&pJ(w,x.createOnlyFields)&&(u(function(){var X,da;v()&&((X=v())==null||(da=X.remove)==null||da.call(X,w))}),xJ[w]=!1);u("create",p,x.createOnlyFields);var y=c.isGtmEvent&&x.fieldsToSet[N.m.Tb];if(!c.isGtmEvent&&x.createOnlyFields[N.m.Tb]||y){var B=rk(c.isGtmEvent?x.fieldsToSet[N.m.Tb]:x.createOnlyFields[N.m.Tb],"/analytics.js");B&&(t=B)}var C=c.isGtmEvent?x.fieldsToSet[N.m.Tb]:x.createOnlyFields[N.m.Tb];
if(C){var E=c.isGtmEvent?x.fieldsToSet[N.m.Vf]:x.createOnlyFields[N.m.Vf];E&&!xJ[w]&&(xJ[w]=!0,u(kB(w,E)))}c.isGtmEvent?x.enableRecaptcha&&d("require","recaptcha","recaptcha.js"):(h(),f(x.linkAttribution));var F=x[N.m.Ga];F&&F[N.m.da]&&mJ(F,w);d("set",x.fieldsToSet);if(c.isGtmEvent){if(x.enableLinkId){var K={};rJ(K,C);d("require","linkid","linkid.js",K)}qJ(p,w,c)}if(b===N.m.Dc)if(c.isGtmEvent){n();if(x.remarketingLists){var L="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","adfeatures",{cookieName:L})}e(C);
d("send","pageview");x.createOnlyFields._useUp&&hB(w+".")}else g(),d("send","pageview",x.fieldsToSend);else b===N.m.ka?(g(),rv(p,c),P(c,N.m.pb)&&(Jt(["aw","dc"]),hB(w+".")),Lt(["aw","dc"]),x.sendPageView!=0&&d("send","pageview",x.fieldsToSend),qJ(p,w,c)):b===N.m.mb?lJ(w,c):b==="screen_view"?d("send","screenview",x.fieldsToSend):b==="timing_complete"?(x.fieldsToSend.hitType="timing",m("timingCategory",x.eventCategory,!0),c.isGtmEvent?m("timingVar",x.timingVar,!0):m("timingVar",x.name,!0),m("timingValue",
kb(x.value)),x.eventLabel!==void 0&&m("timingLabel",x.eventLabel,!0),d("send",x.fieldsToSend)):b==="exception"?d("send","exception",x.fieldsToSend):b===""&&c.isGtmEvent||(b==="track_social"&&c.isGtmEvent?(x.fieldsToSend.hitType="social",m("socialNetwork",x.socialNetwork,!0),m("socialAction",x.socialAction,!0),m("socialTarget",x.socialTarget,!0)):((c.isGtmEvent||yJ[b])&&e(C),c.isGtmEvent&&n(),x.fieldsToSend.hitType="event",m("eventCategory",x.eventCategory,!0),m("eventAction",x.eventAction||b,!0),
x.eventLabel!==void 0&&m("eventLabel",x.eventLabel,!0),x.value!==void 0&&m("eventValue",kb(x.value))),d("send",x.fieldsToSend));var T=t&&!c.eventMetadata.suppress_script_load;if(!zJ&&(!c.isGtmEvent||T)){t=t||"https://www.google-analytics.com/analytics.js";zJ=!0;var J=function(){c.onFailure()},Z=function(){var X;((X=v())==null?0:X.loaded)||J()};Xq()?D(Z):rc(t,Z,J)}}else D(c.onFailure)},BJ=function(a,b,c,d){Do(function(){AJ(a,b,d)},[N.m.Z,N.m.T])},pJ=function(a,b){var c=CJ[a];CJ[a]=Xc(b,null);if(!c)return!1;
for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(var e in c)if(c.hasOwnProperty(e)&&c[e]!==b[e])return!0;return!1},tJ=function(a,b){function c(u){return{id:d(N.m.La),affiliation:d(N.m.bj),revenue:d(N.m.ya),tax:d(N.m.Wg),shipping:d(N.m.ne),coupon:d(N.m.cj),list:d(N.m.Vg)||d(N.m.me)||u}}for(var d=function(u){return P(b,u)},e=d(N.m.la),f,g=0;e&&g<e.length&&!(f=e[g][N.m.Vg]||e[g][N.m.me]);g++);var h=d(N.m.ke);if(Wc(h))for(var m=0;e&&m<e.length;++m){var n=e[m],p;for(p in h)h.hasOwnProperty(p)&&
/^(dimension|metric)\d+$/.test(p)&&h[p]!=null&&wJ(n,p,n[h[p]])}var q=null,r=d(N.m.Gl);if(a===N.m.Va||a===N.m.ld)q={action:a,Eb:c(),ed:DJ(e)};else if(a===N.m.hd)q={action:"add",Eb:c(),ed:DJ(e)};else if(a===N.m.jd)q={action:"remove",Eb:c(),ed:DJ(e)};else if(a===N.m.hb)q={action:"detail",Eb:c(f),ed:DJ(e)};else if(a===N.m.Ob)q={action:"impressions",rk:DJ(e)};else if(a===N.m.Pb)q={action:"promo_view",wf:DJ(r)||DJ(e)};else if(a==="select_content"&&r&&r.length>0||a===N.m.mc)q={action:"promo_click",wf:DJ(r)||
DJ(e)};else if(a==="select_content"||a===N.m.kd)q={action:"click",Eb:{list:d(N.m.Vg)||d(N.m.me)||f},ed:DJ(e)};else if(a===N.m.Cc||a==="checkout_progress"){var t={step:a===N.m.Cc?1:d(N.m.Ug),option:d(N.m.Of)};q={action:"checkout",ed:DJ(e),Eb:Xc(c(),t)}}else a==="set_checkout_option"&&(q={action:"checkout_option",Eb:{step:d(N.m.Ug),option:d(N.m.Of)}});q&&(q.Nh=d(N.m.Ja));return q},sJ=function(a){var b=a.gtmEcommerceData;if(!b)return null;var c={};b.currencyCode&&(c.Nh=b.currencyCode);if(b.impressions){c.action=
"impressions";var d=b.impressions;c.rk=b.translateIfKeyEquals==="impressions"?DJ(d):d}if(b.promoView){c.action="promo_view";var e=b.promoView.promotions;c.wf=b.translateIfKeyEquals==="promoView"?DJ(e):e}if(b.promoClick){var f=b.promoClick;c.action="promo_click";var g=f.promotions;c.wf=b.translateIfKeyEquals==="promoClick"?DJ(g):g;c.Eb=f.actionField;return c}for(var h in b)if(b[h]!==void 0&&h!=="translateIfKeyEquals"&&h!=="impressions"&&h!=="promoView"&&h!=="promoClick"&&h!=="currencyCode"){c.action=
h;var m=b[h].products;c.ed=b.translateIfKeyEquals==="products"?DJ(m):m;c.Eb=b[h].actionField;break}return Object.keys(c).length?c:null},DJ=function(a){function b(e){function f(h,m){for(var n=0;n<m.length;n++){var p=m[n];if(e[p]){g[h]=e[p];break}}}var g=Xc(e,null);f("id",["id","item_id","promotion_id"]);f("name",["name","item_name","promotion_name"]);f("brand",["brand","item_brand"]);f("variant",["variant","item_variant"]);f("list",["list_name","item_list_name"]);f("position",["list_position","creative_slot",
"index"]);(function(){if(e.category)g.category=e.category;else{for(var h="",m=0;m<EJ.length;m++)e[EJ[m]]!==void 0&&(h&&(h+="/"),h+=e[EJ[m]]);h&&(g.category=h)}})();f("listPosition",["list_position"]);f("creative",["creative_name"]);f("list",["list_name"]);f("position",["list_position","creative_slot"]);return g}for(var c=[],d=0;a&&d<a.length;d++)a[d]&&Wc(a[d])&&c.push(b(a[d]));return c.length?c:void 0},oJ=function(a,b,c){var d=function(J){return P(c,J)},e={},f={},g={},h={},m=FJ(d(N.m.Ll));!c.isGtmEvent&&
m&&wJ(f,"exp",m);g["&gtm"]=Zq({Ea:c.eventMetadata.source_canonical_id,pg:!0});c.isGtmEvent||(g._no_slc=!0);Lm()&&(h._cs=GJ);var n=d(N.m.ke);if(!c.isGtmEvent&&Wc(n))for(var p in n)if(n.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&n[p]!=null){var q=d(String(n[p]));q!==void 0&&wJ(f,p,q)}for(var r=!c.isGtmEvent,t=op(c),u=0;u<t.length;++u){var v=t[u];if(c.isGtmEvent){var w=d(v);HJ.hasOwnProperty(v)?e[v]=w:IJ.hasOwnProperty(v)?h[v]=w:g[v]=w}else{var x=void 0;v!==N.m.oa?x=d(v):x=pp(c,v);if(JJ.hasOwnProperty(v))vJ(JJ[v],
v,x,e);else if(KJ.hasOwnProperty(v))vJ(KJ[v],v,x,g);else if(jJ.hasOwnProperty(v))vJ(jJ[v],v,x,f);else if(iJ.hasOwnProperty(v))vJ(iJ[v],v,x,h);else if(/^(dimension|metric|content_group)\d+$/.test(v))vJ(1,v,x,f);else if(v===N.m.oa){if(!LJ){var y=yb(x);y&&(f["&did"]=y)}var B=void 0,C=void 0;b===N.m.ka?B=yb(pp(c,v),"."):(B=yb(pp(c,v,1),"."),C=yb(pp(c,v,2),"."));B&&(f["&gdid"]=B);C&&(f["&edid"]=C)}else v===N.m.Ta&&t.indexOf(N.m.Hc)<0&&(h.cookieName=String(x)+"_ga");H(153)&&MJ[v]&&(c.N.hasOwnProperty(v)||
b===N.m.ka&&c.C.hasOwnProperty(v))&&(r=!1)}}H(153)&&r&&(f["&jsscut"]="1");d(N.m.Hf)!==!1&&d(N.m.nb)!==!1&&hJ()||(g.allowAdFeatures=!1);g.allowAdPersonalizationSignals=Lq(c);!c.isGtmEvent&&d(N.m.pb)&&(h._useUp=!0);if(c.isGtmEvent){h.name=h.name||e.gtmTrackerName;var E=g.hitCallback;g.hitCallback=function(){ab(E)&&E();c.onSuccess()}}else{wJ(h,"cookieDomain","auto");wJ(g,"forceSSL",!0);wJ(e,"eventCategory",XJ(b));YJ[b]&&wJ(f,"nonInteraction",!0);b==="login"||b==="sign_up"||b==="share"?wJ(e,"eventLabel",
d(N.m.wj)):b==="search"||b==="view_search_results"?wJ(e,"eventLabel",d(N.m.Ul)):b==="select_content"&&wJ(e,"eventLabel",d(N.m.Dl));var F=e[N.m.Ga]||{},K=F[N.m.yd];K||K!=0&&F[N.m.da]?h.allowLinker=!0:K===!1&&wJ(h,"useAmpClientId",!1);f.hitCallback=c.onSuccess;h.name=a}Mq()&&(g["&gcs"]=Nq());g["&gcd"]=Rq(c);Lm()&&(Ao(N.m.Z)||(h.storage="none"),Ao([N.m.T,N.m.U])||(g.allowAdFeatures=!1,h.storeGac=!1));Uq()&&(g["&dma_cps"]=Sq());g["&dma"]=Tq();qq(yq())&&(g["&tcfd"]=Vq());Jj()&&(g["&tag_exp"]=Jj());var L=
tk(c)||d(N.m.Tb),T=d(N.m.Vf);L&&(c.isGtmEvent||(h[N.m.Tb]=L),h._cd2l=!0);T&&!c.isGtmEvent&&(h[N.m.Vf]=T);e.fieldsToSend=f;e.fieldsToSet=g;e.createOnlyFields=h;return e},GJ=function(a){return Ao(a)},FJ=function(a){if(Array.isArray(a)){for(var b=[],c=0;c<a.length;c++){var d=a[c];if(d!=null){var e=d.id,f=d.variant;e!=null&&f!=null&&b.push(String(e)+"."+String(f))}}return b.length>0?b.join("!"):void 0}},wJ=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},XJ=function(a){var b="general";ZJ[a]?b="ecommerce":
$J[a]?b="engagement":a==="exception"&&(b="error");return b},kJ=function(a){return a&&bb(a)?a.replace(/(_[a-z])/g,function(b){return b[1].toUpperCase()}):a},vJ=function(a,b,c,d){if(c!==void 0)if(aK[b]&&(c=lb(c)),b!=="anonymize_ip"||c||(c=void 0),a===1)d[kJ(b)]=c;else if(bb(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&c[e]!==void 0&&(d[a[e]]=c[e])},LJ=!1;var zJ=!1,xJ={},nJ={},bK={},MJ=(bK[N.m.xa]=
1,bK[N.m.nb]=1,bK[N.m.Xa]=1,bK[N.m.Ya]=1,bK[N.m.ib]=1,bK[N.m.Hc]=1,bK[N.m.yb]=1,bK[N.m.Ta]=1,bK[N.m.nc]=1,bK[N.m.yj]=1,bK[N.m.ra]=1,bK[N.m.we]=1,bK[N.m.Ka]=1,bK[N.m.ob]=1,bK),cK={},iJ=(cK.client_storage="storage",cK.sample_rate=1,cK.site_speed_sample_rate=1,cK.store_gac=1,cK.use_amp_client_id=1,cK[N.m.wb]=1,cK[N.m.Fa]="storeGac",cK[N.m.Xa]=1,cK[N.m.Ya]=1,cK[N.m.ib]=1,cK[N.m.Hc]=1,cK[N.m.yb]=1,cK[N.m.nc]=1,cK),dK={},IJ=(dK._cs=1,dK._useUp=1,dK.allowAnchor=1,dK.allowLinker=1,dK.alwaysSendReferrer=1,
dK.clientId=1,dK.cookieDomain=1,dK.cookieExpires=1,dK.cookieFlags=1,dK.cookieName=1,dK.cookiePath=1,dK.cookieUpdate=1,dK.legacyCookieDomain=1,dK.legacyHistoryImport=1,dK.name=1,dK.sampleRate=1,dK.siteSpeedSampleRate=1,dK.storage=1,dK.storeGac=1,dK.useAmpClientId=1,dK._cd2l=1,dK),KJ={anonymize_ip:1},eK={},jJ=(eK.campaign={content:"campaignContent",id:"campaignId",medium:"campaignMedium",name:"campaignName",source:"campaignSource",term:"campaignKeyword"},eK.app_id=1,eK.app_installer_id=1,eK.app_name=
1,eK.app_version=1,eK.description="exDescription",eK.fatal="exFatal",eK.language=1,eK.page_hostname="hostname",eK.transport_type="transport",eK[N.m.Ja]="currencyCode",eK[N.m.fg]=1,eK[N.m.ra]="location",eK[N.m.we]="page",eK[N.m.Ka]="referrer",eK[N.m.ob]="title",eK[N.m.fh]=1,eK[N.m.Ha]=1,eK),fK={},JJ=(fK.content_id=1,fK.event_action=1,fK.event_category=1,fK.event_label=1,fK.link_attribution=1,fK.name=1,fK[N.m.Ga]=1,fK[N.m.wj]=1,fK[N.m.Za]=1,fK[N.m.ya]=1,fK),HJ={displayfeatures:1,enableLinkId:1,enableRecaptcha:1,
eventAction:1,eventCategory:1,eventLabel:1,gaFunctionName:1,gtmEcommerceData:1,gtmTrackerName:1,linker:1,remarketingLists:1,socialAction:1,socialNetwork:1,socialTarget:1,timingVar:1,value:1},EJ=["item_category","item_category2","item_category3","item_category4","item_category5"],gK={},uJ=(gK.levels=1,gK[N.m.Ya]="duration",gK[N.m.Hc]=1,gK),hK={},aK=(hK.anonymize_ip=1,hK.fatal=1,hK.send_page_view=1,hK.store_gac=1,hK.use_amp_client_id=1,hK[N.m.Fa]=1,hK[N.m.fg]=1,hK),iK={},yJ=(iK.checkout_progress=1,
iK.select_content=1,iK.set_checkout_option=1,iK[N.m.hd]=1,iK[N.m.jd]=1,iK[N.m.Cc]=1,iK[N.m.kd]=1,iK[N.m.Ob]=1,iK[N.m.mc]=1,iK[N.m.Pb]=1,iK[N.m.Va]=1,iK[N.m.ld]=1,iK[N.m.hb]=1,iK),jK={},ZJ=(jK.checkout_progress=1,jK.set_checkout_option=1,jK[N.m.Ri]=1,jK[N.m.Si]=1,jK[N.m.hd]=1,jK[N.m.jd]=1,jK[N.m.Ti]=1,jK[N.m.Cc]=1,jK[N.m.Va]=1,jK[N.m.ld]=1,jK[N.m.Ui]=1,jK),kK={},$J=(kK.generate_lead=1,kK.login=1,kK.search=1,kK.select_content=1,kK.share=1,kK.sign_up=1,kK.view_search_results=1,kK[N.m.kd]=1,kK[N.m.Ob]=
1,kK[N.m.mc]=1,kK[N.m.Pb]=1,kK[N.m.hb]=1,kK),lK={},YJ=(lK.view_search_results=1,lK[N.m.Ob]=1,lK[N.m.Pb]=1,lK[N.m.hb]=1,lK),CJ={};function mK(a,b,c,d){}mK.K="internal.executeEventProcessor";function nK(a){var b;return md(b,this.J,1)}nK.K="internal.executeJavascriptString";function oK(a){var b;return b};function pK(a){var b={};return md(b)}pK.K="internal.getAdsCookieWritingOptions";function qK(a,b){var c=!1;return c}qK.K="internal.getAllowAdPersonalization";function rK(a,b){b=b===void 0?!0:b;var c;return c}rK.K="internal.getAuid";var sK=null;
function tK(){var a=new Na;M(this,"read_container_data"),H(49)&&sK?a=sK:(a.set("containerId",'UA-123456789'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",ag),a.set("previewMode",bg.Nk),a.set("environmentMode",bg.Nm),a.set("firstPartyServing",Lj()||zj),a.set("containerUrl",ic),a.Qa(),H(49)&&(sK=a));return a}
tK.publicName="getContainerVersion";function uK(a,b){b=b===void 0?!0:b;var c;return c}uK.publicName="getCookieValues";function vK(){var a="";return a}vK.K="internal.getCorePlatformServicesParam";function wK(){return Pn()}wK.K="internal.getCountryCode";function xK(){var a=[];return md(a)}xK.K="internal.getDestinationIds";function yK(a){var b=new Na;return b}yK.K="internal.getDeveloperIds";function zK(a,b){var c=null;return c}zK.K="internal.getElementAttribute";function AK(a){var b=null;return b}AK.K="internal.getElementById";function BK(a){var b="";return b}BK.K="internal.getElementInnerText";function CK(a,b){var c=null;return md(c)}CK.K="internal.getElementProperty";function DK(a){var b;return b}DK.K="internal.getElementValue";function EK(a){var b=0;return b}EK.K="internal.getElementVisibilityRatio";function FK(a){var b=null;return b}FK.K="internal.getElementsByCssSelector";
function GK(a){var b;if(!ah(a))throw I(this.getName(),["string"],arguments);M(this,"read_event_data",a);var c;a:{var d=a,e=iE(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",y=l(n),B=y.next();!B.done;B=
y.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===h?x+".":x+C}x&&w.push(x);for(var E=l(w),F=E.next();!F.done;F=E.next()){if(f==null){c=void 0;break a}f=f[F.value]}c=f}else c=void 0}b=md(c,this.J,1);return b}GK.K="internal.getEventData";var HK={};HK.enableAWFledge=H(34);HK.enableAdsConversionValidation=H(18);HK.enableAdsSupernovaParams=H(30);HK.enableAutoPhoneAndAddressDetection=H(32);HK.enableAutoPiiOnPhoneAndAddress=H(33);HK.enableCachedEcommerceData=H(40);HK.enableCcdSendTo=H(41);HK.enableCloudRecommentationsErrorLogging=H(42);HK.enableCloudRecommentationsSchemaIngestion=H(43);HK.enableCloudRetailInjectPurchaseMetadata=H(45);HK.enableCloudRetailLogging=H(44);HK.enableCloudRetailPageCategories=H(46);HK.enableDCFledge=H(56);
HK.enableDataLayerSearchExperiment=H(128);HK.enableDecodeUri=H(90);HK.enableDeferAllEnhancedMeasurement=H(58);HK.enableFormSkipValidation=H(75);HK.enableGa4OutboundClicksFix=H(94);HK.enableGaAdsConversions=H(121);HK.enableGaAdsConversionsClientId=H(120);HK.enableGppForAds=H(105);HK.enableMerchantRenameForBasketData=H(112);HK.enableUrlDecodeEventUsage=H(139);HK.enableZoneConfigInChildContainers=H(142);HK.useEnableAutoEventOnFormApis=H(156);function IK(){return md(HK)}IK.K="internal.getFlags";function JK(){return new id(tD)}JK.K="internal.getHtmlId";function KK(a){var b;return b}KK.K="internal.getIframingState";function LK(a,b){var c={};return md(c)}LK.K="internal.getLinkerValueFromLocation";function MK(){var a=new Na;return a}MK.K="internal.getPrivacyStrings";function NK(a,b){var c;return c}NK.K="internal.getProductSettingsParameter";function OK(a,b){var c;return c}OK.publicName="getQueryParameters";function PK(a,b){var c;return c}PK.publicName="getReferrerQueryParameters";function QK(a){var b="";return b}QK.publicName="getReferrerUrl";function RK(){return Qn()}RK.K="internal.getRegionCode";function SK(a,b){var c;return c}SK.K="internal.getRemoteConfigParameter";function TK(){var a=new Na;a.set("width",0);a.set("height",0);return a}TK.K="internal.getScreenDimensions";function UK(){var a="";return a}UK.K="internal.getTopSameDomainUrl";function VK(){var a="";return a}VK.K="internal.getTopWindowUrl";function WK(a){var b="";return b}WK.publicName="getUrl";function XK(){M(this,"get_user_agent");return fc.userAgent}XK.K="internal.getUserAgent";function YK(){var a;return a?md(Xx(a)):a}YK.K="internal.getUserAgentClientHints";function fL(){return z.gaGlobal=z.gaGlobal||{}}function gL(){var a=fL();a.hid=a.hid||fb();return a.hid}function hL(a,b){var c=fL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function HL(a){(qx(a)||Lj())&&V(a,N.m.Hj,Qn()||Pn());!qx(a)&&Lj()&&V(a,N.m.Qj,"::")}function IL(a){if(H(78)&&Lj()){Su(a);Tu(a,"cpf",mv(P(a.D,N.m.Ta)));var b=P(a.D,N.m.nc);Tu(a,"cu",b===!0?1:b===!1?0:void 0);Tu(a,"cf",mv(P(a.D,N.m.ib)));Tu(a,"cd",xr(lv(P(a.D,N.m.Xa)),lv(P(a.D,N.m.yb))))}};var dM={AW:An.Tk,G:An.Xl,DC:An.Wl};function eM(a){var b=Gi(a);return""+$q(b.map(function(c){return c.value}).join("!"))}function fM(a){var b=Uo(a);return b&&dM[b.prefix]}function gM(a,b){var c=a[b];c&&(c.clearTimerId&&z.clearTimeout(c.clearTimerId),c.clearTimerId=z.setTimeout(function(){delete a[b]},36E5))};var MM=window,NM=document,OM=function(a){var b=MM._gaUserPrefs;if(b&&b.ioo&&b.ioo()||NM.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&MM["ga-disable-"+a]===!0)return!0;try{var c=MM.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(NM.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m;(m=g.slice(1).join("=").replace(/^\s*|\s*$/g,""))&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return NM.getElementById("__gaOptOutExtension")?!0:!1};function ZM(a){ib(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Db]||{};ib(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function FN(a,b){}function GN(a,b){var c=function(){};return c}
function HN(a,b,c){};var IN=GN;function KN(a,b,c){var d=this;}KN.K="internal.gtagConfig";
function MN(a,b){}
MN.publicName="gtagSet";function NN(){var a={};return a};function ON(a,b){}ON.publicName="injectHiddenIframe";var PN=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function QN(a,b,c,d,e){}QN.K="internal.injectHtml";var UN={};
function WN(a,b,c,d){}var XN={dl:1,id:1},YN={};
function ZN(a,b,c,d){}H(160)?ZN.publicName="injectScript":WN.publicName="injectScript";ZN.K="internal.injectScript";function $N(){return Un()}$N.K="internal.isAutoPiiEligible";function aO(a){var b=!0;return b}aO.publicName="isConsentGranted";function bO(a){var b=!1;return b}bO.K="internal.isDebugMode";function cO(){return Sn()}cO.K="internal.isDmaRegion";function dO(a){var b=!1;return b}dO.K="internal.isEntityInfrastructure";function eO(){var a=!1;return a}eO.K="internal.isLandingPage";function fO(){var a=Ch(function(b){iE(this).log("error",b)});a.publicName="JSON";return a};function gO(a){var b=void 0;return md(b)}gO.K="internal.legacyParseUrl";function hO(){return!1}
var iO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function jO(){}jO.publicName="logToConsole";function kO(a,b){}kO.K="internal.mergeRemoteConfig";function lO(a,b,c){c=c===void 0?!0:c;var d=[];return md(d)}lO.K="internal.parseCookieValuesFromString";function mO(a){var b=void 0;return b}mO.publicName="parseUrl";function nO(a){}nO.K="internal.processAsNewEvent";function oO(a,b,c){var d;return d}oO.K="internal.pushToDataLayer";function pO(a){var b=wa.apply(1,arguments),c=!1;return c}pO.publicName="queryPermission";function qO(a){var b=this;}qO.K="internal.queueAdsTransmission";function rO(){var a="";return a}rO.publicName="readCharacterSet";function sO(){return rj.vb}sO.K="internal.readDataLayerName";function tO(){var a="";return a}tO.publicName="readTitle";function uO(a,b){var c=this;}uO.K="internal.registerCcdCallback";function vO(a){
return!0}vO.K="internal.registerDestination";var wO=["config","event","get","set"];function xO(a,b,c){}xO.K="internal.registerGtagCommandListener";function yO(a,b){var c=!1;return c}yO.K="internal.removeDataLayerEventListener";function zO(a,b){}
zO.K="internal.removeFormData";function AO(){}AO.publicName="resetDataLayer";function BO(a,b,c){var d=void 0;return d}BO.K="internal.scrubUrlParams";function CO(a){}CO.K="internal.sendAdsHit";function DO(a,b,c,d){}DO.K="internal.sendGtagEvent";function EO(a,b,c){}EO.publicName="sendPixel";function FO(a,b){}FO.K="internal.setAnchorHref";function GO(a){}GO.K="internal.setContainerConsentDefaults";function HO(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}HO.publicName="setCookie";function IO(a){}IO.K="internal.setCorePlatformServices";function JO(a,b){}JO.K="internal.setDataLayerValue";function KO(a){}KO.publicName="setDefaultConsentState";function LO(a,b){}LO.K="internal.setDelegatedConsentType";function MO(a,b){}MO.K="internal.setFormAction";function NO(a,b,c){c=c===void 0?!1:c;}NO.K="internal.setInCrossContainerData";function OO(a,b,c){return!1}OO.publicName="setInWindow";function PO(a,b,c){}PO.K="internal.setProductSettingsParameter";function QO(a,b,c){}QO.K="internal.setRemoteConfigParameter";function RO(a,b){}RO.K="internal.setTransmissionMode";function SO(a,b,c,d){var e=this;}SO.publicName="sha256";function TO(a,b,c){}
TO.K="internal.sortRemoteConfigParameters";function UO(a,b){var c=void 0;return c}UO.K="internal.subscribeToCrossContainerData";var VO={},WO={};VO.getItem=function(a){var b=null;return b};VO.setItem=function(a,b){};
VO.removeItem=function(a){};VO.clear=function(){};VO.publicName="templateStorage";function XO(a,b){var c=!1;return c}XO.K="internal.testRegex";function YO(a){var b;return b};function ZO(a){var b;return b}ZO.K="internal.unsiloId";function $O(a,b){var c;return c}$O.K="internal.unsubscribeFromCrossContainerData";function aP(a){}aP.publicName="updateConsentState";var bP;function cP(a,b,c){bP=bP||new Nh;bP.add(a,b,c)}function dP(a,b){var c=bP=bP||new Nh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=ab(b)?ih(a,b):jh(a,b)}
function eP(){return function(a){var b;var c=bP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.C;if(e){var f=!1,g=e.sb();if(g){ph(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function fP(){var a=function(c){return void dP(c.K,c)},b=function(c){return void cP(c.publicName,c)};b(cE);b(jE);b(yF);b(AF);b(BF);b(IF);b(KF);b(OG);b(fO());b(QG);b(tK);b(uK);b(OK);b(PK);b(QK);b(WK);b(MN);b(ON);b(aO);b(jO);b(mO);b(pO);b(rO);b(tO);b(EO);b(HO);b(KO);b(OO);b(SO);b(VO);b(aP);cP("Math",nh());cP("Object",Lh);cP("TestHelper",Qh());cP("assertApi",kh);cP("assertThat",lh);cP("decodeUri",qh);cP("decodeUriComponent",rh);cP("encodeUri",sh);cP("encodeUriComponent",th);cP("fail",yh);cP("generateRandom",
zh);cP("getTimestamp",Ah);cP("getTimestampMillis",Ah);cP("getType",Bh);cP("makeInteger",Dh);cP("makeNumber",Eh);cP("makeString",Fh);cP("makeTableMap",Gh);cP("mock",Jh);cP("mockObject",Kh);cP("fromBase64",oK,!("atob"in z));cP("localStorage",iO,!hO());cP("toBase64",YO,!("btoa"in z));a(bE);a(fE);a(AE);a(ME);a(TE);a(YE);a(nF);a(wF);a(zF);a(CF);a(DF);a(EF);a(FF);a(GF);a(HF);a(JF);a(LF);a(NG);a(PG);a(RG);a(TG);a(UG);a(VG);a(WG);a(XG);a(bH);a(jH);a(kH);a(vH);a(AH);a(FH);a(OH);a(TH);a(fI);a(hI);a(vI);a(wI);
a(yI);a(mK);a(nK);a(pK);a(qK);a(rK);a(wK);a(xK);a(yK);a(zK);a(AK);a(BK);a(CK);a(DK);a(EK);a(FK);a(GK);a(IK);a(JK);a(KK);a(LK);a(MK);a(NK);a(RK);a(SK);a(TK);a(UK);a(VK);a(YK);a(KN);a(QN);a(ZN);a($N);a(bO);a(cO);a(dO);a(eO);a(gO);a(lF);a(kO);a(lO);a(nO);a(oO);a(qO);a(sO);a(uO);a(vO);a(xO);a(yO);a(zO);a(Ph);a(BO);a(CO);a(DO);a(FO);a(GO);a(IO);a(JO);a(LO);a(MO);a(NO);a(PO);a(QO);a(RO);a(TO);a(UO);a(XO);a(ZO);a($O);dP("internal.CrossContainerSchema",SG());dP("internal.IframingStateSchema",NN());H(103)&&a(vK);H(160)?b(ZN):b(WN);return eP()};var $D;
function gP(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;$D=new He;hP();nf=ZD();var e=$D,f=fP(),g=new ed("require",f);g.Qa();e.C.C.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Kf(n,d[m]);try{$D.execute(n),H(119)&&Ck&&n[0]===50&&h.push(n[1])}catch(r){}}H(119)&&(Af=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Hj[q]=
["sandboxedScripts"]}iP(b)}function hP(){$D.C.C.N=function(a,b,c){Ko.SANDBOXED_JS_SEMAPHORE=Ko.SANDBOXED_JS_SEMAPHORE||0;Ko.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Ko.SANDBOXED_JS_SEMAPHORE--}}}function iP(a){a&&ib(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Hj[e]=Hj[e]||[];Hj[e].push(b)}})};function jP(a){Lv(Fv("developer_id."+a,!0),0,{})};var kP=Array.isArray;function lP(a,b){return Xc(a,b||null)}function W(a){return window.encodeURIComponent(a)}function mP(a,b,c){vc(a,b,c)}function nP(a,b){if(!a)return!1;var c=fk(lk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function oP(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var xP=z.clearTimeout,yP=z.setTimeout;function zP(a,b,c){if(Xq()){b&&D(b)}else return rc(a,b,c,void 0)}function AP(){return z.location.href}function BP(a,b){return Rj(a,b||2)}function CP(a,b){z[a]=b}function DP(a,b,c){b&&(z[a]===void 0||c&&!z[a])&&(z[a]=b);return z[a]}function EP(a,b){if(Xq()){b&&D(b)}else tc(a,b)}
var FP={};var Y={securityGroups:{}};

Y.securityGroups.v=["google"],Y.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=BP(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Y.__v.F="v",Y.__v.isVendorTemplate=!0,Y.__v.priorityOverride=0,Y.__v.isInfrastructure=!0,Y.__v.runInSiloedMode=!1;

Y.securityGroups.rep=["google"],Y.__rep=function(a){var b=jm(a.vtp_containerId),c=Uo(b,!0);if(c){var d,e;switch(c.prefix){case "AW":d=CI;e=ym.V.wa;break;case "DC":d=TI;e=ym.V.wa;break;case "GF":d=YI;e=ym.V.qb;break;case "HA":d=dJ;e=ym.V.qb;break;case "UA":d=BJ;e=ym.V.qb;break;case "MC":d=IN(c,a.vtp_gtmEventId);e=ym.V.jc;break;default:D(a.vtp_gtmOnFailure);return}d?(D(a.vtp_gtmOnSuccess),Yp(b,d,e),a.vtp_remoteConfig&&dq(b,a.vtp_remoteConfig||{})):D(a.vtp_gtmOnFailure)}else D(a.vtp_gtmOnFailure)},Y.__rep.F=
"rep",Y.__rep.isVendorTemplate=!0,Y.__rep.priorityOverride=0,Y.__rep.isInfrastructure=!1,Y.__rep.runInSiloedMode=!0;
Y.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Y.__read_event_data=b;Y.__read_event_data.F="read_event_data";Y.__read_event_data.isVendorTemplate=!0;Y.__read_event_data.priorityOverride=0;Y.__read_event_data.isInfrastructure=!1;Y.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!bb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&yg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},P:a}})}();




Y.securityGroups.read_container_data=["google"],Y.__read_container_data=function(){return{assert:function(){},P:function(){return{}}}},Y.__read_container_data.F="read_container_data",Y.__read_container_data.isVendorTemplate=!0,Y.__read_container_data.priorityOverride=0,Y.__read_container_data.isInfrastructure=!1,Y.__read_container_data.runInSiloedMode=!1;









Y.securityGroups.get=["google"],Y.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Iv(String(b.streamId),d,c);Lv(f,e.eventId,e);a.vtp_gtmOnSuccess()},Y.__get.F="get",Y.__get.isVendorTemplate=!0,Y.__get.priorityOverride=0,Y.__get.isInfrastructure=!1,Y.__get.runInSiloedMode=!1;




var No={dataLayer:Sj,callback:function(a){Gj.hasOwnProperty(a)&&ab(Gj[a])&&Gj[a]();delete Gj[a]},bootstrap:0};
function GP(){Mo();mm();SA();sb(Hj,Y.securityGroups);var a=hm(im()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;ko(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);zf={Cm:Qf}}var HP=!1;
function Mn(){try{if(HP||!vm()){qj();oj.O="";oj.ab="ad_storage|analytics_storage|ad_user_data|ad_personalization";
oj.ia="ad_storage|analytics_storage|ad_user_data";oj.fa="5490";oj.fa="54l0";km();if(H(108)){}hg[8]=
!0;var a=Lo("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});ro(a);Jo();RD();zq();Qo();if(nm()){iF();CA().removeExternalRestrictions(fm());}else{OA();xf();tf=Y;uf=BD;Sf=new Zf;gP();GP();Kn||(Jn=On());Go();RC();cC();xC=!1;A.readyState==="complete"?zC():wc(z,"load",zC);XB();Ck&&(Gp(Up),z.setInterval(Tp,864E5),Gp(TD),Gp(uB),Gp(iz),Gp(Xp),Gp(WD),Gp(FB),H(119)&&(Gp(zB),Gp(AB),Gp(BB)));Dk&&(pn(),lp(),TC(),XC(),VC(),fn("bt",String(oj.C?2:zj?1:0)),fn("ct",String(oj.C?0:zj?1:Xq()?2:3)),UC());rD();zn(1);jF();Fj=pb();No.bootstrap=Fj;oj.N&&QC();H(108)&&Bz();H(133)&&(typeof z.name==="string"&&
ub(z.name,"web-pixel-sandbox-CUSTOM")&&Mc()?jP("dMDg0Yz"):z.Shopify&&(jP("dN2ZkMj"),Mc()&&jP("dNTU0Yz")))}}}catch(b){zn(4),Qp()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Xn(n)&&(m=h.Mj)}function c(){m&&ic?g(m):a()}if(!z["__TAGGY_INSTALLED"]){var d=!1;if(A.referrer){var e=lk(A.referrer);d=hk(e,"host")==="cct.google"}if(!d){var f=hr("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(z["__TAGGY_INSTALLED"]=!0,rc("https://cct.google/taggy/agent.js"))}var g=function(u){var v="GTM",w="GTM";xj&&(v="OGT",w="GTAG");var x=z["google.tagmanager.debugui2.queue"];x||(x=
[],z["google.tagmanager.debugui2.queue"]=x,rc("https://"+rj.Ff+"/debug/bootstrap?id="+Wf.ctid+"&src="+w+"&cond="+u+"&gtm="+Zq()));var y={messageType:"CONTAINER_STARTING",data:{scriptSource:ic,containerProduct:v,debug:!1,id:Wf.ctid,targetRef:{ctid:Wf.ctid,isDestination:Xl()},aliases:$l(),destinations:Yl()}};y.data.resume=function(){a()};rj.Wk&&(y.data.initialPublish=!0);x.push(y)},h={Yl:1,Oj:2,Wj:3,Pi:4,Mj:5};h[h.Yl]="GTM_DEBUG_LEGACY_PARAM";h[h.Oj]="GTM_DEBUG_PARAM";h[h.Wj]="REFERRER";h[h.Pi]="COOKIE";h[h.Mj]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=fk(z.location,"query",!1,void 0,"gtm_debug");Xn(p)&&(m=h.Oj);if(!m&&A.referrer){var q=lk(A.referrer);hk(q,"host")==="tagassistant.google.com"&&(m=h.Wj)}if(!m){var r=hr("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Pi)}m||b();if(!m&&Wn(n)){var t=!1;wc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);z.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){H(83)&&HP&&!On()["0"]?Ln():Mn()});

})()

