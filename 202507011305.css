/*
 * 🌌 Aurora Whisper - CherryStudio 极光低语主题 🌌
 * 
 * 设计理念：极光渐变 + 深空美学 + 呼吸动画 + 流体形态
 * 基于MoonlitEchoes设计原则的创新融合重构
 * 
 * 特色功能：
 * - 动态极光渐变背景系统
 * - 深空蓝紫色彩体系
 * - 微妙呼吸动画效果
 * - 流体边框设计语言
 * - 智能响应式适配
 * - 高性能毛玻璃效果
 * 
 * 兼容性：CherryStudio v1.2+ | Chrome 88+ | Firefox 94+ | Safari 14+
 * 作者：CherryStudio UI Theme Artisan v3.0
 */

/* ==================== CSS变量系统定义 ==================== */
:root {
  /* 极光色彩体系 - 深空主题 */
  --aurora-primary: #4c6ef5;
  --aurora-secondary: #7c3aed;
  --aurora-accent: #06b6d4;
  --aurora-warm: #f59e0b;
  --aurora-cool: #10b981;
  
  /* 深空背景渐变 */
  --deep-space-bg: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 25%,
    rgba(51, 65, 85, 0.85) 50%,
    rgba(30, 41, 59, 0.9) 75%,
    rgba(15, 23, 42, 0.95) 100%);
    
  /* 极光动态渐变 */
  --aurora-gradient: linear-gradient(45deg,
    rgba(76, 110, 245, 0.3) 0%,
    rgba(124, 58, 237, 0.25) 25%,
    rgba(6, 182, 212, 0.2) 50%,
    rgba(124, 58, 237, 0.25) 75%,
    rgba(76, 110, 245, 0.3) 100%);
    
  /* 毛玻璃效果参数 */
  --glass-blur: blur(12px);
  --glass-opacity: 0.15;
  --glass-border: rgba(255, 255, 255, 0.1);
  
  /* 动画参数 */
  --breath-duration: 4s;
  --aurora-shift-duration: 8s;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 流体边框参数 */
  --fluid-radius-sm: 12px 8px 12px 8px;
  --fluid-radius-md: 16px 12px 16px 12px;
  --fluid-radius-lg: 24px 16px 24px 16px;
  
  /* 响应式断点 */
  --mobile-breakpoint: 768px;
  --tablet-breakpoint: 1024px;
}

/* 深色主题变量 */
body[theme-mode="dark"] {
  --color-background: #0f172a;
  --color-background-soft: rgba(30, 41, 59, 0.8);
  --color-background-mute: rgba(51, 65, 85, 0.6);
  --color-text: rgba(248, 250, 252, 0.95);
  --color-text-soft: rgba(203, 213, 225, 0.8);
  --color-text-mute: rgba(148, 163, 184, 0.7);
  --color-border: rgba(71, 85, 105, 0.3);
  --color-primary: var(--aurora-primary);
  --color-accent: var(--aurora-accent);
}

/* 浅色主题变量 */
body[theme-mode="light"] {
  --color-background: #f8fafc;
  --color-background-soft: rgba(241, 245, 249, 0.9);
  --color-background-mute: rgba(226, 232, 240, 0.8);
  --color-text: rgba(15, 23, 42, 0.95);
  --color-text-soft: rgba(51, 65, 85, 0.8);
  --color-text-mute: rgba(100, 116, 139, 0.7);
  --color-border: rgba(203, 213, 225, 0.4);
  --color-primary: var(--aurora-secondary);
  --color-accent: var(--aurora-cool);
  
  /* 浅色主题的极光效果调整 */
  --aurora-gradient: linear-gradient(45deg,
    rgba(76, 110, 245, 0.15) 0%,
    rgba(124, 58, 237, 0.12) 25%,
    rgba(6, 182, 212, 0.1) 50%,
    rgba(124, 58, 237, 0.12) 75%,
    rgba(76, 110, 245, 0.15) 100%);
}

/* ==================== 核心动画定义 ==================== */
@keyframes aurora-shift {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: hue-rotate(0deg);
  }
  25% { 
    background-position: 100% 50%; 
    filter: hue-rotate(90deg);
  }
  50% { 
    background-position: 100% 100%; 
    filter: hue-rotate(180deg);
  }
  75% { 
    background-position: 0% 100%; 
    filter: hue-rotate(270deg);
  }
}

@keyframes breath-glow {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1);
    box-shadow: 0 0 20px rgba(76, 110, 245, 0.3);
  }
  50% { 
    opacity: 0.9; 
    transform: scale(1.02);
    box-shadow: 0 0 30px rgba(124, 58, 237, 0.4);
  }
}

@keyframes fluid-pulse {
  0%, 100% { 
    border-radius: var(--fluid-radius-md);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  50% { 
    border-radius: 20px 10px 20px 10px;
    box-shadow: 0 8px 30px rgba(76, 110, 245, 0.2);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ==================== 全局基础样式 ==================== */
* {
  box-sizing: border-box;
}

html, body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background: var(--deep-space-bg);
  background-size: 400% 400%;
  animation: aurora-shift var(--aurora-shift-duration) ease-in-out infinite;
  color: var(--color-text);
  overflow-x: hidden;
}

/* 极光背景层 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--aurora-gradient);
  background-size: 300% 300%;
  animation: aurora-shift calc(var(--aurora-shift-duration) * 1.5) ease-in-out infinite reverse;
  pointer-events: none;
  z-index: -1;
}

/* ==================== 主容器样式 ==================== */
.app, #app, [class*="App"] {
  background: transparent;
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  min-height: 100vh;
  position: relative;
}

/* 主内容区域 */
.main-content, [class*="MainContent"], [class*="main-content"] {
  background: rgba(var(--color-background-soft), var(--glass-opacity));
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--fluid-radius-lg);
  margin: 20px;
  padding: 24px;
  animation: breath-glow var(--breath-duration) ease-in-out infinite;
  transition: var(--transition-smooth);
}

/* ==================== 聊天界面样式 ==================== */
/* 聊天容器 */
.chat-container, [class*="chat"], #chat {
  background: transparent;
  border-radius: var(--fluid-radius-md);
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

/* 消息气泡基础样式 */
.message, .chat-message, [class*="message"] {
  background: rgba(var(--color-background-soft), 0.7);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--fluid-radius-md);
  margin: 12px 0;
  padding: 16px 20px;
  position: relative;
  transition: var(--transition-smooth);
  animation: fluid-pulse 6s ease-in-out infinite;
}

.message:hover {
  background: rgba(var(--color-background-soft), 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 用户消息样式 */
.message.user, .message-user, [class*="user-message"] {
  background: linear-gradient(135deg, 
    rgba(76, 110, 245, 0.2) 0%,
    rgba(124, 58, 237, 0.15) 100%);
  border-left: 3px solid var(--aurora-primary);
  margin-left: auto;
  margin-right: 0;
  max-width: 85%;
}

/* AI消息样式 */
.message.assistant, .message-assistant, [class*="assistant-message"] {
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.2) 0%,
    rgba(16, 185, 129, 0.15) 100%);
  border-left: 3px solid var(--aurora-accent);
  margin-left: 0;
  margin-right: auto;
  max-width: 85%;
}

/* 消息内容 */
.message-content, [class*="message-content"] {
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 消息时间戳 */
.message-timestamp, [class*="timestamp"] {
  font-size: 0.75rem;
  color: var(--color-text-mute);
  margin-top: 8px;
  opacity: 0.7;
}

/* ==================== 输入区域样式 ==================== */
.input-container, [class*="input"], .chat-input {
  background: rgba(var(--color-background-soft), 0.8);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--fluid-radius-md);
  padding: 16px;
  margin: 16px 0;
  position: relative;
  overflow: hidden;
}

/* 输入框 */
.input-field, input[type="text"], textarea {
  background: rgba(var(--color-background-mute), 0.5);
  border: 1px solid var(--color-border);
  border-radius: var(--fluid-radius-sm);
  color: var(--color-text);
  padding: 12px 16px;
  width: 100%;
  font-size: 14px;
  transition: var(--transition-smooth);
  resize: vertical;
  min-height: 44px;
}

.input-field:focus, input:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(76, 110, 245, 0.1);
  background: rgba(var(--color-background-mute), 0.8);
}

/* 发送按钮 */
.send-button, [class*="send"], button[type="submit"] {
  background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-secondary));
  border: none;
  border-radius: var(--fluid-radius-sm);
  color: white;
  padding: 12px 24
