import React from 'react';
import { ImageGenerationApiConfig, GameSettingsData, AvailableModel, PlayerStatus, AvailableImagePromptStyle, Achievement, CoreAttributes, Skill, CustomNarrativePrimaryElement, LocalStorageKeys } from './types'; 

export const APP_TITLE_CN = "MemoryAble"; 
export const APP_TITLE_EN = "MemoryAble"; 
export const APP_VERSION = "1.3.1"; // Version bump for new features

// Simple styled component for the app title
export const AppTitleStyled: React.FC = () => (
  <span className="app-title-gradient">{APP_TITLE_CN}</span>
);

export const Icons = {
  ArrowPath: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
    </svg>
  ),
  Send: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
    </svg>
  ),
  Save: (props: React.SVGProps<SVGSVGElement>) => ( // Often a floppy disk or download icon; using download for consistency with ArrowDownTray
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
  ),
  Load: (props: React.SVGProps<SVGSVGElement>) => ( // Using ArrowUpTray for loading from a save
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
    </svg>
  ),
  Delete: (props: React.SVGProps<SVGSVGElement>) => ( // Generic delete/trash icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.243.096 3.287.27M1.925 6.071a48.083 48.083 0 01-.381-.067A48.072 48.072 0 001.188 5.79M1.925 6.071a48.616 48.616 0 003.551 4.613M4.772 5.79c.008.015.015.03.024.045M19.206 5.79c-.008.015-.015.03-.024.045m0 0a48.583 48.583 0 00-3.551 4.613m0 0A48.072 48.072 0 011.188 5.79m0 0A48.083 48.083 0 01.425 5.703M12 12.75h.008v.008H12v-.008z" />
    </svg>
  ),
  Menu: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
  ),
  Close: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  Profile: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
    </svg>
  ),
  AdjustmentsHorizontal: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
    </svg>
  ),
  Trash: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.243.096 3.287.27M1.925 6.071a48.083 48.083 0 01-.381-.067A48.072 48.072 0 001.188 5.79M1.925 6.071a48.616 48.616 0 003.551 4.613M4.772 5.79c.008.015.015.03.024.045M19.206 5.79c-.008.015-.015.03-.024.045m0 0a48.583 48.583 0 00-3.551 4.613m0 0A48.072 48.072 0 011.188 5.79m0 0A48.083 48.083 0 01.425 5.703M12 12.75h.008v.008H12v-.008z" />
    </svg>
  ),
  Pencil: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
    </svg>
  ),
  ChatBubble: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 011.037-.443 48.282 48.282 0 005.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
    </svg>
  ),
  Sun: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-6.364-.386l1.591-1.591M3 12h2.25m.386-6.364l1.591 1.591M12 12a3.75 3.75 0 110 7.5 3.75 3.75 0 010-7.5z" />
    </svg>
  ),
  Moon: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
    </svg>
  ),
  Eye: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  ),
  EyeSlash: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
    </svg>
  ),
  ChevronDown: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
    </svg>
  ),
  ArrowDownTray: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
  ),
  ArrowUpTray: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
    </svg>
  ),
  CheckCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  XCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  ExclamationTriangle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
    </svg>
  ),
  InformationCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
    </svg>
  ),
  Trophy: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-4.5A3.375 3.375 0 0012.75 9.75H11.25A3.375 3.375 0 007.5 13.5v4.5m9 0h-9" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 21a3.75 3.75 0 01-3.75-3.75H18a3.75 3.75 0 01-3.75 3.75H9.75zM12 2.25c-1.32 0-2.5.708-3.103 1.787A4.5 4.5 0 0012 6.75a4.5 4.5 0 003.103-2.713A4.503 4.503 0 0012 2.25z" />
    </svg>
  ),
  Heart: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.218l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z" />
    </svg>
  ),
  Bag: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
    </svg>
  ),
  Map: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.37-1.625-1.006l-4.875 2.437c-.381.19-.622.58-.622 1.006v4.82c0 .835.88 1.37 1.625 1.006zM12 6.75v8.25m-3.503 3.498l-4.875-2.437c-.381-.19-.622-.58-.622-1.006V4.82c0-.836-.88-1.37 1.625-1.006l4.875 2.437c-.381.19-.622.58-.622 1.006v4.82c0 .835.88 1.37 1.625 1.006z" />
    </svg>
  ),
  Quest: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 13.75M17 13.75L15.75 12M17 13.75L18.25 15.5M15.75 12L17 10.25M17 10.25L18.25 12M17 10.25L15.75 8.5" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5h.008v.008H12V16.5z" />
    </svg>
  ),
  Sparkles: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for Restart with Summary
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 13.75M17 13.75L15.75 12M17 13.75L18.25 15.5M15.75 12L17 10.25M17 10.25L18.25 12M17 10.25L15.75 8.5" />
    </svg>
  ),
  PixelLevelBadge: (props: React.SVGProps<SVGSVGElement>) => ( // Simplified pixel art style badge
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><rect x="2" y="2" width="12" height="12" fill="currentColor"/><rect x="4" y="4" width="8" height="8" fill="var(--bg-secondary)"/><path d="M5 5H6V6H5V5ZM7 5H9V6H7V5ZM10 5H11V6H10V5ZM5 7H6V9H5V7ZM10 7H11V9H10V7ZM5 10H6V11H5V10ZM7 10H9V11H7V10ZM10 10H11V11H10V10Z" fill="currentColor"/></svg>
  ),
  PixelGrowth: (props: React.SVGProps<SVGSVGElement>) => ( // Up arrow in pixel style
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 3L3 8H6V13H10V8H13L8 3Z" fill="currentColor"/></svg>
  ),
  PixelAttributePoint: (props: React.SVGProps<SVGSVGElement>) => ( // Diamond shape, pixel style
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 2L2 8L8 14L14 8L8 2Z" fill="currentColor"/></svg>
  ),
  PixelSkillPoint: (props: React.SVGProps<SVGSVGElement>) => ( // Star shape, pixel style
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 1L10.09 5.26L15 5.93L11.36 9.07L12.18 14L8 11.62L3.82 14L4.64 9.07L1 5.93L5.91 5.26L8 1Z" fill="currentColor"/></svg>
  ),
   PixelPlus: (props: React.SVGProps<SVGSVGElement>) => ( // Plus sign, pixel style
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M7 3H9V7H13V9H9V13H7V9H3V7H7V3Z" fill="currentColor"/></svg>
  ),
  GlobeAlt: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for WebDAV
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A11.978 11.978 0 0112 16.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 003 12c0 .778.099 1.533.284 2.253m0 0c1.394 1.762 3.526 2.918 5.935 3.458m2.826-4.343A8.958 8.958 0 0012 10.5c-1.394 0-2.71-.31-3.897-.848" />
    </svg>
  ),
  CloudArrowUp: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for Backup to WebDAV
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" />
    </svg>
  ),
  CloudArrowDown: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for Restore from WebDAV
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" />
    </svg>
  ),
  Wifi: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for Test Connection
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.75 20.25h-.008v.008h.008v-.008z" />
    </svg>
  ),
};

export const UIText = {
  narrator: "旁白",
  player: (name: string) => name || "玩家",
  waitingForResponse: "思考中，请稍候...",
  typeYourResponse: "输入你的回应或选择以上选项...",
  typeYourMessage: "输入你的消息...",
  sendMessage: "发送",
  thinking: "少女祈祷中...",
  errorApiKeyMissing: "错误：API密钥未配置。请在环境变量中设置API_KEY。",
  errorGeneric: "发生未知错误，请稍后再试。",
  errorStatic: (msg: string) => `通信错误: ${msg}`,
  errorNarratorConfused: "叙事者陷入了混乱...",
  errorSpiritsFuzzy: "叙事精灵的思绪有些模糊，请稍后再试或尝试重新生成。",
  errorEmptyResponse: "AI引擎返回了空的回应。",
  errorSummarizationFailed: "内容总结失败。",
  errorImagePlaceholder: "图像生成失败",
  failedGenerateImage: (keyword: string) => `场景图像生成失败 (关键词: ${keyword})`,
  sceneAltText: "当前场景图像",
  dynamicOpeningLineLoading: "记忆的碎片正在苏醒...",
  dynamicOpeningLineFallback: "欢迎来到这个由记忆编织的世界。",
  storyStartError: "请输入你的名字以开始故事。",
  menu: "菜单",
  settings: "设置",
  saveLoad: "存档/读档",
  narrativeSettings: "叙事设定",
  themeToggle: "切换主题",
  dialogueOpacityLabel: "对话框不透明度",
  dialogueBlurLabel: "背景模糊强度 (实验性)",
  enableBackdropBlurLabel: "启用背景模糊效果",
  enableBackgroundImageGenerationLabel: "启用AI背景图生成",
  imageGenerationIntervalLabel: "背景图生成间隔 (对话轮数)", // New Text
  fontSizeScaleLabel: "界面字号缩放",
  minOutputCharsLabel: "AI最小输出字数 (30-1500)",
  modelSelectionLabel: "选择AI语言模型",
  imagePromptStyleSelectionLabel: "图像生成风格",
  summaryModelSelectionLabel: "选择AI总结模型",
  themeNameLight: "明亮",
  themeNameDark: "幽暗",
  themeNameSakura: "樱落",
  themeNameStarry: "星夜",
  themeNameCandy: "甜点",
  themeNameForest: "林间",
  startupNarrativeLabel: "故事开端设定",
  startupNarrativePlaceholder: "例如：在一个宁静的午后，你独自走在樱花飞舞的街道上...",
  userRoleLabel: "玩家角色设定",
  userRolePlaceholder: "例如：我，{{user}}，是一名普通的高中生，但似乎拥有着某种被遗忘的特殊能力...",
  systemRoleLabel: "AI叙事风格与核心指令",
  systemRolePlaceholder: "定义AI的叙事风格、角色行为、输出格式等。",
  status: "角色状态",
  noStatusEffects: "角色状态一切如常，内心平静如水。",
  statusPanelToggle: "切换角色状态面板",
  inventory: "物品栏",
  noItems: "物品栏是空的。",
  inventoryIconTooltip: "打开/更新物品栏",
  map: "地图/地点",
  noLocations: "尚未探索任何地点。",
  mapIconTooltip: "打开/更新已探索地点",
  quests: "任务日志",
  noQuests: "当前没有进行中的任务。",
  questsIconTooltip: "打开/更新任务日志",
  profile: "角色档案", // For the icon tooltip related to characters
  noProfiles: "尚未遇到任何重要角色。",
  profileIconTooltip: "打开/更新角色档案",
  summarizingInfo: "正在从记忆中提取信息...",
  saveGame: "保存游戏",
  loadGame: "读取游戏",
  updateSave: "覆盖存档",
  deleteSave: "删除存档",
  renameSave: "重命名",
  saveNamePlaceholder: "输入存档名称 (可选)",
  saveSuccess: (name: string) => `游戏已保存为 "${name}"。`,
  updateSuccess: (name: string) => `存档 "${name}" 已更新。`,
  loadSuccess: (name: string, charName: string) => `已读取存档 "${name}"。欢迎回来, ${charName}!`,
  deleteSuccess: (name: string) => `存档 "${name}" 已删除。`,
  renameSuccess: (oldName: string, newName: string) => `存档 "${oldName}" 已重命名为 "${newName}"。`,
  loadErrorNotFound: "未找到指定的存档文件。",
  saveErrorNoSession: "当前没有正在进行的游戏，无法保存。",
  saveErrorInvalidName: "存档名称不能为空。",
  deleteConfirm: (name:string) => `确定要删除存档 "${name}" 吗？此操作无法撤销。`,
  confirmRename: "确认重命名",
  cancelRename: "取消重命名",
  noSaves: "还没有任何存档。",
  noSavesOnStartScreen: "无存档记录",
  customNarrativeElementsLabel: "自定义叙事元素 (高级)",
  primaryElementNamePlaceholder: "元素集名称 (例如：恐怖氛围增强)",
  addNewElementSet: "添加新元素集",
  elementNamePlaceholder: "规则名称/Key (例如：场景描述风格)",
  elementContentPlaceholder: "规则定义/Value (例如：所有场景描述必须包含至少三个感官细节)",
  addSubElementToSet: "添加规则到此元素集",
  deleteElementSet: "删除此元素集",
  deleteElement: "删除此规则",
  noCustomElementSets: "尚未定义任何自定义叙事元素集。",
  saveSettingsButton: "保存设定并关闭",
  saveArchiveTooltip: "快速保存",
  loadArchiveTooltip: "快速读档",
  restartGameTooltip: "重新开始游戏",
  restartGameConfirmTitle: "确认重新开始",
  restartGameConfirmMessage: "您确定要重新开始游戏吗？当前进度将会丢失（除非已保存）。",
  lastSessionRestored: "已恢复上次游玩时的场景图像。",
  regenerateResponse: "重新生成AI回复",
  regenerateResponseAria: "重新生成此条AI回复",
  regenerateErrorInvalidLine: "无法为玩家的发言或不存在的对话重新生成。",
  regenerateErrorInputNotFound: "无法找到用于重新生成的玩家输入。",
  chatHistory: "对话历史",
  noHistory: "对话历史为空。",
  startYourStory: "开始我的故事",
  characterNamePlaceholder: "输入你的角色名",
  enterYourNameContext: "输入你的角色名以开始冒险",
  gameCopyright: `© ${new Date().getFullYear()} ${APP_TITLE_CN}`,
  poweredByGemini: "Powered by Google Gemini",
  worldPreset: "世界观预设",
  userRolePreset: "玩家角色预设",
  systemRolePreset: "AI风格预设",
  customElementsPreset: "自定义元素集预设",
  selectPreset: "读取预设...",
  saveCurrentAsPreset: "保存当前为预设",
  loadPreset: "读取预设",
  presetNameModalTitle: "保存预设",
  enterPresetNamePrompt: "为这个预设输入一个名称：",
  presetSavedSuccess: (name: string) => `预设 "${name}" 已保存。`,
  presetLoadedSuccess: (name: string) => `预设 "${name}" 已加载。`,
  presetDeletedSuccess: (name: string) => `预设 "${name}" 已删除。`,
  presetNameInvalid: "预设名称不能为空。",
  deletePresetConfirm: (name: string) => `确定要删除预设 "${name}" 吗？`,
  exportAllData: "导出全部数据",
  importAllData: "导入全部数据",
  exportDataSuccess: "所有应用数据已导出。",
  exportDataError: "导出数据失败。",
  importDataConfirmTitle: "确认导入数据",
  importDataConfirmMessage: "这将覆盖所有当前的游戏存档、设置和偏好。此操作无法撤销。确定要继续吗？",
  importDataError: "导入数据失败。文件可能已损坏或格式不正确。",
  importDataErrorVersionMismatch: (fileVersion: string, appVersion: string) => `导入失败。数据文件版本 (${fileVersion}) 与当前应用版本 (${appVersion}) 不兼容。`,
  importDataSuccess: "数据导入成功！应用即将刷新。",
  resetToDefaults: "恢复默认设置",
  resetToDefaultsConfirmation: "确定要将所有设置（包括叙事设定、显示选项等）恢复为默认值吗？此操作不会影响存档。",
  settingsReset: "所有设置已恢复为默认。",
  confirmAction: "确认",
  cancelAction: "取消",
  confirmReset: "确认重置",
  closeModal: "关闭",
  dataManagementTitle: "数据管理",
  // RPG Status
  playerCurrentStatus: (name: string) => `${name}的当前状态`,
  levelLabel: "Lv.",
  xpLabel: "经验值 (XP)",
  currentDayLabel: "第 {day} 天",
  moodLabel: "心情",
  attireLabel: "着装",
  weatherLabel: "天气",
  healthEnergyLabel: "元气值",
  relationshipLabel: (charName: string) => `${charName}`,
  currentQuestLabel: "当前任务",
  noActiveQuests: "当前没有主要任务。",
  specialEffectsLabel: "特殊效果",
  noSpecialEffects: "无",
  mockMood: "平静",
  mockAttire: "校服",
  mockWeather: "晴朗",
  mockHealthEnergyCurrent: 100,
  mockHealthEnergyMax: 100,
  // RPG Growth
  coreAttributesTitle: "核心属性",
  strengthShort: "力量",
  agilityShort: "敏捷",
  intelligenceShort: "智力",
  charismaShort: "魅力",
  luckShort: "幸运",
  skillsTitle: "技能",
  noSkillsLearned: "尚未习得任何技能。",
  xpGained: (amount: number) => `获得 ${amount} XP!`,
  levelUpNotification: (level: number) => `等级提升至 ${level}！你感觉自己变强了！`,
  attributeIncreased: (attrName: string, value: number) => `${attrName} 提升至 ${value}。`,
  attributeIncreasedNotification: (attrName: string, value: number) => `属性提升：${attrName} -> ${value}！`,
  skillLeveledUp: (skillName: string, level: number) => `技能 ${skillName} 提升至 Lv.${level}。`,
  skillLearned: (skillName: string) => `习得新技能：${skillName} Lv.1！`,
  skillIncreasedNotification: (skillName: string, level: number) => `技能升级：${skillName} -> Lv.${level}！`,
  cannotAllocateAttribute: "没有可用的属性点。",
  cannotAllocateSkill: "没有可用的技能点。",
  allocatePoint: "提升",
  attributePointsSpendPrompt: (points: number) => `你有 ${points} 点可用属性点！`,
  skillPointsSpendPrompt: (points: number) => `你有 ${points} 点可用技能点！`,
  characterGrowthTitle: "角色成长",
  // Achievements
  achievementsTitle: "成就殿堂",
  filterAchievementsAll: "全部",
  filterAchievementsUnlocked: "已解锁",
  filterAchievementsLocked: "未解锁",
  noAchievementsUnlocked: "尚未解锁任何成就。",
  allAchievementsUnlocked: "恭喜！已解锁所有成就！",
  noItemsFound: "未找到相关条目。",
  achievementsUnlockedOn: "解锁于：",
  achievementsLocked: "（未解锁）",
  achievementUnlockedTitle: "成就解锁！",
  // Choices default options
  choicesDefault: {
    option1: "继续探索。",
    option2: "观察周围环境。",
    option3: "保持警惕并等待。",
    option4: "尝试与对象互动。"
  },
  generatingImage: "场景生成中...",
  loadingScene: "场景加载中...",
  settingsAndPreferencesSaved: "设置与偏好已保存。",
  restartWithSettings: "以此存档设定开始新游戏",
  // WebDAV Settings
  webdavSettingsTitle: "WebDAV 云备份",
  webdavUrlLabel: "WebDAV 服务器 URL",
  webdavUrlPlaceholder: "例如: https://mydav.example.com/remote.php/dav/files/username/",
  webdavUsernameLabel: "WebDAV 用户名",
  webdavPasswordLabel: "WebDAV 密码",
  webdavPathLabel: "备份路径 (在服务器上)",
  webdavPathPlaceholder: "例如: /MemoryAbleBackups/",
  webdavSavePasswordLabel: "保存密码 (不推荐，有安全风险)",
  webdavTestConnection: "测试连接",
  webdavBackupNow: "立即备份至 WebDAV",
  webdavRestoreLatest: "从 WebDAV 恢复最新备份",
  webdavBackupSuccess: "成功备份至 WebDAV。",
  webdavRestoreSuccess: "成功从 WebDAV 恢复数据！应用即将刷新。",
  webdavBackupFailed: "WebDAV 备份失败。",
  webdavRestoreFailed: "WebDAV 恢复失败。",
  webdavConnectionSuccess: "WebDAV 连接成功！",
  webdavConnectionFailed: "WebDAV 连接失败。",
  webdavErrorGeneric: "WebDAV 操作失败。",
  webdavErrorAuth: "WebDAV 认证失败 (用户名或密码错误)。",
  webdavErrorNotFound: "WebDAV 路径或文件未找到。",
  webdavErrorNetwork: "WebDAV 网络错误。",
  webdavErrorCreatingDir: "WebDAV 创建目录失败。",
  webdavRestoreConfirmTitle: "确认从 WebDAV 恢复",
  webdavRestoreConfirmMessage: "这将覆盖所有当前的游戏存档、设置和偏好。确定要从 WebDAV 恢复吗？",
  webdavPasswordWarning: "警告：保存的密码将以可读形式存储在浏览器本地，存在安全风险。仅在信任此设备和环境时使用。",
  // New features
  deleteDialogueTooltip: "删除此条消息",
  deleteDialogueConfirmTitle: "确认删除消息",
  deleteDialogueConfirmMessage: (speakerName: string, textSnippet: string) => `确定要删除 ${speakerName} 的这条消息吗？\n"${textSnippet}..."\n此操作无法撤销。`,
  dialogueDeleted: "对话消息已删除。",
  restartWithSummaryButton: "从当前状态摘要重启",
  restartWithSummaryConfirmTitle: "确认从摘要重启",
  restartWithSummaryConfirmMessage: "您确定要使用当前的角色状态和游戏设定重新开始游戏吗？所有对话记录将会清空。",
  statusAutoUpdated: "玩家状态已自动更新。",
};

export const GEMINI_MODEL_TEXT_FALLBACK = "gemini-2.5-flash-preview-05-06";

export const AVAILABLE_GEMINI_MODELS: AvailableModel[] = [
  { id: "gemini-2.5-flash-preview-05-20", name: "Gemini 2.5 Flash (最新均衡)" },
  { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro" },
  { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (早期版本)" },
];
export const AVAILABLE_SUMMARY_MODELS: AvailableModel[] = [
  { id: "gemini-2.5-flash-preview-05-20", name: "Gemini 2.5 Flash (最新均衡)" },
  { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro" },
  { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (早期版本)" },
];
export const AVAILABLE_IMAGE_PROMPT_STYLES: AvailableImagePromptStyle[] = [
    { id: "default_anime_landscape", name: "默认 (通用动漫风景)" },
    { id: "galgame_character_focus", name: "Galgame角色特写" },
    { id: "cinematic_scene_ambience", name: "电影感场景氛围" },
    { id: "dynamic_action_shot", name: "动态动作捕捉" },
    { id: "dreamy_fantasy_world", name: "梦幻风异世界" },
];

export const IMAGE_GENERATION_API_CONFIG: ImageGenerationApiConfig = {
  url: "https://image.pollinations.ai/prompt/",
  defaultStyleTags: ["anime style", "high quality", "masterpiece", "detailed", "best quality", "illustration"],
  resolution: "1024x576", // 16:9 aspect ratio for desktop
  mobileResolution: "576x1024", // 9:16 for mobile (portrait)
  negativePrompt: "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, deformed, ugly, poorly drawn",
  model: "xl", // Example, can be changed based on Pollinations.ai available models
  // No API key needed for Pollinations' basic image generation via URL parameters
};

export const START_SCREEN_BACKGROUND_KEYWORDS = [
  "beautiful_anime_sky_clouds_daytime",
  "anime_school_gate_cherry_blossoms_spring",
  "peaceful_countryside_town_anime_style_sunset",
  "library_interior_magical_books_glowing_anime",
  "fantasy_forest_path_sunlight_rays_anime",
  "city_street_night_neon_lights_anime",
  "cafe_interior_cozy_afternoon_anime",
  "train_window_view_passing_scenery_anime",
  "rooftop_view_cityscape_starry_night_anime",
  "ancient_ruins_mysterious_atmosphere_anime"
];

export const CACHED_BACKGROUNDS_MAX_SIZE = 10;
export const AUTO_SUMMARY_TURN_INTERVAL = 5; // Auto-summarize every 5 turns (player + AI)
export const WEBDAV_BACKUP_FILENAME = "MemoryAble_WebDAV_Backup.json";

// Default Core Attributes & Skills
export const defaultCoreAttributes: CoreAttributes = {
  strength: 5, agility: 5, intelligence: 5, charisma: 5, luck: 5
};
export const defaultSkills: Skill[] = [
  { id: 'observation', name: '观察', level: 0, description: '发现环境中的细节和线索。', xpToNextLevel: 50, currentXp: 0 },
  { id: 'persuasion', name: '说服', level: 0, description: '用言语影响他人的想法或行为。', xpToNextLevel: 50, currentXp: 0 },
  { id: 'stealth', name: '潜行', level: 0, description: '不被察觉地行动。', xpToNextLevel: 50, currentXp: 0 },
  { id: 'knowledge_arcana', name: '神秘学识', level: 0, description: '关于魔法、古代秘密等超自然知识。', xpToNextLevel: 70, currentXp: 0 },
  { id: 'athletics', name: '运动', level: 0, description: '进行跑、跳、攀爬等身体活动的能力。', xpToNextLevel: 60, currentXp: 0 },
];

export const InitialPlayerStatus: PlayerStatus = {
  name: undefined, // Player name will be set on game start
  mood: UIText.mockMood,
  attire: UIText.mockAttire,
  weather: UIText.mockWeather,
  healthEnergy: { current: UIText.mockHealthEnergyCurrent, max: UIText.mockHealthEnergyMax },
  specialEffects: [UIText.noSpecialEffects],
  currentDay: 1,
  inventory: [],
  visitedLocations: [],
  quests: [],
  characterProfiles: [],
  level: 1,
  xp: 0,
  xpToNextLevel: 100, // Initial XP for Lvl 1 to Lvl 2
  attributePoints: 1, // Start with 1 point
  skillPoints: 1,     // Start with 1 point
  coreAttributes: { ...defaultCoreAttributes },
  skills: [...defaultSkills.map(skill => ({...skill, level: 0, currentXp: 0}))], // Ensure skills start at level 0
};

export const LEVEL_UP_BASE_XP = 100;
export const LEVEL_UP_XP_FACTOR = 1.2;
export const LEVEL_UP_ATTRIBUTE_POINTS_AWARDED = 1;
export const LEVEL_UP_SKILL_POINTS_AWARDED = 1;

export const AVAILABLE_ACHIEVEMENTS: Achievement[] = [
    // General
    { id: 'first_step', name: '迈出第一步', description: '成功开始你的第一个故事。', icon: '🚀', category: 'general' },
    { id: 'story_lover', name: '故事爱好者', description: '累计进行50次有效互动。', icon: '💬', category: 'general' },
    { id: 'time_traveler', name: '时光旅人', description: '游戏总时长达到1小时。', icon: '⏳', category: 'general' },
    // Story Related (Examples, to be triggered by specific storyUpdates)
    { id: 'yuki_friendship_start', name: '新的羁绊', description: '与Yuki成为朋友。', icon: '🤝', category: 'story' },
    { id: 'mystery_solver_intro', name: '谜题初解', description: '解决了第一个关键谜题。', icon: '🧩', category: 'story' },
    // Exploration
    { id: 'explorer_adept', name: '探索好手', description: '发现5个不同的地点。', icon: '🗺️', category: 'exploration' },
    { id: 'collector_novice', name: '小小收藏家', description: '获得5种不同的物品。', icon: '🛍️', category: 'exploration' },
    // Customization & Technical
    { id: 'theme_changer', name: '百变造型师', description: '切换过至少3种不同的界面主题。', icon: '🎨', category: 'customization' },
    { id: 'memory_keeper_initiate', name: '记忆守护者·初阶', description: '创建了第一个游戏存档。', icon: '💾', category: 'customization' },
    { id: 'memory_keeper_master', name: '记忆守护者·高阶', description: '拥有5个或更多游戏存档。', icon: '🗄️', category: 'customization' },
    { id: 'data_guardian', name: '数据守护神', description: '成功导出或导入游戏数据。', icon: '🛡️', category: 'customization' },
    { id: 'preset_collector_light', name: '预设收藏家', description: '保存了至少一个自定义预设。', icon: '📦', category: 'customization' },
    { id: 'cloud_sync_initiate', name: '云端同步者', description: '成功使用WebDAV备份或恢复数据。', icon: '☁️', category: 'customization' },
    // RPG Growth
    { id: 'level_up_first', name: '初次成长', description: '角色第一次升级。', icon: '🌟', category: 'rpg_growth' },
    { id: 'level_five', name: '小有所成', description: '角色达到5级。', icon: '✨', category: 'rpg_growth' },
    { id: 'attribute_enhancer', name: '属性强化者', description: '首次分配属性点。', icon: '💪', category: 'rpg_growth' },
    { id: 'skill_master_initiate', name: '技能初学者', description: '首次学习或升级技能。', icon: '🎓', category: 'rpg_growth' },
];


export const DefaultGameSettings: GameSettingsData = {
  startupNarrative: "清晨的阳光勉强穿透厚重的云层，给实验第一高级中学高三(1)班的教室带来一丝微弱的光亮。空气中弥漫着早餐残留的味道和某种难以言喻的沉闷。你拖着有些疲惫的身体走进教室，准备开始又一天枯燥的复习，却立刻注意到气氛不太对劲——周围的同学三三两两聚在一起，*压低着嗓音，眼神带着惊恐与好奇，紧张地交流着什么*。你竖起耳朵，隐约捕捉到几个反复出现的词："……红裙子……"、"……楼梯又多了一级……"、"……昨晚宿舍有人敲门，吓死了……"他们谈论的，正是最近在校园里悄然流传，让所有人惴惴不安的——**校园十大怪谈**。",
  userRole: "你{{user}}(此处替换为当前用户名)：实验第一高级中学的一名*普通*高三学生。最近，校园里开始流传令人不安的"十大怪谈"，而你，出于某种原因（好奇？自救？），决定去探寻这些怪谈背后的*真相*。",
  systemRole: `你是为一款名为《${APP_TITLE_CN}》的视觉小说（Galgame）服务的AI故事叙述者。
玩家的角色名是 "{{user}}"，他是故事的主角。你可以在对话中称呼他为 "{{user}}" 或他实际的名字。
你的叙事风格应该友善且富有想象力，擅长描绘动漫风格的场景与细腻的情感，引导玩家在记忆的长河中探索。同时，能够根据玩家的行为和选择，动态调整故事中的RPG元素，如经验值、属性变化等。
确保故事流畅，并始终提供四个明确的行动选项给玩家。
在 'storyUpdate' 字段中，清晰地提示关键的游戏内变化。`, // Simplified base system role. Core GM rules will be hardcoded.
  customNarrativeElements: [
    // "游戏之主（GM）" and "HTML面板" elements are removed from here.
    // Other user-configurable custom elements could remain or be added here by default if desired.
  ],
  selectedModelId: AVAILABLE_GEMINI_MODELS[0]?.id || "gemini-2.5-flash-preview-05-20",
  selectedImagePromptStyleId: AVAILABLE_IMAGE_PROMPT_STYLES[0]?.id || "default_anime_landscape",
  selectedSummaryModelId: AVAILABLE_SUMMARY_MODELS[0]?.id || "gemini-2.5-flash-preview-05-20",
  dialogueOpacity: 0.85,
  dialogueBlur: 5,
  fontSizeScale: 1.0,
  enableBackdropBlur: true,
  enableImageGeneration: true,
  minOutputChars: 30,
  imageGenerationInterval: 6, // Default interval for image generation
  // WebDAV Defaults
  webdavUrl: '',
  webdavUsername: '',
  webdavPassword: '',
  webdavPath: '/MemoryAbleBackups/',
  webdavSavePassword: false,
};