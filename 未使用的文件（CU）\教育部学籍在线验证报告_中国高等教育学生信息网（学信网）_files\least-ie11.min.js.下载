var htmStr=document.createElement("div");Function.bind||(htmStr.innerHTML='<div id="noticeTopDiv" style="width:100%; line-height:24px; padding:4px; border-bottom:1px solid #fbdbca; background:#fef7f3; font-size:12px;color:#C30;"><div style="width:1000px; margin:0 auto; text-align:center; font-size:14px;">提醒：您的浏览器版本过低，可能影响到某些功能操作，请升级到最新版浏览器，推荐下载使用（<a href="https://www.google.cn/chrome/" target="_blank">Chrome</a>、<a href="https://www.microsoft.com/zh-cn/windows/microsoft-edge" target="_blank">Microsoft Edge</a>） 等浏览器。</div></div>',document.body.insertBefore(htmStr,document.body.firstChild));var browser=navigator.appName,b_version=navigator.appVersion,version=b_version.split(";"),trim_Version="";version[1]&&(trim_Version=version[1].replace(/[ ]/g,""),"Microsoft Internet Explorer"==browser&&"MSIE9.0"==trim_Version&&(htmStr.innerHTML='<div id="noticeTopDiv" style="width:100%; line-height:24px; padding:4px; border-bottom:1px solid #fbdbca; background:#fef7f3; font-size:12px;color:#C30;"><div style="width:1000px; margin:0 auto; text-align:center; font-size:14px;">提醒：您的浏览器版本过低，可能影响到某些功能操作，请升级到最新版浏览器，推荐下载使用（<a href="https://www.google.cn/chrome/" target="_blank">Chrome</a>、<a href="https://www.microsoft.com/zh-cn/windows/microsoft-edge" target="_blank">Microsoft Edge</a>） 等浏览器。</div></div>',document.body.insertBefore(htmStr,document.body.firstChild)),"Microsoft Internet Explorer"==browser&&"MSIE10.0"==trim_Version&&(htmStr.innerHTML='<div id="noticeTopDiv" style="width:100%; line-height:24px; padding:4px; border-bottom:1px solid #fbdbca; background:#fef7f3; font-size:12px;color:#C30;"><div style="width:1000px; margin:0 auto; text-align:center; font-size:14px;">提醒：您的浏览器版本过低，可能影响到某些功能操作，请升级到最新版浏览器，推荐下载使用（<a href="https://www.google.cn/chrome/" target="_blank">Chrome</a>、<a href="https://www.microsoft.com/zh-cn/windows/microsoft-edge" target="_blank">Microsoft Edge</a>） 等浏览器。</div></div>',document.body.insertBefore(htmStr,document.body.firstChild)));