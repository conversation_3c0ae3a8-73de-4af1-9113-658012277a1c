anaconda-anon-usage @ file:///C:/b/abs_e8r_zga7xy/croot/anaconda-anon-usage_1732732454901/work
annotated-types @ file:///C:/b/abs_0dmaoyhhj3/croot/annotated-types_1709542968311/work
archspec @ file:///croot/archspec_1709217642129/work
boltons @ file:///C:/b/abs_45_52ughkz/croot/boltons_1737061711836/work
Brotli @ file:///C:/b/abs_c415aux9ra/croot/brotli-split_1736182803933/work
certifi @ file:///C:/b/abs_8a944p1_gn/croot/certifi_1738623753421/work/certifi
cffi @ file:///C:/b/abs_29_b57if3f/croot/cffi_1736184144340/work
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
colorama @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/colorama_1699472650914/work
conda @ file:///C:/b/abs_12ybmrg1d4/croot/conda_1738168389704/work
conda-anaconda-telemetry @ file:///C:/b/abs_4c9llcc5ob/croot/conda-anaconda-telemetry_1736524617431/work
conda-anaconda-tos @ file:///C:/b/abs_ceeuq0lee_/croot/conda-anaconda-tos_1739299022910/work
conda-content-trust @ file:///C:/b/abs_bdfatn_wzf/croot/conda-content-trust_1714483201909/work
conda-libmamba-solver @ file:///croot/conda-libmamba-solver_1737733694612/work/src
conda-package-handling @ file:///C:/b/abs_7fz3aferfv/croot/conda-package-handling_1731369038903/work
conda_package_streaming @ file:///C:/b/abs_bdz9vbvbh2/croot/conda-package-streaming_1731366449946/work
cryptography @ file:///C:/b/abs_e2lzchf4i6/croot/cryptography_1732130411942/work
distro @ file:///C:/b/abs_71xr36ua5r/croot/distro_1714488282676/work
frozendict @ file:///C:/b/abs_2alamqss6p/croot/frozendict_1713194885124/work
idna @ file:///C:/b/abs_aad84bnnw5/croot/idna_1714398896795/work
jsonpatch @ file:///C:/b/abs_4fdm88t7zi/croot/jsonpatch_1714483974578/work
jsonpointer==2.1
libmambapy @ file:///C:/b/abs_627vsv8bhu/croot/mamba-split_1734469608328/work/libmambapy
markdown-it-py @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/markdown-it-py_1699473886965/work
mdurl @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/mdurl_1699473506455/work
menuinst @ file:///C:/b/abs_fblttj5gp1/croot/menuinst_1738943438301/work
packaging @ file:///C:/b/abs_3by6s2fa66/croot/packaging_1734472138782/work
platformdirs @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/platformdirs_1701797392447/work
pluggy @ file:///C:/b/abs_dfec_m79vo/croot/pluggy_1733170145382/work
pycosat @ file:///C:/b/abs_18nblzzn70/croot/pycosat_1736868434419/work
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydantic @ file:///C:/b/abs_27dx58x550/croot/pydantic_1734736090499/work
pydantic_core @ file:///C:/b/abs_bdosz7qwys/croot/pydantic-core_1734726071532/work
Pygments @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/pygments_1699474141968/work
PySocks @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/pysocks_1699473336188/work
requests @ file:///C:/b/abs_c3508vg8ez/croot/requests_1731000584867/work
rich @ file:///C:/b/abs_8b92fzmygg/croot/rich_1732638991774/work
ruamel.yaml @ file:///C:/b/abs_0cunwx_ww6/croot/ruamel.yaml_1727980181547/work
ruamel.yaml.clib @ file:///C:/b/abs_5fk8zi6n09/croot/ruamel.yaml.clib_1727769837359/work
setuptools==75.8.0
tqdm @ file:///C:/b/abs_0eh9b6xugj/croot/tqdm_1738945553987/work
truststore @ file:///C:/b/abs_494cm143zh/croot/truststore_1736550137835/work
typing_extensions @ file:///C:/b/abs_0ffjxtihug/croot/typing_extensions_1734714875646/work
urllib3 @ file:///C:/b/abs_7bst06lizn/croot/urllib3_1737133657081/work
wheel==0.45.1
win-inet-pton @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/win_inet_pton_1699472992992/work
zstandard @ file:///C:/b/abs_31t8xmrv_h/croot/zstandard_1731356578015/work
