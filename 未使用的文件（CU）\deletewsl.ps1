# 强制停止并注销Debian子系统（兼容多版本命名）
wsl --terminate Debian 2>$null
wsl --unregister Debian 2>$null

# 清理可能的残留文件
$debianPackagePath = Join-Path $env:USERPROFILE "AppData\Local\Packages\*Debian*"
if (Test-Path $debianPackagePath) {
    Remove-Item -Recurse -Force $debianPackagePath -ErrorAction SilentlyContinue
}

# 额外清理路径（某些版本可能存放于此）
$alternatePath = "$env:LOCALAPPDATA\Microsoft\WindowsApps\*Debian*"
if (Test-Path $alternatePath) {
    Remove-Item -Recurse -Force $alternatePath -ErrorAction SilentlyContinue
}

Write-Host "✅ Debian子系统及残留文件已彻底清除" -ForegroundColor Green
Write-Host "--------------------------------------------"
Write-Host "温馨提示："
Write-Host "1. 操作前请确保已备份重要数据"
Write-Host "2. 若安装过多个Debian版本，请手动检查以下路径："
Write-Host "   - %LocalAppData%\Packages"
Write-Host "   - %LocalAppData%\Microsoft\WindowsApps"
