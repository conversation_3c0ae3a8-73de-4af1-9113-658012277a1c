(function(sttc){'use strict';var aa=Object.defineProperty,ba=globalThis,ca=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",da={},ea={};function fa(a,b,c){if(!c||a!=null){c=ea[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ha(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in da?f=da:f=ba;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ca&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(da,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ea[d]===void 0&&(a=Math.random()*1E9>>>0,ea[d]=ca?ba.Symbol(d):"$jscp$"+a+"$"+d),aa(f,ea[d],{configurable:!0,writable:!0,value:b})))}}function ia(a){return a} 
ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var q=this||self;function ja(a){a=a.split(".");for(var b=q,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function ka(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function la(a){return Object.prototype.hasOwnProperty.call(a,ma)&&a[ma]||(a[ma]=++na)}var ma="closure_uid_"+(Math.random()*1E9>>>0),na=0;function oa(a,b,c){return a.call.apply(a.bind,arguments)} 
function pa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function qa(a,b,c){qa=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?oa:pa;return qa.apply(null,arguments)} 
function ra(a,b,c){a=a.split(".");c=c||q;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function sa(a){q.setTimeout(()=>{throw a;},0)};function ta(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]} 
function ua(a,b){let c=0;a=ta(String(a)).split(".");b=ta(String(b)).split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=va(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||va(e[2].length==0,f[2].length==0)||va(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c}function va(a,b){return a<b?-1:a>b?1:0};var wa,xa=ja("CLOSURE_FLAGS"),ya=xa&&xa[610401301];wa=ya!=null?ya:!1;function za(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}var Aa;const Ba=q.navigator;Aa=Ba?Ba.userAgentData||null:null;function Ca(a){if(!wa||!Aa)return!1;for(let b=0;b<Aa.brands.length;b++){const {brand:c}=Aa.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function r(a){return za().indexOf(a)!=-1};function Da(){return wa?!!Aa&&Aa.brands.length>0:!1}function Ea(){return Da()?!1:r("Trident")||r("MSIE")}function Fa(){return Da()?Ca("Chromium"):(r("Chrome")||r("CriOS"))&&!(Da()?0:r("Edge"))||r("Silk")}function Ga(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""} 
function Ha(){var a=za();if(Ea()){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])a=b[1];else{b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];a=b}return a}c=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");b=[];let d;for(;d=c.exec(a);)b.push([d[1],d[2],d[3]||void 0]);a=Ga(b);return(Da()?0:r("Opera"))?a(["Version", 
"Opera"]):(Da()?0:r("Edge"))?a(["Edge"]):(Da()?Ca("Microsoft Edge"):r("Edg/"))?a(["Edg"]):r("Silk")?a(["Silk"]):Fa()?a(["Chrome","CriOS","HeadlessChrome"]):(a=b[2])&&a[1]||""};function Ia(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ja(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Ka(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d} 
function La(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Ma(a,b){a:{var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;c--)if(c in d&&b.call(void 0,d[c],c,a)){b=c;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function Na(a,b){return Ia(a,b)>=0}function Oa(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};function Pa(a){Pa[" "](a);return a}Pa[" "]=function(){};var Qa=null;function Ra(a){const b=[];Sa(a,function(c){b.push(c)});return b}function Sa(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Qa[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Va();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Va(){if(!Qa){Qa={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));for(let e=0;e<d.length;e++){const f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}}};function Wa(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Xa=void 0,Ya;function Za(a){if(Ya)throw Error("");Ya=b=>{q.setTimeout(()=>{a(b)},0)}}function $a(a){if(Ya)try{Ya(a)}catch(b){throw b.cause=a,b;}}function ab(a){a=Error(a);Wa(a,"warning");$a(a);return a};function bb(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var cb=bb(),eb=bb(),fb=bb(),gb=bb("m_m",!0);const t=bb("jas",!0);var hb;const ib=[];ib[t]=7;hb=Object.freeze(ib);function jb(a){if(4&a)return 512&a?512:1024&a?1024:0}function kb(a){a[t]|=32;return a};var lb={};function mb(a,b){return b===void 0?a.i!==nb&&!!(2&(a.C[t]|0)):!!(2&b)&&a.i!==nb}const nb={};var ob=Object.freeze({});function pb(a){a.uc=!0;return a};var qb=pb(a=>typeof a==="number"),rb=pb(a=>typeof a==="string"),sb=pb(a=>Array.isArray(a));function tb(){return pb(a=>sb(a)?a.every(b=>qb(b)):!1)};function ub(a){if(rb(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(qb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var xb=pb(a=>a>=vb&&a<=wb);const vb=BigInt(Number.MIN_SAFE_INTEGER),wb=BigInt(Number.MAX_SAFE_INTEGER);let yb=0,zb=0;function Ab(a){const b=a>>>0;yb=b;zb=(a-b)/4294967296>>>0}function Bb(a){if(a<0){Ab(-a);a=yb;var b=zb;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];yb=c>>>0;zb=d>>>0}else Ab(a)}function Cb(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Db(){var a=yb,b=zb,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Cb(a,b);return c};function Eb(a,b=`unexpected value ${a}!`){throw Error(b);};const Fb=typeof BigInt==="function"?BigInt.asIntN:void 0,Gb=Number.isSafeInteger,Hb=Number.isFinite,Ib=Math.trunc;function Jb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Kb(a){if(a!=null&&typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a}function Lb(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a} 
const Mb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Nb(a){switch(typeof a){case "bigint":return!0;case "number":return Hb(a);case "string":return Mb.test(a);default:return!1}}function Ob(a){if(!Hb(a))throw ab("enum");return a|0}function Pb(a){return a==null?a:Hb(a)?a|0:void 0}function Qb(a){if(typeof a!=="number")throw ab("int32");if(!Hb(a))throw ab("int32");return a|0}function Rb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Hb(a)?a|0:void 0} 
function Sb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Hb(a)?a>>>0:void 0}function Tb(a){if(!Nb(a))throw ab("int64");switch(typeof a){case "string":return Ub(a);case "bigint":return ub(Fb(64,a));default:return Vb(a)}}function Wb(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337} 
function Vb(a){a=Ib(a);if(!Gb(a)){Bb(a);var b=yb,c=zb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Cb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Xb(a){a=Ib(a);if(Gb(a))a=String(a);else{{const b=String(a);Wb(b)?a=b:(Bb(a),a=Db())}}return a} 
function Ub(a){var b=Ib(Number(a));if(Gb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));Wb(a)||(a.length<16?Bb(Number(a)):(a=BigInt(a),yb=Number(a&BigInt(4294967295))>>>0,zb=Number(a>>BigInt(32)&BigInt(4294967295))),a=Db());return a}function Yb(a){if(typeof a!=="string")throw Error();return a}function Zb(a){if(a!=null&&typeof a!=="string")throw Error();return a}function w(a){return a==null||typeof a==="string"?a:void 0} 
function $b(a,b,c,d){if(a!=null&&typeof a==="object"&&a[gb]===lb)return a;if(!Array.isArray(a))return c?d&2?((a=b[cb])||(d=new b,a=d.C,a[t]|=34,a=b[cb]=d),b=a):b=new b:b=void 0,b;c=a[t]|0;d=c|d&32|d&2;d!==c&&(a[t]=d);return new b(a)};function ac(a){return a};function bc(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let h,k=4294967295,n=!1;const l=!!(b&64),m=l?b&128?0:-1:void 0;b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,k=g):h=void 0,!l||b&128||e||(n=!0,k=(cc??ac)(k-m,m,a,h)+m));b=void 0;for(e=0;e<g;e++){let p=a[e];if(p!=null&&(p=c(p,d))!=null)if(l&&e>=k){const v=e-m;(b??(b={}))[v]=p}else f[e]=p}if(h)for(let p in h){if(!Object.prototype.hasOwnProperty.call(h,p))continue;a=h[p];if(a==null||(a=c(a,d))==null)continue; 
g=+p;let v;l&&!Number.isNaN(g)&&(v=g+m)<k?f[v]=a:(b??(b={}))[p]=a}b&&(n?f.push(b):f[k]=b);return f}function dc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return xb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[t]|0;return a.length===0&&b&1?void 0:bc(a,b,dc)}if(a[gb]===lb)return x(a);return}return a}var ec=typeof structuredClone!="undefined"?structuredClone:a=>bc(a,0,dc);let cc; 
function x(a){a=a.C;return bc(a,a[t]|0,dc)};function fc(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[t]|0;4096&b&&!(2&b)&&gc();if(b&256)throw Error("farr");if(b&64)return b&4096||(a[t]=b|4096),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const f=b&128?0:-1;e-=f;if(e>=1024)throw Error("pvtlmt");for(const g in d){if(!Object.prototype.hasOwnProperty.call(d,g))continue;const h=+g;if(h<e)c[h+f]=d[g],delete d[g];else break}b=b&-16760833|(e&1023)<< 
14}}}a[t]=b|4160;return a}function gc(){if(fb!=null){var a=Xa??(Xa={});var b=a[fb]||0;b>=5||(a[fb]=b+1,a=Error(),Wa(a,"incident"),Ya?$a(a):sa(a))}};function hc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[t]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=ic(a,c,!1,b&&!(c&16)):(a[t]|=34,c&4&&Object.freeze(a)));return a}if(a[gb]===lb)return b=a.C,c=b[t]|0,mb(a,c)?a:ic(b,c)}function ic(a,b,c,d){d??(d=!!(34&b));a=bc(a,b,hc,d);d=32;c||(d|=2);b=b&16761025|d;a[t]=b;return a}function jc(a){const b=a.C,c=b[t]|0;return mb(a,c)?new a.constructor(ic(b,c,!0)):a} 
function kc(a){if(a.i!==nb)return!1;let b=a.C;b=ic(b,b[t]|0,!0);a.C=b;a.i=void 0;a.F=void 0;return!0}function lc(a){if(!kc(a)&&mb(a,a.C[t]|0))throw Error();};const mc=ub(0),nc={};function z(a,b,c,d,e){b=oc(a.C,b,c,e);if(b!==null||d&&a.F!==nb)return b}function oc(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function pc(a,b,c){lc(a);const d=a.C;A(d,d[t]|0,b,c);return a} 
function A(a,b,c,d,e){const f=c+(e?0:-1);var g=a.length-1;if(g>=1+(e?0:-1)&&f>=g){const h=a[g];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(f<=g)return a[f]=d,b;d!==void 0&&(g=(b??(b=a[t]|0))>>14&1023||536870912,c>=g?d!=null&&(a[g+(e?0:-1)]={[c]:d}):a[f]=d);return b}function qc(a,b,c){a=a.C;return rc(a,a[t]|0,b,c)!==void 0}function B(a){return a===ob?2:4} 
function sc(a,b,c,d,e){let f=a.C,g=f[t]|0;d=mb(a,g)?1:d;e=!!e||d===3;d===2&&kc(a)&&(f=a.C,g=f[t]|0);a=tc(f,b);let h=a===hb?7:a[t]|0,k=uc(h,g);var n=4&k?!1:!0;if(n){4&k&&(a=[...a],h=0,k=vc(k,g),g=A(f,g,b,a));let l=0,m=0;for(;l<a.length;l++){const p=c(a[l]);p!=null&&(a[m++]=p)}m<l&&(a.length=m);c=(k|4)&-513;k=c&=-1025;k&=-8193}k!==h&&(a[t]=k,2&k&&Object.freeze(a));return a=wc(a,k,f,g,b,d,n,e)} 
function wc(a,b,c,d,e,f,g,h){let k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?xc(b)||(b|=!a.length||g&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==k&&(a[t]=b),Object.freeze(a)):(f===2&&xc(b)&&(a=[...a],k=0,b=vc(b,d),A(c,d,e,a)),xc(b)||(h||(b|=16),b!==k&&(a[t]=b)));return a}function tc(a,b){a=oc(a,b);return Array.isArray(a)?a:hb}function uc(a,b){2&b&&(a|=2);return a|1}function xc(a){return!!(2&a)&&!!(4&a)||!!(256&a)} 
function yc(a,b,c,d){lc(a);const e=a.C;let f=e[t]|0;if(c==null)return A(e,f,b),a;let g=c===hb?7:c[t]|0,h=g;var k=xc(g);let n=k||Object.isFrozen(c);k||(g=0);n||(c=[...c],h=0,g=vc(g,f),n=!1);g|=5;k=jb(g)??0;for(let l=0;l<c.length;l++){const m=c[l],p=d(m,k);Object.is(m,p)||(n&&(c=[...c],h=0,g=vc(g,f),n=!1),c[l]=p)}g!==h&&(n&&(c=[...c],g=vc(g,f)),c[t]=g);A(e,f,b,c);return a}function zc(a,b,c,d){lc(a);const e=a.C;A(e,e[t]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a} 
function Ac(a,b,c,d){lc(a);const e=a.C;var f=e[t]|0;if(d==null){var g=Bc(e);if(Cc(g,e,f,c)===b)g.set(c,0);else return a}else{g=Bc(e);const h=Cc(g,e,f,c);h!==b&&(h&&(f=A(e,f,h)),g.set(c,b))}A(e,f,b,d);return a}function Dc(a,b,c){return Ec(a,b)===c?c:-1}function Ec(a,b){a=a.C;return Cc(Bc(a),a,void 0,b)}function Bc(a){return a[eb]??(a[eb]=new Map)} 
function Cc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];oc(b,g)!=null&&(e!==0&&(c=A(b,c,e)),e=g)}a.set(d,e);return e}function Fc(a,b,c){lc(a);a=a.C;let d=a[t]|0;const e=oc(a,c);b=jc($b(e,b,!0,d));e!==b&&A(a,d,c,b);return b}function rc(a,b,c,d){a=oc(a,d,void 0,e=>$b(e,c,!1,b));if(a!=null)return a}function Gc(a,b,c){a=a.C;(c=rc(a,a[t]|0,b,c))||(c=b[cb])||(a=new b,c=a.C,c[t]|=34,c=b[cb]=a);return c} 
function C(a,b,c){let d=a.C,e=d[t]|0;b=rc(d,e,b,c);if(b==null)return b;e=d[t]|0;if(!mb(a,e)){const f=jc(b);f!==b&&(kc(a)&&(d=a.C,e=d[t]|0),b=f,A(d,e,c,b))}return b} 
function D(a,b,c,d){var e=a.C,f=e;e=e[t]|0;var g=mb(a,e);const h=g?1:d;d=h===3;var k=!g;(h===2||k)&&kc(a)&&(f=a.C,e=f[t]|0);a=tc(f,c);var n=a===hb?7:a[t]|0,l=uc(n,e);if(g=!(4&l)){var m=a,p=e;const v=!!(2&l);v&&(p|=2);let u=!v,y=!0,I=0,N=0;for(;I<m.length;I++){const Ta=$b(m[I],b,!1,p);if(Ta instanceof b){if(!v){const Ua=mb(Ta);u&&(u=!Ua);y&&(y=Ua)}m[N++]=Ta}}N<I&&(m.length=N);l|=4;l=y?l&-8193:l|8192;l=u?l|8:l&-9}l!==n&&(a[t]=l,2&l&&Object.freeze(a));if(k&&!(8&l||!a.length&&(h===1||(h!==4?0:2&l||!(16& 
l)&&32&e)))){xc(l)&&(a=[...a],l=vc(l,e),e=A(f,e,c,a));b=a;k=l;for(n=0;n<b.length;n++)m=b[n],l=jc(m),m!==l&&(b[n]=l);k|=8;l=k=b.length?k|8192:k&-8193;a[t]=l}return a=wc(a,l,f,e,c,h,g,d)}function Hc(a){a==null&&(a=void 0);return a}function Ic(a,b,c){c=Hc(c);pc(a,b,c);return a}function Jc(a,b,c,d){d=Hc(d);Ac(a,b,c,d);return a} 
function Kc(a,b,c){lc(a);const d=a.C;let e=d[t]|0;if(c==null)return A(d,e,b),a;let f=c===hb?7:c[t]|0,g=f;const h=xc(f),k=h||Object.isFrozen(c);let n=!0,l=!0;for(let p=0;p<c.length;p++){var m=c[p];h||(m=mb(m),n&&(n=!m),l&&(l=m))}h||(f=n?13:5,f=l?f&-8193:f|8192);k&&f===g||(c=[...c],g=0,f=vc(f,e));f!==g&&(c[t]=f);A(d,e,b,c);return a}function vc(a,b){return a=(2&b?a|2:a&-3)&-273} 
function Lc(a,b){lc(a);a=sc(a,4,w,2,!0);const c=jb(a===hb?7:a[t]|0)??0;if(Array.isArray(b)){var d=b.length;for(let e=0;e<d;e++)a.push(Yb(b[e],c))}else for(d of b)a.push(Yb(d,c))}function Mc(a,b){a=z(a,b);b=typeof a;a!=null&&(b==="bigint"?a=ub(Fb(64,a)):Nb(a)?b==="string"?(b=Ib(Number(a)),Gb(b)?a=ub(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=ub(Fb(64,BigInt(a))))):a=Gb(a)?ub(Vb(a)):ub(Xb(a)):a=void 0);return a}function Nc(a,b,c){return Rb(z(a,b,void 0,c))} 
function E(a,b){return Lb(z(a,b))??!1}function F(a,b){return Nc(a,b)??0}function Oc(a,b){return z(a,b,void 0,void 0,Jb)??0}function G(a,b){return w(z(a,b))??""}function H(a,b){return Pb(z(a,b))??0}function Pc(a,b,c){return H(a,Dc(a,c,b))}function Qc(a,b,c,d){return C(a,b,Dc(a,d,c))}function Rc(a,b){return w(z(a,b,void 0,nc))}function Sc(a,b){return Pb(z(a,b,void 0,nc))}function Tc(a,b,c){return pc(a,b,c==null?c:Qb(c))}function Uc(a,b,c){return zc(a,b,c==null?c:Qb(c),0)} 
function Vc(a,b,c){return zc(a,b,c==null?c:Tb(c),"0")}function Wc(a,b){var c=performance.now();if(c!=null&&typeof c!=="number")throw Error(`Value of float/double field must be a number, found ${typeof c}: ${c}`);zc(a,b,c,0)}function Xc(a,b,c){return pc(a,b,Zb(c))}function Yc(a,b,c){return zc(a,b,Zb(c),"")}function Zc(a,b,c){return pc(a,b,c==null?c:Ob(c))}function $c(a,b,c){return zc(a,b,c==null?c:Ob(c),0)}function ad(a,b,c,d){return Ac(a,b,c,d==null?d:Ob(d))};function cd(a){const b=a.C,c=b[t]|0;return mb(a,c)?a:new a.constructor(ic(b,c))}var J=class{constructor(a){this.C=fc(a)}toJSON(){return x(this)}};J.prototype[gb]=lb;function dd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(kb(b))};function ed(a){return()=>{var b;if(!(b=a[cb])){const c=new a;b=c.C;b[t]|=34;b=a[cb]=c}return b}}function fd(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(kb(b))}return b}};var gd=class extends J{};var hd=class extends J{};let id,jd=64;function kd(){try{return id??(id=new Uint32Array(64)),jd>=64&&(crypto.getRandomValues(id),jd=0),id[jd++]}catch(a){return Math.floor(Math.random()*2**32)}};function ld(a,b){if(!qb(a.goog_pvsid))try{const c=kd()+(kd()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(c){b.ka({methodName:784,ua:c})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.ka({methodName:784,ua:Error(`Invalid correlator, ${a}`)});return a||-1};function md(a){return function(){return!a.apply(this,arguments)}}function nd(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function od(a){let b=a;return function(){if(b){const c=b;b=null;c()}}};function pd(){return wa&&Aa?Aa.mobile:!qd()&&(r("iPod")||r("iPhone")||r("Android")||r("IEMobile"))}function qd(){return wa&&Aa?!Aa.mobile&&(r("iPad")||r("Android")||r("Silk")):r("iPad")||r("Android")&&!r("Mobile")||r("Silk")};function rd(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function sd(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function td(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function ud(a){const b={};for(const c in a)b[c]=a[c];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let vd=globalThis.trustedTypes,wd;function xd(){let a=null;if(!vd)return a;try{const b=c=>c;a=vd.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var yd=class{constructor(a){this.g=a}toString(){return this.g+""}};function zd(a){var b;wd===void 0&&(wd=xd());a=(b=wd)?b.createScriptURL(a):a;return new yd(a)}function Ad(a){if(a instanceof yd)return a.g;throw Error("");};var Bd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Cd(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};const Dd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Ed(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};var Fd=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Gd=/#|$/;function Hd(a,b){const c=a.search(Gd);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Id(a,...b){if(b.length===0)return zd(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return zd(c)}function Jd(a,b){a=Ad(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Kd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Kd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return zd(a+b+c)};function Ld(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Pa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Md(a){return Ld(a.top)?a.top:null}function Nd(a,b){const c=Od("SCRIPT",a);c.src=Ad(b);(b=Cd(c.ownerDocument))&&c.setAttribute("nonce",b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function Pd(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function Qd(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Rd(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Sd(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c}var Td=/^([0-9.]+)px$/,Ud=/^(-?[0-9.]{1,30})$/; 
function Vd(a){if(!Ud.test(a))return null;a=Number(a);return isNaN(a)?null:a}function Wd(a){return(a=Td.exec(a))?+a[1]:null}var Xd=nd(()=>pd()?2:qd()?1:0),Yd=a=>{Rd({display:"none"},(b,c)=>{a.style.setProperty(c,b,"important")})};let Zd=[];const $d=()=>{const a=Zd;Zd=[];for(const b of a)try{b()}catch{}};function ae(){var a=K(be).A(ce.g,ce.defaultValue),b=L.document;if(a.length&&b.head)for(const c of a)c&&b.head&&(a=Od("META"),b.head.appendChild(a),a.httpEquiv="origin-trial",a.content=c)} 
var de=a=>ld(a,{ka:()=>{}}),fe=a=>{var b=ee;b.readyState==="complete"||b.readyState==="interactive"?(Zd.push(a),Zd.length==1&&(window.Promise?Promise.resolve().then($d):window.setImmediate?setImmediate($d):setTimeout($d,0))):b.addEventListener("DOMContentLoaded",a)};function Od(a,b=document){return b.createElement(String(a).toLowerCase())};function ge(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function he(a,b,c){return typeof a.removeEventListener==="function"?(a.removeEventListener(b,c,!1),!0):!1};function ie(a,b,c=null,d=!1,e=!1){je(a,b,c,d,e)}function je(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Od("IMG",a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const k=Ia(h,f);k>=0&&Array.prototype.splice.call(h,k,1)}he(f,"load",g);he(f,"error",g)};ge(f,"load",g);ge(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function ke(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;Rd(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});le(c)}function le(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):ie(b,a,void 0,!1,!1)};var ee=document,L=window;let me=null;var ne=(a,b=[])=>{let c=!1;q.google_logging_queue||(c=!0,q.google_logging_queue=[]);q.google_logging_queue.push([a,b]);if(a=c){if(me==null){me=!1;try{const d=Md(q);d&&d.location.hash.indexOf("google_logging")!==-1&&(me=!0)}catch(d){}}a=me}a&&Nd(q.document,Id`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function oe(a,b){this.width=a;this.height=b}oe.prototype.aspectRatio=function(){return this.width/this.height};oe.prototype.isEmpty=function(){return!(this.width*this.height)};oe.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};oe.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};oe.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
oe.prototype.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function pe(a=q){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function qe(a=pe()){return a?Ld(a.master)?a.master:null:null};function re(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function se(a){this.g=a||q.document||document}se.prototype.contains=function(a,b){return a&&b?a==b||a.contains(b):!1};var te=a=>{a=qe(pe(a))||a;a.google_unique_id=(a.google_unique_id||0)+1;return a.google_unique_id},ue=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},ve=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};function we(a){return!!(a.error&&a.meta&&a.id)}var xe=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function ye(a){return new xe(a,{message:Ae(a)})}function Ae(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const Be=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Ce=class{constructor(a,b){this.g=a;this.i=b}},De=class{constructor(a,b,c){this.url=a;this.l=b;this.g=!!c;this.depth=null}};let Ee=null;function Fe(){var a=window;if(Ee===null){Ee="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);Ee=c?c[1]:""}}catch(b){}}return Ee};function Ge(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function He(){const a=q.performance;return a&&a.now?a.now():null};var Ie=class{constructor(a,b){var c=He()||Ge();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Je=q.performance,Ke=!!(Je&&Je.mark&&Je.measure&&Je.clearMarks),Le=nd(()=>{var a;if(a=Ke)a=Fe(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function Me(a){a&&Je&&Le()&&(Je.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Je.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function Ne(a){a.g=!1;if(a.i!==a.j.google_js_reporting_queue){if(Le()){var b=a.i;const c=b.length;b=typeof b==="string"?b.split(""):b;for(let d=0;d<c;d++)d in b&&Me.call(void 0,b[d])}a.i.length=0}} 
var Oe=class{constructor(a){this.i=[];this.j=a||q;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Le()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Ie(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Je&&Le()&&Je.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(He()||Ge())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Je&&Le()&&Je.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Pe(a,b){const c={};c[a]=b;return[c]}function Qe(a,b,c,d,e){const f=[];Rd(a,(g,h)=>{(g=Re(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Re(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Re(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Qe(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Se(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function Te(a,b,c){b="https://"+b+c;let d=Se(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let n=Qe(h[k],a.j,",$");if(n){n=e+n;if(d>=n.length){d-=n.length;b+=n;e=a.j;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var Ue=class{constructor(){this.j="&";this.i={};this.u=0;this.g=[]}};var Xe=class{constructor(a=null){this.F=Ve;this.j=a;this.i=null;this.B=!1;this.D=this.I}J(a){this.D=a}A(a){this.i=a}Y(a){this.B=a}g(a,b,c){let d,e;try{this.j&&this.j.g?(e=this.j.start(a.toString(),3),d=b(),this.j.end(e)):d=b()}catch(f){b=!0;try{Me(e),b=this.D(a,ye(f),void 0,c)}catch(g){this.I(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}I(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const db=new Ue;var g=db;g.g.push(1);g.i[1]=Pe("context", 
a);we(b)||(b=ye(b));g=b;if(g.msg){b=db;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Pe("msg",h)}var k=g.meta||{};h=k;if(this.i)try{this.i(h)}catch(W){}if(d)try{d(h)}catch(W){}d=db;k=[k];d.g.push(3);d.i[3]=k;var n;if(!(n=p)){d=q;k=[];h=null;do{var l=d;if(Ld(l)){var m=l.location.href;h=l.document&&l.document.referrer||null}else m=h,h=null;k.push(new De(m||"",l));try{d=l.parent}catch(W){d=null}}while(d&&l!==d);for(let W=0,Lg=k.length-1;W<=Lg;++W)k[W].depth=Lg-W;l=q;if(l.location&&l.location.ancestorOrigins&& 
l.location.ancestorOrigins.length===k.length-1)for(m=1;m<k.length;++m){const W=k[m];W.url||(W.url=l.location.ancestorOrigins[m-1]||"",W.g=!0)}n=k}var p=n;let bd=new De(q.location.href,q,!1);n=null;const ze=p.length-1;for(l=ze;l>=0;--l){var v=p[l];!n&&Be.test(v.url)&&(n=v);if(v.url&&!v.g){bd=v;break}}v=null;const Fk=p.length&&p[ze].url;bd.depth!==0&&Fk&&(v=p[ze]);f=new Ce(bd,v);if(f.i){p=db;var u=f.i.url||"";p.g.push(4);p.i[4]=Pe("top",u)}var y={url:f.g.url||""};if(f.g.url){const W=f.g.url.match(Fd); 
var I=W[1],N=W[3],Ta=W[4];u="";I&&(u+=I+":");N&&(u+="//",u+=N,Ta&&(u+=":"+Ta));var Ua=u}else Ua="";I=db;y=[y,{url:Ua}];I.g.push(5);I.i[5]=y;We(this.F,e,db,this.B,c)}catch(db){try{We(this.F,e,{context:"ecmserr",rctx:a,msg:Ae(db),url:f?.g.url??""},this.B,c)}catch(bd){}}return!0}na(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,this.i||void 0)})}};var Ye=class extends J{},Ze=[2,3,4];var $e=class extends J{},af=[3,4,5],bf=[6,7];var cf=class extends J{},df=[4,5];function ef(a,b){var c=D(a,$e,2,B());if(!c.length)return ff(a,b);a=H(a,1);if(a===1)return c=ef(c[0],b),c.success?{success:!0,value:!c.value}:c;c=Ka(c,d=>ef(d,b));switch(a){case 2:return c.find(d=>d.success&&!d.value)??c.find(d=>!d.success)??{success:!0,value:!0};case 3:return c.find(d=>d.success&&d.value)??c.find(d=>!d.success)??{success:!0,value:!1};default:return{success:!1,O:3}}} 
function ff(a,b){var c=Ec(a,af);a:{switch(c){case 3:var d=Pc(a,3,af);break a;case 4:d=Pc(a,4,af);break a;case 5:d=Pc(a,5,af);break a}d=void 0}if(!d)return{success:!1,O:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,da:c,O:1};let e;try{var f=sc(a,8,w,B());e=b(...f)}catch(g){return{success:!1,property:d,da:c,O:2}}f=H(a,1);if(f===4)return{success:!0,value:!!e};if(f===5)return{success:!0,value:e!=null};if(f===12)a=G(a,Dc(a,bf,7));else a:{switch(c){case 4:a=Oc(a,Dc(a,bf,6));break a;case 5:a=G(a, 
Dc(a,bf,7));break a}a=void 0}if(a==null)return{success:!1,property:d,da:c,O:3};if(f===6)return{success:!0,value:e===a};if(f===9)return{success:!0,value:e!=null&&ua(String(e),a)===0};if(e==null)return{success:!1,property:d,da:c,O:4};switch(f){case 7:c=e<a;break;case 8:c=e>a;break;case 12:c=rb(a)&&rb(e)&&(new RegExp(a)).test(e);break;case 10:c=e!=null&&ua(String(e),a)===-1;break;case 11:c=e!=null&&ua(String(e),a)===1;break;default:return{success:!1,O:3}}return{success:!0,value:c}} 
function gf(a,b){return a?b?ef(a,b):{success:!1,O:1}:{success:!0,value:!0}};function hf(a){return sc(a,4,w,B())}var jf=class extends J{};var kf=class extends J{getValue(){return C(this,jf,2)}};var lf=class extends J{},mf=fd(lf),nf=[1,2,3,6,7,8];var of=class extends J{};function pf(a,b){try{const c=d=>[{[d.Ca]:d.Aa}];return JSON.stringify([a.filter(d=>d.la).map(c),x(b),a.filter(d=>!d.la).map(c)])}catch(c){return qf(c,b),""}}function qf(a,b){try{ke({m:Ae(a instanceof Error?a:Error(String(a))),b:H(b,1)||null,v:G(b,2)||null},"rcs_internal")}catch(c){}}var rf=class{constructor(a,b){var c=new of;a=$c(c,1,a);b=Yc(a,2,b);this.j=cd(b)}};var sf=class extends J{getWidth(){return F(this,3)}getHeight(){return F(this,4)}};var tf=class extends J{};function uf(a,b){return pc(a,1,b==null?b:Tb(b))}function vf(a,b){return pc(a,2,b==null?b:Tb(b))}var wf=class extends J{getWidth(){return Mc(this,1)??mc}getHeight(){return Mc(this,2)??mc}};var xf=class extends J{};var yf=class extends J{};var zf=class extends J{getValue(){return H(this,1)}};var Af=class extends J{getContentUrl(){return G(this,4)}};var Bf=class extends J{};function Cf(a){return Fc(a,Bf,3)}var Df=class extends J{};var Ef=class extends J{getContentUrl(){return G(this,1)}};var Ff=class extends J{};function Gf(a){var b=new Hf;return $c(b,1,a)}var Hf=class extends J{};var If=class extends J{},Jf=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Kf=class extends J{};function Lf(a,b){return $c(a,1,b)}function Mf(a,b){return $c(a,2,b)}var Nf=class extends J{};var Of=class extends J{},Pf=[1,2];function Qf(a,b){return Ic(a,1,b)}function Rf(a,b){return Kc(a,2,b)}function Sf(a,b){return yc(a,4,b,Qb)}function Tf(a,b){return Kc(a,5,b)}function Uf(a,b){return $c(a,6,b)}var Vf=class extends J{};var Wf=class extends J{},Xf=[1,2,3,4,6];var Yf=class extends J{};function Zf(a){var b=new $f;return Jc(b,4,ag,a)}var $f=class extends J{getTagSessionCorrelator(){return Mc(this,2)??mc}},ag=[4,5,7,8,9];var bg=class extends J{};function cg(){var a=dg();a=jc(a);return Yc(a,1,eg())}var fg=class extends J{};var gg=class extends J{};var hg=class extends J{getTagSessionCorrelator(){return Mc(this,1)??mc}};var ig=class extends J{},jg=[1,7],kg=[4,6,8];class lg extends rf{constructor(){super(...arguments)}}function mg(a,...b){ng(a,...b.map(c=>({la:!0,Ca:3,Aa:x(c)})))}function og(a,...b){ng(a,...b.map(c=>({la:!0,Ca:4,Aa:x(c)})))}function pg(a,...b){ng(a,...b.map(c=>({la:!0,Ca:7,Aa:x(c)})))}var qg=class extends lg{};function rg(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function ng(a,...b){try{a.D&&pf(a.g.concat(b),a.j).length>=65536&&sg(a),a.u&&!a.A&&(a.A=!0,tg(a.u,()=>{sg(a)})),a.g.push(...b),a.g.length>=a.B&&sg(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{sg(a)},a.J))}catch(c){qf(c,a.j)}}function sg(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=pf(a.g,a.j);a.F("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var ug=class extends qg{constructor(a,b,c,d,e,f){super(a,b);this.F=rg;this.J=c;this.B=d;this.D=e;this.u=f;this.g=[];this.i=null;this.A=!1}},vg=class extends ug{constructor(a,b,c=1E3,d=100,e=!1,f){super(a,b,c,d,e&&!0,f)}};function wg(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=Vc(b,1,c);c=de(window);b=Vc(b,2,c);return Vc(b,6,a.A)}function xg(a,b,c,d,e,f){if(a.j){var g=Mf(Lf(new Nf,b),c);b=Uf(Rf(Qf(Tf(Sf(new Vf,d),e),g),a.g.slice()),f);b=Zf(b);og(a.i,wg(a,b));if(f===1||f===3||f===4&&!a.g.some(h=>H(h,1)===H(g,1)&&H(h,2)===c))a.g.push(g),a.g.length>100&&a.g.shift()}}function yg(a,b,c,d){if(a.j){var e=new Kf;b=Tc(e,1,b);c=Tc(b,2,c);d=Zc(c,3,d);c=new $f;d=Jc(c,8,ag,d);og(a.i,wg(a,d))}} 
function zg(a,b,c,d,e){if(a.j){var f=new cf;b=Ic(f,1,b);c=Zc(b,2,c);d=Tc(c,3,d);if(e.da===void 0)ad(d,4,df,e.O);else switch(e.da){case 3:c=new Ye;c=ad(c,2,Ze,e.property);e=Zc(c,1,e.O);Jc(d,5,df,e);break;case 4:c=new Ye;c=ad(c,3,Ze,e.property);e=Zc(c,1,e.O);Jc(d,5,df,e);break;case 5:c=new Ye,c=ad(c,4,Ze,e.property),e=Zc(c,1,e.O),Jc(d,5,df,e)}e=new $f;e=Jc(e,9,ag,d);og(a.i,wg(a,e))}}var Ag=class{constructor(a,b,c,d=new vg(6,"unknown",b)){this.A=a;this.u=c;this.i=d;this.g=[];this.j=a>0&&Qd()<1/a}};var K=a=>{var b="za";if(a.za&&a.hasOwnProperty(b))return a.za;b=new a;return a.za=b};var Bg=class{constructor(){this.N={[3]:{},[4]:{},[5]:{}}}};var Cg=/^true$/.test("false");function Dg(a,b){switch(b){case 1:return Pc(a,1,nf);case 2:return Pc(a,2,nf);case 3:return Pc(a,3,nf);case 6:return Pc(a,6,nf);case 8:return Pc(a,8,nf);default:return null}}function Eg(a,b){if(!a)return null;switch(b){case 1:return E(a,1);case 7:return G(a,3);case 2:return Oc(a,2);case 3:return G(a,3);case 6:return hf(a);case 8:return hf(a);default:return null}}const Fg=nd(()=>{if(!Cg)return{};try{var a=window;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch{b=null}if(b)return JSON.parse(b)}catch{}return{}}); 
function Gg(a,b,c,d=0){K(Hg).j[d]=K(Hg).j[d]?.add(b)??(new Set).add(b);const e=Fg();if(e[b]!=null)return e[b];b=Ig(d)[b];if(!b)return c;b=mf(JSON.stringify(b));b=Jg(b);a=Eg(b,a);return a!=null?a:c}function Jg(a){const b=K(Bg).N;if(b&&Ec(a,nf)!==8){const c=Ma(D(a,kf,5,B()),d=>{d=gf(C(d,$e,1),b);return d.success&&d.value});if(c)return c.getValue()??null}return C(a,jf,4)??null}class Hg{constructor(){this.i={};this.u=[];this.j={};this.g=new Map}}function Kg(a,b=!1,c){return!!Gg(1,a,b,c)} 
function Mg(a,b=0,c){a=Number(Gg(2,a,b,c));return isNaN(a)?b:a}function Ng(a,b="",c){a=Gg(3,a,b,c);return typeof a==="string"?a:b}function Og(a,b=[],c){a=Gg(6,a,b,c);return Array.isArray(a)?a:b}function Pg(a,b=[],c){a=Gg(8,a,b,c);return Array.isArray(a)?a:b}function Ig(a){return K(Hg).i[a]||(K(Hg).i[a]={})} 
function Qg(a,b){const c=Ig(b);Rd(a,(d,e)=>{if(c[e]){var f=d=mf(JSON.stringify(d)),g=Dc(d,nf,8);Pb(z(f,g))!=null&&(g=mf(JSON.stringify(c[e])),f=Fc(d,jf,4),g=hf(Gc(g,jf,4)),Lc(f,g));c[e]=x(d)}else c[e]=d})} 
function Rg(a,b,c,d,e=!1){var f=[],g=[];for(const m of b){b=Ig(m);for(const p of a){var h=Ec(p,nf);const v=Dg(p,h);if(v){a:{var k=v;var n=h,l=K(Hg).g.get(m)?.get(v)?.slice(0)??[];const u=new Wf;switch(n){case 1:ad(u,1,Xf,k);break;case 2:ad(u,2,Xf,k);break;case 3:ad(u,3,Xf,k);break;case 6:ad(u,4,Xf,k);break;case 8:ad(u,6,Xf,k);break;default:k=void 0;break a}yc(u,5,l,Qb);k=u}k&&K(Hg).j[m]?.has(v)&&f.push(k);h===8&&b[v]?(k=mf(JSON.stringify(b[v])),h=Fc(p,jf,4),k=hf(Gc(k,jf,4)),Lc(h,k)):k&&K(Hg).g.get(m)?.has(v)&& 
g.push(k);e||(h=v,k=m,n=d,l=K(Hg),l.g.has(k)||l.g.set(k,new Map),l.g.get(k).has(h)||l.g.get(k).set(h,[]),n&&l.g.get(k).get(h).push(n));b[v]=x(p)}}}if(f.length||g.length)a=d??void 0,c.j&&c.u&&(d=new Yf,f=Kc(d,2,f),g=Kc(f,3,g),a&&Uc(g,1,a),f=new $f,g=Jc(f,7,ag,g),og(c.i,wg(c,g)))}function Sg(a,b){b=Ig(b);for(const c of a){a=mf(JSON.stringify(c));const d=Ec(a,nf);(a=Dg(a,d))&&(b[a]||(b[a]=c))}}function Tg(){return Object.keys(K(Hg).i).map(a=>Number(a))} 
function Ug(a){K(Hg).u.includes(a)||Qg(Ig(4),a)};function M(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Vg(a,b,c){return b[a]||c}function Wg(a){M(5,Kg,a);M(6,Mg,a);M(7,Ng,a);M(8,Og,a);M(17,Pg,a);M(13,Sg,a);M(15,Ug,a)}function Xg(a){M(4,b=>{K(Bg).N=b},a);M(9,(b,c)=>{var d=K(Bg);d.N[3][b]==null&&(d.N[3][b]=c)},a);M(10,(b,c)=>{var d=K(Bg);d.N[4][b]==null&&(d.N[4][b]=c)},a);M(11,(b,c)=>{var d=K(Bg);d.N[5][b]==null&&(d.N[5][b]=c)},a);M(14,b=>{var c=K(Bg);for(const d of[3,4,5])Object.assign(c.N[d],b[d])},a)} 
function Yg(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};function Zg(a,b,c){a.j=Vg(1,b,()=>{});a.u=(d,e)=>Vg(2,b,()=>[])(d,c,e);a.g=()=>Vg(3,b,()=>[])(c);a.i=d=>{Vg(16,b,()=>{})(d,c)}}class $g{j(){}i(){}u(){return[]}g(){return[]}};function We(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Ue?f=c:(f=new Ue,Rd(c,(h,k)=>{var n=f;const l=n.u++;h=Pe(k,h);n.g.push(l);n.i[l]=h}));const g=Te(f,a.domain,a.path+b+"&");g&&ie(q,g)}catch(f){}}function ah(a,b){b>=0&&b<=1&&(a.g=b)}var bh=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Ve,ch;const dh=new Oe(window);(function(a){Ve=a??new bh;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());ah(Ve,window.google_srt);ch=new Xe(dh);ch.A(()=>{});ch.Y(!0);window.document.readyState==="complete"?window.google_measure_js_timing||Ne(dh):dh.g&&ge(window,"load",()=>{window.google_measure_js_timing||Ne(dh)})})();let eh=(new Date).getTime();var fh={Yb:0,Xb:1,Ub:2,Pb:3,Vb:4,Qb:5,Wb:6,Sb:7,Tb:8,Ob:9,Rb:10,Zb:11};var gh={bc:0,dc:1,ac:2};function hh(a){if(a.g!=0)throw Error("Already resolved/rejected.");}var kh=class{constructor(){this.i=new ih(this);this.g=0}resolve(a){hh(this);this.g=1;this.u=a;jh(this.i)}reject(a){hh(this);this.g=2;this.j=a;jh(this.i)}};function jh(a){switch(a.g.g){case 0:break;case 1:a.i&&a.i(a.g.u);break;case 2:a.j&&a.j(a.g.j);break;default:throw Error("Unhandled deferred state.");}}var ih=class{constructor(a){this.g=a}then(a,b){if(this.i)throw Error("Then functions already set.");this.i=a;this.j=b;jh(this)}};var lh=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new lh(Ja(this.g,a))}apply(a){return new lh(a(this.g.slice(0)))}sort(a){return new lh(this.g.slice(0).sort(a))}get(a){return this.g[a]}add(a){const b=this.g.slice(0);b.push(a);return new lh(b)}};function mh(a,b){const c=[],d=a.length;for(let e=0;e<d;e++)c.push(a[e]);c.forEach(b,void 0)};var oh=class{constructor(){this.g={};this.i={}}set(a,b){const c=nh(a);this.g[c]=b;this.i[c]=a}get(a,b){a=nh(a);return this.g[a]!==void 0?this.g[a]:b}clear(){this.g={};this.i={}}};function nh(a){return a instanceof Object?String(la(a)):a+""};function ph(a){return new qh({value:a},null)}function rh(a){return new qh(null,a)}function sh(a){try{return ph(a())}catch(b){return rh(b)}}function th(a){return a.g!=null?a.getValue():null}function uh(a,b){a.g!=null&&b(a.getValue());return a}function vh(a,b){a.g!=null||b(a.i);return a}var qh=class{constructor(a,b){this.g=a;this.i=b}getValue(){return this.g.value}map(a){return this.g!=null?(a=a(this.getValue()),a instanceof qh?a:ph(a)):this}};var wh=class{constructor(a){this.g=new oh;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return this.g.g[nh(a)]!==void 0}};var xh=class{constructor(){this.g=new oh}set(a,b){let c=this.g.get(a);c||(c=new wh,this.g.set(a,c));c.add(b)}};var yh=class extends J{getId(){return Rc(this,3)}};var zh=class{constructor({lb:a,fc:b,tc:c,Fb:d}){this.g=b;this.u=new lh(a||[]);this.j=d;this.i=c}};const Bh=a=>{const b=[],c=a.u;c&&c.g.length&&b.push({ba:"a",ca:Ah(c)});a.g!=null&&b.push({ba:"as",ca:a.g});a.i!=null&&b.push({ba:"i",ca:String(a.i)});a.j!=null&&b.push({ba:"rp",ca:String(a.j)});b.sort(function(d,e){return d.ba.localeCompare(e.ba)});b.unshift({ba:"t",ca:"aa"});return b},Ah=a=>{a=a.g.slice(0).map(Ch);a=JSON.stringify(a);return Sd(a)},Ch=a=>{const b={};w(z(a,7))!=null&&(b.q=Rc(a,7));Nc(a,2)!=null&&(b.o=Nc(a,2,nc));Nc(a,5)!=null&&(b.p=Nc(a,5,nc));return b};function Dh(a){return Sc(a,2)}var Eh=class extends J{setLocation(a){return Zc(this,1,a)}};function Fh(a){const b=[].slice.call(arguments).filter(md(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Ta||[]);d=Object.assign(d,e.ab)});return new Gh(c,d)}function Hh(a){switch(a){case 1:return new Gh(null,{google_ad_semantic_area:"mc"});case 2:return new Gh(null,{google_ad_semantic_area:"h"});case 3:return new Gh(null,{google_ad_semantic_area:"f"});case 4:return new Gh(null,{google_ad_semantic_area:"s"});default:return null}} 
function Ih(a){if(a==null)var b=null;else{b=Gh;var c=Bh(a);a=[];for(let d of c)c=String(d.ca),a.push(d.ba+"."+(c.length<=20?c:c.slice(0,19)+"_"));b=new b(null,{google_placement_id:a.join("~")})}return b}var Gh=class{constructor(a,b){this.Ta=a;this.ab=b}};var Jh=new Gh(["google-auto-placed"],{google_reactive_ad_format:40,google_tag_origin:"qs"});var Kh=fd(class extends J{});function Lh(a){return C(a,yh,1)}function Mh(a){return Sc(a,2)}var Nh=class extends J{};var Oh=class extends J{};var Ph=class extends J{};function Qh(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b};function Rh(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){let d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Qh(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")};var O=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},P=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},Sh=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var Th=new P(1359),Uh=new P(1358),Vh=new O(1360),Wh=new P(1357),Xh=new O(1345),Yh=new P(1130,100),Zh=new P(1340,.2),$h=new P(1338,.3),ai=new P(1336,1),bi=new P(1339,.3),ci=new O(1337),di=new class{constructor(a,b=""){this.g=a;this.defaultValue=b}}(14),ei=new O(1342),fi=new O(1344),gi=new P(1343,300),hi=new O(1384),ii=new O(316),ji=new O(313),ki=new O(369),li=new O(1318,!0),mi=new O(626390500),ni=new Sh(635821288,["29_18","30_19"]),oi=new Sh(683929765),pi=new O(506914611),qi=new O(750577535),ri=new P(717888910, 
.7),si=new P(643258048,.15),ti=new P(643258049,.16),ui=new P(717888911,.7),vi=new P(717888912,.7),wi=new P(748662193,8),xi=new O(711741274),yi=new O(45650663),zi=new P(684147711,-1),Ai=new P(684147712,-1),Bi=new O(*********),Ci=new P(1079,5),Di=new O(10013),Ei=new O(750586557),ce=new class{constructor(a,b=[]){this.g=a;this.defaultValue=b}}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==", 
"Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9", 
"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]),Fi=new O(84);var be=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.i=(b,c)=>a[b]!=null?a[b]:c;this.A=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.B=()=>{}}};function Q(a){return K(be).j(a.g,a.defaultValue)}function R(a){return K(be).u(a.g,a.defaultValue)};function Gi(a,b){const c=e=>{e=Hi(e);return e==null?!1:0<e},d=e=>{e=Hi(e);return e==null?!1:0>e};switch(b){case 0:return{init:Ii(a.previousSibling,c),ga:e=>Ii(e.previousSibling,c),ma:0};case 2:return{init:Ii(a.lastChild,c),ga:e=>Ii(e.previousSibling,c),ma:0};case 3:return{init:Ii(a.nextSibling,d),ga:e=>Ii(e.nextSibling,d),ma:3};case 1:return{init:Ii(a.firstChild,d),ga:e=>Ii(e.nextSibling,d),ma:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Hi(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Ii(a,b){return a&&b(a)?a:null};var Ji={rectangle:1,horizontal:2,vertical:4};var Ki={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7};function Li(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}}function S(a){return Li(a).clientWidth??void 0};function Mi(a,b){do{const c=Pd(a,b);if(c&&c.position=="fixed")return!1}while(a=a.parentElement);return!0};function Ni(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=Wd(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function Oi(a,b){return!((Ud.test(b.google_ad_width)||Td.test(a.style.width))&&(Ud.test(b.google_ad_height)||Td.test(a.style.height)))}function Pi(a,b){return(a=Qi(a,b))?a.y:0} 
function Qi(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}} 
function Ri(a,b,c,d,e){if(a!==a.top)return Md(a)?3:16;if(!(S(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=S(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=S(a);c;c=c.parentElement)if((d=Pd(c,a))&&(e=Wd(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function Si(a,b,c,d){const e=Ri(b,c,a,R(bi),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Mi(c,b)?(b=S(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function Ti(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function Ui(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=Pd(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function Vi(a,b,c){a=Qi(b,a);return c==="rtl"?-a.x:a.x} 
function Wi(a,b){var c;c=(c=b.parentElement)?(c=Pd(c,a))?c.direction:"":"";if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";Ti(b,c,"0px");d.width=`${S(a)}px`;if(Vi(a,b,c)!==0){Ti(b,c,"0px");var e=Vi(a,b,c);Ti(b,c,`${-1*e}px`);a=Vi(a,b,c);a!==0&&a!==e&&Ti(b,c,`${e/(a-e)*e}px`)}d.zIndex="30"}};function Xi(a,b,c){let d;return a.style&&!!a.style[c]&&Wd(a.style[c])||(d=Pd(a,b))&&!!d[c]&&Wd(d[c])||null}function Yi(a,b){const c=ue(a)===0;return b&&c?Math.max(250,2*Li(a).clientHeight/3):250}function Zi(a,b){let c;return a.style&&a.style.zIndex||(c=Pd(a,b))&&c.zIndex||null}function $i(a){return b=>b.g<=a}function aj(a,b,c,d){const e=a&&bj(c,b),f=Yi(b,d);return g=>!(e&&g.height()>=f)}function cj(a){return b=>b.height()<=a}function bj(a,b){return Pi(a,b)<Li(b).clientHeight-100} 
function dj(a,b){var c=Xi(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=Xi(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&Wd(b.style.height))&&(c=Math.min(c,d)),(d=Xi(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};var ej={google_ad_channel:!0,google_ad_client:!0,google_ad_host:!0,google_ad_host_channel:!0,google_adtest:!0,google_tag_for_child_directed_treatment:!0,google_tag_for_under_age_of_consent:!0,google_tag_partner:!0,google_restrict_data_processing:!0,google_page_url:!0,google_debug_params:!0,google_adbreak_test:!0,google_ad_frequency_hint:!0,google_admob_interstitial_slot:!0,google_admob_rewarded_slot:!0,google_admob_ads_only:!0,google_ad_start_delay_hint:!0,google_max_ad_content_rating:!0,google_traffic_source:!0, 
google_overlays:!0,google_privacy_treatments:!0,google_special_category_data:!0,google_ad_intent_query:!0,google_ad_intent_qetid:!0,google_ad_intent_eids:!0,google_ad_intents_format:!0};const fj=RegExp("(^| )adsbygoogle($| )");function gj(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Ed(d.property);a[e]=d.value}};var hj=class extends J{g(){return Lb(z(this,23,void 0,nc))}};var ij=class extends J{g(){return Mc(this,1)}};var jj=class extends J{};var kj=class extends J{};var lj=class extends J{};var mj=class extends J{};var nj=class extends J{getName(){return Rc(this,4)}},oj=[1,2,3];var pj=class extends J{};var qj=class extends J{};var sj=class extends J{g(){return Qc(this,qj,2,rj)}},rj=[1,2];var tj=class extends J{g(){return C(this,sj,3)}};var uj=class extends J{},vj=fd(uj);function wj(a){const b=[];mh(a.getElementsByTagName("p"),function(c){xj(c)>=100&&b.push(c)});return b}function xj(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;mh(a.childNodes,function(c){b+=xj(c)});return b}function yj(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function zj(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Aj(a,b){var c=[];try{c=b.querySelectorAll(a.u)}catch(d){}if(!c.length)return[];b=Oa(c);b=zj(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=wj(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Bj=class{constructor(a,b,c,d){this.u=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.u,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Cj=class{constructor(){this.i=Id`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}I(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;we(b)||(b=new xe(b,{context:a,id:d}));q.google_js_errors=q.google_js_errors||[];q.google_js_errors.push(b);q.error_rep_loaded||(Nd(q.document,this.i),q.error_rep_loaded=!0);return!1}g(a,b){try{return b()}catch(c){if(!this.I(a,c,.01,"jserror"))throw c;}}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}na(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.I(a,c instanceof Error?c:Error(c),void 0)})}};function Dj(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Ej(a,b,c,d,e=!1){const f=d||window,g=typeof queueMicrotask!=="undefined";return function(...h){e&&g&&queueMicrotask(()=>{f.google_rum_task_id_counter=f.google_rum_task_id_counter||1;f.google_rum_task_id_counter+=1});const k=He();let n,l=3;try{n=b.apply(this,h)}catch(m){l=13;if(!c)throw m;c(a,m)}finally{f.google_measure_js_timing&&k&&Dj({label:a.toString(),value:k,duration:(He()||0)-k,type:l,...(e&&g&&{taskId:f.google_rum_task_id_counter=f.google_rum_task_id_counter||1})},f)}return n}} 
function Fj(a,b){return Ej(a,b,(c,d)=>{(new Cj).I(c,d)},void 0,!1)};function Gj(a,b,c){return Ej(a,b,void 0,c,!0).apply()}function Hj(a){if(!a)return null;var b=Rc(a,7);if(Rc(a,1)||a.getId()||sc(a,4,w,B()).length>0){var c=a.getId(),d=Rc(a,1),e=sc(a,4,w,B());b=Nc(a,2,nc);var f=Nc(a,5,nc);a=Ij(Sc(a,6));let g="";d&&(g+=d);c&&(g+="#"+yj(c));if(e)for(c=0;c<e.length;c++)g+="."+yj(e[c]);b=(e=g)?new Bj(e,b,f,a):null}else b=b?new Bj(b,Nc(a,2,nc),Nc(a,5,nc),Ij(Sc(a,6))):null;return b}const Jj={1:1,2:2,3:3,0:0};function Ij(a){return a==null?a:Jj[a]}const Kj={1:0,2:1,3:2,4:3}; 
function Lj(a){return a.google_ama_state=a.google_ama_state||{}}function Mj(a){a=Lj(a);return a.optimization=a.optimization||{}};var Nj=a=>{switch(Sc(a,8)){case 1:case 2:if(a==null)var b=null;else b=C(a,yh,1),b==null?b=null:(a=Sc(a,2),b=a==null?null:new zh({lb:[b],Fb:a}));return b!=null?ph(b):rh(Error("Missing dimension when creating placement id"));case 3:return rh(Error("Missing dimension when creating placement id"));default:return b="Invalid type: "+Sc(a,8),rh(Error(b))}};var Oj=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function Pj(a,b){const c=new xh,d=new wh;b.forEach(e=>{if(Qc(e,lj,1,oj)){e=Qc(e,lj,1,oj);if(C(e,Nh,1)&&Lh(C(e,Nh,1))&&C(e,Nh,2)&&Lh(C(e,Nh,2))){const g=Qj(a,Lh(C(e,Nh,1))),h=Qj(a,Lh(C(e,Nh,2)));if(g&&h)for(var f of Oj({anchor:g,position:Mh(C(e,Nh,1))},{anchor:h,position:Mh(C(e,Nh,2))}))c.set(la(f.anchor),f.position)}C(e,Nh,3)&&Lh(C(e,Nh,3))&&(f=Qj(a,Lh(C(e,Nh,3))))&&c.set(la(f),Mh(C(e,Nh,3)))}else Qc(e,mj,2,oj)?Rj(a,Qc(e,mj,2,oj),c):Qc(e,kj,3,oj)&&Sj(a,Qc(e,kj,3,oj),d)});return new Tj(c,d)} 
var Tj=class{constructor(a,b){this.i=a;this.g=b}};const Rj=(a,b,c)=>{C(b,Nh,2)?(b=C(b,Nh,2),(a=Qj(a,Lh(b)))&&c.set(la(a),Mh(b))):C(b,yh,1)&&(a=Uj(a,C(b,yh,1)))&&a.forEach(d=>{d=la(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},Sj=(a,b,c)=>{C(b,yh,1)&&(a=Uj(a,C(b,yh,1)))&&a.forEach(d=>{c.add(la(d))})},Qj=(a,b)=>(a=Uj(a,b))&&a.length>0?a[0]:null,Uj=(a,b)=>(b=Hj(b))?Aj(b,a):null;function eg(){return"m202505060101"};var Vj=ed(bg);var dg=ed(fg);function Wj(a,b){return b(a)?a:void 0} 
function Xj(a,b,c,d,e){c=c instanceof xe?c.error:c;var f=new ig;const g=new hg;try{var h=de(window);Vc(g,1,h)}catch(p){}try{var k=K($g).g();yc(g,2,k,Qb)}catch(p){}try{Yc(g,3,window.document.URL)}catch(p){}h=Ic(f,2,g);k=new gg;b=$c(k,1,b);try{var n=rb(c?.name)?c.name:"Unknown error";Yc(b,2,n)}catch(p){}try{var l=rb(c?.message)?c.message:`Caught ${c}`;Yc(b,3,l)}catch(p){}try{var m=rb(c?.stack)?c.stack:Error().stack;m&&yc(b,4,m.split(/\n\s*/),Yb)}catch(p){}n=Jc(h,1,jg,b);if(e){l=0;switch(e.errSrc){case "LCC":l= 
1;break;case "PVC":l=2}m=cg();b=Wj(e.shv,rb);m=Yc(m,2,b);l=$c(m,6,l);m=Vj();m=jc(m);b=Wj(e.es,tb());m=yc(m,1,b,Qb);m=cd(m);l=Ic(l,4,m);m=Wj(e.client,rb);l=Xc(l,3,m);m=Wj(e.slotname,rb);l=Yc(l,7,m);e=Wj(e.tag_origin,rb);e=Yc(l,8,e);e=cd(e)}else e=cd(cg());e=Jc(n,6,kg,e);d=Vc(e,5,d??1);mg(a,d)};var Zj=class{constructor(){this.g=Yj}};function Yj(){return{Cb:kd()+(kd()&2**21-1)*2**32,rb:Number.MAX_SAFE_INTEGER}};var ck=class{constructor(a=!1){var b=ak;this.D=bk;this.B=a;this.F=b;this.i=null;this.j=this.I}J(a){this.j=a}A(a){this.i=a}Y(){}g(a,b,c){let d;try{d=b()}catch(e){b=this.B;try{b=this.j(a,ye(e),void 0,c)}catch(f){this.I(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}na(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,void 0)})}I(a,b,c,d){try{const g=c===void 0?1/this.F:c===0?0:1/c;var e=(new Zj).g(); 
if(g>0&&e.Cb*g<=e.rb){var f=this.D;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}Xj(f,a,b,g,c)}}catch(g){}return this.B}};var T=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,T):this.stack=Error().stack||""}};let bk,dk,ek,fk,ak;const gk=new Oe(q);function hk(a){a!=null&&(q.google_measure_js_timing=a);q.google_measure_js_timing||Ne(gk)}(function(a,b,c=!0){({Eb:ak,ub:ek}=ik());dk=a||new bh;ah(dk,ek);bk=b||new vg(2,eg(),1E3);fk=new ck(c);q.document.readyState==="complete"?hk():gk.g&&ge(q,"load",()=>{hk()})})();function jk(a,b,c){return fk.g(a,b,c)}function kk(a,b){return fk.u(a,b)}function lk(a,b){fk.na(a,b)}function mk(a,b,c=.01){const d=K($g).g();!b.eid&&d.length&&(b.eid=d.toString());We(dk,a,b,!0,c)} 
function nk(a,b,c=ak,d,e){return fk.I(a,b,c,d,e)}function ok(a,b,c=ak,d,e){return(we(b)?b.msg||Ae(b.error):Ae(b)).indexOf("TagError")===0?((we(b)?b.error:b).pbr=!0,!1):nk(a,b,c,d,e)}function ik(){let a,b;typeof q.google_srt==="number"?(b=q.google_srt,a=q.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Eb:a,ub:b}};var pk=class{constructor(){var a=Math.random;this.g=Math.floor(a()*2**52);this.i=0}};function qk(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(rk(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function sk(a){a=tk(a);return a.has("all")||a.has("after")}function uk(a){a=tk(a);return a.has("all")||a.has("before")}function tk(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function rk(a){const b=tk(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var vk=class{constructor(){this.g=new Set;this.i=new pk}};function wk(a,b){if(!a)return!1;a=Pd(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function xk(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null}function yk(a){return!!a.nextSibling||!!a.parentNode&&yk(a.parentNode)};function zk(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function Ak(a){return{ec:Bk(a),hc:U(a,"body ins.adsbygoogle"),ib:Ck(a),jb:U(a,".google-auto-placed"),kb:Dk(a),sb:Ek(a),lc:Gk(a),vc:Hk(a),Bb:Ik(a),kc:U(a,"div.googlepublisherpluginad"),Mb:U(a,"html > ins.adsbygoogle")}}function Gk(a){return Jk(a)||U(a,"div[id^=div-gpt-ad]")}function Jk(a){const b=zk(a);return b?Ja(Ka(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function U(a,b){return Oa(a.document.querySelectorAll(b))} 
function Dk(a){return U(a,"ins.adsbygoogle[data-anchor-status]")}function Ck(a){return U(a,"iframe[id^=aswift_],iframe[id^=google_ads_frame]")}function Hk(a){return U(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")}function Ek(a){return Gk(a).concat(U(a,"iframe[id^=google_ads_iframe]"))} 
function Ik(a){return U(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function Bk(a){return U(a,"ins.adsbygoogle-ablated-ad-slot")}function Kk(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};function Lk(a,b){if(a.u)return!0;a.u=!0;const c=D(a.j,Ph,1,B());a.i=0;const d=Mk(a.F);var e=a.g;var f;try{var g=(f=e.localStorage.getItem("google_ama_settings"))?Kh(f):null}catch(v){g=null}f=g!==null&&E(g,2);g=Lj(e);f&&(g.eatf=!0,ne(7,[!0,0,!1]));b:{var h={wb:!1,xb:!1},k=U(e,".google-auto-placed"),n=Dk(e),l=Hk(e),m=Ek(e);const v=Ik(e),u=Bk(e),y=U(e,"div.googlepublisherpluginad"),I=U(e,"html > ins.adsbygoogle");let N=[].concat(...Ck(e),...U(e,"body ins.adsbygoogle"));f=[];for(const [Ta,Ua]of[[h.nc, 
k],[h.wb,n],[h.rc,l],[h.oc,m],[h.sc,v],[h.mc,u],[h.qc,y],[h.xb,I]])Ta===!1?f=f.concat(Ua):N=N.concat(Ua);h=Kk(N);f=Kk(f);h=h.slice(0);for(p of f)for(f=0;f<h.length;f++)(p.contains(h[f])||h[f].contains(p))&&h.splice(f,1);var p=h;e=Li(e).clientHeight;for(f=0;f<p.length;f++)if(!(p[f].getBoundingClientRect().top>e)){e=!0;break b}e=!1}e=e?g.eatfAbg=!0:!1;if(e)return!0;e=new wh([2]);for(g=0;g<c.length;g++){p=a;h=c[g];f=g;k=b;(n=!C(h,Eh,4))||(n=e,l=n.contains,m=C(h,Eh,4),m=Sc(m,1),n=!l.call(n,m));if(n|| 
Sc(h,8)!==1||!Nk(h,d))p=null;else{p.i++;if(k=Ok(p,h,k,d))n=Lj(p.g),n.numAutoAdsPlaced||(n.numAutoAdsPlaced=0),(l=!C(h,yh,1))||(h=C(h,yh,1),l=Nc(h,5)==null),l||(n.numPostPlacementsPlaced?n.numPostPlacementsPlaced++:n.numPostPlacementsPlaced=1),n.placed==null&&(n.placed=[]),n.numAutoAdsPlaced++,n.placed.push({index:f,element:k.ea}),ne(7,[!1,p.i,!0]);p=k}if(p)return!0}ne(7,[!1,a.i,!1]);return!1} 
function Ok(a,b,c,d){if(!Nk(b,d)||Pb(z(b,8))!=1)return null;d=C(b,yh,1);if(!d)return null;d=Hj(d);if(!d)return null;d=Aj(d,a.g.document);if(d.length==0)return null;d=d[0];var e=Sc(b,2);e=Kj[e];e=e===void 0?null:e;var f;if(!(f=e==null)){a:{f=a.g;switch(e){case 0:f=wk(xk(d),f);break a;case 3:f=wk(d,f);break a;case 2:var g=d.lastChild;f=wk(g?g.nodeType==1?g:xk(g):null,f);break a}f=!1}if(c=!f&&!(!c&&e==2&&!yk(d)))c=e==1||e==2?d:d.parentNode,c=!(c&&!Qh(c)&&c.offsetWidth<=0);f=!c}if(!(c=f)){c=a.B;f=Sc(b, 
2);g=c.i;var h=la(d);g=g.g.get(h);if(!(g=g?g.contains(f):!1))a:{if(c.g.contains(la(d)))switch(f){case 2:case 3:g=!0;break a;default:g=!1;break a}for(f=d.parentElement;f;){if(c.g.contains(la(f))){g=!0;break a}f=f.parentElement}g=!1}c=g}if(!c){c=a.D;g=Sc(b,2);a:switch(g){case 1:f=sk(d.previousElementSibling)||uk(d);break a;case 4:f=sk(d)||uk(d.nextElementSibling);break a;case 2:f=uk(d.firstElementChild);break a;case 3:f=sk(d.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+ 
g);}g=qk(c,d,g);c=c.i;mk("ama_exclusion_zone",{typ:f?g?"siuex":"siex":g?"suex":"noex",cor:c.g,num:c.i++,dvc:Xd()},.1);c=f||g}if(c)return null;f=C(b,Oh,3);c={};f&&(c.fb=Rc(f,1),c.Ra=Rc(f,2),c.pb=!!Lb(z(f,3,void 0,nc)));f=C(b,Eh,4)&&Dh(C(b,Eh,4))?Dh(C(b,Eh,4)):null;f=Hh(f);g=Nc(b,12)!=null?Nc(b,12,nc):null;g=g==null?null:new Gh(null,{google_ml_rank:g});b=Pk(a,b);b=Fh(a.A,f,g,b);f=a.g;a=a.J;h=f.document;var k=c.pb||!1;g=re((new se(h)).g,"DIV");const n=g.style;n.width="100%";n.height="auto";n.clear=k? 
"both":"none";k=g.style;k.textAlign="center";c.Db&&gj(k,c.Db);h=re((new se(h)).g,"INS");k=h.style;k.display="block";k.margin="auto";k.backgroundColor="transparent";c.fb&&(k.marginTop=c.fb);c.Ra&&(k.marginBottom=c.Ra);c.hb&&gj(k,c.hb);g.appendChild(h);c={xa:g,ea:h};c.ea.setAttribute("data-ad-format","auto");g=[];if(h=b&&b.Ta)c.xa.className=h.join(" ");h=c.ea;h.className="adsbygoogle";h.setAttribute("data-ad-client",a);g.length&&h.setAttribute("data-ad-channel",g.join("+"));a:{try{var l=c.xa;if(Q(ji)){{const y= 
Gi(d,e);if(y.init){var m=y.init;for(d=m;d=y.ga(d);)m=d;var p={anchor:m,position:y.ma}}else p={anchor:d,position:e}}l["google-ama-order-assurance"]=0;Rh(l,p.anchor,p.position)}else Rh(l,d,e);b:{var v=c.ea;v.dataset.adsbygoogleStatus="reserved";v.className+=" adsbygoogle-noablate";l={element:v};var u=b&&b.ab;if(v.hasAttribute("data-pub-vars")){try{u=JSON.parse(v.getAttribute("data-pub-vars"))}catch(y){break b}v.removeAttribute("data-pub-vars")}u&&(l.params=u);(f.adsbygoogle=f.adsbygoogle||[]).push(l)}}catch(y){(v= 
c.xa)&&v.parentNode&&(u=v.parentNode,u.removeChild(v),Qh(u)&&(u.style.display=u.getAttribute("data-init-display")||"none"));v=!1;break a}v=!0}return v?c:null}function Pk(a,b){return th(vh(Nj(b).map(Ih),c=>{Lj(a.g).exception=c}))}var Qk=class{constructor(a,b,c,d,e){this.g=a;this.J=b;this.j=c;this.A=e||null;(this.F=d)?(a=a.document,d=D(d,nj,5,B()),d=Pj(a,d)):d=Pj(a.document,[]);this.B=d;this.D=new vk;this.i=0;this.u=!1}};function Mk(a){const b={};a&&sc(a,6,Pb,B()).forEach(c=>{b[c]=!0});return b} 
function Nk(a,b){return a&&qc(a,Eh,4)&&b[Dh(C(a,Eh,4))]?!1:!0};var Rk=fd(class extends J{});function Sk(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?sh(()=>Rk(c)):ph(null)};function Tk(){if(Uk)return Uk;var a=qe()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?Uk=b:a.google_persistent_state_async=Uk=new Vk}function Wk(a){return Xk[a]||`google_ps_${a}`}function Yk(a,b,c){b=Wk(b);a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function Zk(a,b,c){return Yk(a,b,()=>c)}var Vk=class{constructor(){this.S={}}},Uk=null;const Xk={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function $k(a){this.g=a||{cookie:""}} 
$k.prototype.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.wc,g=c.xc||!1,f=c.domain||void 0,e=c.path||void 0,d=c.zb);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
$k.prototype.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=ta(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};$k.prototype.isEmpty=function(){return!this.g.cookie}; 
$k.prototype.clear=function(){var a=(this.g.cookie||"").split(";");const b=[];var c=[];let d,e;for(let f=0;f<a.length;f++)e=ta(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(c=b.length-1;c>=0;c--)a=b[c],this.get(a),this.set(a,"",{zb:0,path:void 0,domain:void 0})};function al(a,b=window){if(E(a,5))try{return b.localStorage}catch{}return null};function bl(a){var b=new cl;return pc(b,5,Kb(a))}var cl=class extends J{};function dl(){this.A=this.A;this.i=this.i}dl.prototype.A=!1;dl.prototype.dispose=function(){this.A||(this.A=!0,this.D())};dl.prototype[fa(Symbol,"dispose")]=function(){this.dispose()};function el(a,b){a.A?b():(a.i||(a.i=[]),a.i.push(b))}dl.prototype.D=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function fl(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function gl(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=fl(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(ke({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function hl(a){if(a.g)return a.g;a:{let d=a.j;for(let e=0;e<50;++e){try{var b=!(!d.frames||!d.frames.__tcfapiLocator)}catch{b=!1}if(b){b=d;break a}b:{try{const f=d.parent;if(f&&f!=d){var c=f;break b}}catch{}c=null}if(!(d=c))break}b=null}a.g=b;return a.g}function il(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):hl(a)?(jl(a),e=++a.Y,a.B[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function jl(a){if(!a.u){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.B[d.callId](d.returnValue,d.success)}catch(e){}};a.u=b;ge(a.j,"message",b)}} 
var kl=class extends dl{constructor(a){var b={};super();this.g=null;this.B={};this.Y=0;this.u=null;this.j=a;this.J=b.eb??500;this.F=b.ic??!1}D(){this.B={};this.u&&(he(this.j,"message",this.u),delete this.u);delete this.B;delete this.j;delete this.g;super.D()}addEventListener(a){let b={internalBlockOnErrors:this.F};const c=od(()=>a(b));let d=0;this.J!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.J));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
fl(b),b.internalBlockOnErrors=this.F,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{il(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&il(this,"removeEventListener",null,a.listenerId)}};var pl=({l:a,aa:b,eb:c,ob:d,ha:e=!1,ia:f=!1})=>{b=ll({l:a,aa:b,ha:e,ia:f});b.g!=null||b.i.message!="tcunav"?d(b):ml(a,c).then(g=>g.map(nl)).then(g=>g.map(h=>ol(a,h))).then(d)},ll=({l:a,aa:b,ha:c=!1,ia:d=!1})=>{if(!ql({l:a,aa:b,ha:c,ia:d}))return ol(a,bl(!0));b=Tk();return(b=Zk(b,24))?ol(a,nl(b)):rh(Error("tcunav"))}; 
function ql({l:a,aa:b,ha:c,ia:d}){if(d=!d)d=new kl(a),d=typeof d.j.__tcfapi==="function"||hl(d)!=null;if(!d){if(c=!c){if(b){a=Sk(a);if(a.g!=null)if((a=a.getValue())&&Pb(z(a,1))!=null)b:switch(a=H(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else nk(806,a.i),a=!1;b=!a}c=b}d=c}return d?!0:!1}function ml(a,b){return Promise.race([rl(),sl(a,b)])} 
function rl(){return(new Promise(a=>{var b=Tk();a={resolve:a};const c=Zk(b,25,[]);c.push(a);b.S[Wk(25)]=c})).then(tl)}function sl(a,b){return new Promise(c=>{a.setTimeout(c,b,rh(Error("tcto")))})}function tl(a){return a?ph(a):rh(Error("tcnull"))} 
function nl(a){var b={};if(gl(a))if(a.gdprApplies===!1)a=!0;else if(a.tcString==="tcunavailable")a=!b.Va;else if((b.Va||a.gdprApplies!==void 0||b.jc)&&(b.Va||typeof a.tcString==="string"&&a.tcString.length)){b:{if(a.publisher&&a.publisher.restrictions&&(b=a.publisher.restrictions["1"],b!==void 0)){b=b["755"];break b}b=void 0}b===0?a=!1:a.purpose&&a.vendor?(b=a.vendor.consents,(b=!(!b||!b["755"]))&&a.purposeOneTreatment&&a.publisherCC==="CH"?a=!0:(b&&(a=a.purpose.consents,b=!(!a||!a["1"])),a=b)):a= 
!0}else a=!0;else a=!1;return bl(a)}function ol(a,b){return(a=al(b,a))?ph(a):rh(Error("unav"))};var ul=class extends J{};var vl=class extends J{};var wl=class{constructor(a){this.exception=a}};function xl(a,b){try{var c=a.i,d=c.resolve,e=a.g;Lj(e.g);D(e.j,Ph,1,B());d.call(c,new wl(b))}catch(f){a.i.reject(f)}}var yl=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}start(){this.u()}u(){try{switch(this.j.document.readyState){case "complete":case "interactive":Lk(this.g,!0);xl(this);break;default:Lk(this.g,!1)?xl(this):this.j.setTimeout(qa(this.u,this),100)}}catch(a){xl(this,a)}}};var zl=class extends J{getVersion(){return F(this,2)}};function Al(a){return Ra(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function Bl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function Cl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function Dl(a){var b=Al(a),c=Bl(b.slice(0,6));a=Bl(b.slice(6,12));var d=new zl;c=Uc(d,1,c);a=Uc(c,2,a);b=b.slice(12);c=Bl(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=Bl(e[0])===0;e=e.slice(1);var g=El(e,b),h=d.length===0?0:d[d.length-1];h=Cl(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=El(e,b);g=Cl(f);for(let n=0;n<=g;n++)d.push(h+n);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return yc(a,3,d,Qb)}function El(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var Fl="a".charCodeAt(),Gl=td(fh),Hl=td(gh);function Il(){var a=new Jl;return Vc(a,1,0)}function Kl(a){var b=Number;{var c=z(a,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Fb(64,c)):Nb(c)?d==="string"?Ub(c):Xb(c):void 0}b=b(c??"0");a=F(a,2);return new Date(b*1E3+a/1E6)}var Jl=class extends J{};function V(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function Ll(a){let b=V(a,12);const c=[];for(;b--;){var d=!!V(a,1)===!0,e=V(a,16);if(d)for(d=V(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c}function Ml(a,b,c){const d=[];for(let e=0;e<b;e++)if(V(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d} 
function Nl(a){const b=V(a,16);return!!V(a,1)===!0?(a=Ll(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):Ml(a,b)}var Ol=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var Ql=(a,b)=>{try{var c=Ra(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new Ol(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=V(d,12);c.cmpVersion=V(d,12);d.skip(30);c.tcfPolicyVersion=V(d,6);c.isServiceSpecific=!!V(d,1);c.useNonStandardStacks=!!V(d,1);c.specialFeatureOptins=Pl(Ml(d,12,Hl),Hl);c.purpose={consents:Pl(Ml(d,24,Gl),Gl),legitimateInterests:Pl(Ml(d,24,Gl),Gl)};c.purposeOneTreatment=!!V(d,1);c.publisherCC=String.fromCharCode(Fl+V(d,6))+String.fromCharCode(Fl+ 
V(d,6));c.vendor={consents:Pl(Nl(d),null),legitimateInterests:Pl(Nl(d),null)};return c}catch(d){return null}};const Pl=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var Rl=class extends J{g(){return w(z(this,2))!=null}};var Sl=class extends J{g(){return w(z(this,2))!=null}};var Tl=class extends J{};var Ul=fd(class extends J{});function Vl(a){a=Wl(a);try{var b=a?Ul(a):null}catch(c){b=null}return b?C(b,Tl,4)||null:null}function Wl(a){a=(new $k(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};td(fh).map(a=>Number(a));td(gh).map(a=>Number(a));function Xl(a){a.__tcfapiPostMessageReady||Yl(new Zl(a))} 
function Yl(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.l.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.l.addEventListener("message",a.g);a.l.__tcfapiPostMessageReady=!0}var Zl=class{constructor(a){this.l=a}};function $l(a){a.__uspapiPostMessageReady||am(new bm(a))} 
function am(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.l.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.l.addEventListener("message",a.g);a.l.__uspapiPostMessageReady=!0} 
var bm=class{constructor(a){this.l=a;this.g=null}};var cm=class extends J{};var dm=fd(class extends J{g(){return w(z(this,1))!=null}});function em(a,b){function c(m){if(m.length<10)return null;var p=h(m.slice(0,4));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function d(m){if(m.length<10)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function e(m){if(m.length<12)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(8,12));m=n(m);return"1"+p+m+"N"}function f(m){if(m.length<18)return null;var p=h(m.slice(0,8));p=k(p);m=h(m.slice(12,18));m=n(m);return"1"+p+m+"N"}function g(m){if(m.length<10)return null; 
var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function h(m){const p=[];let v=0;for(let u=0;u<m.length/2;u++)p.push(Bl(m.slice(v,v+2))),v+=2;return p}function k(m){return m.every(p=>p===1)?"Y":"N"}function n(m){return m.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=Al(a[0]);const l=Bl(a.slice(0,6));a=a.slice(6);if(l!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function fm(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Od("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function gm(a){if(a!=null)return hm(a)}function hm(a){return xb(a)?Number(a):String(a)};function im(a){var b=Q(yi);L!==L.top||L.__uspapi||L.frames.__uspapiLocator||(a=new jm(a,b),km(a),lm(a))}function km(a){!a.u||a.l.__uspapi||a.l.frames.__uspapiLocator||(a.l.__uspapiManager="fc",fm(a.l,"__uspapiLocator"),ra("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!E(a.j,3),d({version:1,uspString:b?"1---":a.u},!0))},a.l),$l(a.l))} 
function lm(a){!a.tcString||a.l.__tcfapi||a.l.frames.__tcfapiLocator||(a.l.__tcfapiManager="fc",fm(a.l,"__tcfapiLocator"),a.l.__tcfapiEventListeners=a.l.__tcfapiEventListeners||[],ra("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.l.__tcfapiEventListeners;c=a.i&&!a.j.g();switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=Ql(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.l),Xl(a.l))} 
function mm(a){if(!a?.g()||G(a,1).length===0||D(a,cm,2,B()).length===0)return null;const b=G(a,1);let c;try{var d=Dl(b.split("~")[0]);c=b.includes("~")?b.split("~").slice(1):[]}catch(e){return null}a=D(a,cm,2,B()).reduce((e,f)=>{var g=nm(e);g=Mc(g,1)??mc;g=hm(g);var h=nm(f);h=Mc(h,1)??mc;return g>hm(h)?e:f});d=sc(d,3,Rb,B()).indexOf(F(a,1));return d===-1||d>=c.length?null:{uspString:em(c[d],F(a,1)),wa:Kl(nm(a))}} 
function om(a){a=a.find(b=>b&&H(b,1)===13);if(a?.g())try{return dm(G(a,2))}catch(b){}return null}function nm(a){return qc(a,Jl,2)?C(a,Jl,2):Il()} 
var jm=class{constructor(a,b){var c=L;this.l=c;this.j=a;this.i=b;a=Wl(this.l.document);try{var d=a?Ul(a):null}catch(e){d=null}(a=d)?(d=C(a,Sl,5)||null,a=D(a,Rl,7,B()),a=om(a??[]),d={Sa:d,Ua:a}):d={Sa:null,Ua:null};a=d;d=mm(a.Ua);a=a.Sa;a?.g()&&G(a,2).length!==0?(b=qc(a,Jl,1)?C(a,Jl,1):Il(),a={uspString:G(a,2),wa:Kl(b)}):a=null;this.u=a&&d?d.wa>a.wa?d.uspString:a.uspString:a?a.uspString:d?d.uspString:null;this.tcString=(d=Vl(c.document))&&w(z(d,1))!=null?G(d,1):null;this.g=(c=Vl(c.document))&&w(z(c, 
2))!=null?G(c,2):null}};const pm={google_ad_channel:!0,google_ad_host:!0};function qm(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));mk("ama",b,.01)}function rm(a){const b={};Rd(pm,(c,d)=>{d in a&&(b[d]=a[d])});return b};function sm(a){return a.replace(/(^\/)|(\/$)/g,"")}function tm(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function um(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function vm(a){a=sc(a,2,Pb,B());if(!a)return!1;for(let b=0;b<a.length;b++)if(a[b]==1)return!0;return!1}function wm(a,b){a=sm(um(tm(a.location.pathname)));const c=Sd(a),d=xm(a);return b.find(e=>{if(qc(e,jj,7)){var f=C(e,jj,7);f=Sb(z(f,1,void 0,nc))}else f=Sb(z(e,1,void 0,nc));qc(e,jj,7)?(e=C(e,jj,7),e=Sc(e,2)):e=2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null} 
function xm(a){const b={};for(;;){b[Sd(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function X(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function ym(a){a=X(a);const b=a.space_collapsing||"none";return a.remove_ads_by_default?{Qa:!0,Kb:b,ta:a.ablation_viewport_offset}:null}function zm(a){a=X(a);a.had_ads_ablation=!0;a.remove_ads_by_default=!0;a.space_collapsing="slot";a.ablation_viewport_offset=1}function Am(a){X(L).allow_second_reactive_tag=a}function Bm(){const a=X(window);a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function Cm(a){return X(a)?.head_tag_slot_vars?.google_ad_host??Dm(a)}function Dm(a){return a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};const Em=[2,7,1];function Fm(a,b,c,d=""){return b===1&&c&&(Gm(a,d,c)?.j()??!1)?!0:Hm(a,d,e=>La(D(e,gd,2,B()),f=>Sc(f,1)===b),Q(Ei)?!!C(c,Im,26)?.g():E(c,6))}function Jm(a,b){const c=Md(L)||L;return Km(c,a)?!0:Hm(L,"",d=>La(sc(d,3,Pb,B()),e=>e===a),b)}function Km(a,b){a=(a=(a=a.location&&a.location.hash)&&a.match(/forced_clientside_labs=([\d,]+)/))&&a[1];return!!a&&Na(a.split(","),b.toString())} 
function Hm(a,b,c,d){a=Md(a)||a;const e=Lm(a,d);b&&(b=ve(String(b)));return sd(e,(f,g)=>Object.prototype.hasOwnProperty.call(e,g)&&(!b||b===g)&&c(f))}function Lm(a,b){a=Mm(a,b);const c={};Rd(a,(d,e)=>{try{const f=dd(hd,ec(d));c[e]=f}catch(f){}});return c}function Mm(a,b){a=ll({l:a,aa:b});return a.g!=null?Nm(a.getValue()):{}} 
function Nm(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:rd(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}}function Om(a,b){const c=[];a=Cm(q)?Em:(a=Gm(q,a,b)?.B())?[...sc(a,3,Pb,B())]:Em;a.includes(1)||c.push(1);a.includes(2)||c.push(2);a.includes(7)||c.push(7);return c} 
function Gm(a,b,c){if(!b)return null;const d=Pm(c)?.D();a=Pm(c)?.g()?.g()===b&&a.location.host&&G(c,17)===a.location.host;return d===b||a?Pm(c):null};function Qm(a,b,c,d){Rm(new Sm(a,b,c,d))}function Rm(a){const b=Q(Ei)?!!C(a.g,Im,26)?.g():E(a.g,6);vh(uh(ll({l:a.l,aa:b}),c=>{Tm(a,c,!0)}),()=>{Um(a)})}function Tm(a,b,c){vh(uh(Vm(b),d=>{Wm("ok");a.i(d,{fromLocalStorage:!0})}),()=>{var d=a.l;try{b.removeItem("google_ama_config")}catch(e){qm(d,{lserr:1})}c?Um(a):a.i(null,null)})}function Um(a){vh(uh(Xm(a),b=>{a.i(b,{fromPABGSettings:!0})}),()=>{Ym(a)})} 
function Vm(a){if(Q(ii))var b=null;else try{b=a.getItem("google_ama_config")}catch(d){b=null}try{var c=b?vj(b):null}catch(d){c=null}return(a=(a=c)?(gm(C(a,ij,3)?.g())??0)>Date.now()?a:null:null)?ph(a):rh(Error("invlocst"))}function Xm(a){if(Cm(a.l)&&!E(a.g,22))return rh(Error("invtag"));if(a=(a=Gm(a.l,a.j,a.g)?.A())&&D(a,Ph,1,B()).length>0?a:null){var b=new uj;var c=D(a,Ph,1,B());b=Kc(b,1,c);a=D(a,pj,2,B());a=Kc(b,7,a);a=ph(a)}else a=rh(Error("invtag"));return a} 
function Ym(a){const b=Q(Ei)?!!C(a.g,Im,26)?.g():E(a.g,6);pl({l:a.l,aa:b,eb:50,ob:c=>{Zm(a,c)}})}function Zm(a,b){vh(uh(b,c=>{Tm(a,c,!1)}),c=>{Wm(c.message);a.i(null,null)})}function Wm(a){mk("abg::amalserr",{status:a,guarding:"true",timeout:50,rate:.01},.01)}class Sm{constructor(a,b,c,d){this.l=a;this.g=b;this.j=c;this.i=d}};function $m(a,b,c,d){var e=an;try{const f=wm(a,D(c,pj,7,B()));if(f&&vm(f)){if(w(z(f,4))){const h=new Gh(null,{google_package:w(z(f,4))});d=Fh(d,h)}const g=e(a,b,c,f,d);Gj(1E3,()=>{const h=new kh;(new yl(a,g,h)).start();return h.i},a).then(()=>{qm(a,{atf:1})},h=>{(a.google_ama_state=a.google_ama_state||{}).exception=h;qm(a,{atf:0})})}}catch(f){qm(a,{atf:-1})}}function an(a,b,c,d,e){return new Qk(a,b,c,d,e)};function bn(a){return a.length?a.join("~"):void 0};function cn(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=dn(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function dn(a){let b="";Rd(a.split("_"),c=>{b+=c.substr(0,2)});return b};var Im=class extends J{g(){return E(this,1)}j(){return E(this,2)}};var en=class extends J{g(){return G(this,3)}};var fn=class extends J{g(){return Gc(this,en,1)}};function gn(a){const b=new fn;var c=new en;var d=F(a,1);c=Tc(c,1,d);d=F(a,18);c=Tc(c,2,d);d=G(a,2);c=Xc(c,3,d);d=Q(Ei)?!!C(a,Im,26)?.g():E(a,6);c=pc(c,4,Kb(d));d=E(a,20);c=pc(c,5,Kb(d));d=E(a,9);c=pc(c,6,Kb(d));d=Q(Ei)?!!C(a,Im,26)?.j():E(a,25);c=pc(c,7,Kb(d));d=G(a,8);c=Xc(c,8,d);d=G(a,3);c=Xc(c,9,d);a=C(a,Im,26);a=Ic(c,10,a);Ic(b,1,a);return b};function hn(){const a={};K(be).i(di.g,di.defaultValue)&&(a.bust=K(be).i(di.g,di.defaultValue));return a};class jn{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function kn(){const {promise:a,resolve:b}=new jn;return{promise:a,resolve:b}};function ln(a=()=>{}){q.google_llp||(q.google_llp={});const b=q.google_llp;let c=b[7];if(c)return c;c=kn();b[7]=c;a();return c}function mn(a){return ln(()=>{Nd(q.document,a)}).promise};Array.from({length:11},(a,b)=>b/10);function nn(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new on;return a.google_reactive_ads_global_state} 
var on=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new pn;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},pn=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};var qn=a=>{if(q.google_apltlad||a.google_ad_intent_query)return null;var b=a.google_loader_used!=="sd"&&Q(li)&&(q.top==q?0:Ld(q.top)?1:2)===1;if(q!==q.top&&!b||!a.google_ad_client)return null;q.google_apltlad=!0;b={enable_page_level_ads:{pltais:!0},google_ad_client:a.google_ad_client};const c=b.enable_page_level_ads;Rd(a,(d,e)=>{ej[e]&&e!=="google_ad_client"&&(c[e]=d)});c.google_pgb_reactive=7;c.asro=Q(pi);c.aihb=Q(mi);c.aifxl=bn(K(be).g(ni.g,ni.defaultValue));R(si)&&(c.aiapm=R(si));R(ti)&&(c.aiapmi= 
R(ti));R(ri)&&(c.aiact=R(ri));R(ui)&&(c.aicct=R(ui));R(vi)&&(c.ailct=R(vi));R(wi)&&(c.aimart=R(wi));c.aiof=bn(K(be).g(oi.g,oi.defaultValue));if("google_ad_section"in a||"google_ad_region"in a)c.google_ad_section=a.google_ad_section||a.google_ad_region;return b};function rn(a,b){X(L).ama_ran_on_page||Gj(1001,()=>{sn(new tn(a,b))},q)}function sn(a){Qm(a.l,a.i,a.g.google_ad_client||"",(b,c)=>{var d=a.l,e=a.g;X(L).ama_ran_on_page||b&&un(d,e,b,c)})}class tn{constructor(a,b){this.l=q;this.g=a;this.i=b}} 
function un(a,b,c,d){d&&(Lj(a).configSourceInAbg=d);qc(c,tj,24)&&(d=Mj(a),d.availableAbg=!0,d.ablationFromStorage=!!C(c,tj,24)?.g()?.g());if(ka(b.enable_page_level_ads)&&b.enable_page_level_ads.google_pgb_reactive===7){if(!wm(a,D(c,pj,7,B()))){mk("amaait",{value:"true"});return}mk("amaait",{value:"false"})}X(L).ama_ran_on_page=!0;C(c,hj,15)?.g()&&(X(a).enable_overlap_observer=!0);C(c,tj,24)?.g()?.g()&&(Mj(a).ablatingThisPageview=!0,zm(a));ne(3,[x(c)]);const e=b.google_ad_client||"";b=rm(ka(b.enable_page_level_ads)? 
b.enable_page_level_ads:{});const f=Fh(Jh,new Gh(null,b));jk(782,()=>{$m(a,e,c,f)})};function vn(a,b){a=a.document;for(var c=void 0,d=0;!c||a.getElementById(c+"_host");)c="aswift_"+d++;a=c;c=Number(b.google_ad_width||0);b=Number(b.google_ad_height||0);d=document.createElement("div");d.id=a+"_host";const e=d.style;e.border="none";e.height=`${b}px`;e.width=`${c}px`;e.margin="0px";e.padding="0px";e.position="relative";e.visibility="visible";e.backgroundColor="transparent";e.display="inline-block";return{vb:a,Nb:d}};function wn({va:a,Ba:b}){return a||(b==="dev"?"dev":"")};function xn(a){return a.google_ad_client?String(a.google_ad_client):X(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??""};var yn={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function zn(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};var An=class extends J{getVersion(){return G(this,2)}};function Bn(a,b){return Xc(a,2,b)}function Cn(a,b){return Xc(a,3,b)}function Dn(a,b){return Xc(a,4,b)}function En(a,b){return Xc(a,5,b)}function Fn(a,b){return Xc(a,9,b)}function Gn(a,b){return Kc(a,10,b)}function Hn(a,b){return pc(a,11,Kb(b))}function In(a,b){return Xc(a,1,b)}function Jn(a,b){return pc(a,7,Kb(b))}var Kn=class extends J{};const Ln="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Mn(){var a=L;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Ln).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function Nn(a){return Hn(Gn(En(Bn(In(Dn(Jn(Fn(Cn(new Kn,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new An;c=Xc(c,1,b.brand);return Xc(c,2,b.version)})||[]),a.wow64||!1)}function On(){return Mn()?.then(a=>Nn(a))??null};function Pn(a,b){b.google_ad_host||(a=Dm(a))&&(b.google_ad_host=a)}function Qn(a,b,c=""){L.google_sa_queue||(L.google_sa_queue=[],L.google_process_slots=kk(215,()=>{Rn(L.google_sa_queue)}),a=Sn(c,a,b),Nd(L.document,a))}function Rn(a){const b=a.shift();typeof b==="function"&&jk(216,b);a.length&&q.setTimeout(kk(215,()=>{Rn(a)}),0)}function Tn(a,b){a.google_sa_queue=a.google_sa_queue||[];a.google_sa_impl?b():a.google_sa_queue.push(b)} 
function Sn(a,b,c){var d=L;b=E(c,4)?b.Gb:b.Hb;a:{if(E(c,4)){if(a=a||xn(d)){b:{try{for(;d;){if(d.location?.hostname){var e=d.location.hostname;break b}d=d.parent}}catch(f){}e=""}e={client:ve(a),plah:e};break a}throw Error("PublisherCodeNotFoundForAma");}e={}}e={...e,...hn()};d=R(zi);!E(c,4)&&[0,1].includes(d)&&(e.osttc=`${d}`);return Jd(b,new Map(Object.entries(e)))} 
function Un(a,b,c,d){const {vb:e,Nb:f}=vn(a,b);c.appendChild(f);Vn(a,c,b);c=b.google_start_time??eh;const g=(new Date).getTime();b.google_lrv=wn({va:eg(),Ba:G(d,2)});b.google_async_iframe_id=e;b.google_start_time=c;b.google_bpp=g>c?g-c:1;a.google_sv_map=a.google_sv_map||{};a.google_sv_map[e]=b;Tn(a,()=>{var h=f;if(!h||!h.isConnected)if(h=a.document.getElementById(String(b.google_async_iframe_id)+"_host"),h==null)throw Error("no_div");(h=a.google_sa_impl({pubWin:a,vars:b,innerInsElement:h}))&&lk(911, 
h)})} 
function Vn(a,b,c){var d=c.google_ad_output,e=c.google_ad_format,f=c.google_ad_width||0,g=c.google_ad_height||0;e||d!=="html"&&d!=null||(e=`${f}x${g}`);Q(Di)&&(c.google_reactive_ad_format===10?e="interstitial":c.google_reactive_ad_format===11&&(e="rewarded"));d=!c.google_ad_slot||c.google_override_format||!yn[c.google_ad_width+"x"+c.google_ad_height]&&c.google_loader_used==="aa";e=e&&d?e.toLowerCase():"";c.google_ad_format=e;if(typeof c.google_reactive_sra_index!=="number"||!c.google_ad_unit_key){e=[c.google_ad_slot, 
c.google_orig_ad_format||c.google_ad_format,c.google_ad_type,c.google_orig_ad_width||c.google_ad_width,c.google_orig_ad_height||c.google_ad_height];d=[];f=0;for(g=b;g&&f<25;g=g.parentNode,++f)g.nodeType===9?d.push(""):d.push(g.id);(d=d.join())&&e.push(d);c.google_ad_unit_key=Sd(e.join(":")).toString();e=[];for(d=0;b&&d<25;++d){f=(f=b.nodeType!==9&&b.id)?"/"+f:"";a:{if(b&&b.nodeName&&b.parentElement){g=b.nodeName.toString().toLowerCase();const h=b.parentElement.childNodes;let k=0;for(let n=0;n<h.length;++n){const l= 
h[n];if(l.nodeName&&l.nodeName.toString().toLowerCase()===g){if(b===l){g="."+k;break a}++k}}}g=""}e.push((b.nodeName&&b.nodeName.toString().toLowerCase())+f+g);b=b.parentElement}b=e.join()+":";e=[];if(a)try{let h=a.parent;for(d=0;h&&h!==a&&d<25;++d){const k=h.frames;for(f=0;f<k.length;++f)if(a===k[f]){e.push(f);break}a=h;h=a.parent}}catch(h){}c.google_ad_dom_fingerprint=Sd(b+e.join()).toString()}} 
function Wn(){var a=Md(q);a&&(a=nn(a),a.tagSpecificState[1]||(a.tagSpecificState[1]={debugCard:null,debugCardRequested:!1}))}function Xn(){const a=On();a!=null&&a.then(b=>{L.google_user_agent_client_hint=JSON.stringify(x(b))});ae()};var Yn=class{constructor(a,b){this.g=a;this.u=b}height(){return this.u}i(a){return a>R(gi)&&this.u>300?this.g:Math.min(1200,Math.round(a))}j(){}};function Zn(a){return b=>!!(b.Z()&a)}var Y=class extends Yn{constructor(a,b,c,d=!1){super(a,b);this.B=c;this.A=d}Z(){return this.B}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const $n={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},ao={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},bo={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function co(a){var b=0;a.R&&b++;a.K&&b++;a.L&&b++;if(b<3)return{X:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.R.split(",");const c=a.L.split(",");a=a.K.split(",");if(b.length!==c.length||b.length!==a.length)return{X:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{X:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{L:d,K:e,Xa:b}} 
function eo(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function fo(a,b,c,d){b=Math.floor(((a-8*b-8)/b*$n[d]+ao[d])*c+8*c+8);return a>1500?{width:0,height:0,Ib:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,Ib:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function go(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*$n.mobile_banner_image_sidebyside+ao.mobile_banner_image_sidebyside)*b+8*b+8)}};const ho=Pa("script");var io=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,k=null,n=null,l=null,m=null){this.D=a;this.V=b;this.Z=c;this.g=d;this.F=e;this.G=f;this.P=g;this.u=h;this.A=k;this.i=n;this.j=l;this.B=m}size(){return this.V}};const jo=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var ko=class extends Yn{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function lo(a,b){mo(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new io(9,new ko(a,Math.floor(a*2.189)));if(Q(Vh)){var c=pd();var d=R(Wh);var e=R(Uh),f=R(Th);a<468?c?(a=go(a,d),d={W:a.width,U:a.height,K:1,L:d,R:"mobile_banner_image_sidebyside"}):(a=fo(a,1,d,"image_sidebyside"),d={W:a.width,U:a.height,K:1,L:d,R:"image_sidebyside"}):(d=eo(a),e===1&&(d.height=Math.floor(d.height*.5)),d={W:d.width,U:d.height,K:f,L:e,R:"image_stacked"})}else d=pd(),a<468?d?(d=go(a,12),d={W:d.width, 
U:d.height,K:1,L:12,R:"mobile_banner_image_sidebyside"}):(d=eo(a),d={W:d.width,U:d.height,K:1,L:13,R:"image_sidebyside"}):(d=eo(a),d={W:d.width,U:d.height,K:4,L:2,R:"image_stacked"});no(b,d);return new io(9,new ko(d.W,d.U))} 
function oo(a,b){mo(a,b);{const f=co({L:b.google_content_recommendation_rows_num,K:b.google_content_recommendation_columns_num,R:b.google_content_recommendation_ui_type});if(f.X)a={W:0,U:0,K:0,L:0,R:"image_stacked",X:f.X};else{var c=f.Xa.length===2&&a>=468?1:0;var d=f.Xa[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=bo[d];let g=f.K[c];for(;a/g<e&&g>1;)g--;e=g;c=f.L[c];a=fo(a,e,c,d);a={W:a.width,U:a.height,K:e,L:c,R:d}}}if(a.X)throw new T(a.X);no(b,a);return new io(9,new ko(a.W,a.U))} 
function mo(a,b){if(a<=0)throw new T(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function no(a,b){a.google_content_recommendation_ui_type=b.R;a.google_content_recommendation_columns_num=b.K;a.google_content_recommendation_rows_num=b.L};var po=class extends Yn{i(){return this.g}j(a,b,c){Wi(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const qo={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var ro=class extends Yn{i(){return Math.min(1200,this.g)}}; 
function so(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=Ri(b,c,g,R(Zh),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=S(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const k=g.parentElement.childNodes;for(let n=0;n<k.length;++n){const l=k[n];if(l!==g&&Ui(b,l))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}Wi(b,c)}else a=g;else a= 
g}if(a<250)throw new T("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new T("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new io(11,new Yn(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new T(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new T(`Invalid height: height=${f}`);if(f<50)throw new T("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new T("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new io(11,new Yn(a,f))}d=qo[f];if(!d)throw new T("Invalid data-ad-layout value: "+f);c=bj(c,b);b=S(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25);return new io(11, 
f==="in-article"?new ro(a,b):new Yn(a,b))};function to(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function uo(a,b){var c=vo.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var Z=[new Y(970,90,2),new Y(728,90,2),new Y(468,60,2),new Y(336,280,1),new Y(320,100,2),new Y(320,50,2),new Y(300,600,4),new Y(300,250,1),new Y(250,250,1),new Y(234,60,2),new Y(200,200,1),new Y(180,150,1),new Y(160,600,4),new Y(125,125,1),new Y(120,600,4),new Y(120,240,4),new Y(120,120,1,!0)],vo=[Z[6],Z[12],Z[3],Z[0],Z[7],Z[14],Z[1],Z[8],Z[10],Z[4],Z[15],Z[2],Z[11],Z[5],Z[13],Z[9],Z[16]];function wo(a,b,c,d,e){e.google_full_width_responsive==="false"?c={H:a,G:1}:b==="autorelaxed"&&e.google_full_width_responsive||xo(b)||e.google_ad_resize?(b=Si(a,c,d,e),c=b!==!0?{H:a,G:b}:{H:S(c)||a,G:!0}):c={H:a,G:2};const {H:f,G:g}=c;return g!==!0?{H:a,G:g}:d.parentElement?{H:f,G:g}:{H:a,G:g}} 
function yo(a,b,c,d,e){const {H:f,G:g}=jk(247,()=>wo(a,b,c,d,e));var h=g===!0;const k=Wd(d.style.width),n=Wd(d.style.height),{V:l,P:m,Z:p,Wa:v}=zo(f,b,c,d,e,h);h=Ao(b,p);var u;const y=(u=Xi(d,c,"marginLeft"))?`${u}px`:"",I=(u=Xi(d,c,"marginRight"))?`${u}px`:"";u=Zi(d,c)||"";return new io(h,l,p,null,v,g,m,y,I,n,k,u)}function xo(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function zo(a,b,c,d,e,f){b=Bo(c,a,b);let g;var h=!1;let k=!1;var n=S(c)<488;if(n){g=Mi(d,c);var l=bj(d,c);h=!l&&g;k=l&&g}l=[$i(a),Zn(b)];Q(ei)||l.push(aj(n,c,d,k));e.google_max_responsive_height!=null&&l.push(cj(e.google_max_responsive_height));n=[u=>!u.A];if(h||k)h=dj(c,d),n.push(cj(h));const m=uo(to(l),to(n));if(!m)throw new T(`No slot size for availableWidth=${a}`);const {V:p,P:v}=jk(248,()=>{var u;a:if(f){if(e.gfwrnh&&(u=Wd(e.gfwrnh))){u={V:new po(a,u),P:!0};break a}u=R(ai);u=u>0?a/u:a/1.2;if(e.google_resizing_allowed|| 
e.google_full_width_responsive==="true")var y=Infinity;else{y=d;let N=Infinity;do{var I=Xi(y,c,"height");I&&(N=Math.min(N,I));(I=Xi(y,c,"maxHeight"))&&(N=Math.min(N,I))}while(y.parentElement&&(y=y.parentElement)&&y.tagName!=="HTML");y=N}!(Q(ci)&&y<=u*2)&&(y=Math.min(u,y),y<u*.5||y<100)&&(y=u);u={V:new po(a,Math.floor(y)),P:y<u?102:!0}}else u={V:m,P:100};return u});return e.google_ad_layout==="in-article"?{V:Co(a,c,d,p,e),P:!1,Z:b,Wa:g}:{V:p,P:v,Z:b,Wa:g}} 
function Ao(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function Bo(a,b,c){if(c==="auto")c=Math.min(1200,S(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Ji)c.indexOf(d)!==-1&&(b|=Ji[d])}return b}function Co(a,b,c,d,e){const f=e.google_ad_height||Xi(c,b,"height");b=so(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new Y(b.g,b.height(),1):d};function Do(a,b,c,d,e){var f;(f=S(b))?S(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,Wi(b,c),f={H:f,G:!0}):f={H:a,G:5}:f={H:a,G:4}:f={H:a,G:10};const {H:g,G:h}=f;if(h!==!0||a===g)return new io(12,new Yn(a,d),null,null,!0,h,100);const {V:k,P:n,Z:l}=zo(g,"auto",b,c,e,!0);return new io(1,k,l,2,!0,h,n)};function Eo(a){const b=a.google_ad_format;if(b==="autorelaxed"){if(Q(hi))return a.google_ad_format="auto",1;a:{if(a.google_content_recommendation_ui_type!=="pedestal")for(const c of jo)if(a[c]!=null){a=!0;break a}a=!1}return a?9:5}if(xo(b))return 1;if(b==="link")return 4;if(b==="fluid")return a.google_ad_layout==="in-article"?(Fo(a),1):8;if(a.google_reactive_ad_format===27)return Fo(a),1} 
function Go(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&Xi(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=Ho(a,f,b,c,d))?e:yo(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.Z!=null&&(c.google_responsive_formats=e.Z);e.F!=null&&(c.google_safe_for_responsive_override=e.F);e.G!=null&&(e.G===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.G));e.P!=null&&e.P!==!0&&(c.gfwrnher=e.P);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.D;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.G===!0&&(c.gfwrnh=`${e.size().height()}px`);e.u!=null&&(c.gfwroml=e.u);e.A!=null&&(c.gfwromr=e.A);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.B!=null&&(c.gfwroz=e.B);f=Md(window)||window;cn(f.location,"google_responsive_dummy_ad")&&(Na([1,2,3,4,5,6,7,8],e.D)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${ho}>window.top.postMessage('${f}', '*'); 
          </${ho}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function Ho(a,b,c,d,e){const f=d.google_ad_height||Xi(c,e,"height")||0;switch(a){case 5:const {H:g,G:h}=jk(247,()=>wo(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&Wi(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return lo(g,d);case 9:return oo(b,d);case 8:return so(b,e,c,f,d);case 10:return Do(b,e,c,f,d)}}function Fo(a){a.google_ad_format="auto";a.armr=3};function Io(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};function Jo(a,b){var c=Md(b);if(c){c=S(c);const d=Pd(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function Ko(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}} 
function Lo(a,b){if(a.getAttribute("src")){var c=a.getAttribute("src")||"";const d=Hd(c,"client");d&&(b.google_ad_client=Ko("google_ad_client",d));(c=Hd(c,"host"))&&(b.google_ad_host=Ko("google_ad_host",c))}for(const d of a.attributes)/data-/.test(d.name)&&(a=ta(d.name.replace("data-matched-content","google_content_recommendation").replace("data","google").replace(/-/g,"_")),b.hasOwnProperty(a)||(c=Ko(a,d.value),c!==null&&(b[a]=c)))} 
function Mo(a,b){if(a=pe(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?17:12} 
function No(a,b,c,d){Lo(a,b);if(c.document&&c.document.body&&!Eo(b)&&!b.google_reactive_ad_format&&!b.google_ad_intent_query){var e=parseInt(a.style.width,10),f=Jo(a,c);if(f>0&&e>f){var g=parseInt(a.style.height,10);e=!!yn[e+"x"+g];let h=f;if(e){const k=zn(f,g);if(k)h=k,b.google_ad_format=k+"x"+g+"_0ads_al";else throw new T("No slot size for availableWidth="+f);}b.google_ad_resize=!0;b.google_ad_width=h;e||(b.google_ad_format=null,b.google_override_format=!0);f=h;a.style.width=`${f}px`;Io(b,4)}}if(Q(Xh)|| 
S(c)<488){f=Md(c)||c;g=a.offsetWidth||Xi(a,c,"width")||b.google_ad_width||0;e=b.google_ad_client;if(d=cn(f.location,"google_responsive_slot_preview")||Fm(f,1,d,e))b:if(b.google_reactive_ad_format||b.google_ad_resize||Eo(b)||Oi(a,b))d=!1;else{for(d=a;d;d=d.parentElement){f=Pd(d,c);if(!f){b.gfwrnwer=18;d=!1;break b}if(!Na(["static","relative"],f.position)){b.gfwrnwer=17;d=!1;break b}}if(!Q(fi)&&(d=R($h),d=Ri(c,a,g,d,b),d!==!0)){b.gfwrnwer=d;d=!1;break b}d=c===c.top?!0:!1}d?(Io(b,1),d=!0):d=!1}else d= 
!1;if(g=Eo(b))Go(g,a,b,c,d);else{if(Oi(a,b)){if(d=Pd(a,c))a.style.width=d.width,a.style.height=d.height,Ni(d,b);b.google_ad_width||(b.google_ad_width=a.offsetWidth);b.google_ad_height||(b.google_ad_height=a.offsetHeight);b.google_loader_features_used=256;b.google_responsive_auto_format=Mo(c,b)}else Ni(a.style,b);c.location&&c.location.hash==="#gfwmrp"||b.google_responsive_auto_format===12&&b.google_full_width_responsive==="true"?Go(10,a,b,c,!1):Math.random()<.01&&b.google_responsive_auto_format=== 
12&&(a=Si(a.offsetWidth||parseInt(a.style.width,10)||b.google_ad_width,c,a,b),a!==!0?(b.efwr=!1,b.gfwrnwer=a):b.efwr=!0)}};function Oo(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&Ld(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function Po(a,b,c){for(const f of b)a:{b=a;var d=f,e=c;for(let g=0;g<b.g.length;g++){if(b.g[g].element.contains(d)){b.g[g].labels.add(e);break a}if(d.contains(b.g[g].element)){b.g[g].element=d;b.g[g].labels.add(e);break a}}b.g.push({element:d,labels:new Set([e])})}}class Qo{constructor(){this.g=[]}getSlots(){return this.g}} 
function Ro(a){const b=Ak(a),c=new Qo;Po(c,b.ib,1);Po(c,b.jb,2);Po(c,b.sb,3);Po(c,b.Mb,4);Po(c,b.kb,5);Po(c,b.Bb,6);return c.getSlots().map(d=>{var e=new tf;var f=[...d.labels];e=yc(e,1,f,Ob);d=d.element.getBoundingClientRect();f=new sf;f=Tc(f,1,d.left+a.scrollX);f=Tc(f,2,d.top+a.scrollY);f=Tc(f,3,d.width);d=Tc(f,4,d.height);d=cd(d);e=Ic(e,2,d);return cd(e)}).sort((d,e)=>{d=C(d,sf,2);d=F(d,2);e=C(e,sf,2);e=F(e,2);return d-e})};function tg(a,b,c=0){a.g.size>0||So(a);c=Math.min(Math.max(0,c),9);const d=a.g.get(c);d?d.push(b):a.g.set(c,[b])}function To(a,b,c,d){ge(b,c,d);el(a,()=>he(b,c,d))}function Uo(a,b){a.j!==1&&(a.j=1,a.g.size>0&&Vo(a,b))} 
function So(a){a.l.document.visibilityState?To(a,a.l.document,"visibilitychange",b=>{a.l.document.visibilityState==="hidden"&&Uo(a,b);a.l.document.visibilityState==="visible"&&(a.j=0)}):"onpagehide"in a.l?(To(a,a.l,"pagehide",b=>{Uo(a,b)}),To(a,a.l,"pageshow",()=>{a.j=0})):To(a,a.l,"beforeunload",b=>{Uo(a,b)})}function Vo(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var Wo=class extends dl{constructor(a){super();this.l=a;this.j=0;this.g=new Map}};async function Xo(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function Yo(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=de(a.l)}function Zo(a){const b=a.g.wpc;return b!==null&&b!==""?b:a.g.wpc=xn(a.l)}function $o(a,b){var c=new If;var d=Yo(a);c=Vc(c,1,d);d=Zo(a);c=Yc(c,2,d);c=Vc(c,3,a.g.sd);return Vc(c,7,Math.round(b||a.l.performance.now()))}async function ap(a){ia(await ia(Xo(a.l,()=>!(!Yo(a)||!Zo(a)))))}function bp(a){var b=K(cp);if(b.j){var c=b.A;a(c);b.g.psi=x(c)}} 
function dp(a){tg(a.u,()=>{var b=$o(a);b=Jc(b,12,Jf,a.B);a.j&&!a.g.le.includes(3)&&(a.g.le.push(3),pg(a.i,b))},9)}function ep(a){const b=new Ef;tg(a.u,()=>{Ic(b,2,a.A);Vc(b,3,a.g.tar);var c=a.l;var d=new xf;var e=Ro(c);d=Kc(d,1,e);e=cd(vf(uf(new wf,S(c)),Li(c).clientHeight));d=Ic(d,2,e);c=cd(vf(uf(new wf,Li(c).scrollWidth),Li(c).scrollHeight));c=Ic(d,3,c);c=cd(c);Ic(b,4,c);c=a.i;d=$o(a);d=Jc(d,8,Jf,b);pg(c,d)},9)} 
async function fp(a){var b=K(cp);if(b.j&&!b.g.le.includes(1)){b.g.le.push(1);var c=b.l.performance.now();ia(await ia(ap(b)));var d=new Af;a=zc(d,5,Kb(a),!1);d=vf(uf(new wf,Li(b.l).scrollWidth),Li(b.l).scrollHeight);a=Ic(a,2,d);d=vf(uf(new wf,S(b.l)),Li(b.l).clientHeight);a=Ic(a,1,d);for(var e=d=b.l;d&&d!=d.parent;)d=d.parent,Ld(d)&&(e=d);a=Yc(a,4,e.location.href);d=Oo(b.l);d!==0&&(e=new zf,d=Zc(e,1,d),Ic(a,3,d));d=b.i;c=$o(b,c);c=Jc(c,4,Jf,a);pg(d,c);dp(b);ep(b)}} 
async function gp(a,b,c){if(a.j&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.l.performance.now();ia(await ia(ap(a)));var e=a.i;a=$o(a,d);d=new yf;b=$c(d,1,b);c=yc(b,2,c,Qb);c=Jc(a,9,Jf,c);pg(e,c)}}async function hp(a,b){ia(await ia(ap(a)));var c=a.i;a=$o(a);a=Vc(a,3,1);b=Jc(a,10,Jf,b);pg(c,b)} 
var cp=class{constructor(a,b){this.l=qe()||window;this.u=b??new Wo(this.l);this.i=a??new vg(2,eg(),100,100,!0,this.u);this.g=Yk(Tk(),33,()=>{const c=R(Yh);return{sd:c,ssp:c>0&&Qd()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get j(){return this.g.ssp}get A(){return jk(1178,()=>dd(Df,ec(this.g.psi||[])))||new Df}get B(){return jk(1227,()=>dd(Ff,ec(this.g.cc||[])))||new Ff}};function ip(){var a=window;return q.google_adtest==="on"||q.google_adbreak_test==="on"||a.location.host.endsWith("h5games.usercontent.goog")||a.location.host==="gamesnacks.com"?a.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(b=>Math.floor(Number(b))).filter(b=>!isNaN(b)&&b>0)||[]:[]};function jp(a,b){return a instanceof HTMLScriptElement&&b.test(a.src)?0:1}function kp(a){var b=L.document;if(b.currentScript)return jp(b.currentScript,a);for(const c of b.scripts)if(jp(c,a)===0)return 0;return 1};function lp(a,b){const c=Q(Ei)?!!C(b,Im,26)?.g():E(b,6);return{[3]:{[55]:()=>a===0,[23]:d=>Fm(L,Number(d),b),[24]:d=>Jm(Number(d),c),[61]:()=>c,[63]:()=>c||G(b,8)===".google.ch"},[4]:{},[5]:{[6]:()=>G(b,15)}}};function mp(a=q){return a.ggeac||(a.ggeac={})};function np(a,b=document){return!!b.featurePolicy?.features().includes(a)}function op(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function pp(a,b=navigator){try{return!!b.protectedAudience?.queryFeatureSupport?.(a)}catch(c){return!1}};function qp(a,b){try{const d=a.split(".");a=q;let e=0,f;for(;a!=null&&e<d.length;e++)f=a,a=a[d[e]],typeof a==="function"&&(a=f[d[e]]());var c=a;if(typeof c===b)return c}catch{}} 
var rp={[3]:{[8]:a=>{try{return ja(a)!=null}catch{}},[9]:a=>{try{var b=ja(a)}catch{return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},[10]:()=>window===window.top,[6]:a=>Na(K($g).g(),Number(a)),[27]:a=>{a=qp(a,"boolean");return a!==void 0?a:void 0},[60]:a=>{try{return!!q.document.querySelector(a)}catch{}},[80]:a=>{try{return!!q.matchMedia(a).matches}catch{}},[69]:a=>np(a,q.document),[70]:a=>op(a,q.document),[79]:a=>pp(a, 
q.navigator)},[4]:{[3]:()=>Xd(),[6]:a=>{a=qp(a,"number");return a!==void 0?a:void 0}},[5]:{[2]:()=>window.location.href,[3]:()=>{try{return window.top.location.hash}catch{return""}},[4]:a=>{a=qp(a,"string");return a!==void 0?a:void 0},[12]:a=>{try{const b=qp(a,"string");if(b!==void 0)return atob(b)}catch(b){}}}};var sp=class extends J{getId(){return F(this,1)}};function tp(a){return D(a,sp,2,B())}var up=class extends J{};var vp=class extends J{};var wp=class extends J{g(){return Mc(this,2)??mc}j(){return Mc(this,4)??mc}u(){return E(this,3)}};var xp=class extends J{};function yp(a){return zp({[0]:new Map,[1]:new Map,[2]:new Map},a)} 
function zp(a,b){const c=new Map;for(const [f,g]of a[1].entries()){var d=f,e=g;const {bb:h,Ya:k,Za:n}=e[e.length-1];c.set(d,h+k*n)}for(const f of b)for(const g of D(f,up,2,B()))if(tp(g).length!==0){b=Sb(z(g,8))??0;!H(g,4)||H(g,13)||H(g,14)||(b=c.get(H(g,4))??0,d=(Sb(z(g,1))??0)*tp(g).length,c.set(H(g,4),b+d));d=[];for(e=0;e<tp(g).length;e++){const h={bb:b,Ya:Sb(z(g,1))??0,Za:tp(g).length,Ab:e,fa:H(f,1),oa:g,T:tp(g)[e]};d.push(h)}Ap(a[2],H(g,10),d)||Ap(a[1],H(g,4),d)||Ap(a[0],tp(g)[0].getId(),d)}return a} 
function Ap(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a.get(b).push(...c);return!0};function Bp(a=Qd()){return b=>Sd(`${b} + ${a}`)%1E3};const Cp=[12,13,20];function Dp(a,b){var c=K(Bg).N;const d=gf(C(b.oa,$e,3),c);if(!d.success)return zg(a.M,C(b.oa,$e,3),b.fa,b.T.getId(),d),!1;if(!d.value)return!1;c=gf(C(b.T,$e,3),c);return c.success?c.value?!0:!1:(zg(a.M,C(b.T,$e,3),b.fa,b.T.getId(),c),!1)}function Ep(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];a.includes(b)||a.push(b)} 
function Fp(a,b,c,d){const e=[];var f;if(f=b!==9)a.u[b]?f=!0:(a.u[b]=!0,f=!1);if(f)return xg(a.M,b,c,e,[],4),e;f=Cp.includes(b);const g=[],h=[];for(const m of[0,1,2])for(const [p,v]of a.ja[m].entries()){var k=p,n=v;const u=new Of;var l=n.filter(y=>y.fa===b&&a.i[y.T.getId()]&&Dp(a,y));if(l.length)for(const y of l)h.push(y.T);else if(!a.ya&&(m===2?(l=d[1],ad(u,2,Pf,k)):l=d[0],k=l?.(String(k))??(m===2&&H(n[0].oa,11)===1?void 0:d[0](String(k))),k!==void 0)){for(const y of n){if(y.fa!==b)continue;n=k- 
y.bb;l=y.Ya;const I=y.Za,N=y.Ab;n<0||n>=l*I||n%I!==N||!Dp(a,y)||(n=H(y.oa,13),n!==0&&n!==void 0&&(l=a.j[String(n)],l!==void 0&&l!==y.T.getId()?yg(a.M,a.j[String(n)],y.T.getId(),n):a.j[String(n)]=y.T.getId()),h.push(y.T))}Ec(u,Pf)!==0&&(Uc(u,3,k),g.push(u))}}for(const m of h)d=m.getId(),e.push(d),Ep(a,d,f?4:c),Rg(D(m,lf,2,B()),f?Tg():[c],a.M,d);xg(a.M,b,c,e,g,1);return e}function Gp(a,b){b=b.map(c=>new vp(c)).filter(c=>!Cp.includes(H(c,1)));a.ja=zp(a.ja,b)} 
function Hp(a,b){M(1,c=>{a.i[c]=!0},b);M(2,(c,d,e)=>Fp(a,c,d,e),b);M(3,c=>(a.g[c]||[]).concat(a.g[4]),b);M(12,c=>void Gp(a,c),b);M(16,(c,d)=>void Ep(a,c,d),b)}var Ip=class{constructor(a,b,c,{ya:d=!1,yc:e=[]}={}){this.ja=a;this.M=c;this.u={};this.ya=d;this.g={[b]:[],[4]:[]};this.i={};this.j={};if(a=Fe()){a=a.split(",")||[];for(const f of a)(a=Number(f))&&(this.i[a]=!0)}for(const f of e)this.i[f]=!0}};function Jp(a,b){a.g=Vg(14,b,()=>{})}class Kp{constructor(){this.g=()=>{}}}function Lp(a){K(Kp).g(a)};function Mp({tb:a,N:b,config:c,mb:d=mp(),nb:e=0,M:f=new Ag(gm(C(a,wp,5)?.g())??0,gm(C(a,wp,5)?.j())??0,C(a,wp,5)?.u()??!1),ja:g=yp(D(a,vp,2,B(ob)))}){d.hasOwnProperty("init-done")?(Vg(12,d,()=>{})(D(a,vp,2,B()).map(h=>x(h))),Vg(13,d,()=>{})(D(a,lf,1,B()).map(h=>x(h)),e),b&&Vg(14,d,()=>{})(b),Np(e,d)):(Hp(new Ip(g,e,f,c),d),Wg(d),Xg(d),Yg(d),Np(e,d),Rg(D(a,lf,1,B(ob)),[e],f,void 0,!0),Cg=Cg||!(!c||!c.yb),Lp(rp),b&&Lp(b))}function Np(a,b=mp()){Zg(K($g),b,a);Op(b,a);Jp(K(Kp),b);K(be).B()} 
function Op(a,b){const c=K(be);c.j=(d,e)=>Vg(5,a,()=>!1)(d,e,b);c.u=(d,e)=>Vg(6,a,()=>0)(d,e,b);c.i=(d,e)=>Vg(7,a,()=>"")(d,e,b);c.A=(d,e)=>Vg(8,a,()=>[])(d,e,b);c.g=(d,e)=>Vg(17,a,()=>[])(d,e,b);c.B=()=>{Vg(15,a,()=>{})(b)}};function Pp(a,b){b={[0]:Bp(de(b).toString())};b=K($g).u(a,b);a=gp(K(cp),a,b);ch.na(1085,a)}function Qp(a,b,c){var d=X(a);if(d.plle)Np(1,mp(a));else{d.plle=!0;d=C(b,xp,12);var e=E(b,9);Mp({tb:d,N:lp(c,b),config:{ya:e&&!!a.google_disable_experiments,yb:e},mb:mp(a),nb:1});if(c=G(b,15))c=Number(c),K($g).j(c);for(const f of sc(b,19,Rb,B()))K($g).i(f);Pp(12,a);Pp(10,a);a=Md(a)||a;cn(a.location,"google_mc_lab")&&K($g).i(44738307)}};function Rp(a){fk.A(b=>{b.shv=String(a);b.mjsv=wn({va:eg(),Ba:a});const c=K($g).g(),d=ip();b.eid=c.concat(d).join(",")})}function Sp(a,b){a=b?.g()?.g()||G(a,2);Rp(a)};var Tp={google_pause_ad_requests:!0,google_user_agent_client_hint:!0};var Up=class extends J{g(){return G(this,1)}j(){return H(this,2)}};var Vp=class extends J{D(){return G(this,1)}g(){return C(this,Up,2)}j(){return E(this,3)}u(){return E(this,4)}A(){return C(this,ul,5)}B(){return C(this,vl,6)}};function Pm(a){return Qc(a,Vp,27,Wp)}var Xp=class extends J{},Wp=[27,28];function Yp(a){var b=fk;try{if(!rb(a))throw Error(String(a));if(a.length>0)return new Xp(JSON.parse(a))}catch(c){b.I(838,c instanceof Error?c:Error(String(c)))}return new Xp};function Zp(a,b){if(E(b,22))return 7;if(E(b,16))return 6;const c=Pm(b)?.g()?.g();b=Pm(b)?.g()?.j()??0;a=c===a;switch(b){case 1:return a?9:8;case 2:return a?11:10;case 3:return a?13:12}return 1}function $p(a,b,c){b.google_loader_used!=="sd"&&(b.abgtt=Zp(a,c))};function aq(a,b){var c=new bq;try{const f=a.createElement("link");if(f.relList?.supports("compression-dictionary")&&Fa()){var d=f;if(b instanceof yd)d.href=Ad(b).toString(),d.rel="compression-dictionary";else{if(Dd.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var e=Bd.test(b)?b:void 0;e!==void 0&&(d.href=e,d.rel="compression-dictionary")}a.head.appendChild(f)}}catch(f){c.ka({methodName:1296,ua:f})}} 
function cq(a){return Id`https://googleads.g.doubleclick.net/pagead/managed/dict/${a}/adsbygoogle`};var bq=class{constructor(){this.g=fk}ka(a){const b=a.ua;this.g.I(a.methodName??0,b instanceof Error?b:Error(String(b)))}};function dq(a){ge(window,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="sc-cnf"||a(c,b)})};function eq(a,b){return b==null?`&${a}=null`:`&${a}=${Math.floor(b)}`}function fq(a,b){return`&${a}=${b.toFixed(3)}`}function gq(){const a=new Set,b=zk();try{if(!b)return a;const c=b.pubads();for(const d of c.getSlots())a.add(d.getSlotId().getDomId())}catch{}return a}function hq(a){a=a.id;return a!=null&&(gq().has(a)||a.startsWith("google_ads_iframe_")||a.startsWith("aswift"))} 
function iq(a,b,c){if(!a.sources)return!1;switch(jq(a)){case 2:const d=kq(a);if(d)return c.some(f=>lq(d,f));break;case 1:const e=mq(a);if(e)return b.some(f=>lq(e,f))}return!1}function jq(a){if(!a.sources)return 0;a=a.sources.filter(b=>b.previousRect&&b.currentRect);if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function mq(a){return nq(a,b=>b.currentRect)}function kq(a){return nq(a,b=>b.previousRect)} 
function nq(a,b){return a.sources.reduce((c,d)=>{d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function lq(a,b){const c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50} 
function oq(){const a=Array.from(document.getElementsByTagName("iframe")).filter(hq),b=[...gq()].map(c=>document.getElementById(c)).filter(c=>c!==null);pq=window.scrollX;qq=window.scrollY;return rq=[...a,...b].map(c=>c.getBoundingClientRect())} 
function sq(){var a=new tq;if(Q(Bi)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.gb.qb&&b.push("event");for(const c of b)b={type:c,buffered:!0},c==="event"&&(b.durationThreshold=40),uq(a).observe(b);vq(a)}}} 
function wq(a,b){const c=pq!==window.scrollX||qq!==window.scrollY?[]:rq,d=oq();for(const e of b.getEntries())switch(b=e.entryType,b){case "layout-shift":xq(a,e,c,d);break;case "largest-contentful-paint":b=e;a.Ia=Math.floor(b.renderTime||b.loadTime);a.Ha=b.size;break;case "first-input":b=e;a.Ea=Number((b.processingStart-b.startTime).toFixed(3));a.Fa=!0;a.g.some(f=>f.entries.some(g=>e.duration===g.duration&&e.startTime===g.startTime))||yq(a,e);break;case "longtask":b=Math.max(0,e.duration-50);a.B+= 
b;a.J=Math.max(a.J,b);a.ra+=1;break;case "event":yq(a,e);break;default:Eb(b,void 0)}}function uq(a){a.M||(a.M=new PerformanceObserver(Fj(640,b=>{wq(a,b)})));return a.M} 
function vq(a){const b=Fj(641,()=>{var d=document;(d.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""]??0)===2&&zq(a)}),c=Fj(641,()=>void zq(a));document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.Da=()=>{document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);uq(a).disconnect()}} 
function zq(a){if(!a.La){a.La=!0;uq(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=fq("cls",a.D),b+=fq("mls",a.Y),b+=eq("nls",a.qa),window.LayoutShiftAttribution&&(b+=fq("cas",a.A),b+=eq("nas",a.Ka),b+=fq("was",a.Pa)),b+=fq("wls",a.sa),b+=fq("tls",a.Oa));window.LargestContentfulPaint&&(b+=eq("lcp",a.Ia),b+=eq("lcps",a.Ha));window.PerformanceEventTiming&&a.Fa&&(b+=eq("fid",a.Ea));window.PerformanceLongTaskTiming&&(b+=eq("cbt",a.B), 
b+=eq("mbt",a.J),b+=eq("nlt",a.ra));let d=0;for(var c of document.getElementsByTagName("iframe"))hq(c)&&d++;b+=eq("nif",d);b+=eq("ifi",ue(window));c=K($g).g();b+=`&${"eid"}=${encodeURIComponent(c.join())}`;b+=`&${"top"}=${q===q.top?1:0}`;b+=a.Na?`&${"qqid"}=${encodeURIComponent(a.Na)}`:eq("pvsid",de(q));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.M?a.Ga:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=eq("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include", 
redirect:"follow",method:"get",mode:"no-cors"});a.Da()}}function xq(a,b,c,d){if(!b.hadRecentInput){a.D+=Number(b.value);Number(b.value)>a.Y&&(a.Y=Number(b.value));a.qa+=1;if(c=iq(b,c,d))a.A+=b.value,a.Ka++;if(b.startTime-a.Ja>5E3||b.startTime-a.Ma>1E3)a.Ja=b.startTime,a.i=0,a.j=0;a.Ma=b.startTime;a.i+=b.value;c&&(a.j+=b.value);a.i>a.sa&&(a.sa=a.i,a.Pa=a.j,a.Oa=b.startTime+b.duration)}} 
function yq(a,b){Aq(a,b);const c=a.g[a.g.length-1],d=a.F[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(d.entries.push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.F[b.id]=b,a.g.push(b)),a.g.sort((e,f)=>f.latency-e.latency),a.g.splice(10).forEach(e=>{delete a.F[e.id]})}function Aq(a,b){b.interactionId&&(a.pa=Math.min(a.pa,b.interactionId),a.u=Math.max(a.u,b.interactionId),a.Ga=a.u?(a.u-a.pa)/7+1:0)} 
var tq=class{constructor(){this.j=this.i=this.qa=this.Y=this.D=0;this.Ma=this.Ja=Number.NEGATIVE_INFINITY;this.g=[];this.F={};this.Ga=0;this.pa=Infinity;this.Ea=this.Ha=this.Ia=this.Ka=this.Pa=this.A=this.Oa=this.sa=this.u=0;this.Fa=!1;this.ra=this.J=this.B=0;this.M=null;this.La=!1;this.Da=()=>{};const a=document.querySelector("[data-google-query-id]");this.Na=a?a.getAttribute("data-google-query-id"):null;this.gb={qb:!0}}},pq,qq,rq=[];let Bq=null;const Cq=[],Dq=new Map;let Eq=-1;function Fq(a){return fj.test(a.className)&&a.dataset.adsbygoogleStatus!=="done"}function Gq(a,b,c){a.dataset.adsbygoogleStatus="done";Hq(a,b,c)} 
function Hq(a,b,c){var d=window,e=b.google_reactive_ads_config;e||No(a,b,d,c);Pn(d,b);if(!Iq(a,b,d)){if(e){e=e.page_level_pubvars||{};if(X(L).page_contains_reactive_tag&&!X(L).allow_second_reactive_tag){if(e.pltais){Am(!1);return}throw new T("Only one 'enable_page_level_ads' allowed per page.");}X(L).page_contains_reactive_tag=!0;Am(e.google_pgb_reactive===7)}b.google_unique_id=te(d);Rd(Tp,(f,g)=>{b[g]=b[g]||d[g]});b.google_loader_used!=="sd"&&(b.google_loader_used="aa");b.google_reactive_tag_first= 
(X(L).first_tag_on_page||0)===1;jk(164,()=>{Un(d,b,a,c)})}} 
function Iq(a,b,c){var d=b.google_reactive_ads_config,e=typeof a.className==="string"&&RegExp("(\\W|^)adsbygoogle-noablate(\\W|$)").test(a.className),f=ym(c);if(f&&f.Qa&&b.google_adtest!=="on"&&!e){e=Pi(a,c);const g=Li(c).clientHeight;e=g===0?null:e/g;if(!f.ta||f.ta&&(e||0)>=f.ta)return a.className+=" adsbygoogle-ablated-ad-slot",c=c.google_sv_map=c.google_sv_map||{},d=la(a),b.google_element_uid=d,c[b.google_element_uid]=b,a.setAttribute("google_element_uid",String(d)),f.Kb==="slot"&&(Vd(a.getAttribute("width"))!== 
null&&a.setAttribute("width","0"),Vd(a.getAttribute("height"))!==null&&a.setAttribute("height","0"),a.style.width="0px",a.style.height="0px"),!0}if((f=Pd(a,c))&&f.display==="none"&&!(b.google_adtest==="on"||b.google_reactive_ad_format>0||d))return c.document.createComment&&a.appendChild(c.document.createComment("No ad requested because of display:none on the adsbygoogle tag")),!0;a=b.google_pgb_reactive==null||b.google_pgb_reactive===3;return b.google_reactive_ad_format!==1&&b.google_reactive_ad_format!== 
8||!a?!1:(q.console&&q.console.warn("Adsbygoogle tag with data-reactive-ad-format="+String(b.google_reactive_ad_format)+" is deprecated. Check out page-level ads at https://www.google.com/adsense"),!0)}function Jq(a){var b=document.getElementsByTagName("INS");for(let d=0,e=b[d];d<b.length;e=b[++d]){var c=e;if(Fq(c)&&c.dataset.adsbygoogleStatus!=="reserved"&&(!a||e.id===a))return e}return null} 
function Kq(a,b,c){if(a&&"shift"in a){bp(e=>{var f=Cf(e);Oc(f,2)||(e=Cf(e),Wc(e,2))});for(var d=20;a.length>0&&d>0;){try{Lq(a.shift(),b,c)}catch(e){setTimeout(()=>{throw e;})}--d}}}function Mq(){const a=Od("INS");a.className="adsbygoogle";a.className+=" adsbygoogle-noablate";Yd(a);return a} 
function Nq(a,b){const c={},d=Om(a.google_ad_client,b);Rd(Ki,(g,h)=>{a.enable_page_level_ads===!1?c[h]=!1:a.hasOwnProperty(h)?c[h]=a[h]:d.includes(g)&&(c[h]=!1)});ka(a.enable_page_level_ads)&&(c.page_level_pubvars=a.enable_page_level_ads);const e=Mq();ee.body.appendChild(e);const f={google_reactive_ads_config:c,google_ad_client:a.google_ad_client};f.google_pause_ad_requests=!!X(L).pause_ad_requests;$p(Oq(a)||xn(L),f,b);Gq(e,f,b);bp(g=>{var h=Cf(g);Oc(h,6)||(g=Cf(g),Wc(g,6))})} 
function Pq(a,b){nn(q).wasPlaTagProcessed=!0;const c=()=>{Nq(a,b)},d=q.document;if(d.body||d.readyState==="complete"||d.readyState==="interactive")Nq(a,b);else{const e=od(kk(191,c));ge(d,"DOMContentLoaded",e);q.MutationObserver!=null&&(new q.MutationObserver((f,g)=>{d.body&&(e(),g.disconnect())})).observe(d,{childList:!0,subtree:!0})}} 
function Lq(a,b,c){const d={};jk(165,()=>{Qq(a,d,b,c)},e=>{e.client=e.client||d.google_ad_client||a.google_ad_client;e.slotname=e.slotname||d.google_ad_slot;e.tag_origin=e.tag_origin||d.google_tag_origin})}function Rq(a){delete a.google_checked_head;Rd(a,(b,c)=>{ej[c]||(delete a[c],b=c.replace("google","data").replace(/_/g,"-"),q.console.warn(`AdSense head tag doesn't support ${b} attribute.`))})} 
function Sq(a,b){var c=L.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])');if(c){c.setAttribute("data-checked-head", 
"true");var d=X(window);if(d.head_tag_slot_vars)Tq(c);else{bp(g=>{g=Cf(g);zc(g,7,Kb(!0),!1)});var e={};Lo(c,e);Rq(e);e.google_ad_intent_query&&(e.google_responsive_auto_format=17,Q(qi)&&(e.google_reactive_ad_format=42));var f=ud(e);d.head_tag_slot_vars=f;c={google_ad_client:e.google_ad_client,enable_page_level_ads:e};e.google_ad_intent_query&&(c.enable_ad_intent_display_ads=!0);e.google_overlays==="bottom"&&(c.overlays={bottom:!0});delete e.google_overlays;L.adsbygoogle||(L.adsbygoogle=[]);d=L.adsbygoogle; 
d.loaded?d.push(c):d.splice&&d.splice(0,0,c);b=Pm(b)?.u();e.google_adbreak_test||b?Uq(f,a):dq(()=>{Uq(f,a)})}}}function Tq(a){const b=X(window).head_tag_slot_vars,c=a.getAttribute("src")||"";if((a=Hd(c,"client")||a.getAttribute("data-ad-client")||"")&&a!==b.google_ad_client)throw new T("Warning: Do not add multiple property codes with AdSense tag to avoid seeing unexpected behavior. These codes were found on the page "+a+", "+b.google_ad_client);} 
function Vq(a){if(typeof a==="object"&&a!=null){if(typeof a.type==="string")return 2;if(typeof a.sound==="string"||typeof a.preloadAdBreaks==="string"||typeof a.h5AdsConfig==="object")return 3}return 0} 
function Qq(a,b,c,d){if(a==null)throw new T("push() called with no parameters.");bp(f=>{var g=Cf(f);Oc(g,3)||(f=Cf(f),Wc(f,3))});var e=Vq(a);if(e!==0)if(d=Bm(),d.first_slotcar_request_processing_time||(d.first_slotcar_request_processing_time=Date.now(),d.adsbygoogle_execution_start_time=eh),Bq==null)Wq(a),Cq.push(a);else if(e===3){const f=Bq;jk(787,()=>{f.handleAdConfig(a)})}else lk(730,Bq.handleAdBreak(a));else{eh=(new Date).getTime();Qn(c,d,Oq(a));Xq();a:{if(!a.enable_ad_intent_display_ads&&a.enable_page_level_ads!= 
void 0){if(typeof a.google_ad_client==="string"){e=!0;break a}throw new T("'google_ad_client' is missing from the tag config.");}e=!1}if(e)bp(f=>{var g=Cf(f);Oc(g,4)||(f=Cf(f),Wc(f,4))}),Yq(a,d);else if((e=a.params)&&Rd(e,(f,g)=>{b[g]=f}),b.google_ad_output==="js")console.warn("Ads with google_ad_output='js' have been deprecated and no longer work. Contact your AdSense account manager or switch to standard AdSense ads.");else{$p(Oq(a)||xn(L),b,d);e=Zq(b,a);Lo(e,b);c=X(q).head_tag_slot_vars||{};Rd(c, 
(f,g)=>{b.hasOwnProperty(g)||(b[g]=f)});if(e.hasAttribute("data-require-head")&&!X(q).head_tag_slot_vars)throw new T("AdSense head tag is missing. AdSense body tags don't work without the head tag. You can copy the head tag from your account on https://adsense.com.");if(!b.google_ad_client)throw new T("Ad client is missing from the slot.");if(c=(X(L).first_tag_on_page||0)===0&&qn(b))bp(f=>{var g=Cf(f);Oc(g,5)||(f=Cf(f),Wc(f,5))}),$q(c);(X(L).first_tag_on_page||0)===0&&(X(L).first_tag_on_page=2);b.google_pause_ad_requests= 
!!X(L).pause_ad_requests;Gq(e,b,d)}}}function Oq(a){return a.google_ad_client?a.google_ad_client:(a=a.params)&&a.google_ad_client?a.google_ad_client:""}function Xq(){if(Q(ki)){const a=ym(L);a&&a.Qa||zm(L)}}function $q(a){fe(()=>{nn(q).wasPlaTagProcessed||q.adsbygoogle&&q.adsbygoogle.push(a)})}function Yq(a,b){(X(L).first_tag_on_page||0)===0&&(X(L).first_tag_on_page=1);if(a.tag_partner){var c=a.tag_partner;const d=X(q);d.tag_partners=d.tag_partners||[];d.tag_partners.push(c)}rn(a,b);Pq(a,b)} 
function Zq(a,b){if(a.google_ad_format==="rewarded"){if(a.google_ad_slot==null)throw new T("Rewarded format does not have valid ad slot");if(a.google_ad_loaded_callback==null)throw new T("Rewarded format does not have ad loaded callback");a.google_reactive_ad_format=11;a.google_wrap_fullscreen_ad=!0;a.google_video_play_muted=!1;a.google_acr=a.google_ad_loaded_callback;delete a.google_ad_loaded_callback;delete a.google_ad_format}var c=!!a.google_wrap_fullscreen_ad;if(c)b=Mq(),b.dataset.adsbygoogleStatus= 
"reserved",ee.documentElement.appendChild(b);else if(b=b.element){if(!Fq(b)&&(b.id?b=Jq(b.id):b=null,!b))throw new T("'element' has already been filled.");if(!("innerHTML"in b))throw new T("'element' is not a good DOM element.");}else if(b=Jq(),!b)throw new T("All 'ins' elements in the DOM with class=adsbygoogle already have ads in them.");if(c){c=L;try{const e=(c||window).document,f=e.compatMode=="CSS1Compat"?e.documentElement:e.body;var d=(new oe(f.clientWidth,f.clientHeight)).round()}catch(e){d= 
new oe(-12245933,-12245933)}a.google_ad_height=d.height;a.google_ad_width=d.width;a.fsapi=!0}return b}function ar(a){Tk().S[Wk(26)]=!!Number(a)} 
function br(a){Number(a)?X(L).pause_ad_requests=!0:(X(L).pause_ad_requests=!1,a=()=>{if(!X(L).pause_ad_requests){var b={};let c;typeof window.CustomEvent==="function"?c=new CustomEvent("adsbygoogle-pub-unpause-ad-requests-event",b):(c=document.createEvent("CustomEvent"),c.initCustomEvent("adsbygoogle-pub-unpause-ad-requests-event",!!b.bubbles,!!b.cancelable,b.detail));L.dispatchEvent(c)}},q.setTimeout(a,0),q.setTimeout(a,1E3))}function cr(a){typeof a==="function"&&window.setTimeout(a,0)} 
function Uq(a,b){const c={...hn()},d=R(Ai);[0,1].includes(d)&&(c.osttc=`${d}`);b=mn(Jd(b.Jb,new Map(Object.entries(c)))).then(e=>{Bq==null&&(e.init(a),Bq=e,dr(e))});lk(723,b);b.finally(()=>{Cq.length=0;mk("slotcar",{event:"api_ld",time:Date.now()-eh,time_pr:Date.now()-Eq});hp(K(cp),Gf(23))})} 
function dr(a){for(const [c,d]of Dq){var b=c;const e=d;e!==-1&&(q.clearTimeout(e),Dq.delete(b))}for(b=0;b<Cq.length;b++){if(Dq.has(b))continue;const c=Cq[b],d=Vq(c);jk(723,()=>{d===3?a.handleAdConfig(c):d===2&&lk(730,a.handleAdBreakBeforeReady(c))})}} 
function Wq(a){var b=Cq.length;if(Vq(a)===2&&a.type==="preroll"&&a.adBreakDone!=null){var c=a.adBreakDone;Eq===-1&&(Eq=Date.now());var d=q.setTimeout(()=>{try{c({breakType:"preroll",breakName:a.name,breakFormat:"preroll",breakStatus:"timeout"}),Dq.set(b,-1),mk("slotcar",{event:"pr_to",source:"adsbygoogle"}),hp(K(cp),Gf(22))}catch(e){console.error("[Ad Placement API] adBreakDone callback threw an error:",e instanceof Error?e:Error(String(e)))}},R(Ci)*1E3);Dq.set(b,d)}};(function(a,b,c,d=()=>{}){fk.J(ok);jk(166,()=>{const e=new vg(2,a);try{Za(l=>{Xj(e,1191,l)})}catch(l){}const f=Yp(b);var g=gn(f);Sp(f,g);d();ne(16,[1,x(f)]);var h=qe(pe(L))||L,k=wn({va:a,Ba:G(f,2)});const n=c(k,f);k=L.document.currentScript===null?1:kp(n.Lb);Qp(h,f,k);Q(xi)&&G(f,29)&&aq(h.document,cq(G(f,29)));bp(l=>{var m=F(l,1)+1;Uc(l,1,m);L.top===L&&(m=F(l,2)+1,Uc(l,2,m));m=Cf(l);Oc(m,1)||(l=Cf(l),Wc(l,1))});lk(1086,fp(k===0));if(!Ea()||ua(Ha(),11)>=0){hk(Q(Fi));Xn();im(Fc(f,Im,26));try{sq()}catch{}Wn(); 
Sq(n,f);h=window;k=h.adsbygoogle;if(!k||!k.loaded){g={push:l=>{Lq(l,n,f)},loaded:!0,pageState:JSON.stringify(x(g))};try{Object.defineProperty(g,"requestNonPersonalizedAds",{set:ar}),Object.defineProperty(g,"pauseAdRequests",{set:br}),Object.defineProperty(g,"onload",{set:cr})}catch{}if(k)for(const l of["requestNonPersonalizedAds","pauseAdRequests"])k[l]!==void 0&&(g[l]=k[l]);Kq(k,n,f);h.adsbygoogle=g;k&&(g.onload=k.onload)}}})})(eg(),typeof sttc==="undefined"?void 0:sttc,function(a,b){b=F(b,1)>2012? 
`_fy${F(b,1)}`:"";Id`data:text/javascript,//show_ads_impl_preview.js`;return{Jb:Id`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${b}.js`,Hb:Id`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl${b}.js`,Gb:Id`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl_with_ama${b}.js`,Lb:/^(?:https?:)?\/\/(?:pagead2\.googlesyndication\.com|securepubads\.g\.doubleclick\.net)\/pagead\/(?:js\/)?(?:show_ads|adsbygoogle(_direct)?)\.js(?:[?#].*)?$/}}); 
}).call(this,"[2021,\"r20250506\",\"r20190131\",null,null,null,null,null,null,null,null,[[[698926295,null,null,[1]],[null,619278254,null,[null,10]],[676894296,null,null,[1]],[682658313,null,null,[1]],[null,1130,null,[null,100]],[45694447,null,null,[]],[null,1340,null,[null,0.2]],[null,1338,null,[null,0.3]],[null,1336,null,[null,1]],[null,1339,null,[null,0.3]],[null,1032,null,[null,200],[[[12,null,null,null,4,null,\"Android\",[\"navigator.userAgent\"]],[null,500]]]],[null,728201648,null,[null,100]],[null,1224,null,[null,0.01]],[null,1346,null,[null,6]],[null,1347,null,[null,3]],[null,1343,null,[null,300]],[1384,null,null,[]],[null,1263,null,[null,-1]],[null,1323,null,[null,-1]],[null,1265,null,[null,-1]],[null,1264,null,[null,-1]],[1267,null,null,[1]],[null,66,null,[null,-1]],[null,65,null,[null,-1]],[1241,null,null,[1]],[1300,null,null,[1]],[null,null,null,[null,null,null,[\"en\",\"de\",\"fr\",\"es\",\"ja\"]],null,1273],[null,null,null,[null,null,null,[\"44786015\",\"44786016\"]],null,1261],[1318,null,null,[1]],[1372,null,null,[1]],[45682913,null,null,[1]],[622128248,null,null,[]],[null,null,null,[null,null,null,[\"29_18\",\"30_19\"]],null,null,null,635821288],[null,null,716722045,[null,null,\"600px\"]],[636570127,null,null,[1]],[null,626062006,null,[null,670]],[null,666400580,null,[null,22]],[null,null,null,[null,null,null,[\"\",\"ar\",\"bn\",\"en\",\"es\",\"fr\",\"hi\",\"id\",\"ja\",\"ko\",\"mr\",\"pt\",\"ru\",\"sr\",\"te\",\"th\",\"tr\",\"uk\",\"vi\",\"zh\"]],null,712458671],[748685380,null,null,[1]],[748689018,null,null,[1]],[null,null,null,[],null,null,null,683929765],[742688665,null,null,[1]],[655991266,null,null,[1]],[750577535,null,null,[]],[750973575,null,null,[1]],[null,717888910,null,[null,0.7]],[null,643258048,null,[null,0.15]],[null,643258049,null,[null,0.16]],[null,618163195,null,[null,15000]],[null,624950166,null,[null,15000]],[null,623405755,null,[null,300]],[null,508040914,null,[null,500]],[null,547455356,null,[null,49]],[null,717888911,null,[null,0.7]],[null,717888912,null,[null,0.7]],[null,727864505,null,[null,3]],[null,652486359,null,[null,3]],[null,748662193,null,[null,8]],[null,688905693,null,[null,2]],[null,650548030,null,[null,2]],[null,650548032,null,[null,300]],[null,650548031,null,[null,1]],[null,655966487,null,[null,300]],[null,655966486,null,[null,250]],[null,687270738,null,[null,500]],[null,469675170,null,[null,60000]],[675298507,null,null,[]],[711741274,null,null,[]],[null,684147713,null,[null,-1]],[null,684147711,null,[null,-1]],[null,684147712,null,[null,-1]],[45675667,null,null,[1]],[570863962,null,null,[1]],[null,null,570879859,[null,null,\"control_1\\\\.\\\\d\"]],[null,570863961,null,[null,50]],[570879858,null,null,[1]],[10024,null,null,[1]],[10020,null,null,[1]],[10018,null,null,[1]],[null,1085,null,[null,5]],[null,63,null,[null,30]],[null,1080,null,[null,5]],[null,10019,null,[null,5]],[null,1027,null,[null,10]],[null,57,null,[null,120]],[1134,null,null,[1]],[null,1079,null,[null,5]],[null,1050,null,[null,30]],[null,58,null,[null,120]],[null,10021,null,[null,1.5]],[751557128,null,null,[]],[715572365,null,null,[1]],[715572366,null,null,[1]],[555237685,null,null,[1]],[null,732217386,null,[null,10000]],[null,732217387,null,[null,500]],[null,733329086,null,[null,30000]],[null,629808663,null,[null,100]],[null,736623795,null,[null,250]],[null,745376892,null,[null,1]],[null,745376893,null,[null,2]],[null,550718588,null,[null,250]],[null,624290870,null,[null,50]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[10,[[50,[[31067422],[31067423,[[null,1032,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31083552],[44776368]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31084127],[31084128]]],[1,[[31089421],[31089422,[[676460084,null,null,[1]]]]],null,139,null,null,null,998,null,null,null,null,null,8],[1,[[31089423],[31089424]],[4,null,61],139,null,null,null,998,null,null,null,null,null,8],[1,[[31089628],[31089629,[[710737579,null,null,[1]]]]]],[100,[[31092192],[31092193,[[10025,null,null,[1]]]]]],[1,[[31092194],[31092195,[[751557128,null,null,[1]]]]]],[50,[[31092196],[31092197,[[750586557,null,null,[1]]]]]],[1,[[31092200],[31092201,[[45689742,null,null,[1]]]]]],[1000,[[31092280,[[null,null,14,[null,null,\"31092280\"]]],[6,null,null,null,6,null,\"31092280\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092281,[[null,null,14,[null,null,\"31092281\"]]],[6,null,null,null,6,null,\"31092281\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092319,[[null,null,14,[null,null,\"31092319\"]]],[6,null,null,null,6,null,\"31092319\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092320,[[null,null,14,[null,null,\"31092320\"]]],[6,null,null,null,6,null,\"31092320\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092327,[[null,null,14,[null,null,\"31092327\"]]],[6,null,null,null,6,null,\"31092327\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092328,[[null,null,14,[null,null,\"31092328\"]]],[6,null,null,null,6,null,\"31092328\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1,[[42531513],[42531514,[[316,null,null,[1]]]]]],[1,[[42531644],[42531645,[[368,null,null,[1]]]],[42531646,[[369,null,null,[1]],[368,null,null,[1]]]]]],[50,[[42531705],[42531706]]],[1,[[42532242],[42532243,[[1256,null,null,[1]],[290,null,null,[1]]]]]],[50,[[42532523],[42532524,[[1300,null,null,[]]]]]],[null,[[42532525],[42532526]]],[100,[[42533293],[42533294,[[1383,null,null,[1]],[null,54,null,[null,100]],[null,66,null,[null,10]],[null,65,null,[null,1000]]]]],null,145],[1,[[44801778],[44801779,[[506914611,null,null,[1]]]]],[4,null,55],143],[50,[[95331832],[95331833,[[1342,null,null,[1]]]]]],[10,[[95332584],[95332585,[[null,1343,null,[null,600]]]],[95332586,[[null,1343,null,[null,900]]]],[95332587,[[null,1343,null,[null,1200]]]]]],[10,[[95332923],[95332924,[[null,1338,null,[null,0.8]]]],[95332925,[[null,1339,null,[null,0.8]]]],[95332926,[[null,1340,null,[null,0.8]]]],[95332927,[[null,1340,null,[null,0.8]],[null,1338,null,[null,0.8]],[null,1339,null,[null,0.8]]]]]],[10,[[95333409],[95333410,[[null,1346,null,[null,-1]],[null,1347,null,[null,-1]]]],[95333411,[[null,1346,null,[null,3]],[null,1347,null,[null,1]]]],[95333412,[[null,1346,null,[null,8]],[null,1347,null,[null,5]]]]]],[360,[[95334516,[[null,null,null,[null,null,null,[\"95334518\"]],null,null,null,630330362]]],[95334517,[[626390500,null,null,[1]],[null,null,null,[null,null,null,[\"95334519\"]],null,null,null,630330362]]]],[2,[[4,null,55],[12,null,null,null,2,null,\"buzzfun\\\\.me\/|diggfun\\\\.co\/|indiaimagine\\\\.com\/\"]]],143],[50,[[95344787,[[null,null,null,[null,null,null,[\"95344792\"]],null,null,null,630330362]]],[95344788,[[566279275,null,null,[1]],[622128248,null,null,[1]],[null,null,null,[null,null,null,[\"95344793\"]],null,null,null,630330362]]],[95344789,[[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344794\"]],null,null,null,630330362]]],[95344790,[[566279275,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344795\"]],null,null,null,630330362]]],[95344791,[[566279275,null,null,[1]],[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344796\"]],null,null,null,630330362]]]],[4,null,55],143],[1,[[95345037],[95345038,[[1377,null,null,[1]]]]],[4,null,55]],[10,[[95352051,[[null,null,null,[null,null,null,[\"95352054\"]],null,null,null,630330362]]],[95352052,[[null,643258048,null,[]],[null,null,null,[null,null,null,[\"95352055\"]],null,null,null,630330362]]],[95352053,[[null,643258048,null,[]],[null,643258049,null,[]],[null,null,null,[null,null,null,[\"95352056\"]],null,null,null,630330362]]]],[4,null,55],144],[100,[[95354562],[95354563,[[1382,null,null,[1]]]]],[4,null,55]],[100,[[95354564],[95354565]],[4,null,55]],[null,[[95358213,[[null,null,null,[null,null,null,[\"95358215\"]],null,null,null,630330362]]],[95358214,[[747408261,null,null,[1]],[null,null,null,[null,null,null,[\"95358216\"]],null,null,null,630330362]]]],[4,null,55]],[10,[[95358621],[95358622,[[1384,null,null,[1]]]]]],[125,[[95359114,[[null,null,null,[null,null,null,[\"95359114\"]],null,null,null,630330362]]],[95359115,[[null,508040914,null,[null,500]],[null,717888912,null,[null,0.65]],[null,650548030,null,[null,2]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359115\"]],null,null,null,630330362]]],[95359116,[[null,508040914,null,[null,606]],[null,717888912,null,[null,0.5865572741861419]],[null,650548030,null,[null,3]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359116\"]],null,null,null,630330362]]],[95359117,[[null,508040914,null,[null,388]],[null,717888912,null,[null,0.717174870428953]],[null,650548030,null,[null,2]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359117\"]],null,null,null,630330362]]],[95359118,[[null,508040914,null,[null,383]],[null,717888912,null,[null,0.57923590775794542]],[null,650548030,null,[null,1]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359118\"]],null,null,null,630330362]]],[95359119,[[null,508040914,null,[null,512]],[null,717888912,null,[null,0.643067974618898]],[null,650548030,null,[null,1]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359119\"]],null,null,null,630330362]]],[95359120,[[null,508040914,null,[null,565]],[null,717888912,null,[null,0.58827036950502865]],[null,650548030,null,[null,2]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359120\"]],null,null,null,630330362]]],[95359121,[[null,508040914,null,[null,444]],[null,717888912,null,[null,0.63648377059048022]],[null,650548030,null,[null,2]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359121\"]],null,null,null,630330362]]]],[4,null,55],146],[500,[[95359239,[[null,null,null,[null,null,null,[\"95359239\",\"95359241\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95359241\"]],null,null,null,630330362]]],[95359240,[[null,null,716722045,[null,null,\"max(600px, calc(\\u003cDH\\u003e - 102px))\"]],[741481545,null,null,[1]],[null,null,null,[null,null,null,[\"95359240\",\"95359242\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95359242\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[95359265],[95359266,[[45650867,null,null,[1]]]]],null,130,null,null,null,null,null,null,null,null,null,7]]],[17,[[10,[[31084487],[31084488]],null,null,null,null,32,null,null,142,1],[10,[[31089209],[31089210]],null,null,null,null,39,null,null,189,1],[10,[[31090956],[31090957,[[733329085,null,null,[1]]]],[31092112,[[733329085,null,null,[1]],[749213612,null,null,[1]]]]],null,null,null,null,null,500,null,198,1],[10,[[31091205],[31091206,[[732217385,null,null,[1]]]],[31091638,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]]]],[31091871,[[732217385,null,null,[1]],[745631622,null,null,[1]]]],[31091872,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]],[745631622,null,null,[1]]]]],null,null,null,null,null,700,null,198,1],[10,[[31091243],[31091244,[[736623794,null,null,[1]]]],[31091873,[[736623794,null,null,[1]],[null,745376892,null,[null,2]],[null,745376893,null,[null,4]]]]],null,null,null,null,null,800,null,198,1],[1,[[31092293],[31092294,[[752401956,null,null,[1]]]],[31092295,[[752401956,null,null,[1]],[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,300,null,200,1],[50,[[95356797,[[null,652486359,null,[null,9]],[null,null,null,[null,null,null,[\"95356799\"]],null,null,null,630330362]]],[95356798,[[null,652486359,null,[null,11]],[null,null,null,[null,null,null,[\"95356800\"]],null,null,null,630330362]]],[95356807,[[null,null,null,[null,null,null,[\"95356810\"]],null,null,null,630330362]]],[95356808,[[null,652486359,null,[null,5]],[null,null,null,[null,null,null,[\"95356811\"]],null,null,null,630330362]]],[95356809,[[null,652486359,null,[null,7]],[null,null,null,[null,null,null,[\"95356812\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,204,1],[50,[[95359271],[95359272,[[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,200,null,200,1],[100,[[95359475],[95359476,[[null,1336,null,[null,1.2]]]]],null,null,null,null,null,null,null,211,1],[50,[[95360294,[[null,null,null,[null,null,null,[\"95360294\",\"95360296\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95360296\"]],null,null,null,630330362]]],[95360295,[[655991266,null,null,[]],[null,null,null,[null,null,null,[\"95360295\",\"95360297\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95360297\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,212]]],[11,[[50,[[31092107],[31092108]],null,122,null,null,null,null,null,null,null,null,null,4]]]],null,null,[null,1000,1,1000]],null,null,\"31092281\",null,\"fauxid.com\",819397136,[95358863,95358865],null,null,null,null,null,null,[0,0,1],[null,[\"ca-pub-0583047843012866\",1],1,null,[[[[null,0,null,null,null,null,\"DIV.main-content\\u003eDIV.core-msg.spacer.font-red\"],1,[\"32px\",\"32px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV.main-content\"],4,[\"10px\",\"128px\",1],[2],null,null,null,1]],[[null,[1,3,2],null,\"6246676918\",null,null,[0,2],null,null,[0.5,null,1]]]],[null,null,[1,2,7]]],null,\"m202505060101\"]");
