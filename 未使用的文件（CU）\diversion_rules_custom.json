{"rules": [{"outbound": "direct", "name": "反代直连", "switch": true, "or": true, "domain": ["share.lzf.email", "gt.551543.xyz", "hub.gitmirror.com", "cfgit-3ft.pages.dev", "gt.nariahlamb31.us.kg"], "domain_keyword": ["js<PERSON><PERSON><PERSON>"]}, {"outbound": "urltest", "name": "🤖 AI", "switch": true, "or": true, "domain": ["chat.openai.com", "platform.openai.com", "gemini.google.com", "ai.google.dev", "bard.google.com", "claude.ai", "anthropic.com", "copilot.microsoft.com", "bing.com", "perplexity.ai", "character.ai", "poe.com", "huggingface.co", "stability.ai", "midjourney.com", "replicate.com", "deepmind.com", "forefront.ai", "writesonic.com", "jasper.ai", "copy.ai", "you.com", "phind.com", "cohere.ai", "inflection.ai", "together.ai", "deepl.com", "runway.ai", "leonardo.ai", "cloudflare.com", "play-fe.googleapis.com", "www.perplexity.ai"], "domain_keyword": ["gpt", "chatgpt", "claude", "copilot", "chat", "gemini", "cloudflare"], "rule_set_build_in": ["geosite:openai", "geoip:openai", "acl:OpenAi", "acl:Gemini", "geosite:google-gemini", "acl:<PERSON>", "acl:ClaudeAI", "geosite:perplexity"], "package": ["com.dison.chatX", "cc.arthur63.chatbot", "ai.chat.gpt.bot", "com.microsoft.copilot", "com.google.android.apps.bard", "ai.perplexity.app.android", "com.twitter.android"], "process_name": ["Cursor.exe", "Cherry Studio.exe", "msedgewebview2.exe"]}, {"outbound": "currentSelected", "name": "✈️代理", "switch": true, "or": true, "domain_suffix": ["423down.com", "ping0.cc", "ip38.com", "linux.do"], "domain": ["ip38.com", "my.551543.xyz", "web3.52pokemon.cc"], "domain_keyword": ["zashboard"], "ip_cidr": ["************"], "rule_set_build_in": ["geosite:geolocation-!cn"], "package": ["substor.e"]}, {"outbound": "currentSelected", "name": "🌏 Google Play", "switch": true, "or": true, "rule_set_build_in": ["geosite:google-play"], "package": ["com.android.vending"]}, {"outbound": "currentSelected", "name": "🎦Youtube", "switch": true, "or": true, "rule_set_build_in": ["geosite:youtube", "acl:YouTube", "acl:YouTubeMusic", "geosite:medium"]}, {"outbound": "direct", "name": "🏰直连", "switch": true, "or": true, "domain": ["api.curseforge.com", "api.gametimedev.de", "media.forgecdn.net", "gt.551543.xyz"], "ip_cidr": ["***********/32"], "port": [8199, 8299, 9876, 9867], "rule_set_build_in": ["acl:GameDownload", "geosite:category-android-app-download", "acl:ChinaIp", "acl:ChinaDomain", "acl:ChinaCompanyIp", "acl:UnBan", "acl:SteamCN", "acl:Download", "acl:ChinaMedia"], "package": ["com.tencent.tim", "com.taptap", "cn.skyrin.ntfh", "com.jingdong.app.mall", "com.xingin.xhs", "com.happyelements.AndroidAnimal", "com.tencent.mm", "com.xunmeng.pinduoduo", "com.sohu.inputmethod.sogou.xiaomi", "com.eg.android.AlipayGphone", "com.dragon.read", "com.zhihu.android", "com.baidu.tieba", "com.sankuai.meituan", "com.mt.hongbao", "com.linchuan.meituanredpackage", "com.cainiao.wireless", "cn.longtu.pages", "me.ele", "com.alicloud.databox", "com.taobao.idlefish", "com.coolapk.market"]}]}