(function(sttc){'use strict';var aa=Object.defineProperty,ca=globalThis,da=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ea={},fa={};function ha(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ia(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in ea?f=ea:f=ca;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=da&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(ea,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=da?ca.Symbol(d):"$jscp$"+a+"$"+d),aa(f,fa[d],{configurable:!0,writable:!0,value:b})))}}function q(a){return a} 
ia("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var r=this||self;function ja(a){a=a.split(".");for(var b=r,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function ka(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function la(a){return Object.prototype.hasOwnProperty.call(a,ma)&&a[ma]||(a[ma]=++na)}var ma="closure_uid_"+(Math.random()*1E9>>>0),na=0;function oa(a,b,c){return a.call.apply(a.bind,arguments)} 
function pa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function qa(a,b,c){qa=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?oa:pa;return qa.apply(null,arguments)} 
function ra(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function sa(a,b,c){a=a.split(".");c=c||r;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function ta(a){r.setTimeout(()=>{throw a;},0)};function ua(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]} 
function va(a,b){let c=0;a=ua(String(a)).split(".");b=ua(String(b)).split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=wa(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||wa(e[2].length==0,f[2].length==0)||wa(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c}function wa(a,b){return a<b?-1:a>b?1:0};var xa,ya=ja("CLOSURE_FLAGS"),za=ya&&ya[610401301];xa=za!=null?za:!1;function Aa(){var a=r.navigator;return a&&(a=a.userAgent)?a:""}var Ba;const Ca=r.navigator;Ba=Ca?Ca.userAgentData||null:null;function Da(a){if(!xa||!Ba)return!1;for(let b=0;b<Ba.brands.length;b++){const {brand:c}=Ba.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function u(a){return Aa().indexOf(a)!=-1};function Ea(){return xa?!!Ba&&Ba.brands.length>0:!1}function Fa(){return Ea()?!1:u("Trident")||u("MSIE")}function Ga(){return Ea()?Da("Chromium"):(u("Chrome")||u("CriOS"))&&!(Ea()?0:u("Edge"))||u("Silk")}function Ha(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""} 
function Ia(){var a=Aa();if(Fa()){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])a=b[1];else{b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];a=b}return a}c=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");b=[];let d;for(;d=c.exec(a);)b.push([d[1],d[2],d[3]||void 0]);a=Ha(b);return(Ea()?0:u("Opera"))?a(["Version", 
"Opera"]):(Ea()?0:u("Edge"))?a(["Edge"]):(Ea()?Da("Microsoft Edge"):u("Edg/"))?a(["Edg"]):u("Silk")?a(["Silk"]):Ga()?a(["Chrome","CriOS","HeadlessChrome"]):(a=b[2])&&a[1]||""};function Ja(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ka(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function La(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d} 
function Ma(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Na(a,b){a:{var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;c--)if(c in d&&b.call(void 0,d[c],c,a)){b=c;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function Oa(a,b){return Ja(a,b)>=0}function Pa(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};function Qa(a){Qa[" "](a);return a}Qa[" "]=function(){};var Ra=null;function Sa(a){const b=[];Va(a,function(c){b.push(c)});return b}function Va(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Ra[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Wa();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Wa(){if(!Ra){Ra={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));for(let e=0;e<d.length;e++){const f=d[e];Ra[f]===void 0&&(Ra[f]=e)}}}};function Xa(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Ya=void 0,Za;function $a(a){if(Za)throw Error("");Za=b=>{r.setTimeout(()=>{a(b)},0)}}function ab(a){if(Za)try{Za(a)}catch(b){throw b.cause=a,b;}}function bb(a){a=Error(a);Xa(a,"warning");ab(a);return a};function db(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var eb=db(),fb=db(),gb=db(),hb=db("m_m",!0);const w=db("jas",!0);var ib;const jb=[];jb[w]=7;ib=Object.freeze(jb);function kb(a){if(4&a)return 512&a?512:1024&a?1024:0}function lb(a){a[w]|=32;return a};var mb={};function nb(a,b){return b===void 0?a.i!==ob&&!!(2&(a.C[w]|0)):!!(2&b)&&a.i!==ob}const ob={};var pb=Object.freeze({});function qb(a){return a}function rb(a){a.Ic=!0;return a};var sb=rb(a=>typeof a==="number"),tb=rb(a=>typeof a==="string"),ub=rb(a=>Array.isArray(a));function vb(){return rb(a=>ub(a)?a.every(b=>sb(b)):!1)};function wb(a){if(tb(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(sb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var zb=rb(a=>a>=xb&&a<=yb);const xb=BigInt(Number.MIN_SAFE_INTEGER),yb=BigInt(Number.MAX_SAFE_INTEGER);let Ab=0,Bb=0;function Cb(a){const b=a>>>0;Ab=b;Bb=(a-b)/4294967296>>>0}function Db(a){if(a<0){Cb(-a);a=Ab;var b=Bb;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];Ab=c>>>0;Bb=d>>>0}else Cb(a)}function Eb(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Fb(){var a=Ab,b=Bb,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Eb(a,b);return c};function Gb(a,b=`unexpected value ${a}!`){throw Error(b);};const Hb=typeof BigInt==="function"?BigInt.asIntN:void 0,Ib=Number.isSafeInteger,Jb=Number.isFinite,Kb=Math.trunc;function Lb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Mb(a){if(a!=null&&typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a}const Nb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/; 
function Ob(a){switch(typeof a){case "bigint":return!0;case "number":return Jb(a);case "string":return Nb.test(a);default:return!1}}function Pb(a){if(!Jb(a))throw bb("enum");return a|0}function Qb(a){return a==null?a:Jb(a)?a|0:void 0}function Rb(a){if(typeof a!=="number")throw bb("int32");if(!Jb(a))throw bb("int32");return a|0}function Sb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Jb(a)?a|0:void 0} 
function Tb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Jb(a)?a>>>0:void 0}function Ub(a){if(!Ob(a))throw bb("int64");switch(typeof a){case "string":return Vb(a);case "bigint":return wb(Hb(64,a));default:return Wb(a)}}function Xb(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337} 
function Wb(a){a=Kb(a);if(!Ib(a)){Db(a);var b=Ab,c=Bb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Eb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Yb(a){a=Kb(a);if(Ib(a))a=String(a);else{{const b=String(a);Xb(b)?a=b:(Db(a),a=Fb())}}return a} 
function Vb(a){var b=Kb(Number(a));if(Ib(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));Xb(a)||(a.length<16?Db(Number(a)):(a=BigInt(a),Ab=Number(a&BigInt(4294967295))>>>0,Bb=Number(a>>BigInt(32)&BigInt(4294967295))),a=Fb());return a}function Zb(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return wb(Hb(64,a));if(Ob(a))return b==="string"?(b=Kb(Number(a)),Ib(b)?a=wb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=wb(Hb(64,BigInt(a))))):a=Ib(a)?wb(Wb(a)):wb(Yb(a)),a} 
function $b(a){if(typeof a!=="string")throw Error();return a}function ac(a){if(a!=null&&typeof a!=="string")throw Error();return a}function bc(a){return a==null||typeof a==="string"?a:void 0}function cc(a,b,c,d){if(a!=null&&typeof a==="object"&&a[hb]===mb)return a;if(!Array.isArray(a))return c?d&2?((a=b[eb])||(d=new b,a=d.C,a[w]|=34,a=b[eb]=d),b=a):b=new b:b=void 0,b;c=a[w]|0;d=c|d&32|d&2;d!==c&&(a[w]=d);return new b(a)};function dc(a){return a};function ec(a,b,c,d=!1,e=!1){const f=[];var g=a.length;let h,k=4294967295,n=!1;const l=!!(b&64),m=l?b&128?0:-1:void 0;b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,k=g):h=void 0,!l||b&128||e||(n=!0,k=(fc??dc)(k-m,m,a,h)+m));let p=void 0;for(let v=0;v<g;v++){let t=a[v];if(t!=null&&(t=c(t,d))!=null)if(l&&v>=k){const x=v-m;(p??(p={}))[x]=t}else f[v]=t}if(h)for(let v in h){if(!Object.prototype.hasOwnProperty.call(h,v))continue;a=h[v];if(a==null||(a=c(a,d))==null)continue; 
g=+v;let t;l&&!Number.isNaN(g)&&(t=g+m)<k?f[t]=a:(p??(p={}))[v]=a}p&&(n?f.push(p):f[k]=p);e&&(f[w]=b&16761025|34);return f}function gc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return zb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[w]|0;return a.length===0&&b&1?void 0:ec(a,b,gc)}if(a[hb]===mb)return y(a);return}return a}var hc=typeof structuredClone!="undefined"?structuredClone:a=>ec(a,0,gc);let fc; 
function y(a){a=a.C;return ec(a,a[w]|0,gc)};function ic(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[w]|0;4096&b&&!(2&b)&&jc();if(b&256)throw Error("farr");if(b&64)return b&4096||(a[w]=b|4096),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const f=b&128?0:-1;e-=f;if(e>=1024)throw Error("pvtlmt");for(const g in d){if(!Object.prototype.hasOwnProperty.call(d,g))continue;const h=+g;if(h<e)c[h+f]=d[g],delete d[g];else break}b=b&-16760833|(e&1023)<< 
14}}}a[w]=b|4160;return a}function jc(){if(gb!=null){var a=Ya??(Ya={});var b=a[gb]||0;b>=5||(a[gb]=b+1,a=Error(),Xa(a,"incident"),Za?ab(a):ta(a))}};function kc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[w]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=lc(a,c,b&&!(c&16)):(a[w]|=34,c&4&&Object.freeze(a)));return a}if(a[hb]===mb)return b=a.C,c=b[w]|0,nb(a,c)?a:lc(b,c)}function lc(a,b,c){c??(c=!!(34&b));return ec(a,b,kc,c,!0)}function mc(a){var b=a.C;const c=b[w]|0;if(!nb(a,c))return a;a=new a.constructor(lc(b,c));b=a.C;b[w]&=-3;return a} 
function nc(a){if(a.i!==ob)return!1;let b=a.C;b=lc(b,b[w]|0);b[w]&=-3;a.C=b;a.i=void 0;return!0}function oc(a){if(!nc(a)&&nb(a,a.C[w]|0))throw Error();};const pc=wb(0);function z(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function A(a,b,c){oc(a);const d=a.C;qc(d,d[w]|0,b,c);return a} 
function qc(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&&e>=f){const g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&&(f=(b??(b=a[w]|0))>>14&1023||536870912,c>=f?d!=null&&(a[f+-1]={[c]:d}):a[e]=d);return b}function rc(a,b,c){a=a.C;return sc(a,a[w]|0,b,c)!==void 0}function B(a){return a===pb?2:4} 
function tc(a,b,c,d,e){let f=a.C,g=f[w]|0;d=nb(a,g)?1:d;e=!!e||d===3;d===2&&nc(a)&&(f=a.C,g=f[w]|0);a=uc(f,b);let h=a===ib?7:a[w]|0,k=vc(h,g);var n=4&k?!1:!0;if(n){4&k&&(a=[...a],h=0,k=wc(k,g),g=qc(f,g,b,a));let l=0,m=0;for(;l<a.length;l++){const p=c(a[l]);p!=null&&(a[m++]=p)}m<l&&(a.length=m);c=(k|4)&-513;k=c&=-1025;k&=-8193}k!==h&&(a[w]=k,2&k&&Object.freeze(a));return a=xc(a,k,f,g,b,d,n,e)} 
function xc(a,b,c,d,e,f,g,h){let k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?yc(b)||(b|=!a.length||g&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==k&&(a[w]=b),Object.freeze(a)):(f===2&&yc(b)&&(a=[...a],k=0,b=wc(b,d),qc(c,d,e,a)),yc(b)||(h||(b|=16),b!==k&&(a[w]=b)));return a}function uc(a,b){a=z(a,b);return Array.isArray(a)?a:ib}function vc(a,b){2&b&&(a|=2);return a|1}function yc(a){return!!(2&a)&&!!(4&a)||!!(256&a)} 
function zc(a,b,c,d){oc(a);const e=a.C;let f=e[w]|0;if(c==null)return qc(e,f,b),a;let g=c===ib?7:c[w]|0,h=g;var k=yc(g);let n=k||Object.isFrozen(c);k||(g=0);n||(c=[...c],h=0,g=wc(g,f),n=!1);g|=5;k=kb(g)??0;for(let l=0;l<c.length;l++){const m=c[l],p=d(m,k);Object.is(m,p)||(n&&(c=[...c],h=0,g=wc(g,f),n=!1),c[l]=p)}g!==h&&(n&&(c=[...c],g=wc(g,f)),c[w]=g);qc(e,f,b,c);return a}function Ac(a,b,c,d){oc(a);const e=a.C;qc(e,e[w]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a} 
function Bc(a,b,c,d){oc(a);const e=a.C;var f=e[w]|0;if(d==null){var g=Cc(e);if(Dc(g,e,f,c)===b)g.set(c,0);else return a}else{g=Cc(e);const h=Dc(g,e,f,c);h!==b&&(h&&(f=qc(e,f,h)),g.set(c,b))}qc(e,f,b,d);return a}function Ec(a,b,c){return Fc(a,b)===c?c:-1}function Fc(a,b){a=a.C;return Dc(Cc(a),a,void 0,b)}function Cc(a){return a[fb]??(a[fb]=new Map)} 
function Dc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];z(b,g)!=null&&(e!==0&&(c=qc(b,c,e)),e=g)}a.set(d,e);return e}function Gc(a,b,c){oc(a);a=a.C;let d=a[w]|0;const e=z(a,c);b=mc(cc(e,b,!0,d));e!==b&&qc(a,d,c,b);return b}function sc(a,b,c,d){a=z(a,d,void 0,e=>cc(e,c,!1,b));if(a!=null)return a}function Hc(a,b,c){a=a.C;(c=sc(a,a[w]|0,b,c))||(c=b[eb])||(a=new b,c=a.C,c[w]|=34,c=b[eb]=a);return c} 
function C(a,b,c){let d=a.C,e=d[w]|0;b=sc(d,e,b,c);if(b==null)return b;e=d[w]|0;if(!nb(a,e)){const f=mc(b);f!==b&&(nc(a)&&(d=a.C,e=d[w]|0),b=f,qc(d,e,c,b))}return b} 
function D(a,b,c,d){var e=a.C,f=e;e=e[w]|0;var g=nb(a,e);const h=g?1:d;d=h===3;var k=!g;(h===2||k)&&nc(a)&&(f=a.C,e=f[w]|0);a=uc(f,c);var n=a===ib?7:a[w]|0,l=vc(n,e);if(g=!(4&l)){var m=a,p=e;const v=!!(2&l);v&&(p|=2);let t=!v,x=!0,J=0,P=0;for(;J<m.length;J++){const Ta=cc(m[J],b,!1,p);if(Ta instanceof b){if(!v){const Ua=nb(Ta);t&&(t=!Ua);x&&(x=Ua)}m[P++]=Ta}}P<J&&(m.length=P);l|=4;l=x?l&-8193:l|8192;l=t?l|8:l&-9}l!==n&&(a[w]=l,2&l&&Object.freeze(a));if(k&&!(8&l||!a.length&&(h===1||(h!==4?0:2&l||!(16& 
l)&&32&e)))){yc(l)&&(a=[...a],l=wc(l,e),e=qc(f,e,c,a));b=a;k=l;for(n=0;n<b.length;n++)m=b[n],l=mc(m),m!==l&&(b[n]=l);k|=8;l=k=b.length?k|8192:k&-8193;a[w]=l}return a=xc(a,l,f,e,c,h,g,d)}function Ic(a){a==null&&(a=void 0);return a}function E(a,b,c){c=Ic(c);A(a,b,c);return a}function Jc(a,b,c,d){d=Ic(d);Bc(a,b,c,d);return a} 
function Kc(a,b,c){oc(a);const d=a.C;let e=d[w]|0;if(c==null)return qc(d,e,b),a;let f=c===ib?7:c[w]|0,g=f;const h=yc(f),k=h||Object.isFrozen(c);let n=!0,l=!0;for(let p=0;p<c.length;p++){var m=c[p];h||(m=nb(m),n&&(n=!m),l&&(l=m))}h||(f=n?13:5,f=l?f&-8193:f|8192);k&&f===g||(c=[...c],g=0,f=wc(f,e));f!==g&&(c[w]=f);qc(d,e,b,c);return a}function wc(a,b){return a=(2&b?a|2:a&-3)&-273} 
function Lc(a,b){oc(a);a=tc(a,4,bc,2,!0);const c=kb(a===ib?7:a[w]|0)??0;if(Array.isArray(b)){var d=b.length;for(let e=0;e<d;e++)a.push($b(b[e],c))}else for(d of b)a.push($b(d,c))}function Mc(a,b){a=z(a.C,b);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0}function Nc(a,b){return Sb(z(a.C,b))}function F(a,b){return bc(z(a.C,b))}function G(a,b){return Qb(z(a.C,b))}function H(a,b){return Mc(a,b)??!1}function I(a,b){return Nc(a,b)??0}function Oc(a,b){return Zb(z(a.C,b))??pc} 
function Pc(a,b){return z(a.C,b,void 0,Lb)??0}function K(a,b){return F(a,b)??""}function L(a,b){return G(a,b)??0}function Qc(a,b,c){return L(a,Ec(a,c,b))}function Rc(a,b,c,d){return C(a,b,Ec(a,d,c))}function Sc(a,b,c){return A(a,b,c==null?c:Rb(c))}function Tc(a,b,c){return Ac(a,b,c==null?c:Rb(c),0)}function Uc(a,b,c){return Ac(a,b,c==null?c:Ub(c),"0")} 
function Vc(a,b){var c=performance.now();if(c!=null&&typeof c!=="number")throw Error(`Value of float/double field must be a number, found ${typeof c}: ${c}`);Ac(a,b,c,0)}function Wc(a,b,c){return A(a,b,ac(c))}function Xc(a,b,c){return Ac(a,b,ac(c),"")}function Yc(a,b,c){return A(a,b,c==null?c:Pb(c))}function Zc(a,b,c){return Ac(a,b,c==null?c:Pb(c),0)}function $c(a,b,c,d){return Bc(a,b,c,d==null?d:Pb(d))};function ad(a){const b=a.C,c=b[w]|0;return nb(a,c)?a:new a.constructor(lc(b,c))}var M=class{constructor(a){this.C=ic(a)}toJSON(){return y(this)}};M.prototype[hb]=mb;function bd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(lb(b))};function cd(a){return()=>{var b;if(!(b=a[eb])){const c=new a;b=c.C;b[w]|=34;b=a[eb]=c}return b}}function dd(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(lb(b))}return b}};var ed=class extends M{},fd=[2,3];function hd(a){var b=new id;b=Wc(b,1,"");return Kc(b,2,a)}var id=class extends M{};var jd=class extends M{};var kd=class extends M{};function ld(a){var b=new md;return Yc(b,1,a)}var md=class extends M{};function nd(a){var b=window.Date.now();b=Number.isFinite(b)?Math.round(b):0;return A(a,3,b==null?b:Ub(b))}var od=class extends M{setError(a){return E(this,10,a)}},pd=dd(od);let qd,rd=64;function sd(){try{return qd??(qd=new Uint32Array(64)),rd>=64&&(crypto.getRandomValues(qd),rd=0),qd[rd++]}catch(a){return Math.floor(Math.random()*2**32)}};function td(a,b){if(!sb(a.goog_pvsid))try{const c=sd()+(sd()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(c){b.la({methodName:784,sa:c})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.la({methodName:784,sa:Error(`Invalid correlator, ${a}`)});return a||-1};function ud(a){return function(){return!a.apply(this,arguments)}}function vd(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function wd(a){let b=a;return function(){if(b){const c=b;b=null;c()}}};function xd(){return xa&&Ba?Ba.mobile:!yd()&&(u("iPod")||u("iPhone")||u("Android")||u("IEMobile"))}function yd(){return xa&&Ba?!Ba.mobile&&(u("iPad")||u("Android")||u("Silk")):u("iPad")||u("Android")&&!u("Mobile")||u("Silk")};function zd(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function Ad(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Bd(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function Cd(a){const b={};for(const c in a)b[c]=a[c];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let Dd=globalThis.trustedTypes,Ed;function Fd(){let a=null;if(!Dd)return a;try{const b=c=>c;a=Dd.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var Gd=class{constructor(a){this.g=a}toString(){return this.g+""}};function Hd(a){var b;Ed===void 0&&(Ed=Fd());a=(b=Ed)?b.createScriptURL(a):a;return new Gd(a)}function Id(a){if(a instanceof Gd)return a.g;throw Error("");};var Jd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Kd(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Ld(a,b){a.src=Id(b);(b=Kd(a.ownerDocument))&&a.setAttribute("nonce",b)};const Md="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Nd(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};var Od=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Pd=/#|$/;function Qd(a,b){const c=a.search(Pd);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Rd(a,...b){if(b.length===0)return Hd(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Hd(c)}function Sd(a,b){a=Id(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Td(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Td(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return Hd(a+b+c)};function Ud(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Qa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Vd(a){return Ud(a.top)?a.top:null}function Wd(a,b){const c=Xd("SCRIPT",a);Ld(c,b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function Yd(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function Zd(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function $d(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function ae(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c}var be=/^([0-9.]+)px$/,ce=/^(-?[0-9.]{1,30})$/; 
function de(a){if(!ce.test(a))return null;a=Number(a);return isNaN(a)?null:a}function ee(a){return(a=be.exec(a))?+a[1]:null}var fe=vd(()=>xd()?2:yd()?1:0),ge=a=>{$d({display:"none"},(b,c)=>{a.style.setProperty(c,b,"important")})};let he=[];const ie=()=>{const a=he;he=[];for(const b of a)try{b()}catch{}};function je(){var a=N(ke).A(le.g,le.defaultValue),b=O.document;if(a.length&&b.head)for(const c of a)c&&b.head&&(a=Xd("META"),b.head.appendChild(a),a.httpEquiv="origin-trial",a.content=c)} 
var me=a=>td(a,{la:()=>{}}),oe=a=>{var b=ne;b.readyState==="complete"||b.readyState==="interactive"?(he.push(a),he.length==1&&(window.Promise?Promise.resolve().then(ie):window.setImmediate?setImmediate(ie):setTimeout(ie,0))):b.addEventListener("DOMContentLoaded",a)};function Xd(a,b=document){return b.createElement(String(a).toLowerCase())};function pe(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function qe(a,b,c){return typeof a.removeEventListener==="function"?(a.removeEventListener(b,c,!1),!0):!1};function re(a,b,c=null,d=!1,e=!1){se(a,b,c,d,e)}function se(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Xd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const k=Ja(h,f);k>=0&&Array.prototype.splice.call(h,k,1)}qe(f,"load",g);qe(f,"error",g)};pe(f,"load",g);pe(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function te(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;$d(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});ue(c)}function ue(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):re(b,a,void 0,!1,!1)};var ne=document,O=window;let ve=null;var we=(a,b=[])=>{let c=!1;r.google_logging_queue||(c=!0,r.google_logging_queue=[]);r.google_logging_queue.push([a,b]);if(a=c){if(ve==null){ve=!1;try{const d=Vd(r);d&&d.location.hash.indexOf("google_logging")!==-1&&(ve=!0)}catch(d){}}a=ve}a&&Wd(r.document,Rd`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function xe(a,b){this.width=a;this.height=b}xe.prototype.aspectRatio=function(){return this.width/this.height};xe.prototype.isEmpty=function(){return!(this.width*this.height)};xe.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};xe.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};xe.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
xe.prototype.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function ye(a=r){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function ze(a=ye()){return a?Ud(a.master)?a.master:null:null};function Ae(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function Be(a){this.g=a||r.document||document}Be.prototype.contains=function(a,b){return a&&b?a==b||a.contains(b):!1};var Ce=a=>{a=ze(ye(a))||a;a.google_unique_id=(a.google_unique_id||0)+1;return a.google_unique_id},De=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},Ee=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};function Fe(a){return!!(a.error&&a.meta&&a.id)}var Ge=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function He(a){return new Ge(a,{message:Ie(a)})}function Ie(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const Je=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Ke=class{constructor(a,b){this.g=a;this.i=b}},Le=class{constructor(a,b,c){this.url=a;this.u=b;this.g=!!c;this.depth=null}};let Me=null;function Ne(){var a=window;if(Me===null){Me="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);Me=c?c[1]:""}}catch(b){}}return Me};function Pe(){const a=r.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Qe(){const a=r.performance;return a&&a.now?a.now():null};var Re=class{constructor(a,b){var c=Qe()||Pe();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Se=r.performance,Te=!!(Se&&Se.mark&&Se.measure&&Se.clearMarks),Ue=vd(()=>{var a;if(a=Te)a=Ne(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function Ve(a){a&&Se&&Ue()&&(Se.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Se.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function We(a){a.g=!1;if(a.i!==a.j.google_js_reporting_queue){if(Ue()){var b=a.i;const c=b.length;b=typeof b==="string"?b.split(""):b;for(let d=0;d<c;d++)d in b&&Ve.call(void 0,b[d])}a.i.length=0}} 
var Xe=class{constructor(a){this.i=[];this.j=a||r;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Ue()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Re(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Se&&Ue()&&Se.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Qe()||Pe())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Se&&Ue()&&Se.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Ye(a,b){const c={};c[a]=b;return[c]}function Ze(a,b,c,d,e){const f=[];$d(a,(g,h)=>{(g=$e(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function $e(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push($e(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Ze(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function af(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function bf(a,b,c){b="https://"+b+c;let d=af(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let n=Ze(h[k],a.j,",$");if(n){n=e+n;if(d>=n.length){d-=n.length;b+=n;e=a.j;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var cf=class{constructor(){this.j="&";this.i={};this.l=0;this.g=[]}};var ff=class{constructor(a=null){this.D=df;this.j=a;this.i=null;this.B=!1;this.R=this.L}F(a){this.R=a}A(a){this.i=a}H(a){this.B=a}g(a,b,c){let d,e;try{this.j&&this.j.g?(e=this.j.start(a.toString(),3),d=b(),this.j.end(e)):d=b()}catch(f){b=!0;try{Ve(e),b=this.R(a,He(f),void 0,c)}catch(g){this.L(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}l(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}L(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const cb=new cf;var g=cb;g.g.push(1);g.i[1]=Ye("context", 
a);Fe(b)||(b=He(b));g=b;if(g.msg){b=cb;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Ye("msg",h)}var k=g.meta||{};h=k;if(this.i)try{this.i(h)}catch(ba){}if(d)try{d(h)}catch(ba){}d=cb;k=[k];d.g.push(3);d.i[3]=k;var n;if(!(n=p)){d=r;k=[];h=null;do{var l=d;if(Ud(l)){var m=l.location.href;h=l.document&&l.document.referrer||null}else m=h,h=null;k.push(new Le(m||"",l));try{d=l.parent}catch(ba){d=null}}while(d&&l!==d);for(let ba=0,gh=k.length-1;ba<=gh;++ba)k[ba].depth=gh-ba;l=r;if(l.location&&l.location.ancestorOrigins&& 
l.location.ancestorOrigins.length===k.length-1)for(m=1;m<k.length;++m){const ba=k[m];ba.url||(ba.url=l.location.ancestorOrigins[m-1]||"",ba.g=!0)}n=k}var p=n;let gd=new Le(r.location.href,r,!1);n=null;const Oe=p.length-1;for(l=Oe;l>=0;--l){var v=p[l];!n&&Je.test(v.url)&&(n=v);if(v.url&&!v.g){gd=v;break}}v=null;const ul=p.length&&p[Oe].url;gd.depth!==0&&ul&&(v=p[Oe]);f=new Ke(gd,v);if(f.i){p=cb;var t=f.i.url||"";p.g.push(4);p.i[4]=Ye("top",t)}var x={url:f.g.url||""};if(f.g.url){const ba=f.g.url.match(Od); 
var J=ba[1],P=ba[3],Ta=ba[4];t="";J&&(t+=J+":");P&&(t+="//",t+=P,Ta&&(t+=":"+Ta));var Ua=t}else Ua="";J=cb;x=[x,{url:Ua}];J.g.push(5);J.i[5]=x;ef(this.D,e,cb,this.B,c)}catch(cb){try{ef(this.D,e,{context:"ecmserr",rctx:a,msg:Ie(cb),url:f?.g.url??""},this.B,c)}catch(gd){}}return!0}za(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.L(a,c instanceof Error?c:Error(c),void 0,this.i||void 0)})}};var gf=class extends M{},hf=[2,3,4];var jf=class extends M{},kf=[3,4,5],lf=[6,7];var mf=class extends M{},nf=[4,5];function of(a,b){var c=D(a,jf,2,B());if(!c.length)return pf(a,b);a=L(a,1);if(a===1)return c=of(c[0],b),c.success?{success:!0,value:!c.value}:c;c=La(c,d=>of(d,b));switch(a){case 2:return c.find(d=>d.success&&!d.value)??c.find(d=>!d.success)??{success:!0,value:!0};case 3:return c.find(d=>d.success&&d.value)??c.find(d=>!d.success)??{success:!0,value:!1};default:return{success:!1,T:3}}} 
function pf(a,b){var c=Fc(a,kf);a:{switch(c){case 3:var d=Qc(a,3,kf);break a;case 4:d=Qc(a,4,kf);break a;case 5:d=Qc(a,5,kf);break a}d=void 0}if(!d)return{success:!1,T:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,ma:c,T:1};let e;try{var f=tc(a,8,bc,B());e=b(...f)}catch(g){return{success:!1,property:d,ma:c,T:2}}f=L(a,1);if(f===4)return{success:!0,value:!!e};if(f===5)return{success:!0,value:e!=null};if(f===12)a=K(a,Ec(a,lf,7));else a:{switch(c){case 4:a=Pc(a,Ec(a,lf,6));break a;case 5:a=K(a, 
Ec(a,lf,7));break a}a=void 0}if(a==null)return{success:!1,property:d,ma:c,T:3};if(f===6)return{success:!0,value:e===a};if(f===9)return{success:!0,value:e!=null&&va(String(e),a)===0};if(e==null)return{success:!1,property:d,ma:c,T:4};switch(f){case 7:c=e<a;break;case 8:c=e>a;break;case 12:c=tb(a)&&tb(e)&&(new RegExp(a)).test(e);break;case 10:c=e!=null&&va(String(e),a)===-1;break;case 11:c=e!=null&&va(String(e),a)===1;break;default:return{success:!1,T:3}}return{success:!0,value:c}} 
function qf(a,b){return a?b?of(a,b):{success:!1,T:1}:{success:!0,value:!0}};function rf(a){return tc(a,4,bc,B())}var sf=class extends M{};var tf=class extends M{getValue(){return C(this,sf,2)}};var uf=class extends M{},vf=dd(uf),wf=[1,2,3,6,7,8];var xf=class extends M{};function yf(a,b){try{const c=d=>[{[d.Na]:d.La}];return JSON.stringify([a.filter(d=>d.xa).map(c),y(b),a.filter(d=>!d.xa).map(c)])}catch(c){return zf(c,b),""}}function zf(a,b){try{te({m:Ie(a instanceof Error?a:Error(String(a))),b:L(b,1)||null,v:K(b,2)||null},"rcs_internal")}catch(c){}}var Af=class{constructor(a,b){var c=new xf;a=Zc(c,1,a);b=Xc(a,2,b);this.j=ad(b)}};var Bf=class extends M{getWidth(){return I(this,3)}getHeight(){return I(this,4)}};var Cf=class extends M{};function Df(a,b){return A(a,1,b==null?b:Ub(b))}function Ef(a,b){return A(a,2,b==null?b:Ub(b))}var Ff=class extends M{getWidth(){return Oc(this,1)}getHeight(){return Oc(this,2)}};var Gf=class extends M{};var Hf=class extends M{};var If=class extends M{getValue(){return L(this,1)}};var Jf=class extends M{getContentUrl(){return K(this,4)}};var Kf=class extends M{};function Lf(a){return Gc(a,Kf,3)}var Mf=class extends M{};var Nf=class extends M{getContentUrl(){return K(this,1)}};var Of=class extends M{};function Pf(a){var b=new Qf;return Zc(b,1,a)}var Qf=class extends M{};var Rf=class extends M{},Sf=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Tf=class extends M{};function Uf(a,b){return Zc(a,1,b)}function Vf(a,b){return Zc(a,2,b)}var Wf=class extends M{};var Xf=class extends M{},Yf=[1,2];function Zf(a,b){return E(a,1,b)}function $f(a,b){return Kc(a,2,b)}function ag(a,b){return zc(a,4,b,Rb)}function bg(a,b){return Kc(a,5,b)}function cg(a,b){return Zc(a,6,b)}var dg=class extends M{};var eg=class extends M{},fg=[1,2,3,4,6];var gg=class extends M{};function hg(a){var b=new ig;return Jc(b,4,jg,a)}var ig=class extends M{getTagSessionCorrelator(){return Oc(this,2)}},jg=[4,5,7,8,9];var kg=class extends M{};function lg(){var a=mg();a=mc(a);return Xc(a,1,ng())}var og=class extends M{};var pg=class extends M{};var qg=class extends M{getTagSessionCorrelator(){return Oc(this,1)}};var rg=class extends M{},sg=[1,7],tg=[4,6,8];class ug extends Af{constructor(){super(...arguments)}}function vg(a,...b){wg(a,...b.map(c=>({xa:!0,Na:3,La:y(c)})))}function xg(a,...b){wg(a,...b.map(c=>({xa:!0,Na:4,La:y(c)})))}function yg(a,...b){wg(a,...b.map(c=>({xa:!0,Na:7,La:y(c)})))}var zg=class extends ug{};function Ag(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function wg(a,...b){try{a.D&&yf(a.g.concat(b),a.j).length>=65536&&Bg(a),a.l&&!a.A&&(a.A=!0,Cg(a.l,()=>{Bg(a)})),a.g.push(...b),a.g.length>=a.B&&Bg(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{Bg(a)},a.H))}catch(c){zf(c,a.j)}}function Bg(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=yf(a.g,a.j);a.F("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var Dg=class extends zg{constructor(a,b,c,d,e,f){super(a,b);this.F=Ag;this.H=c;this.B=d;this.D=e;this.l=f;this.g=[];this.i=null;this.A=!1}},Eg=class extends Dg{constructor(a,b,c=1E3,d=100,e=!1,f){super(a,b,c,d,e&&!0,f)}};function Fg(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=Uc(b,1,c);c=me(window);b=Uc(b,2,c);return Uc(b,6,a.A)}function Gg(a,b,c,d,e,f){if(a.j){var g=Vf(Uf(new Wf,b),c);b=cg($f(Zf(bg(ag(new dg,d),e),g),a.g.slice()),f);b=hg(b);xg(a.i,Fg(a,b));if(f===1||f===3||f===4&&!a.g.some(h=>L(h,1)===L(g,1)&&L(h,2)===c))a.g.push(g),a.g.length>100&&a.g.shift()}}function Hg(a,b,c,d){if(a.j){var e=new Tf;b=Sc(e,1,b);c=Sc(b,2,c);d=Yc(c,3,d);c=new ig;d=Jc(c,8,jg,d);xg(a.i,Fg(a,d))}} 
function Ig(a,b,c,d,e){if(a.j){var f=new mf;b=E(f,1,b);c=Yc(b,2,c);d=Sc(c,3,d);if(e.ma===void 0)$c(d,4,nf,e.T);else switch(e.ma){case 3:c=new gf;c=$c(c,2,hf,e.property);e=Yc(c,1,e.T);Jc(d,5,nf,e);break;case 4:c=new gf;c=$c(c,3,hf,e.property);e=Yc(c,1,e.T);Jc(d,5,nf,e);break;case 5:c=new gf,c=$c(c,4,hf,e.property),e=Yc(c,1,e.T),Jc(d,5,nf,e)}e=new ig;e=Jc(e,9,jg,d);xg(a.i,Fg(a,e))}}var Jg=class{constructor(a,b,c,d=new Eg(6,"unknown",b)){this.A=a;this.l=c;this.i=d;this.g=[];this.j=a>0&&Zd()<1/a}};var N=a=>{var b="fa";if(a.fa&&a.hasOwnProperty(b))return a.fa;b=new a;return a.fa=b};var Kg=class{constructor(){this.P={[3]:{},[4]:{},[5]:{}}}};var Lg=/^true$/.test("false");function Mg(a,b){switch(b){case 1:return Qc(a,1,wf);case 2:return Qc(a,2,wf);case 3:return Qc(a,3,wf);case 6:return Qc(a,6,wf);case 8:return Qc(a,8,wf);default:return null}}function Ng(a,b){if(!a)return null;switch(b){case 1:return H(a,1);case 7:return K(a,3);case 2:return Pc(a,2);case 3:return K(a,3);case 6:return rf(a);case 8:return rf(a);default:return null}}const Og=vd(()=>{if(!Lg)return{};try{var a=window;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch{b=null}if(b)return JSON.parse(b)}catch{}return{}}); 
function Pg(a,b,c,d=0){N(Qg).j[d]=N(Qg).j[d]?.add(b)??(new Set).add(b);const e=Og();if(e[b]!=null)return e[b];b=Rg(d)[b];if(!b)return c;b=vf(JSON.stringify(b));b=Sg(b);a=Ng(b,a);return a!=null?a:c}function Sg(a){const b=N(Kg).P;if(b&&Fc(a,wf)!==8){const c=Na(D(a,tf,5,B()),d=>{d=qf(C(d,jf,1),b);return d.success&&d.value});if(c)return c.getValue()??null}return C(a,sf,4)??null}class Qg{constructor(){this.i={};this.l=[];this.j={};this.g=new Map}}function Tg(a,b=!1,c){return!!Pg(1,a,b,c)} 
function Ug(a,b=0,c){a=Number(Pg(2,a,b,c));return isNaN(a)?b:a}function Vg(a,b="",c){a=Pg(3,a,b,c);return typeof a==="string"?a:b}function Wg(a,b=[],c){a=Pg(6,a,b,c);return Array.isArray(a)?a:b}function Xg(a,b=[],c){a=Pg(8,a,b,c);return Array.isArray(a)?a:b}function Rg(a){return N(Qg).i[a]||(N(Qg).i[a]={})} 
function Yg(a,b){const c=Rg(b);$d(a,(d,e)=>{if(c[e]){const g=vf(JSON.stringify(d));if(G(g,Ec(g,wf,8))!=null){var f=vf(JSON.stringify(c[e]));d=Gc(g,sf,4);f=rf(Hc(f,sf,4));Lc(d,f)}c[e]=y(g)}else c[e]=d})} 
function Zg(a,b,c,d,e=!1){var f=[],g=[];for(const m of b){b=Rg(m);for(const p of a){var h=Fc(p,wf);const v=Mg(p,h);if(v){a:{var k=v;var n=h,l=N(Qg).g.get(m)?.get(v)?.slice(0)??[];const t=new eg;switch(n){case 1:$c(t,1,fg,k);break;case 2:$c(t,2,fg,k);break;case 3:$c(t,3,fg,k);break;case 6:$c(t,4,fg,k);break;case 8:$c(t,6,fg,k);break;default:k=void 0;break a}zc(t,5,l,Rb);k=t}k&&N(Qg).j[m]?.has(v)&&f.push(k);h===8&&b[v]?(k=vf(JSON.stringify(b[v])),h=Gc(p,sf,4),k=rf(Hc(k,sf,4)),Lc(h,k)):k&&N(Qg).g.get(m)?.has(v)&& 
g.push(k);e||(h=v,k=m,n=d,l=N(Qg),l.g.has(k)||l.g.set(k,new Map),l.g.get(k).has(h)||l.g.get(k).set(h,[]),n&&l.g.get(k).get(h).push(n));b[v]=y(p)}}}if(f.length||g.length)a=d??void 0,c.j&&c.l&&(d=new gg,f=Kc(d,2,f),g=Kc(f,3,g),a&&Tc(g,1,a),f=new ig,g=Jc(f,7,jg,g),xg(c.i,Fg(c,g)))}function $g(a,b){b=Rg(b);for(const c of a){a=vf(JSON.stringify(c));const d=Fc(a,wf);(a=Mg(a,d))&&(b[a]||(b[a]=c))}}function ah(){return Object.keys(N(Qg).i).map(a=>Number(a))} 
function bh(a){N(Qg).l.includes(a)||Yg(Rg(4),a)};function Q(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function ch(a,b,c){return b[a]||c}function dh(a){Q(5,Tg,a);Q(6,Ug,a);Q(7,Vg,a);Q(8,Wg,a);Q(17,Xg,a);Q(13,$g,a);Q(15,bh,a)}function eh(a){Q(4,b=>{N(Kg).P=b},a);Q(9,(b,c)=>{var d=N(Kg);d.P[3][b]==null&&(d.P[3][b]=c)},a);Q(10,(b,c)=>{var d=N(Kg);d.P[4][b]==null&&(d.P[4][b]=c)},a);Q(11,(b,c)=>{var d=N(Kg);d.P[5][b]==null&&(d.P[5][b]=c)},a);Q(14,b=>{var c=N(Kg);for(const d of[3,4,5])Object.assign(c.P[d],b[d])},a)} 
function fh(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};function hh(a,b,c){a.j=ch(1,b,()=>{});a.l=(d,e)=>ch(2,b,()=>[])(d,c,e);a.g=()=>ch(3,b,()=>[])(c);a.i=d=>{ch(16,b,()=>{})(d,c)}}class ih{j(){}i(){}l(){return[]}g(){return[]}};function ef(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof cf?f=c:(f=new cf,$d(c,(h,k)=>{var n=f;const l=n.l++;h=Ye(k,h);n.g.push(l);n.i[l]=h}));const g=bf(f,a.domain,a.path+b+"&");g&&re(r,g)}catch(f){}}function jh(a,b){b>=0&&b<=1&&(a.g=b)}var kh=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let df,lh;const mh=new Xe(window);(function(a){df=a??new kh;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());jh(df,window.google_srt);lh=new ff(mh);lh.A(()=>{});lh.H(!0);window.document.readyState==="complete"?window.google_measure_js_timing||We(mh):mh.g&&pe(window,"load",()=>{window.google_measure_js_timing||We(mh)})})();let nh=(new Date).getTime();var oh={oc:0,nc:1,kc:2,ec:3,lc:4,fc:5,mc:6,ic:7,jc:8,dc:9,hc:10,qc:11};var ph={sc:0,tc:1,rc:2};function qh(a){if(a.g!=0)throw Error("Already resolved/rejected.");}var th=class{constructor(){this.i=new rh(this);this.g=0}resolve(a){qh(this);this.g=1;this.l=a;sh(this.i)}reject(a){qh(this);this.g=2;this.j=a;sh(this.i)}};function sh(a){switch(a.g.g){case 0:break;case 1:a.i&&a.i(a.g.l);break;case 2:a.j&&a.j(a.g.j);break;default:throw Error("Unhandled deferred state.");}}var rh=class{constructor(a){this.g=a}then(a,b){if(this.i)throw Error("Then functions already set.");this.i=a;this.j=b;sh(this)}};var uh=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new uh(Ka(this.g,a))}apply(a){return new uh(a(this.g.slice(0)))}sort(a){return new uh(this.g.slice(0).sort(a))}get(a){return this.g[a]}add(a){const b=this.g.slice(0);b.push(a);return new uh(b)}};function vh(a,b){const c=[],d=a.length;for(let e=0;e<d;e++)c.push(a[e]);c.forEach(b,void 0)};var xh=class{constructor(){this.g={};this.i={}}set(a,b){const c=wh(a);this.g[c]=b;this.i[c]=a}get(a,b){a=wh(a);return this.g[a]!==void 0?this.g[a]:b}clear(){this.g={};this.i={}}};function wh(a){return a instanceof Object?String(la(a)):a+""};function yh(a){return new zh({value:a},null)}function Ah(a){return new zh(null,a)}function Bh(a){try{return yh(a())}catch(b){return Ah(b)}}function Ch(a){return a.g!=null?a.getValue():null}function Dh(a,b){a.g!=null&&b(a.getValue());return a}function Eh(a,b){a.g!=null||b(a.i);return a}var zh=class{constructor(a,b){this.g=a;this.i=b}getValue(){return this.g.value}map(a){return this.g!=null?(a=a(this.getValue()),a instanceof zh?a:yh(a)):this}};var Fh=class{constructor(a){this.g=new xh;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return this.g.g[wh(a)]!==void 0}};var Gh=class{constructor(){this.g=new xh}set(a,b){let c=this.g.get(a);c||(c=new Fh,this.g.set(a,c));c.add(b)}};var Hh=class extends M{getId(){return F(this,3)}};var Ih=class{constructor({yb:a,vc:b,Hc:c,Sb:d}){this.g=b;this.l=new uh(a||[]);this.j=d;this.i=c}};const Kh=a=>{const b=[],c=a.l;c&&c.g.length&&b.push({ha:"a",ka:Jh(c)});a.g!=null&&b.push({ha:"as",ka:a.g});a.i!=null&&b.push({ha:"i",ka:String(a.i)});a.j!=null&&b.push({ha:"rp",ka:String(a.j)});b.sort(function(d,e){return d.ha.localeCompare(e.ha)});b.unshift({ha:"t",ka:"aa"});return b},Jh=a=>{a=a.g.slice(0).map(Lh);a=JSON.stringify(a);return ae(a)},Lh=a=>{const b={};F(a,7)!=null&&(b.q=F(a,7));Nc(a,2)!=null&&(b.o=Nc(a,2));Nc(a,5)!=null&&(b.p=Nc(a,5));return b};function Mh(a){return G(a,2)}var Nh=class extends M{setLocation(a){return Yc(this,1,a)}};function Oh(a){const b=[].slice.call(arguments).filter(ud(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.gb||[]);d=Object.assign(d,e.pb)});return new Ph(c,d)}function Qh(a){switch(a){case 1:return new Ph(null,{google_ad_semantic_area:"mc"});case 2:return new Ph(null,{google_ad_semantic_area:"h"});case 3:return new Ph(null,{google_ad_semantic_area:"f"});case 4:return new Ph(null,{google_ad_semantic_area:"s"});default:return null}} 
function Rh(a){if(a==null)var b=null;else{b=Ph;var c=Kh(a);a=[];for(let d of c)c=String(d.ka),a.push(d.ha+"."+(c.length<=20?c:c.slice(0,19)+"_"));b=new b(null,{google_placement_id:a.join("~")})}return b}var Ph=class{constructor(a,b){this.gb=a;this.pb=b}};var Sh=new Ph(["google-auto-placed"],{google_reactive_ad_format:40,google_tag_origin:"qs"});var Th=dd(class extends M{});function Uh(a){return C(a,Hh,1)}function Vh(a){return G(a,2)}var Wh=class extends M{};var Xh=class extends M{};var Yh=class extends M{};function Zh(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b};function $h(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){let d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Zh(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")};var R=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},S=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},ai=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var bi=new S(1359),ci=new S(1358),di=new R(1360),ei=new S(1357),fi=new R(1345),gi=new R(745713445),hi=new S(1130,100),ii=new S(1340,.2),ji=new S(1338,.3),ki=new S(1336,1),li=new S(1339,.3),mi=new R(1337),ni=new class{constructor(a,b=""){this.g=a;this.defaultValue=b}}(14),oi=new R(1342),pi=new R(1344),qi=new S(1343,300),ri=new R(1384),si=new R(316),ti=new R(313),ui=new R(369),vi=new R(1318,!0),wi=new R(626390500),xi=new ai(635821288,["29_18","30_19"]),yi=new ai(683929765),zi=new R(506914611),Ai=new R(750577535), 
Bi=new S(717888910,.7),Ci=new S(643258048,.15),Di=new S(643258049,.16),Ei=new S(717888911,.7),Fi=new S(717888912,.7),Gi=new S(748662193,8),Hi=new R(711741274),Ii=new R(45650663),Ji=new S(684147711,-1),Ki=new S(684147712,-1),Li=new R(*********),Mi=new S(1079,5),Ni=new R(10013),Oi=new R(750586557),le=new class{constructor(a,b=[]){this.g=a;this.defaultValue=b}}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==", 
"Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9", 
"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]),Pi=new R(84);var ke=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.l=(b,c)=>a[b]!=null?a[b]:c;this.i=(b,c)=>a[b]!=null?a[b]:c;this.A=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.B=()=>{}}};function T(a){return N(ke).j(a.g,a.defaultValue)}function U(a){return N(ke).l(a.g,a.defaultValue)};function Qi(a,b){const c=e=>{e=Ri(e);return e==null?!1:0<e},d=e=>{e=Ri(e);return e==null?!1:0>e};switch(b){case 0:return{init:Si(a.previousSibling,c),va:e=>Si(e.previousSibling,c),ya:0};case 2:return{init:Si(a.lastChild,c),va:e=>Si(e.previousSibling,c),ya:0};case 3:return{init:Si(a.nextSibling,d),va:e=>Si(e.nextSibling,d),ya:3};case 1:return{init:Si(a.firstChild,d),va:e=>Si(e.nextSibling,d),ya:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Ri(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Si(a,b){return a&&b(a)?a:null};var Ti={rectangle:1,horizontal:2,vertical:4};var Ui={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7};function Vi(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}}function V(a){return Vi(a).clientWidth??void 0};function Wi(a,b){do{const c=Yd(a,b);if(c&&c.position=="fixed")return!1}while(a=a.parentElement);return!0};function Xi(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=ee(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function Yi(a,b){return!((ce.test(b.google_ad_width)||be.test(a.style.width))&&(ce.test(b.google_ad_height)||be.test(a.style.height)))}function Zi(a,b){return(a=$i(a,b))?a.y:0} 
function $i(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}} 
function aj(a,b,c,d,e){if(a!==a.top)return Vd(a)?3:16;if(!(V(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=V(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=V(a);c;c=c.parentElement)if((d=Yd(c,a))&&(e=ee(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function bj(a,b,c,d){const e=aj(b,c,a,U(li),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Wi(c,b)?(b=V(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function cj(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function dj(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=Yd(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function ej(a,b,c){a=$i(b,a);return c==="rtl"?-a.x:a.x} 
function fj(a,b){var c;c=(c=b.parentElement)?(c=Yd(c,a))?c.direction:"":"";if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";cj(b,c,"0px");d.width=`${V(a)}px`;if(ej(a,b,c)!==0){cj(b,c,"0px");var e=ej(a,b,c);cj(b,c,`${-1*e}px`);a=ej(a,b,c);a!==0&&a!==e&&cj(b,c,`${e/(a-e)*e}px`)}d.zIndex="30"}};function gj(a,b,c){let d;return a.style&&!!a.style[c]&&ee(a.style[c])||(d=Yd(a,b))&&!!d[c]&&ee(d[c])||null}function hj(a,b){const c=De(a)===0;return b&&c?Math.max(250,2*Vi(a).clientHeight/3):250}function ij(a,b){let c;return a.style&&a.style.zIndex||(c=Yd(a,b))&&c.zIndex||null}function jj(a){return b=>b.g<=a}function kj(a,b,c,d){const e=a&&lj(c,b),f=hj(b,d);return g=>!(e&&g.height()>=f)}function mj(a){return b=>b.height()<=a}function lj(a,b){return Zi(a,b)<Vi(b).clientHeight-100} 
function nj(a,b){var c=gj(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=gj(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&ee(b.style.height))&&(c=Math.min(c,d)),(d=gj(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};var oj={google_ad_channel:!0,google_ad_client:!0,google_ad_host:!0,google_ad_host_channel:!0,google_adtest:!0,google_tag_for_child_directed_treatment:!0,google_tag_for_under_age_of_consent:!0,google_tag_partner:!0,google_restrict_data_processing:!0,google_page_url:!0,google_debug_params:!0,google_adbreak_test:!0,google_ad_frequency_hint:!0,google_admob_interstitial_slot:!0,google_admob_rewarded_slot:!0,google_admob_ads_only:!0,google_ad_start_delay_hint:!0,google_max_ad_content_rating:!0,google_traffic_source:!0, 
google_overlays:!0,google_privacy_treatments:!0,google_special_category_data:!0,google_ad_intent_query:!0,google_ad_intent_qetid:!0,google_ad_intent_eids:!0,google_ad_intents_format:!0};const pj=RegExp("(^| )adsbygoogle($| )");function qj(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Nd(d.property);a[e]=d.value}};var rj=class extends M{g(){return Mc(this,23)}};var sj=class extends M{g(){return Zb(z(this.C,1))??void 0}};var tj=class extends M{};var uj=class extends M{};var vj=class extends M{};var wj=class extends M{};var xj=class extends M{getName(){return F(this,4)}},yj=[1,2,3];var zj=class extends M{};var Aj=class extends M{};var Cj=class extends M{g(){return Rc(this,Aj,2,Bj)}},Bj=[1,2];var Dj=class extends M{g(){return C(this,Cj,3)}};var Ej=class extends M{},Fj=dd(Ej);function Gj(a){const b=[];vh(a.getElementsByTagName("p"),function(c){Hj(c)>=100&&b.push(c)});return b}function Hj(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;vh(a.childNodes,function(c){b+=Hj(c)});return b}function Ij(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function Jj(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Kj(a,b){var c=[];try{c=b.querySelectorAll(a.l)}catch(d){}if(!c.length)return[];b=Pa(c);b=Jj(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=Gj(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Lj=class{constructor(a,b,c,d){this.l=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.l,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Mj=class{constructor(){this.i=Rd`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}L(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;Fe(b)||(b=new Ge(b,{context:a,id:d}));r.google_js_errors=r.google_js_errors||[];r.google_js_errors.push(b);r.error_rep_loaded||(Wd(r.document,this.i),r.error_rep_loaded=!0);return!1}g(a,b){try{return b()}catch(c){if(!this.L(a,c,.01,"jserror"))throw c;}}l(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}za(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.L(a,c instanceof Error?c:Error(c),void 0)})}};function Nj(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Oj(a,b,c,d,e=!1){const f=d||window,g=typeof queueMicrotask!=="undefined";return function(...h){e&&g&&queueMicrotask(()=>{f.google_rum_task_id_counter=f.google_rum_task_id_counter||1;f.google_rum_task_id_counter+=1});const k=Qe();let n,l=3;try{n=b.apply(this,h)}catch(m){l=13;if(!c)throw m;c(a,m)}finally{f.google_measure_js_timing&&k&&Nj({label:a.toString(),value:k,duration:(Qe()||0)-k,type:l,...(e&&g&&{taskId:f.google_rum_task_id_counter=f.google_rum_task_id_counter||1})},f)}return n}} 
function Pj(a,b){return Oj(a,b,(c,d)=>{(new Mj).L(c,d)},void 0,!1)};function Qj(a,b,c){return Oj(a,b,void 0,c,!0).apply()}function Rj(a){if(!a)return null;var b=F(a,7);if(F(a,1)||a.getId()||tc(a,4,bc,B()).length>0){var c=a.getId(),d=F(a,1),e=tc(a,4,bc,B());b=Nc(a,2);var f=Nc(a,5);a=Sj(G(a,6));let g="";d&&(g+=d);c&&(g+="#"+Ij(c));if(e)for(c=0;c<e.length;c++)g+="."+Ij(e[c]);b=(e=g)?new Lj(e,b,f,a):null}else b=b?new Lj(b,Nc(a,2),Nc(a,5),Sj(G(a,6))):null;return b}const Tj={1:1,2:2,3:3,0:0};function Sj(a){return a==null?a:Tj[a]}const Uj={1:0,2:1,3:2,4:3}; 
function Vj(a){return a.google_ama_state=a.google_ama_state||{}}function Wj(a){a=Vj(a);return a.optimization=a.optimization||{}};var Xj=a=>{switch(G(a,8)){case 1:case 2:if(a==null)var b=null;else b=C(a,Hh,1),b==null?b=null:(a=G(a,2),b=a==null?null:new Ih({yb:[b],Sb:a}));return b!=null?yh(b):Ah(Error("Missing dimension when creating placement id"));case 3:return Ah(Error("Missing dimension when creating placement id"));default:return b="Invalid type: "+G(a,8),Ah(Error(b))}};var Yj=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function Zj(a,b){const c=new Gh,d=new Fh;b.forEach(e=>{if(Rc(e,vj,1,yj)){e=Rc(e,vj,1,yj);if(C(e,Wh,1)&&Uh(C(e,Wh,1))&&C(e,Wh,2)&&Uh(C(e,Wh,2))){const g=ak(a,Uh(C(e,Wh,1))),h=ak(a,Uh(C(e,Wh,2)));if(g&&h)for(var f of Yj({anchor:g,position:Vh(C(e,Wh,1))},{anchor:h,position:Vh(C(e,Wh,2))}))c.set(la(f.anchor),f.position)}C(e,Wh,3)&&Uh(C(e,Wh,3))&&(f=ak(a,Uh(C(e,Wh,3))))&&c.set(la(f),Vh(C(e,Wh,3)))}else Rc(e,wj,2,yj)?bk(a,Rc(e,wj,2,yj),c):Rc(e,uj,3,yj)&&ck(a,Rc(e,uj,3,yj),d)});return new dk(c,d)} 
var dk=class{constructor(a,b){this.i=a;this.g=b}};const bk=(a,b,c)=>{C(b,Wh,2)?(b=C(b,Wh,2),(a=ak(a,Uh(b)))&&c.set(la(a),Vh(b))):C(b,Hh,1)&&(a=ek(a,C(b,Hh,1)))&&a.forEach(d=>{d=la(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},ck=(a,b,c)=>{C(b,Hh,1)&&(a=ek(a,C(b,Hh,1)))&&a.forEach(d=>{c.add(la(d))})},ak=(a,b)=>(a=ek(a,b))&&a.length>0?a[0]:null,ek=(a,b)=>(b=Rj(b))?Kj(b,a):null;function ng(){return"m202505050101"};var fk=cd(kg);var mg=cd(og);function gk(a,b){return b(a)?a:void 0} 
function hk(a,b,c,d,e){c=c instanceof Ge?c.error:c;var f=new rg;const g=new qg;try{var h=me(window);Uc(g,1,h)}catch(p){}try{var k=N(ih).g();zc(g,2,k,Rb)}catch(p){}try{Xc(g,3,window.document.URL)}catch(p){}h=E(f,2,g);k=new pg;b=Zc(k,1,b);try{var n=tb(c?.name)?c.name:"Unknown error";Xc(b,2,n)}catch(p){}try{var l=tb(c?.message)?c.message:`Caught ${c}`;Xc(b,3,l)}catch(p){}try{var m=tb(c?.stack)?c.stack:Error().stack;m&&zc(b,4,m.split(/\n\s*/),$b)}catch(p){}n=Jc(h,1,sg,b);if(e){l=0;switch(e.errSrc){case "LCC":l= 
1;break;case "PVC":l=2}m=lg();b=gk(e.shv,tb);m=Xc(m,2,b);l=Zc(m,6,l);m=fk();m=mc(m);b=gk(e.es,vb());m=zc(m,1,b,Rb);m=ad(m);l=E(l,4,m);m=gk(e.client,tb);l=Wc(l,3,m);m=gk(e.slotname,tb);l=Xc(l,7,m);e=gk(e.tag_origin,tb);e=Xc(l,8,e);e=ad(e)}else e=ad(lg());e=Jc(n,6,tg,e);d=Uc(e,5,d??1);vg(a,d)};var jk=class{constructor(){this.g=ik}};function ik(){return{Pb:sd()+(sd()&2**21-1)*2**32,Db:Number.MAX_SAFE_INTEGER}};var mk=class{constructor(a=!1){var b=kk;this.B=lk;this.j=a;this.D=b;this.i=null;this.R=this.L}F(a){this.R=a}A(a){this.i=a}H(){}g(a,b,c){let d;try{d=b()}catch(e){b=this.j;try{b=this.R(a,He(e),void 0,c)}catch(f){this.L(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}l(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}za(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.L(a,c instanceof Error?c:Error(c),void 0,void 0)})}L(a,b,c,d){try{const g=c===void 0?1/this.D:c===0?0:1/c;var e=(new jk).g(); 
if(g>0&&e.Pb*g<=e.Db){var f=this.B;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}hk(f,a,b,g,c)}}catch(g){}return this.j}};var W=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,W):this.stack=Error().stack||""}};let lk,nk,ok,pk,kk;const qk=new Xe(r);function rk(a){a!=null&&(r.google_measure_js_timing=a);r.google_measure_js_timing||We(qk)}(function(a,b,c=!0){({Rb:kk,Gb:ok}=sk());nk=a||new kh;jh(nk,ok);lk=b||new Eg(2,ng(),1E3);pk=new mk(c);r.document.readyState==="complete"?rk():qk.g&&pe(r,"load",()=>{rk()})})();function tk(a,b,c){return pk.g(a,b,c)}function uk(a,b){return pk.l(a,b)}function vk(a,b){pk.za(a,b)}function wk(a,b,c=.01){const d=N(ih).g();!b.eid&&d.length&&(b.eid=d.toString());ef(nk,a,b,!0,c)} 
function xk(a,b,c=kk,d,e){return pk.L(a,b,c,d,e)}function yk(a,b,c=kk,d,e){return(Fe(b)?b.msg||Ie(b.error):Ie(b)).indexOf("TagError")===0?((Fe(b)?b.error:b).pbr=!0,!1):xk(a,b,c,d,e)}function sk(){let a,b;typeof r.google_srt==="number"?(b=r.google_srt,a=r.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Rb:a,Gb:b}};var zk=class{constructor(){var a=Math.random;this.g=Math.floor(a()*2**52);this.i=0}};function Ak(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(Bk(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function Ck(a){a=Dk(a);return a.has("all")||a.has("after")}function Ek(a){a=Dk(a);return a.has("all")||a.has("before")}function Dk(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function Bk(a){const b=Dk(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var Fk=class{constructor(){this.g=new Set;this.i=new zk}};function Gk(a,b){if(!a)return!1;a=Yd(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function Hk(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null}function Ik(a){return!!a.nextSibling||!!a.parentNode&&Ik(a.parentNode)};function Jk(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function Kk(a){return{uc:Lk(a),wc:Mk(a,"body ins.adsbygoogle"),vb:Nk(a),wb:Mk(a,".google-auto-placed"),xb:Ok(a),Eb:Pk(a),Ac:Qk(a),Jc:Rk(a),Ob:Sk(a),zc:Mk(a,"div.googlepublisherpluginad"),ac:Mk(a,"html > ins.adsbygoogle")}}function Qk(a){return Tk(a)||Mk(a,"div[id^=div-gpt-ad]")}function Tk(a){const b=Jk(a);return b?Ka(La(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function Mk(a,b){return Pa(a.document.querySelectorAll(b))} 
function Ok(a){return Mk(a,"ins.adsbygoogle[data-anchor-status]")}function Nk(a){return Mk(a,"iframe[id^=aswift_],iframe[id^=google_ads_frame]")}function Rk(a){return Mk(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")}function Pk(a){return Qk(a).concat(Mk(a,"iframe[id^=google_ads_iframe]"))} 
function Sk(a){return Mk(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function Lk(a){return Mk(a,"ins.adsbygoogle-ablated-ad-slot")}function Uk(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};function Vk(a,b){if(a.l)return!0;a.l=!0;const c=D(a.j,Yh,1,B());a.i=0;const d=Wk(a.F);var e=a.g;var f;try{var g=(f=e.localStorage.getItem("google_ama_settings"))?Th(f):null}catch(v){g=null}f=g!==null&&H(g,2);g=Vj(e);f&&(g.eatf=!0,we(7,[!0,0,!1]));b:{var h={Ib:!1,Jb:!1},k=Mk(e,".google-auto-placed"),n=Ok(e),l=Rk(e),m=Pk(e);const v=Sk(e),t=Lk(e),x=Mk(e,"div.googlepublisherpluginad"),J=Mk(e,"html > ins.adsbygoogle");let P=[].concat(...Nk(e),...Mk(e,"body ins.adsbygoogle"));f=[];for(const [Ta,Ua]of[[h.Cc, 
k],[h.Ib,n],[h.Fc,l],[h.Dc,m],[h.Gc,v],[h.Bc,t],[h.Ec,x],[h.Jb,J]])Ta===!1?f=f.concat(Ua):P=P.concat(Ua);h=Uk(P);f=Uk(f);h=h.slice(0);for(p of f)for(f=0;f<h.length;f++)(p.contains(h[f])||h[f].contains(p))&&h.splice(f,1);var p=h;e=Vi(e).clientHeight;for(f=0;f<p.length;f++)if(!(p[f].getBoundingClientRect().top>e)){e=!0;break b}e=!1}e=e?g.eatfAbg=!0:!1;if(e)return!0;e=new Fh([2]);for(g=0;g<c.length;g++){p=a;h=c[g];f=g;k=b;(n=!C(h,Nh,4))||(n=e,l=n.contains,m=C(h,Nh,4),m=G(m,1),n=!l.call(n,m));if(n||G(h, 
8)!==1||!Xk(h,d))p=null;else{p.i++;if(k=Yk(p,h,k,d))n=Vj(p.g),n.numAutoAdsPlaced||(n.numAutoAdsPlaced=0),(l=!C(h,Hh,1))||(h=C(h,Hh,1),l=(Nc(h,5)??void 0)==null),l||(n.numPostPlacementsPlaced?n.numPostPlacementsPlaced++:n.numPostPlacementsPlaced=1),n.placed==null&&(n.placed=[]),n.numAutoAdsPlaced++,n.placed.push({index:f,element:k.qa}),we(7,[!1,p.i,!0]);p=k}if(p)return!0}we(7,[!1,a.i,!1]);return!1} 
function Yk(a,b,c,d){if(!Xk(b,d)||(G(b,8)??void 0)!=1)return null;d=C(b,Hh,1);if(!d)return null;d=Rj(d);if(!d)return null;d=Kj(d,a.g.document);if(d.length==0)return null;d=d[0];var e=G(b,2);e=Uj[e];e=e===void 0?null:e;var f;if(!(f=e==null)){a:{f=a.g;switch(e){case 0:f=Gk(Hk(d),f);break a;case 3:f=Gk(d,f);break a;case 2:var g=d.lastChild;f=Gk(g?g.nodeType==1?g:Hk(g):null,f);break a}f=!1}if(c=!f&&!(!c&&e==2&&!Ik(d)))c=e==1||e==2?d:d.parentNode,c=!(c&&!Zh(c)&&c.offsetWidth<=0);f=!c}if(!(c=f)){c=a.B; 
f=G(b,2);g=c.i;var h=la(d);g=g.g.get(h);if(!(g=g?g.contains(f):!1))a:{if(c.g.contains(la(d)))switch(f){case 2:case 3:g=!0;break a;default:g=!1;break a}for(f=d.parentElement;f;){if(c.g.contains(la(f))){g=!0;break a}f=f.parentElement}g=!1}c=g}if(!c){c=a.D;g=G(b,2);a:switch(g){case 1:f=Ck(d.previousElementSibling)||Ek(d);break a;case 4:f=Ck(d)||Ek(d.nextElementSibling);break a;case 2:f=Ek(d.firstElementChild);break a;case 3:f=Ck(d.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+ 
g);}g=Ak(c,d,g);c=c.i;wk("ama_exclusion_zone",{typ:f?g?"siuex":"siex":g?"suex":"noex",cor:c.g,num:c.i++,dvc:fe()},.1);c=f||g}if(c)return null;f=C(b,Xh,3);c={};f&&(c.sb=F(f,1),c.eb=F(f,2),c.Bb=!!Mc(f,3));f=C(b,Nh,4)&&Mh(C(b,Nh,4))?Mh(C(b,Nh,4)):null;f=Qh(f);g=Nc(b,12)!=null?Nc(b,12):null;g=g==null?null:new Ph(null,{google_ml_rank:g});b=Zk(a,b);b=Oh(a.A,f,g,b);f=a.g;a=a.H;h=f.document;var k=c.Bb||!1;g=Ae((new Be(h)).g,"DIV");const n=g.style;n.width="100%";n.height="auto";n.clear=k?"both":"none";k=g.style; 
k.textAlign="center";c.Qb&&qj(k,c.Qb);h=Ae((new Be(h)).g,"INS");k=h.style;k.display="block";k.margin="auto";k.backgroundColor="transparent";c.sb&&(k.marginTop=c.sb);c.eb&&(k.marginBottom=c.eb);c.ub&&qj(k,c.ub);g.appendChild(h);c={Ja:g,qa:h};c.qa.setAttribute("data-ad-format","auto");g=[];if(h=b&&b.gb)c.Ja.className=h.join(" ");h=c.qa;h.className="adsbygoogle";h.setAttribute("data-ad-client",a);g.length&&h.setAttribute("data-ad-channel",g.join("+"));a:{try{var l=c.Ja;if(T(ti)){{const x=Qi(d,e);if(x.init){var m= 
x.init;for(d=m;d=x.va(d);)m=d;var p={anchor:m,position:x.ya}}else p={anchor:d,position:e}}l["google-ama-order-assurance"]=0;$h(l,p.anchor,p.position)}else $h(l,d,e);b:{var v=c.qa;v.dataset.adsbygoogleStatus="reserved";v.className+=" adsbygoogle-noablate";l={element:v};var t=b&&b.pb;if(v.hasAttribute("data-pub-vars")){try{t=JSON.parse(v.getAttribute("data-pub-vars"))}catch(x){break b}v.removeAttribute("data-pub-vars")}t&&(l.params=t);(f.adsbygoogle=f.adsbygoogle||[]).push(l)}}catch(x){(v=c.Ja)&&v.parentNode&& 
(t=v.parentNode,t.removeChild(v),Zh(t)&&(t.style.display=t.getAttribute("data-init-display")||"none"));v=!1;break a}v=!0}return v?c:null}function Zk(a,b){return Ch(Eh(Xj(b).map(Rh),c=>{Vj(a.g).exception=c}))}var $k=class{constructor(a,b,c,d,e){this.g=a;this.H=b;this.j=c;this.A=e||null;(this.F=d)?(a=a.document,d=D(d,xj,5,B()),d=Zj(a,d)):d=Zj(a.document,[]);this.B=d;this.D=new Fk;this.i=0;this.l=!1}};function Wk(a){const b={};a&&tc(a,6,Qb,B()).forEach(c=>{b[c]=!0});return b} 
function Xk(a,b){return a&&rc(a,Nh,4)&&b[Mh(C(a,Nh,4))]?!1:!0};var al=dd(class extends M{});function bl(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?Bh(()=>al(c)):yh(null)};function cl(){if(dl)return dl;var a=ze()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?dl=b:a.google_persistent_state_async=dl=new el}function fl(a){return gl[a]||`google_ps_${a}`}function hl(a,b,c){b=fl(b);a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function il(a,b,c){return hl(a,b,()=>c)}function jl(){var a=cl();return il(a,24)}var el=class{constructor(){this.S={}}},dl=null; 
const gl={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function kl(a){this.g=a||{cookie:""}} 
kl.prototype.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.Kc,g=c.Lc||!1,f=c.domain||void 0,e=c.path||void 0,d=c.Mb);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
kl.prototype.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=ua(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};kl.prototype.isEmpty=function(){return!this.g.cookie}; 
kl.prototype.clear=function(){var a=(this.g.cookie||"").split(";");const b=[];var c=[];let d,e;for(let f=0;f<a.length;f++)e=ua(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(c=b.length-1;c>=0;c--)a=b[c],this.get(a),this.set(a,"",{Mb:0,path:void 0,domain:void 0})};function ll(a,b=window){if(H(a,5))try{return b.localStorage}catch{}return null};function ml(a){var b=new nl;return A(b,5,Mb(a))}var nl=class extends M{};function ol(a){a&&typeof a.dispose=="function"&&a.dispose()};function pl(){this.j=this.j;this.l=this.l}pl.prototype.j=!1;pl.prototype.dispose=function(){this.j||(this.j=!0,this.i())};pl.prototype[ha(Symbol,"dispose")]=function(){this.dispose()};function ql(a,b){a.j?b():(a.l||(a.l=[]),a.l.push(b))}pl.prototype.i=function(){if(this.l)for(;this.l.length;)this.l.shift()()};function rl(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function sl(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=rl(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(te({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function tl(a){if(a.g)return a.g;a:{let d=a.A;for(let e=0;e<50;++e){try{var b=!(!d.frames||!d.frames.__tcfapiLocator)}catch{b=!1}if(b){b=d;break a}b:{try{const f=d.parent;if(f&&f!=d){var c=f;break b}}catch{}c=null}if(!(d=c))break}b=null}a.g=b;return a.g}function vl(a,b,c,d){c||(c=()=>{});var e=a.A;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):tl(a)?(wl(a),e=++a.J,a.D[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function wl(a){if(!a.B){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.D[d.callId](d.returnValue,d.success)}catch(e){}};a.B=b;pe(a.A,"message",b)}} 
var xl=class extends pl{constructor(a){var b={};super();this.g=null;this.D={};this.J=0;this.B=null;this.A=a;this.H=b.Ba??500;this.F=b.xc??!1}i(){this.D={};this.B&&(qe(this.A,"message",this.B),delete this.B);delete this.D;delete this.A;delete this.g;super.i()}addEventListener(a){let b={internalBlockOnErrors:this.F};const c=wd(()=>a(b));let d=0;this.H!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.H));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
rl(b),b.internalBlockOnErrors=this.F,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{vl(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&vl(this,"removeEventListener",null,a.listenerId)}};var Cl=({u:a,X:b,Ba:c,Ea:d,ia:e=!1,ja:f=!1})=>{b=yl({u:a,X:b,ia:e,ja:f});b.g!=null||b.i.message!="tcunav"?d(b):zl(a,c).then(g=>g.map(Al)).then(g=>g.map(h=>Bl(a,h))).then(d)},Fl=({Ea:a,u:b,X:c,ia:d=!1,ja:e=!1})=>{Dl({u:b,X:c,ia:d,ja:e})?(b=(b=jl())?Al(b):void 0)?a(yh(b)):El().then(f=>f.map(Al)).then(a):a(yh(ml(!0)))},yl=({u:a,X:b,ia:c=!1,ja:d=!1})=>Dl({u:a,X:b,ia:c,ja:d})?(b=jl())?Bl(a,Al(b)):Ah(Error("tcunav")):Bl(a,ml(!0)); 
function Dl({u:a,X:b,ia:c,ja:d}){if(d=!d)d=new xl(a),d=typeof d.A.__tcfapi==="function"||tl(d)!=null;if(!d){if(c=!c){if(b){a=bl(a);if(a.g!=null)if((a=a.getValue())&&G(a,1)!=null)b:switch(a=L(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else xk(806,a.i),a=!1;b=!a}c=b}d=c}return d?!0:!1}function zl(a,b){return Promise.race([El(),Gl(a,b)])} 
function El(){return(new Promise(a=>{var b=cl();a={resolve:a};const c=il(b,25,[]);c.push(a);b.S[fl(25)]=c})).then(Hl)}function Gl(a,b){return new Promise(c=>{a.setTimeout(c,b,Ah(Error("tcto")))})}function Hl(a){return a?yh(a):Ah(Error("tcnull"))} 
function Al(a){var b={};if(sl(a))if(a.gdprApplies===!1)a=!0;else if(a.tcString==="tcunavailable")a=!b.kb;else if((b.kb||a.gdprApplies!==void 0||b.yc)&&(b.kb||typeof a.tcString==="string"&&a.tcString.length)){b:{if(a.publisher&&a.publisher.restrictions&&(b=a.publisher.restrictions["1"],b!==void 0)){b=b["755"];break b}b=void 0}b===0?a=!1:a.purpose&&a.vendor?(b=a.vendor.consents,(b=!(!b||!b["755"]))&&a.purposeOneTreatment&&a.publisherCC==="CH"?a=!0:(b&&(a=a.purpose.consents,b=!(!a||!a["1"])),a=b)):a= 
!0}else a=!0;else a=!1;return ml(a)}function Bl(a,b){return(a=ll(b,a))?yh(a):Ah(Error("unav"))};var Il=class extends M{};var Jl=class extends M{};var Kl=class{constructor(a){this.exception=a}};function Ll(a,b){try{var c=a.i,d=c.resolve,e=a.g;Vj(e.g);D(e.j,Yh,1,B());d.call(c,new Kl(b))}catch(f){a.i.reject(f)}}var Ml=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}start(){this.l()}l(){try{switch(this.j.document.readyState){case "complete":case "interactive":Vk(this.g,!0);Ll(this);break;default:Vk(this.g,!1)?Ll(this):this.j.setTimeout(qa(this.l,this),100)}}catch(a){Ll(this,a)}}};var Nl=class extends M{getVersion(){return I(this,2)}};function Ol(a){return Sa(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function Pl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function Ql(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function Rl(a){var b=Ol(a),c=Pl(b.slice(0,6));a=Pl(b.slice(6,12));var d=new Nl;c=Tc(d,1,c);a=Tc(c,2,a);b=b.slice(12);c=Pl(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=Pl(e[0])===0;e=e.slice(1);var g=Sl(e,b),h=d.length===0?0:d[d.length-1];h=Ql(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=Sl(e,b);g=Ql(f);for(let n=0;n<=g;n++)d.push(h+n);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return zc(a,3,d,Rb)}function Sl(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var Tl="a".charCodeAt(),Ul=Bd(oh),Vl=Bd(ph);function Wl(){var a=new Xl;return Uc(a,1,0)}function Yl(a){var b=Number;{var c=z(a.C,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Hb(64,c)):Ob(c)?d==="string"?Vb(c):Yb(c):void 0}b=b(c??"0");a=I(a,2);return new Date(b*1E3+a/1E6)}var Xl=class extends M{};function Zl(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function $l(a){let b=Zl(a,12);const c=[];for(;b--;){var d=!!Zl(a,1)===!0,e=Zl(a,16);if(d)for(d=Zl(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c}function am(a,b,c){const d=[];for(let e=0;e<b;e++)if(Zl(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d} 
function bm(a){const b=Zl(a,16);return!!Zl(a,1)===!0?(a=$l(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):am(a,b)}var cm=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var em=(a,b)=>{try{var c=Sa(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new cm(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=Zl(d,12);c.cmpVersion=Zl(d,12);d.skip(30);c.tcfPolicyVersion=Zl(d,6);c.isServiceSpecific=!!Zl(d,1);c.useNonStandardStacks=!!Zl(d,1);c.specialFeatureOptins=dm(am(d,12,Vl),Vl);c.purpose={consents:dm(am(d,24,Ul),Ul),legitimateInterests:dm(am(d,24,Ul),Ul)};c.purposeOneTreatment=!!Zl(d,1);c.publisherCC=String.fromCharCode(Tl+Zl(d,6))+String.fromCharCode(Tl+ 
Zl(d,6));c.vendor={consents:dm(bm(d),null),legitimateInterests:dm(bm(d),null)};return c}catch(d){return null}};const dm=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var fm=class extends M{g(){return F(this,2)!=null}};var gm=class extends M{g(){return F(this,2)!=null}};var hm=class extends M{};var im=dd(class extends M{});function jm(a){a=km(a);try{var b=a?im(a):null}catch(c){b=null}return b?C(b,hm,4)||null:null}function km(a){a=(new kl(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};Bd(oh).map(a=>Number(a));Bd(ph).map(a=>Number(a));function lm(a){a.__tcfapiPostMessageReady||mm(new nm(a))} 
function mm(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.u.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.u.addEventListener("message",a.g);a.u.__tcfapiPostMessageReady=!0}var nm=class{constructor(a){this.u=a}};function om(a){a.__uspapiPostMessageReady||pm(new qm(a))} 
function pm(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.u.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.u.addEventListener("message",a.g);a.u.__uspapiPostMessageReady=!0} 
var qm=class{constructor(a){this.u=a;this.g=null}};var rm=class extends M{};var sm=dd(class extends M{g(){return F(this,1)!=null}});function tm(a,b){function c(m){if(m.length<10)return null;var p=h(m.slice(0,4));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function d(m){if(m.length<10)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function e(m){if(m.length<12)return null;var p=h(m.slice(0,6));p=k(p);m=h(m.slice(8,12));m=n(m);return"1"+p+m+"N"}function f(m){if(m.length<18)return null;var p=h(m.slice(0,8));p=k(p);m=h(m.slice(12,18));m=n(m);return"1"+p+m+"N"}function g(m){if(m.length<10)return null; 
var p=h(m.slice(0,6));p=k(p);m=h(m.slice(6,10));m=n(m);return"1"+p+m+"N"}function h(m){const p=[];let v=0;for(let t=0;t<m.length/2;t++)p.push(Pl(m.slice(v,v+2))),v+=2;return p}function k(m){return m.every(p=>p===1)?"Y":"N"}function n(m){return m.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=Ol(a[0]);const l=Pl(a.slice(0,6));a=a.slice(6);if(l!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function um(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Xd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function vm(a){if(a!=null)return wm(a)}function wm(a){return zb(a)?Number(a):String(a)};function xm(a){var b=T(Ii);O!==O.top||O.__uspapi||O.frames.__uspapiLocator||(a=new ym(a,b),zm(a),Am(a))}function zm(a){!a.l||a.u.__uspapi||a.u.frames.__uspapiLocator||(a.u.__uspapiManager="fc",um(a.u,"__uspapiLocator"),sa("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!H(a.j,3),d({version:1,uspString:b?"1---":a.l},!0))},a.u),om(a.u))} 
function Am(a){!a.tcString||a.u.__tcfapi||a.u.frames.__tcfapiLocator||(a.u.__tcfapiManager="fc",um(a.u,"__tcfapiLocator"),a.u.__tcfapiEventListeners=a.u.__tcfapiEventListeners||[],sa("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.u.__tcfapiEventListeners;c=a.i&&!a.j.g();switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=em(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.u),lm(a.u))} 
function Bm(a){if(!a?.g()||K(a,1).length===0||D(a,rm,2,B()).length===0)return null;const b=K(a,1);let c;try{var d=Rl(b.split("~")[0]);c=b.includes("~")?b.split("~").slice(1):[]}catch(e){return null}a=D(a,rm,2,B()).reduce((e,f)=>{var g=Cm(e);g=Oc(g,1);g=wm(g);var h=Cm(f);h=Oc(h,1);return g>wm(h)?e:f});d=tc(d,3,Sb,B()).indexOf(I(a,1));return d===-1||d>=c.length?null:{uspString:tm(c[d],I(a,1)),Ha:Yl(Cm(a))}} 
function Dm(a){a=a.find(b=>b&&L(b,1)===13);if(a?.g())try{return sm(K(a,2))}catch(b){}return null}function Cm(a){return rc(a,Xl,2)?C(a,Xl,2):Wl()} 
var ym=class{constructor(a,b){var c=O;this.u=c;this.j=a;this.i=b;a=km(this.u.document);try{var d=a?im(a):null}catch(e){d=null}(a=d)?(d=C(a,gm,5)||null,a=D(a,fm,7,B()),a=Dm(a??[]),d={fb:d,jb:a}):d={fb:null,jb:null};a=d;d=Bm(a.jb);a=a.fb;a?.g()&&K(a,2).length!==0?(b=rc(a,Xl,1)?C(a,Xl,1):Wl(),a={uspString:K(a,2),Ha:Yl(b)}):a=null;this.l=a&&d?d.Ha>a.Ha?d.uspString:a.uspString:a?a.uspString:d?d.uspString:null;this.tcString=(d=jm(c.document))&&F(d,1)!=null?K(d,1):null;this.g=(c=jm(c.document))&&F(c,2)!= 
null?K(c,2):null}};const Em={google_ad_channel:!0,google_ad_host:!0};function Fm(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));wk("ama",b,.01)}function Gm(a){const b={};$d(Em,(c,d)=>{d in a&&(b[d]=a[d])});return b};function Hm(a){return a.replace(/(^\/)|(\/$)/g,"")}function Im(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function Jm(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function Km(a){a=tc(a,2,Qb,B());if(!a)return!1;for(let b=0;b<a.length;b++)if(a[b]==1)return!0;return!1}function Lm(a,b){a=Hm(Jm(Im(a.location.pathname)));const c=ae(a),d=Mm(a);return b.find(e=>{if(rc(e,tj,7)){var f=C(e,tj,7);f=Tb(z(f.C,1))}else f=Tb(z(e.C,1));rc(e,tj,7)?(e=C(e,tj,7),e=G(e,2)):e=2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null}function Mm(a){const b={};for(;;){b[ae(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function X(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function Nm(a){a=X(a);const b=a.space_collapsing||"none";return a.remove_ads_by_default?{bb:!0,Yb:b,Da:a.ablation_viewport_offset}:null}function Om(a){a=X(a);a.had_ads_ablation=!0;a.remove_ads_by_default=!0;a.space_collapsing="slot";a.ablation_viewport_offset=1}function Pm(a){X(O).allow_second_reactive_tag=a}function Qm(){const a=X(window);a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function Rm(a){return X(a)?.head_tag_slot_vars?.google_ad_host??Sm(a)}function Sm(a){return a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};const Tm=[2,7,1];function Um(a,b,c,d=""){return b===1&&c&&(Vm(a,d,c)?.j()??!1)?!0:Wm(a,d,e=>Ma(D(e,jd,2,B()),f=>G(f,1)===b),T(Oi)?!!C(c,Xm,26)?.g():c.g())}function Ym(a,b){const c=Vd(O)||O;return Zm(c,a)?!0:Wm(O,"",d=>Ma(tc(d,3,Qb,B()),e=>e===a),b)}function Zm(a,b){a=(a=(a=a.location&&a.location.hash)&&a.match(/forced_clientside_labs=([\d,]+)/))&&a[1];return!!a&&Oa(a.split(","),b.toString())} 
function Wm(a,b,c,d){a=Vd(a)||a;const e=$m(a,d);b&&(b=Ee(String(b)));return Ad(e,(f,g)=>Object.prototype.hasOwnProperty.call(e,g)&&(!b||b===g)&&c(f))}function $m(a,b){a=an(a,b);const c={};$d(a,(d,e)=>{try{const f=bd(kd,hc(d));c[e]=f}catch(f){}});return c}function an(a,b){a=yl({u:a,X:b});return a.g!=null?bn(a.getValue()):{}} 
function bn(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:zd(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}}function cn(a,b){const c=[];a=Rm(r)?Tm:(a=Vm(r,a,b)?.B())?[...tc(a,3,Qb,B())]:Tm;a.includes(1)||c.push(1);a.includes(2)||c.push(2);a.includes(7)||c.push(7);return c} 
function Vm(a,b,c){if(!b)return null;const d=dn(c)?.D();a=dn(c)?.g()?.g()===b&&a.location.host&&K(c,17)===a.location.host;return d===b||a?dn(c):null};function en(a,b,c,d){fn(new gn(a,b,c,d))}function fn(a){const b=T(Oi)?!!C(a.g,Xm,26)?.g():a.g.g();Eh(Dh(yl({u:a.u,X:b}),c=>{hn(a,c,!0)}),()=>{jn(a)})}function hn(a,b,c){Eh(Dh(kn(b),d=>{ln("ok");a.i(d,{fromLocalStorage:!0})}),()=>{var d=a.u;try{b.removeItem("google_ama_config")}catch(e){Fm(d,{lserr:1})}c?jn(a):a.i(null,null)})}function jn(a){Eh(Dh(mn(a),b=>{a.i(b,{fromPABGSettings:!0})}),()=>{nn(a)})} 
function kn(a){if(T(si))var b=null;else try{b=a.getItem("google_ama_config")}catch(d){b=null}try{var c=b?Fj(b):null}catch(d){c=null}return(a=(a=c)?(vm(C(a,sj,3)?.g())??0)>Date.now()?a:null:null)?yh(a):Ah(Error("invlocst"))}function mn(a){if(Rm(a.u)&&!H(a.g,22))return Ah(Error("invtag"));if(a=(a=Vm(a.u,a.j,a.g)?.A())&&D(a,Yh,1,B()).length>0?a:null){var b=new Ej;var c=D(a,Yh,1,B());b=Kc(b,1,c);a=D(a,zj,2,B());a=Kc(b,7,a);a=yh(a)}else a=Ah(Error("invtag"));return a} 
function nn(a){const b=T(Oi)?!!C(a.g,Xm,26)?.g():a.g.g();Cl({u:a.u,X:b,Ba:50,Ea:c=>{on(a,c)}})}function on(a,b){Eh(Dh(b,c=>{hn(a,c,!1)}),c=>{ln(c.message);a.i(null,null)})}function ln(a){wk("abg::amalserr",{status:a,guarding:"true",timeout:50,rate:.01},.01)}class gn{constructor(a,b,c,d){this.u=a;this.g=b;this.j=c;this.i=d}};function pn(a,b,c,d){var e=qn;try{const f=Lm(a,D(c,zj,7,B()));if(f&&Km(f)){if(F(f,4)??void 0){const h=new Ph(null,{google_package:F(f,4)??void 0});d=Oh(d,h)}const g=e(a,b,c,f,d);Qj(1E3,()=>{const h=new th;(new Ml(a,g,h)).start();return h.i},a).then(()=>{Fm(a,{atf:1})},h=>{(a.google_ama_state=a.google_ama_state||{}).exception=h;Fm(a,{atf:0})})}}catch(f){Fm(a,{atf:-1})}}function qn(a,b,c,d,e){return new $k(a,b,c,d,e)};function rn(a){return a.length?a.join("~"):void 0};function sn(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=tn(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function tn(a){let b="";$d(a.split("_"),c=>{b+=c.substr(0,2)});return b};var Xm=class extends M{g(){return H(this,1)}j(){return H(this,2)}};var un=class extends M{g(){return K(this,3)}};var vn=class extends M{g(){return Hc(this,un,1)}};function wn(a){const b=new vn;var c=new un;var d=I(a,1);c=Sc(c,1,d);d=I(a,18);c=Sc(c,2,d);d=K(a,2);c=Wc(c,3,d);d=T(Oi)?!!C(a,Xm,26)?.g():a.g();c=A(c,4,Mb(d));d=H(a,20);c=A(c,5,Mb(d));d=H(a,9);c=A(c,6,Mb(d));d=T(Oi)?!!C(a,Xm,26)?.j():H(a,25);c=A(c,7,Mb(d));d=K(a,8);c=Wc(c,8,d);d=K(a,3);c=Wc(c,9,d);a=C(a,Xm,26);a=E(c,10,a);E(b,1,a);return b};function xn(){const a={};N(ke).i(ni.g,ni.defaultValue)&&(a.bust=N(ke).i(ni.g,ni.defaultValue));return a};class yn{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function zn(){const {promise:a,resolve:b}=new yn;return{promise:a,resolve:b}};function An(a=()=>{}){r.google_llp||(r.google_llp={});const b=r.google_llp;let c=b[7];if(c)return c;c=zn();b[7]=c;a();return c}function Bn(a){return An(()=>{Wd(r.document,a)}).promise};Array.from({length:11},(a,b)=>b/10);function Cn(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new Dn;return a.google_reactive_ads_global_state} 
var Dn=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new En;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},En=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};var Fn=a=>{if(r.google_apltlad||a.google_ad_intent_query)return null;var b=a.google_loader_used!=="sd"&&T(vi)&&(r.top==r?0:Ud(r.top)?1:2)===1;if(r!==r.top&&!b||!a.google_ad_client)return null;r.google_apltlad=!0;b={enable_page_level_ads:{pltais:!0},google_ad_client:a.google_ad_client};const c=b.enable_page_level_ads;$d(a,(d,e)=>{oj[e]&&e!=="google_ad_client"&&(c[e]=d)});c.google_pgb_reactive=7;c.asro=T(zi);c.aihb=T(wi);c.aifxl=rn(N(ke).g(xi.g,xi.defaultValue));U(Ci)&&(c.aiapm=U(Ci));U(Di)&&(c.aiapmi= 
U(Di));U(Bi)&&(c.aiact=U(Bi));U(Ei)&&(c.aicct=U(Ei));U(Fi)&&(c.ailct=U(Fi));U(Gi)&&(c.aimart=U(Gi));c.aiof=rn(N(ke).g(yi.g,yi.defaultValue));if("google_ad_section"in a||"google_ad_region"in a)c.google_ad_section=a.google_ad_section||a.google_ad_region;return b};function Gn(a,b){X(O).ama_ran_on_page||Qj(1001,()=>{Hn(new In(a,b))},r)}function Hn(a){en(a.u,a.i,a.g.google_ad_client||"",(b,c)=>{var d=a.u,e=a.g;X(O).ama_ran_on_page||b&&Jn(d,e,b,c)})}class In{constructor(a,b){this.u=r;this.g=a;this.i=b}} 
function Jn(a,b,c,d){d&&(Vj(a).configSourceInAbg=d);rc(c,Dj,24)&&(d=Wj(a),d.availableAbg=!0,d.ablationFromStorage=!!C(c,Dj,24)?.g()?.g());if(ka(b.enable_page_level_ads)&&b.enable_page_level_ads.google_pgb_reactive===7){if(!Lm(a,D(c,zj,7,B()))){wk("amaait",{value:"true"});return}wk("amaait",{value:"false"})}X(O).ama_ran_on_page=!0;C(c,rj,15)?.g()&&(X(a).enable_overlap_observer=!0);C(c,Dj,24)?.g()?.g()&&(Wj(a).ablatingThisPageview=!0,Om(a));we(3,[y(c)]);const e=b.google_ad_client||"";b=Gm(ka(b.enable_page_level_ads)? 
b.enable_page_level_ads:{});const f=Oh(Sh,new Ph(null,b));tk(782,()=>{pn(a,e,c,f)})};var Kn=class{constructor(){this.g=pk}la(a){const b=a.sa;this.g.L(a.methodName??0,b instanceof Error?b:Error(String(b)))}};var Ln=class extends Error{constructor(a){super(a)}},Mn=class{constructor(){this.reason=void 0}};class Nn{constructor(){this.g=!1}}function On(a,b){a.g||(a.g=!0,a.l=b,a.i.resolve(b))}class Pn extends Nn{constructor(){super(...arguments);this.i=new yn}get promise(){return this.i.promise}get qb(){return this.g}get error(){return this.j}}var Qn=class extends Pn{setError(a,b){this.g||(this.g=!0,this.l=null,this.j=a,b&&b(this.j),this.i.reject(a))}}; 
class Rn extends Nn{constructor(a){super();this.i=a}get error(){return this.i.j}qb(){return this.i.g}}var Sn=class extends Rn{constructor(a){super(a);this.i=a}get value(){return this.i.l}},Tn=class extends Rn{constructor(a){super(a);this.i=a}get value(){return this.i.l??null}},Un=class extends Pn{notify(){On(this,null)}};function Vn(a,b){a.g.push({ra:!1,Ia:b})}var Wn=class extends pl{constructor(){super(...arguments);this.A=[];this.g=[];this.B=[]}ra(a){const b=this.g.find(c=>c.Ia===a);b&&(b.ra=!0)}i(){this.A.length=0;this.B.length=0;this.g.length=0;super.i()}};async function Xn(a,b){const c=b?a.filter(d=>!d.ra):a;q(await q(Promise.all(c.map(({Ia:d})=>d.promise))));a.length!==c.length&&(a=a.filter(d=>d.ra),q(await q(Promise.race([Promise.all(a.map(({Ia:d})=>d.promise)),new Promise(d=>void setTimeout(d,b))]))))}function Yn(a){var b=new Qn;a.g.A.push(b);return b} 
var Zn=class extends pl{constructor(a,b){super();this.id=a;this.da=b;this.Ba=void 0;this.H=!1;this.g=new Wn;ql(this,ra(ol,this.g))}async start(){if(!this.H){this.H=!0;try{if(q(await q(Xn(this.g.g,this.pa??this.Ba))),!this.j){let a=0;for(const b of this.g.B){if(b.i.l==null)throw Error(`missing input: ${this.id}/${a}`);++a}this.B()}}catch(a){this.j||(a instanceof Ln?this.F(a):a instanceof Error&&(this.da.la({methodName:this.id,sa:a}),this.A(a)))}}}F(){}A(a){if(this.g.A.length){var b=new Ln(a.message); 
for(const e of this.g.A)if(!e.qb){var c=e,d=b;c.g=!0;c.j=d;c.i.reject(d)}}a instanceof Ln||console?.error(a)}};var ao=class extends Zn{constructor(a,b,c,d,e){super(a,c);this.f=b;this.J=e;a={};for(const [f,g]of Object.entries(d))if(d=g)Vn(this.g,d),a[f]=new Tn(d);this.D=a}B(){var a=this.f;const b={};for(const [c,d]of Object.entries(this.D))b[c]=d.value;a=a.call(this,b,...this.J);$n(this,a)}F(a){this.A(a)}reportError(){}};function $n(a,b){for(const [c,d]of Object.entries(b)){b=c;const e=d;e instanceof Error&&a[b].setError(e);e instanceof Mn||On(a[b],e)}a.finished.notify()}class bo extends ao{constructor(a,b,c,d,e,f,g){super(a,b,c,d,g);this.R=f;this.finished=new Un;a=Object.keys(e);for(const h of a)this[h]=Yn(this)}A(a){this.R?$n(this,this.R(a)):super.A(a)}}function co(a,b){a.id=b.id;a.ga=b.ga;a.R=b.R;return a}function eo(a,b,c,...d){return new bo(a.id,a,b,c,a.ga,a.R,d)};function fo(a,b){ql(a,ra(ol,b));a.B.push(b);return b}function go(a,b,c,...d){return fo(a,eo(b,a.J,c,...d))}async function ho(a){a.g.length&&q(await q(Promise.all(a.g.map(d=>d.D.promise))));for(var b of a.B)b.start();for(var c of a.H)ho(c);if(a.A&&(b=Object.keys(a.A),b.length)){c=q(await q(Promise.all(Object.values(a.A).map(e=>e.promise))));let d=0;for(const e of b)a.F[e]=c[d++]}a.D.resolve(a.F)} 
var io=class extends pl{constructor(a){super();this.J=a;this.B=[];this.H=[];this.F={};this.g=[];this.D=new yn;this.A={}}i(){super.i();this.B.length=0;this.H.length=0;this.g.length=0}};var jo=co(function(a){return(a=a.Tb)?{na:D(a,ed,2,B()).map(b=>{var c=K(b,Ec(b,fd,2)),d=K(b,1);c=c&&(c.startsWith(location.protocol)||c.startsWith("data:")&&c.length<=80)?Hd(c===null?"null":c===void 0?"undefined":c):void 0;return{ta:d,url:c,Lb:H(b,4)}})}:{na:[]}},{id:1040,ga:{na:void 0}});function ko(a,b=window,c=()=>{}){try{return b.localStorage.getItem(a)}catch(d){return c(d),null}}function lo(a,b,c=window,d=()=>{}){return H(b,5)?ko(a,c,d):null}function mo(a,b,c=window,d=()=>{}){try{return c.localStorage.setItem(a,b),!0}catch(e){d(e)}return!1}function no(a,b,c,d=window,e=()=>{}){return H(c,5)?mo(a,b,d,e):!1};class oo{static ib(){throw Error("Must be overridden");}}class po extends oo{constructor(){super(...arguments);this.g=0}}(function(){var a=po;a.fa=void 0;a.ib=function(){return a.fa?a.fa:a.fa=new a}})();function qo(a,b,c=null,d={}){const e=po.ib();e.g===0&&(e.g=Math.random()<.001?2:1);e.g===2&&te({c:String(a),pc:String(me(window)),em:c,lid:b,eids:N(ih).g().join(),...d},"esp")};function ro(){so||(so=new to);return so} 
var to=class{constructor(){this.cache={}}get(a,b,c){function d(g){qo(6,a,g?.message);e=!0}if(this.cache[a])return{G:this.cache[a],success:!0};let e=!1;const f=`${"_GESPSK"}-${a}`;b=c?ko(f,window,d):lo(f,b,window,d);if(e)return{G:null,success:!1};if(!b)return{G:null,success:!0};try{const g=pd(b);this.cache[a]=g;return{G:g,success:!0}}catch(g){return qo(5,a,g?.message),{G:null,success:!1}}}set(a,b,c){function d(g){qo(7,e,g?.message)}const e=qb(F(a,1)),f=`${"_GESPSK"}-${e}`;nd(a);if(c?!mo(f,JSON.stringify(y(a)), 
window,d):!no(f,JSON.stringify(y(a)),b,window,d))return!1;this.cache[e]=a;return!0}},so=null;var vo=co(uo,{id:1041,ga:{}});function uo(a,b){ro().set(a.G,a.hb,b)&&F(a.G,2)!=null&&(a=F(a.G,1),qo(27,a));return{}};var wo=class extends Zn{constructor(a,b,c,d){super(1027,d);this.ta=a;this.J=b;this.Ga=c;this.oa=Yn(this);this.D=Yn(this)}B(){var {G:a}=ro().get(this.ta,this.Ga,this.J);if(!a){a=new od;a=Wc(a,1,this.ta);a=nd(a);var b=this.D,c=a.setError(ld(100));On(b,c)}On(this.oa,a)}};var xo=co(function(a){var b=a.G;var c=Zb(z(b.C,3))??void 0;(c=c==null?null:wm(c))?F(b,2)===void 0?b=4:(b=Date.now(),b=b>c+2592E5?2:b>c+432E5?1:0):b=3;return b!==0?{G:a.G}:{G:new Mn}},{id:1036,ga:{G:void 0}});var yo=class extends Zn{constructor(a,b,c){super(1035,c);this.J=b;this.D=Yn(this);Vn(this.g,a);a=new Sn(a);this.g.B.push(a);this.ea=a}B(){const a=this.ea.value,b=qb(F(a,1)),c=this.J.toString();qo(30,b,null,{url:c});const d=document.createElement("script");d.setAttribute("esp-signal","true");Ld(d,this.J);const e=()=>{qo(31,b,null,{url:c});var f=this.D,g=a.setError(ld(109));On(f,g);qe(d,"error",e)};document.head.appendChild(d);pe(d,"error",e)}};var Ao=co(zo,{id:1028,ga:{oa:void 0}});function zo(a){var b=F(a.G,1);var c=z(a.G.C,3);c!=null&&(typeof c==="bigint"?zb(c)?c=Number(c):(c=Hb(64,c),c=zb(c)?Number(c):String(c)):c=Ob(c)?typeof c==="number"?Wb(c):Vb(c):void 0);c!=null||qo(35,b);return{oa:a.G}};var Bo=class extends io{constructor(a,b,c,d,e){super(e);a=new wo(a,c,d,e);const f=new Qn;On(f,d);go(this,vo,{G:a.D,hb:f},c);d=go(this,xo,{G:a.oa});d=go(this,Ao,{G:d.G});b=new yo(d.oa,b,e);go(this,vo,{G:b.D,hb:f},c);c=[a,b];for(const g of c)fo(this,g)}};var Co=new Set,Do=co(function(a,b,c,d){c=a.na;a=a.Ga;if(!c?.length)return{};const e=H(a,5);for(const {ta:f,url:g,Lb:h}of c)g&&(e||h)&&!Co.has(g.toString())&&(Co.add(g.toString()),c=new Bo(f,g,!!h,a,b),ql(d,ra(ol,c)),ho(c));return{}},{id:813,ga:{}});var Eo=class extends io{constructor(a,b){var c=new Kn;super(c);this.ea=a;this.da=b;({na:a}=go(this,jo,{Tb:this.ea}));go(this,Do,{na:a,Ga:this.da},c,{},this)}};function Fo(a){var b=window,c=Go(window);return d=>{if(d.g!=null){var e=new Qn,f=Ho(Vm(b,c,a));On(e,f);f=new Qn;d=d.getValue();On(f,d);e=new Eo(e,f);ho(e)}}}function Ho(a){a=a?.F();if(!a)return new id;a=D(a,Io,1,B()).map(b=>{var c=new ed;var d=b.getName();c=Wc(c,1,d);b=K(b,2);b=Bc(c,2,fd,ac(b));return A(b,4,Mb(!1))});return hd(a)};function Jo(a,b){a=a.document;for(var c=void 0,d=0;!c||a.getElementById(c+"_host");)c="aswift_"+d++;a=c;c=Number(b.google_ad_width||0);b=Number(b.google_ad_height||0);d=document.createElement("div");d.id=a+"_host";const e=d.style;e.border="none";e.height=`${b}px`;e.width=`${c}px`;e.margin="0px";e.padding="0px";e.position="relative";e.visibility="visible";e.backgroundColor="transparent";e.display="inline-block";return{Hb:a,bc:d}};function Ko({Fa:a,Ma:b}){return a||(b==="dev"?"dev":"")};function Go(a){return a.google_ad_client?String(a.google_ad_client):X(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??""};var Lo={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function Mo(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};var No=class extends M{getVersion(){return K(this,2)}};function Oo(a,b){return Wc(a,2,b)}function Po(a,b){return Wc(a,3,b)}function Qo(a,b){return Wc(a,4,b)}function Ro(a,b){return Wc(a,5,b)}function So(a,b){return Wc(a,9,b)}function To(a,b){return Kc(a,10,b)}function Uo(a,b){return A(a,11,Mb(b))}function Vo(a,b){return Wc(a,1,b)}function Wo(a,b){return A(a,7,Mb(b))}var Xo=class extends M{};const Yo="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Zo(){var a=O;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Yo).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function $o(a){return Uo(To(Ro(Oo(Vo(Qo(Wo(So(Po(new Xo,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new No;c=Wc(c,1,b.brand);return Wc(c,2,b.version)})||[]),a.wow64||!1)}function ap(){return Zo()?.then(a=>$o(a))??null};function bp(a,b){b.google_ad_host||(a=Sm(a))&&(b.google_ad_host=a)}function cp(a,b,c=""){O.google_sa_queue||(O.google_sa_queue=[],O.google_process_slots=uk(215,()=>{dp(O.google_sa_queue)}),a=ep(c,a,b),Wd(O.document,a))}function dp(a){const b=a.shift();typeof b==="function"&&tk(216,b);a.length&&r.setTimeout(uk(215,()=>{dp(a)}),0)}function fp(a,b){a.google_sa_queue=a.google_sa_queue||[];a.google_sa_impl?b():a.google_sa_queue.push(b)} 
function ep(a,b,c){var d=O;b=H(c,4)?b.Ub:b.Vb;a:{if(H(c,4)){if(a=a||Go(d)){b:{try{for(;d;){if(d.location?.hostname){var e=d.location.hostname;break b}d=d.parent}}catch(f){}e=""}e={client:Ee(a),plah:e};break a}throw Error("PublisherCodeNotFoundForAma");}e={}}e={...e,...xn()};d=U(Ji);!H(c,4)&&[0,1].includes(d)&&(e.osttc=`${d}`);return Sd(b,new Map(Object.entries(e)))} 
function gp(a,b,c,d){const {Hb:e,bc:f}=Jo(a,b);c.appendChild(f);hp(a,c,b);c=b.google_start_time??nh;const g=(new Date).getTime();b.google_lrv=Ko({Fa:ng(),Ma:K(d,2)});b.google_async_iframe_id=e;b.google_start_time=c;b.google_bpp=g>c?g-c:1;a.google_sv_map=a.google_sv_map||{};a.google_sv_map[e]=b;fp(a,()=>{var h=f;if(!h||!h.isConnected)if(h=a.document.getElementById(String(b.google_async_iframe_id)+"_host"),h==null)throw Error("no_div");(h=a.google_sa_impl({pubWin:a,vars:b,innerInsElement:h}))&&vk(911, 
h)})} 
function hp(a,b,c){var d=c.google_ad_output,e=c.google_ad_format,f=c.google_ad_width||0,g=c.google_ad_height||0;e||d!=="html"&&d!=null||(e=`${f}x${g}`);T(Ni)&&(c.google_reactive_ad_format===10?e="interstitial":c.google_reactive_ad_format===11&&(e="rewarded"));d=!c.google_ad_slot||c.google_override_format||!Lo[c.google_ad_width+"x"+c.google_ad_height]&&c.google_loader_used==="aa";e=e&&d?e.toLowerCase():"";c.google_ad_format=e;if(typeof c.google_reactive_sra_index!=="number"||!c.google_ad_unit_key){e=[c.google_ad_slot, 
c.google_orig_ad_format||c.google_ad_format,c.google_ad_type,c.google_orig_ad_width||c.google_ad_width,c.google_orig_ad_height||c.google_ad_height];d=[];f=0;for(g=b;g&&f<25;g=g.parentNode,++f)g.nodeType===9?d.push(""):d.push(g.id);(d=d.join())&&e.push(d);c.google_ad_unit_key=ae(e.join(":")).toString();e=[];for(d=0;b&&d<25;++d){f=(f=b.nodeType!==9&&b.id)?"/"+f:"";a:{if(b&&b.nodeName&&b.parentElement){g=b.nodeName.toString().toLowerCase();const h=b.parentElement.childNodes;let k=0;for(let n=0;n<h.length;++n){const l= 
h[n];if(l.nodeName&&l.nodeName.toString().toLowerCase()===g){if(b===l){g="."+k;break a}++k}}}g=""}e.push((b.nodeName&&b.nodeName.toString().toLowerCase())+f+g);b=b.parentElement}b=e.join()+":";e=[];if(a)try{let h=a.parent;for(d=0;h&&h!==a&&d<25;++d){const k=h.frames;for(f=0;f<k.length;++f)if(a===k[f]){e.push(f);break}a=h;h=a.parent}}catch(h){}c.google_ad_dom_fingerprint=ae(b+e.join()).toString()}} 
function ip(){var a=Vd(r);a&&(a=Cn(a),a.tagSpecificState[1]||(a.tagSpecificState[1]={debugCard:null,debugCardRequested:!1}))}function jp(){const a=ap();a!=null&&a.then(b=>{O.google_user_agent_client_hint=JSON.stringify(y(b))});je()};var kp=class{constructor(a,b){this.g=a;this.l=b}height(){return this.l}i(a){return a>U(qi)&&this.l>300?this.g:Math.min(1200,Math.round(a))}j(){}};function lp(a){return b=>!!(b.ca()&a)}var Y=class extends kp{constructor(a,b,c,d=!1){super(a,b);this.B=c;this.A=d}ca(){return this.B}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const mp={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},np={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},op={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function pp(a){var b=0;a.V&&b++;a.M&&b++;a.N&&b++;if(b<3)return{ba:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.V.split(",");const c=a.N.split(",");a=a.M.split(",");if(b.length!==c.length||b.length!==a.length)return{ba:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{ba:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{ba:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{ba:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{N:d,M:e,mb:b}} 
function qp(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function rp(a,b,c,d){b=Math.floor(((a-8*b-8)/b*mp[d]+np[d])*c+8*c+8);return a>1500?{width:0,height:0,Wb:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,Wb:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function sp(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*mp.mobile_banner_image_sidebyside+np.mobile_banner_image_sidebyside)*b+8*b+8)}};const tp=Qa("script");var up=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,k=null,n=null,l=null,m=null){this.D=a;this.Z=b;this.ca=c;this.g=d;this.F=e;this.I=f;this.U=g;this.l=h;this.A=k;this.i=n;this.j=l;this.B=m}size(){return this.Z}};const vp=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var wp=class extends kp{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function xp(a,b){yp(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new up(9,new wp(a,Math.floor(a*2.189)));if(T(di)){var c=xd();var d=U(ei);var e=U(ci),f=U(bi);a<468?c?(a=sp(a,d),d={aa:a.width,Y:a.height,M:1,N:d,V:"mobile_banner_image_sidebyside"}):(a=rp(a,1,d,"image_sidebyside"),d={aa:a.width,Y:a.height,M:1,N:d,V:"image_sidebyside"}):(d=qp(a),e===1&&(d.height=Math.floor(d.height*.5)),d={aa:d.width,Y:d.height,M:f,N:e,V:"image_stacked"})}else d=xd(),a<468?d?(d=sp(a,12),d={aa:d.width, 
Y:d.height,M:1,N:12,V:"mobile_banner_image_sidebyside"}):(d=qp(a),d={aa:d.width,Y:d.height,M:1,N:13,V:"image_sidebyside"}):(d=qp(a),d={aa:d.width,Y:d.height,M:4,N:2,V:"image_stacked"});zp(b,d);return new up(9,new wp(d.aa,d.Y))} 
function Ap(a,b){yp(a,b);{const f=pp({N:b.google_content_recommendation_rows_num,M:b.google_content_recommendation_columns_num,V:b.google_content_recommendation_ui_type});if(f.ba)a={aa:0,Y:0,M:0,N:0,V:"image_stacked",ba:f.ba};else{var c=f.mb.length===2&&a>=468?1:0;var d=f.mb[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=op[d];let g=f.M[c];for(;a/g<e&&g>1;)g--;e=g;c=f.N[c];a=rp(a,e,c,d);a={aa:a.width,Y:a.height,M:e,N:c,V:d}}}if(a.ba)throw new W(a.ba);zp(b,a);return new up(9,new wp(a.aa, 
a.Y))}function yp(a,b){if(a<=0)throw new W(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function zp(a,b){a.google_content_recommendation_ui_type=b.V;a.google_content_recommendation_columns_num=b.M;a.google_content_recommendation_rows_num=b.N};var Bp=class extends kp{i(){return this.g}j(a,b,c){fj(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const Cp={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var Dp=class extends kp{i(){return Math.min(1200,this.g)}}; 
function Ep(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=aj(b,c,g,U(ii),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=V(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const k=g.parentElement.childNodes;for(let n=0;n<k.length;++n){const l=k[n];if(l!==g&&dj(b,l))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}fj(b,c)}else a=g;else a= 
g}if(a<250)throw new W("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new W("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new up(11,new kp(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new W(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new W(`Invalid height: height=${f}`);if(f<50)throw new W("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new W("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new up(11,new kp(a,f))}d=Cp[f];if(!d)throw new W("Invalid data-ad-layout value: "+f);c=lj(c,b);b=V(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25);return new up(11, 
f==="in-article"?new Dp(a,b):new kp(a,b))};function Fp(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function Gp(a,b){var c=Hp.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var Z=[new Y(970,90,2),new Y(728,90,2),new Y(468,60,2),new Y(336,280,1),new Y(320,100,2),new Y(320,50,2),new Y(300,600,4),new Y(300,250,1),new Y(250,250,1),new Y(234,60,2),new Y(200,200,1),new Y(180,150,1),new Y(160,600,4),new Y(125,125,1),new Y(120,600,4),new Y(120,240,4),new Y(120,120,1,!0)],Hp=[Z[6],Z[12],Z[3],Z[0],Z[7],Z[14],Z[1],Z[8],Z[10],Z[4],Z[15],Z[2],Z[11],Z[5],Z[13],Z[9],Z[16]];function Ip(a,b,c,d,e){e.google_full_width_responsive==="false"?c={K:a,I:1}:b==="autorelaxed"&&e.google_full_width_responsive||Jp(b)||e.google_ad_resize?(b=bj(a,c,d,e),c=b!==!0?{K:a,I:b}:{K:V(c)||a,I:!0}):c={K:a,I:2};const {K:f,I:g}=c;return g!==!0?{K:a,I:g}:d.parentElement?{K:f,I:g}:{K:a,I:g}} 
function Kp(a,b,c,d,e){const {K:f,I:g}=tk(247,()=>Ip(a,b,c,d,e));var h=g===!0;const k=ee(d.style.width),n=ee(d.style.height),{Z:l,U:m,ca:p,lb:v}=Lp(f,b,c,d,e,h);h=Mp(b,p);var t;const x=(t=gj(d,c,"marginLeft"))?`${t}px`:"",J=(t=gj(d,c,"marginRight"))?`${t}px`:"";t=ij(d,c)||"";return new up(h,l,p,null,v,g,m,x,J,n,k,t)}function Jp(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function Lp(a,b,c,d,e,f){b=Np(c,a,b);let g;var h=!1;let k=!1;var n=V(c)<488;if(n){g=Wi(d,c);var l=lj(d,c);h=!l&&g;k=l&&g}l=[jj(a),lp(b)];T(oi)||l.push(kj(n,c,d,k));e.google_max_responsive_height!=null&&l.push(mj(e.google_max_responsive_height));n=[t=>!t.A];if(h||k)h=nj(c,d),n.push(mj(h));const m=Gp(Fp(l),Fp(n));if(!m)throw new W(`No slot size for availableWidth=${a}`);const {Z:p,U:v}=tk(248,()=>{var t;a:if(f){if(e.gfwrnh&&(t=ee(e.gfwrnh))){t={Z:new Bp(a,t),U:!0};break a}t=U(ki);t=t>0?a/t:a/1.2;if(e.google_resizing_allowed|| 
e.google_full_width_responsive==="true")var x=Infinity;else{x=d;let P=Infinity;do{var J=gj(x,c,"height");J&&(P=Math.min(P,J));(J=gj(x,c,"maxHeight"))&&(P=Math.min(P,J))}while(x.parentElement&&(x=x.parentElement)&&x.tagName!=="HTML");x=P}!(T(mi)&&x<=t*2)&&(x=Math.min(t,x),x<t*.5||x<100)&&(x=t);t={Z:new Bp(a,Math.floor(x)),U:x<t?102:!0}}else t={Z:m,U:100};return t});return e.google_ad_layout==="in-article"?{Z:Op(a,c,d,p,e),U:!1,ca:b,lb:g}:{Z:p,U:v,ca:b,lb:g}} 
function Mp(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function Np(a,b,c){if(c==="auto")c=Math.min(1200,V(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Ti)c.indexOf(d)!==-1&&(b|=Ti[d])}return b}function Op(a,b,c,d,e){const f=e.google_ad_height||gj(c,b,"height");b=Ep(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new Y(b.g,b.height(),1):d};function Pp(a,b,c,d,e){var f;(f=V(b))?V(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,fj(b,c),f={K:f,I:!0}):f={K:a,I:5}:f={K:a,I:4}:f={K:a,I:10};const {K:g,I:h}=f;if(h!==!0||a===g)return new up(12,new kp(a,d),null,null,!0,h,100);const {Z:k,U:n,ca:l}=Lp(g,"auto",b,c,e,!0);return new up(1,k,l,2,!0,h,n)};function Qp(a){const b=a.google_ad_format;if(b==="autorelaxed"){if(T(ri))return a.google_ad_format="auto",1;a:{if(a.google_content_recommendation_ui_type!=="pedestal")for(const c of vp)if(a[c]!=null){a=!0;break a}a=!1}return a?9:5}if(Jp(b))return 1;if(b==="link")return 4;if(b==="fluid")return a.google_ad_layout==="in-article"?(Rp(a),1):8;if(a.google_reactive_ad_format===27)return Rp(a),1} 
function Sp(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&gj(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=Tp(a,f,b,c,d))?e:Kp(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.ca!=null&&(c.google_responsive_formats=e.ca);e.F!=null&&(c.google_safe_for_responsive_override=e.F);e.I!=null&&(e.I===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.I));e.U!=null&&e.U!==!0&&(c.gfwrnher=e.U);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.D;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.I===!0&&(c.gfwrnh=`${e.size().height()}px`);e.l!=null&&(c.gfwroml=e.l);e.A!=null&&(c.gfwromr=e.A);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.B!=null&&(c.gfwroz=e.B);f=Vd(window)||window;sn(f.location,"google_responsive_dummy_ad")&&(Oa([1,2,3,4,5,6,7,8],e.D)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${tp}>window.top.postMessage('${f}', '*'); 
          </${tp}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function Tp(a,b,c,d,e){const f=d.google_ad_height||gj(c,e,"height")||0;switch(a){case 5:const {K:g,I:h}=tk(247,()=>Ip(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&fj(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return xp(g,d);case 9:return Ap(b,d);case 8:return Ep(b,e,c,f,d);case 10:return Pp(b,e,c,f,d)}}function Rp(a){a.google_ad_format="auto";a.armr=3};function Up(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};function Vp(a,b){var c=Vd(b);if(c){c=V(c);const d=Yd(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function Wp(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}} 
function Xp(a,b){if(a.getAttribute("src")){var c=a.getAttribute("src")||"";const d=Qd(c,"client");d&&(b.google_ad_client=Wp("google_ad_client",d));(c=Qd(c,"host"))&&(b.google_ad_host=Wp("google_ad_host",c))}for(const d of a.attributes)/data-/.test(d.name)&&(a=ua(d.name.replace("data-matched-content","google_content_recommendation").replace("data","google").replace(/-/g,"_")),b.hasOwnProperty(a)||(c=Wp(a,d.value),c!==null&&(b[a]=c)))} 
function Yp(a,b){if(a=ye(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?17:12} 
function Zp(a,b,c,d){Xp(a,b);if(c.document&&c.document.body&&!Qp(b)&&!b.google_reactive_ad_format&&!b.google_ad_intent_query){var e=parseInt(a.style.width,10),f=Vp(a,c);if(f>0&&e>f){var g=parseInt(a.style.height,10);e=!!Lo[e+"x"+g];let h=f;if(e){const k=Mo(f,g);if(k)h=k,b.google_ad_format=k+"x"+g+"_0ads_al";else throw new W("No slot size for availableWidth="+f);}b.google_ad_resize=!0;b.google_ad_width=h;e||(b.google_ad_format=null,b.google_override_format=!0);f=h;a.style.width=`${f}px`;Up(b,4)}}if(T(fi)|| 
V(c)<488){f=Vd(c)||c;g=a.offsetWidth||gj(a,c,"width")||b.google_ad_width||0;e=b.google_ad_client;if(d=sn(f.location,"google_responsive_slot_preview")||Um(f,1,d,e))b:if(b.google_reactive_ad_format||b.google_ad_resize||Qp(b)||Yi(a,b))d=!1;else{for(d=a;d;d=d.parentElement){f=Yd(d,c);if(!f){b.gfwrnwer=18;d=!1;break b}if(!Oa(["static","relative"],f.position)){b.gfwrnwer=17;d=!1;break b}}if(!T(pi)&&(d=U(ji),d=aj(c,a,g,d,b),d!==!0)){b.gfwrnwer=d;d=!1;break b}d=c===c.top?!0:!1}d?(Up(b,1),d=!0):d=!1}else d= 
!1;if(g=Qp(b))Sp(g,a,b,c,d);else{if(Yi(a,b)){if(d=Yd(a,c))a.style.width=d.width,a.style.height=d.height,Xi(d,b);b.google_ad_width||(b.google_ad_width=a.offsetWidth);b.google_ad_height||(b.google_ad_height=a.offsetHeight);b.google_loader_features_used=256;b.google_responsive_auto_format=Yp(c,b)}else Xi(a.style,b);c.location&&c.location.hash==="#gfwmrp"||b.google_responsive_auto_format===12&&b.google_full_width_responsive==="true"?Sp(10,a,b,c,!1):Math.random()<.01&&b.google_responsive_auto_format=== 
12&&(a=bj(a.offsetWidth||parseInt(a.style.width,10)||b.google_ad_width,c,a,b),a!==!0?(b.efwr=!1,b.gfwrnwer=a):b.efwr=!0)}};function $p(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&Ud(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function aq(a,b,c){for(const f of b)a:{b=a;var d=f,e=c;for(let g=0;g<b.g.length;g++){if(b.g[g].element.contains(d)){b.g[g].labels.add(e);break a}if(d.contains(b.g[g].element)){b.g[g].element=d;b.g[g].labels.add(e);break a}}b.g.push({element:d,labels:new Set([e])})}}class bq{constructor(){this.g=[]}getSlots(){return this.g}} 
function cq(a){const b=Kk(a),c=new bq;aq(c,b.vb,1);aq(c,b.wb,2);aq(c,b.Eb,3);aq(c,b.ac,4);aq(c,b.xb,5);aq(c,b.Ob,6);return c.getSlots().map(d=>{var e=new Cf;var f=[...d.labels];e=zc(e,1,f,Pb);d=d.element.getBoundingClientRect();f=new Bf;f=Sc(f,1,d.left+a.scrollX);f=Sc(f,2,d.top+a.scrollY);f=Sc(f,3,d.width);d=Sc(f,4,d.height);d=ad(d);e=E(e,2,d);return ad(e)}).sort((d,e)=>{d=C(d,Bf,2);d=I(d,2);e=C(e,Bf,2);e=I(e,2);return d-e})};function Cg(a,b,c=0){a.g.size>0||dq(a);c=Math.min(Math.max(0,c),9);const d=a.g.get(c);d?d.push(b):a.g.set(c,[b])}function eq(a,b,c,d){pe(b,c,d);ql(a,()=>qe(b,c,d))}function fq(a,b){a.A!==1&&(a.A=1,a.g.size>0&&gq(a,b))} 
function dq(a){a.u.document.visibilityState?eq(a,a.u.document,"visibilitychange",b=>{a.u.document.visibilityState==="hidden"&&fq(a,b);a.u.document.visibilityState==="visible"&&(a.A=0)}):"onpagehide"in a.u?(eq(a,a.u,"pagehide",b=>{fq(a,b)}),eq(a,a.u,"pageshow",()=>{a.A=0})):eq(a,a.u,"beforeunload",b=>{fq(a,b)})}function gq(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var hq=class extends pl{constructor(a){super();this.u=a;this.A=0;this.g=new Map}};async function iq(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function jq(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=me(a.u)}function kq(a){const b=a.g.wpc;return b!==null&&b!==""?b:a.g.wpc=Go(a.u)}function lq(a,b){var c=new Rf;var d=jq(a);c=Uc(c,1,d);d=kq(a);c=Xc(c,2,d);c=Uc(c,3,a.g.sd);return Uc(c,7,Math.round(b||a.u.performance.now()))}async function mq(a){q(await q(iq(a.u,()=>!(!jq(a)||!kq(a)))))}function nq(a){var b=N(oq);if(b.j){var c=b.A;a(c);b.g.psi=y(c)}} 
function pq(a){Cg(a.l,()=>{var b=lq(a);b=Jc(b,12,Sf,a.B);a.j&&!a.g.le.includes(3)&&(a.g.le.push(3),yg(a.i,b))},9)}function qq(a){const b=new Nf;Cg(a.l,()=>{E(b,2,a.A);Uc(b,3,a.g.tar);var c=a.u;var d=new Gf;var e=cq(c);d=Kc(d,1,e);e=ad(Ef(Df(new Ff,V(c)),Vi(c).clientHeight));d=E(d,2,e);c=ad(Ef(Df(new Ff,Vi(c).scrollWidth),Vi(c).scrollHeight));c=E(d,3,c);c=ad(c);E(b,4,c);c=a.i;d=lq(a);d=Jc(d,8,Sf,b);yg(c,d)},9)} 
async function rq(a){var b=N(oq);if(b.j&&!b.g.le.includes(1)){b.g.le.push(1);var c=b.u.performance.now();q(await q(mq(b)));var d=new Jf;a=Ac(d,5,Mb(a),!1);d=Ef(Df(new Ff,Vi(b.u).scrollWidth),Vi(b.u).scrollHeight);a=E(a,2,d);d=Ef(Df(new Ff,V(b.u)),Vi(b.u).clientHeight);a=E(a,1,d);for(var e=d=b.u;d&&d!=d.parent;)d=d.parent,Ud(d)&&(e=d);a=Xc(a,4,e.location.href);d=$p(b.u);d!==0&&(e=new If,d=Yc(e,1,d),E(a,3,d));d=b.i;c=lq(b,c);c=Jc(c,4,Sf,a);yg(d,c);pq(b);qq(b)}} 
async function sq(a,b,c){if(a.j&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.u.performance.now();q(await q(mq(a)));var e=a.i;a=lq(a,d);d=new Hf;b=Zc(d,1,b);c=zc(b,2,c,Rb);c=Jc(a,9,Sf,c);yg(e,c)}}async function tq(a,b){q(await q(mq(a)));var c=a.i;a=lq(a);a=Uc(a,3,1);b=Jc(a,10,Sf,b);yg(c,b)} 
var oq=class{constructor(a,b){this.u=ze()||window;this.l=b??new hq(this.u);this.i=a??new Eg(2,ng(),100,100,!0,this.l);this.g=hl(cl(),33,()=>{const c=U(hi);return{sd:c,ssp:c>0&&Zd()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get j(){return this.g.ssp}get A(){return tk(1178,()=>bd(Mf,hc(this.g.psi||[])))||new Mf}get B(){return tk(1227,()=>bd(Of,hc(this.g.cc||[])))||new Of}};function uq(){var a=window;return r.google_adtest==="on"||r.google_adbreak_test==="on"||a.location.host.endsWith("h5games.usercontent.goog")||a.location.host==="gamesnacks.com"?a.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(b=>Math.floor(Number(b))).filter(b=>!isNaN(b)&&b>0)||[]:[]};function vq(a,b){return a instanceof HTMLScriptElement&&b.test(a.src)?0:1}function wq(a){var b=O.document;if(b.currentScript)return vq(b.currentScript,a);for(const c of b.scripts)if(vq(c,a)===0)return 0;return 1};function xq(a,b){const c=T(Oi)?!!C(b,Xm,26)?.g():b.g();return{[3]:{[55]:()=>a===0,[23]:d=>Um(O,Number(d),b),[24]:d=>Ym(Number(d),c),[61]:()=>c,[63]:()=>c||K(b,8)===".google.ch"},[4]:{},[5]:{[6]:()=>K(b,15)}}};function yq(a=r){return a.ggeac||(a.ggeac={})};function zq(a,b=document){return!!b.featurePolicy?.features().includes(a)}function Aq(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function Bq(a,b=navigator){try{return!!b.protectedAudience?.queryFeatureSupport?.(a)}catch(c){return!1}};function Cq(a,b){try{const d=a.split(".");a=r;let e=0,f;for(;a!=null&&e<d.length;e++)f=a,a=a[d[e]],typeof a==="function"&&(a=f[d[e]]());var c=a;if(typeof c===b)return c}catch{}} 
var Dq={[3]:{[8]:a=>{try{return ja(a)!=null}catch{}},[9]:a=>{try{var b=ja(a)}catch{return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},[10]:()=>window===window.top,[6]:a=>Oa(N(ih).g(),Number(a)),[27]:a=>{a=Cq(a,"boolean");return a!==void 0?a:void 0},[60]:a=>{try{return!!r.document.querySelector(a)}catch{}},[80]:a=>{try{return!!r.matchMedia(a).matches}catch{}},[69]:a=>zq(a,r.document),[70]:a=>Aq(a,r.document),[79]:a=>Bq(a, 
r.navigator)},[4]:{[3]:()=>fe(),[6]:a=>{a=Cq(a,"number");return a!==void 0?a:void 0}},[5]:{[2]:()=>window.location.href,[3]:()=>{try{return window.top.location.hash}catch{return""}},[4]:a=>{a=Cq(a,"string");return a!==void 0?a:void 0},[12]:a=>{try{const b=Cq(a,"string");if(b!==void 0)return atob(b)}catch(b){}}}};var Eq=class extends M{getId(){return I(this,1)}};function Fq(a){return D(a,Eq,2,B())}var Gq=class extends M{};var Hq=class extends M{};var Iq=class extends M{g(){return Oc(this,2)}j(){return Oc(this,4)}l(){return H(this,3)}};var Jq=class extends M{};function Kq(a){return Lq({[0]:new Map,[1]:new Map,[2]:new Map},a)} 
function Lq(a,b){const c=new Map;for(const [f,g]of a[1].entries()){var d=f,e=g;const {rb:h,nb:k,ob:n}=e[e.length-1];c.set(d,h+k*n)}for(const f of b)for(const g of D(f,Gq,2,B()))if(Fq(g).length!==0){b=Tb(z(g.C,8))??0;!L(g,4)||L(g,13)||L(g,14)||(b=c.get(L(g,4))??0,d=(Tb(z(g.C,1))??0)*Fq(g).length,c.set(L(g,4),b+d));d=[];for(e=0;e<Fq(g).length;e++){const h={rb:b,nb:Tb(z(g.C,1))??0,ob:Fq(g).length,Nb:e,ua:L(f,1),Aa:g,W:Fq(g)[e]};d.push(h)}Mq(a[2],L(g,10),d)||Mq(a[1],L(g,4),d)||Mq(a[0],Fq(g)[0].getId(), 
d)}return a}function Mq(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a.get(b).push(...c);return!0};function Nq(a=Zd()){return b=>ae(`${b} + ${a}`)%1E3};const Oq=[12,13,20];function Pq(a,b){var c=N(Kg).P;const d=qf(C(b.Aa,jf,3),c);if(!d.success)return Ig(a.O,C(b.Aa,jf,3),b.ua,b.W.getId(),d),!1;if(!d.value)return!1;c=qf(C(b.W,jf,3),c);return c.success?c.value?!0:!1:(Ig(a.O,C(b.W,jf,3),b.ua,b.W.getId(),c),!1)}function Qq(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];a.includes(b)||a.push(b)} 
function Rq(a,b,c,d){const e=[];var f;if(f=b!==9)a.l[b]?f=!0:(a.l[b]=!0,f=!1);if(f)return Gg(a.O,b,c,e,[],4),e;f=Oq.includes(b);const g=[],h=[];for(const m of[0,1,2])for(const [p,v]of a.wa[m].entries()){var k=p,n=v;const t=new Xf;var l=n.filter(x=>x.ua===b&&a.i[x.W.getId()]&&Pq(a,x));if(l.length)for(const x of l)h.push(x.W);else if(!a.Ka&&(m===2?(l=d[1],$c(t,2,Yf,k)):l=d[0],k=l?.(String(k))??(m===2&&L(n[0].Aa,11)===1?void 0:d[0](String(k))),k!==void 0)){for(const x of n){if(x.ua!==b)continue;n=k- 
x.rb;l=x.nb;const J=x.ob,P=x.Nb;n<0||n>=l*J||n%J!==P||!Pq(a,x)||(n=L(x.Aa,13),n!==0&&n!==void 0&&(l=a.j[String(n)],l!==void 0&&l!==x.W.getId()?Hg(a.O,a.j[String(n)],x.W.getId(),n):a.j[String(n)]=x.W.getId()),h.push(x.W))}Fc(t,Yf)!==0&&(Tc(t,3,k),g.push(t))}}for(const m of h)d=m.getId(),e.push(d),Qq(a,d,f?4:c),Zg(D(m,uf,2,B()),f?ah():[c],a.O,d);Gg(a.O,b,c,e,g,1);return e}function Sq(a,b){b=b.map(c=>new Hq(c)).filter(c=>!Oq.includes(L(c,1)));a.wa=Lq(a.wa,b)} 
function Tq(a,b){Q(1,c=>{a.i[c]=!0},b);Q(2,(c,d,e)=>Rq(a,c,d,e),b);Q(3,c=>(a.g[c]||[]).concat(a.g[4]),b);Q(12,c=>void Sq(a,c),b);Q(16,(c,d)=>void Qq(a,c,d),b)}var Uq=class{constructor(a,b,c,{Ka:d=!1,Mc:e=[]}={}){this.wa=a;this.O=c;this.l={};this.Ka=d;this.g={[b]:[],[4]:[]};this.i={};this.j={};if(a=Ne()){a=a.split(",")||[];for(const f of a)(a=Number(f))&&(this.i[a]=!0)}for(const f of e)this.i[f]=!0}};function Vq(a,b){a.g=ch(14,b,()=>{})}class Wq{constructor(){this.g=()=>{}}}function Xq(a){N(Wq).g(a)};function Yq({Fb:a,P:b,config:c,zb:d=yq(),Ab:e=0,O:f=new Jg(vm(C(a,Iq,5)?.g())??0,vm(C(a,Iq,5)?.j())??0,C(a,Iq,5)?.l()??!1),wa:g=Kq(D(a,Hq,2,B(pb)))}){d.hasOwnProperty("init-done")?(ch(12,d,()=>{})(D(a,Hq,2,B()).map(h=>y(h))),ch(13,d,()=>{})(D(a,uf,1,B()).map(h=>y(h)),e),b&&ch(14,d,()=>{})(b),Zq(e,d)):(Tq(new Uq(g,e,f,c),d),dh(d),eh(d),fh(d),Zq(e,d),Zg(D(a,uf,1,B(pb)),[e],f,void 0,!0),Lg=Lg||!(!c||!c.Kb),Xq(Dq),b&&Xq(b))}function Zq(a,b=yq()){hh(N(ih),b,a);$q(b,a);Vq(N(Wq),b);N(ke).B()} 
function $q(a,b){const c=N(ke);c.j=(d,e)=>ch(5,a,()=>!1)(d,e,b);c.l=(d,e)=>ch(6,a,()=>0)(d,e,b);c.i=(d,e)=>ch(7,a,()=>"")(d,e,b);c.A=(d,e)=>ch(8,a,()=>[])(d,e,b);c.g=(d,e)=>ch(17,a,()=>[])(d,e,b);c.B=()=>{ch(15,a,()=>{})(b)}};function ar(a,b){b={[0]:Nq(me(b).toString())};b=N(ih).l(a,b);a=sq(N(oq),a,b);lh.za(1085,a)}function br(a,b,c){var d=X(a);if(d.plle)Zq(1,yq(a));else{d.plle=!0;d=C(b,Jq,12);var e=H(b,9);Yq({Fb:d,P:xq(c,b),config:{Ka:e&&!!a.google_disable_experiments,Kb:e},zb:yq(a),Ab:1});if(c=K(b,15))c=Number(c),N(ih).j(c);for(const f of tc(b,19,Sb,B()))N(ih).i(f);ar(12,a);ar(10,a);a=Vd(a)||a;sn(a.location,"google_mc_lab")&&N(ih).i(44738307)}};function cr(a){pk.A(b=>{b.shv=String(a);b.mjsv=Ko({Fa:ng(),Ma:a});const c=N(ih).g(),d=uq();b.eid=c.concat(d).join(",")})}function dr(a,b){a=b?.g()?.g()||K(a,2);cr(a)};var er={google_pause_ad_requests:!0,google_user_agent_client_hint:!0};var fr=class extends M{g(){return K(this,1)}j(){return L(this,2)}};var Io=class extends M{getName(){return K(this,1)}};var gr=class extends M{};var hr=class extends M{D(){return K(this,1)}g(){return C(this,fr,2)}j(){return H(this,3)}l(){return H(this,4)}A(){return C(this,Il,5)}B(){return C(this,Jl,6)}F(){return C(this,gr,7)}};function dn(a){return Rc(a,hr,27,ir)}var jr=class extends M{g(){return H(this,6)}},ir=[27,28];function kr(a){var b=pk;try{if(!tb(a))throw Error(String(a));if(a.length>0)return new jr(JSON.parse(a))}catch(c){b.L(838,c instanceof Error?c:Error(String(c)))}return new jr};function lr(a,b){if(H(b,22))return 7;if(H(b,16))return 6;const c=dn(b)?.g()?.g();b=dn(b)?.g()?.j()??0;a=c===a;switch(b){case 1:return a?9:8;case 2:return a?11:10;case 3:return a?13:12}return 1}function mr(a,b,c){b.google_loader_used!=="sd"&&(b.abgtt=lr(a,c))};function nr(a,b){var c=new Kn;try{const f=a.createElement("link");if(f.relList?.supports("compression-dictionary")&&Ga()){var d=f;if(b instanceof Gd)d.href=Id(b).toString(),d.rel="compression-dictionary";else{if(Md.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var e=Jd.test(b)?b:void 0;e!==void 0&&(d.href=e,d.rel="compression-dictionary")}a.head.appendChild(f)}}catch(f){c.la({methodName:1296,sa:f})}} 
function or(a){return Rd`https://googleads.g.doubleclick.net/pagead/managed/dict/${a}/adsbygoogle`};function pr(a){pe(window,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="sc-cnf"||a(c,b)})};function qr(a,b){return b==null?`&${a}=null`:`&${a}=${Math.floor(b)}`}function rr(a,b){return`&${a}=${b.toFixed(3)}`}function sr(){const a=new Set,b=Jk();try{if(!b)return a;const c=b.pubads();for(const d of c.getSlots())a.add(d.getSlotId().getDomId())}catch{}return a}function tr(a){a=a.id;return a!=null&&(sr().has(a)||a.startsWith("google_ads_iframe_")||a.startsWith("aswift"))} 
function ur(a,b,c){if(!a.sources)return!1;switch(vr(a)){case 2:const d=wr(a);if(d)return c.some(f=>xr(d,f));break;case 1:const e=yr(a);if(e)return b.some(f=>xr(e,f))}return!1}function vr(a){if(!a.sources)return 0;a=a.sources.filter(b=>b.previousRect&&b.currentRect);if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function yr(a){return zr(a,b=>b.currentRect)}function wr(a){return zr(a,b=>b.previousRect)} 
function zr(a,b){return a.sources.reduce((c,d)=>{d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function xr(a,b){const c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50} 
function Ar(){const a=Array.from(document.getElementsByTagName("iframe")).filter(tr),b=[...sr()].map(c=>document.getElementById(c)).filter(c=>c!==null);Br=window.scrollX;Cr=window.scrollY;return Dr=[...a,...b].map(c=>c.getBoundingClientRect())} 
function Er(){var a=new Fr;if(T(Li)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.tb.Cb&&b.push("event");for(const c of b)b={type:c,buffered:!0},c==="event"&&(b.durationThreshold=40),Gr(a).observe(b);Hr(a)}}} 
function Ir(a,b){const c=Br!==window.scrollX||Cr!==window.scrollY?[]:Dr,d=Ar();for(const e of b.getEntries())switch(b=e.entryType,b){case "layout-shift":Jr(a,e,c,d);break;case "largest-contentful-paint":b=e;a.Ta=Math.floor(b.renderTime||b.loadTime);a.Sa=b.size;break;case "first-input":b=e;a.Pa=Number((b.processingStart-b.startTime).toFixed(3));a.Qa=!0;a.g.some(f=>f.entries.some(g=>e.duration===g.duration&&e.startTime===g.startTime))||Kr(a,e);break;case "longtask":b=Math.max(0,e.duration-50);a.B+= 
b;a.H=Math.max(a.H,b);a.pa+=1;break;case "event":Kr(a,e);break;default:Gb(b,void 0)}}function Gr(a){a.O||(a.O=new PerformanceObserver(Pj(640,b=>{Ir(a,b)})));return a.O} 
function Hr(a){const b=Pj(641,()=>{var d=document;(d.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""]??0)===2&&Lr(a)}),c=Pj(641,()=>void Lr(a));document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.Oa=()=>{document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);Gr(a).disconnect()}} 
function Lr(a){if(!a.Wa){a.Wa=!0;Gr(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=rr("cls",a.D),b+=rr("mls",a.J),b+=qr("nls",a.ea),window.LayoutShiftAttribution&&(b+=rr("cas",a.A),b+=qr("nas",a.Va),b+=rr("was",a.ab)),b+=rr("wls",a.Ca),b+=rr("tls",a.Za));window.LargestContentfulPaint&&(b+=qr("lcp",a.Ta),b+=qr("lcps",a.Sa));window.PerformanceEventTiming&&a.Qa&&(b+=qr("fid",a.Pa));window.PerformanceLongTaskTiming&&(b+=qr("cbt",a.B), 
b+=qr("mbt",a.H),b+=qr("nlt",a.pa));let d=0;for(var c of document.getElementsByTagName("iframe"))tr(c)&&d++;b+=qr("nif",d);b+=qr("ifi",De(window));c=N(ih).g();b+=`&${"eid"}=${encodeURIComponent(c.join())}`;b+=`&${"top"}=${r===r.top?1:0}`;b+=a.Ya?`&${"qqid"}=${encodeURIComponent(a.Ya)}`:qr("pvsid",me(r));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.O?a.Ra:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=qr("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include", 
redirect:"follow",method:"get",mode:"no-cors"});a.Oa()}}function Jr(a,b,c,d){if(!b.hadRecentInput){a.D+=Number(b.value);Number(b.value)>a.J&&(a.J=Number(b.value));a.ea+=1;if(c=ur(b,c,d))a.A+=b.value,a.Va++;if(b.startTime-a.Ua>5E3||b.startTime-a.Xa>1E3)a.Ua=b.startTime,a.i=0,a.j=0;a.Xa=b.startTime;a.i+=b.value;c&&(a.j+=b.value);a.i>a.Ca&&(a.Ca=a.i,a.ab=a.j,a.Za=b.startTime+b.duration)}} 
function Kr(a,b){Mr(a,b);const c=a.g[a.g.length-1],d=a.F[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(d.entries.push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.F[b.id]=b,a.g.push(b)),a.g.sort((e,f)=>f.latency-e.latency),a.g.splice(10).forEach(e=>{delete a.F[e.id]})}function Mr(a,b){b.interactionId&&(a.da=Math.min(a.da,b.interactionId),a.l=Math.max(a.l,b.interactionId),a.Ra=a.l?(a.l-a.da)/7+1:0)} 
var Fr=class{constructor(){this.j=this.i=this.ea=this.J=this.D=0;this.Xa=this.Ua=Number.NEGATIVE_INFINITY;this.g=[];this.F={};this.Ra=0;this.da=Infinity;this.Pa=this.Sa=this.Ta=this.Va=this.ab=this.A=this.Za=this.Ca=this.l=0;this.Qa=!1;this.pa=this.H=this.B=0;this.O=null;this.Wa=!1;this.Oa=()=>{};const a=document.querySelector("[data-google-query-id]");this.Ya=a?a.getAttribute("data-google-query-id"):null;this.tb={Cb:!0}}},Br,Cr,Dr=[];let Nr=null;const Or=[],Pr=new Map;let Qr=-1;function Rr(a){return pj.test(a.className)&&a.dataset.adsbygoogleStatus!=="done"}function Sr(a,b,c){a.dataset.adsbygoogleStatus="done";Tr(a,b,c)} 
function Tr(a,b,c){var d=window,e=b.google_reactive_ads_config;e||Zp(a,b,d,c);bp(d,b);if(!Ur(a,b,d)){if(e){e=e.page_level_pubvars||{};if(X(O).page_contains_reactive_tag&&!X(O).allow_second_reactive_tag){if(e.pltais){Pm(!1);return}throw new W("Only one 'enable_page_level_ads' allowed per page.");}X(O).page_contains_reactive_tag=!0;Pm(e.google_pgb_reactive===7)}b.google_unique_id=Ce(d);$d(er,(f,g)=>{b[g]=b[g]||d[g]});b.google_loader_used!=="sd"&&(b.google_loader_used="aa");b.google_reactive_tag_first= 
(X(O).first_tag_on_page||0)===1;tk(164,()=>{gp(d,b,a,c)})}} 
function Ur(a,b,c){var d=b.google_reactive_ads_config,e=typeof a.className==="string"&&RegExp("(\\W|^)adsbygoogle-noablate(\\W|$)").test(a.className),f=Nm(c);if(f&&f.bb&&b.google_adtest!=="on"&&!e){e=Zi(a,c);const g=Vi(c).clientHeight;e=g===0?null:e/g;if(!f.Da||f.Da&&(e||0)>=f.Da)return a.className+=" adsbygoogle-ablated-ad-slot",c=c.google_sv_map=c.google_sv_map||{},d=la(a),b.google_element_uid=d,c[b.google_element_uid]=b,a.setAttribute("google_element_uid",String(d)),f.Yb==="slot"&&(de(a.getAttribute("width"))!== 
null&&a.setAttribute("width","0"),de(a.getAttribute("height"))!==null&&a.setAttribute("height","0"),a.style.width="0px",a.style.height="0px"),!0}if((f=Yd(a,c))&&f.display==="none"&&!(b.google_adtest==="on"||b.google_reactive_ad_format>0||d))return c.document.createComment&&a.appendChild(c.document.createComment("No ad requested because of display:none on the adsbygoogle tag")),!0;a=b.google_pgb_reactive==null||b.google_pgb_reactive===3;return b.google_reactive_ad_format!==1&&b.google_reactive_ad_format!== 
8||!a?!1:(r.console&&r.console.warn("Adsbygoogle tag with data-reactive-ad-format="+String(b.google_reactive_ad_format)+" is deprecated. Check out page-level ads at https://www.google.com/adsense"),!0)}function Vr(a){var b=document.getElementsByTagName("INS");for(let d=0,e=b[d];d<b.length;e=b[++d]){var c=e;if(Rr(c)&&c.dataset.adsbygoogleStatus!=="reserved"&&(!a||e.id===a))return e}return null} 
function Wr(a,b,c){if(a&&"shift"in a){nq(e=>{var f=Lf(e);Pc(f,2)||(e=Lf(e),Vc(e,2))});for(var d=20;a.length>0&&d>0;){try{Xr(a.shift(),b,c)}catch(e){setTimeout(()=>{throw e;})}--d}}}function Yr(){const a=Xd("INS");a.className="adsbygoogle";a.className+=" adsbygoogle-noablate";ge(a);return a} 
function Zr(a,b){const c={},d=cn(a.google_ad_client,b);$d(Ui,(g,h)=>{a.enable_page_level_ads===!1?c[h]=!1:a.hasOwnProperty(h)?c[h]=a[h]:d.includes(g)&&(c[h]=!1)});ka(a.enable_page_level_ads)&&(c.page_level_pubvars=a.enable_page_level_ads);const e=Yr();ne.body.appendChild(e);const f={google_reactive_ads_config:c,google_ad_client:a.google_ad_client};f.google_pause_ad_requests=!!X(O).pause_ad_requests;mr($r(a)||Go(O),f,b);Sr(e,f,b);nq(g=>{var h=Lf(g);Pc(h,6)||(g=Lf(g),Vc(g,6))})} 
function as(a,b){Cn(r).wasPlaTagProcessed=!0;const c=()=>{Zr(a,b)},d=r.document;if(d.body||d.readyState==="complete"||d.readyState==="interactive")Zr(a,b);else{const e=wd(uk(191,c));pe(d,"DOMContentLoaded",e);r.MutationObserver!=null&&(new r.MutationObserver((f,g)=>{d.body&&(e(),g.disconnect())})).observe(d,{childList:!0,subtree:!0})}} 
function Xr(a,b,c){const d={};tk(165,()=>{bs(a,d,b,c)},e=>{e.client=e.client||d.google_ad_client||a.google_ad_client;e.slotname=e.slotname||d.google_ad_slot;e.tag_origin=e.tag_origin||d.google_tag_origin})}function cs(a){delete a.google_checked_head;$d(a,(b,c)=>{oj[c]||(delete a[c],b=c.replace("google","data").replace(/_/g,"-"),r.console.warn(`AdSense head tag doesn't support ${b} attribute.`))})} 
function ds(a,b){var c=O.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||O.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||O.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||O.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])');if(c){c.setAttribute("data-checked-head", 
"true");var d=X(window);if(d.head_tag_slot_vars)es(c);else{nq(g=>{g=Lf(g);Ac(g,7,Mb(!0),!1)});var e={};Xp(c,e);cs(e);e.google_ad_intent_query&&(e.google_responsive_auto_format=17,T(Ai)&&(e.google_reactive_ad_format=42));var f=Cd(e);d.head_tag_slot_vars=f;c={google_ad_client:e.google_ad_client,enable_page_level_ads:e};e.google_ad_intent_query&&(c.enable_ad_intent_display_ads=!0);e.google_overlays==="bottom"&&(c.overlays={bottom:!0});delete e.google_overlays;O.adsbygoogle||(O.adsbygoogle=[]);d=O.adsbygoogle; 
d.loaded?d.push(c):d.splice&&d.splice(0,0,c);b=dn(b)?.l();e.google_adbreak_test||b?fs(f,a):pr(()=>{fs(f,a)})}}}function es(a){const b=X(window).head_tag_slot_vars,c=a.getAttribute("src")||"";if((a=Qd(c,"client")||a.getAttribute("data-ad-client")||"")&&a!==b.google_ad_client)throw new W("Warning: Do not add multiple property codes with AdSense tag to avoid seeing unexpected behavior. These codes were found on the page "+a+", "+b.google_ad_client);} 
function gs(a){if(typeof a==="object"&&a!=null){if(typeof a.type==="string")return 2;if(typeof a.sound==="string"||typeof a.preloadAdBreaks==="string"||typeof a.h5AdsConfig==="object")return 3}return 0} 
function bs(a,b,c,d){if(a==null)throw new W("push() called with no parameters.");nq(f=>{var g=Lf(f);Pc(g,3)||(f=Lf(f),Vc(f,3))});var e=gs(a);if(e!==0)if(d=Qm(),d.first_slotcar_request_processing_time||(d.first_slotcar_request_processing_time=Date.now(),d.adsbygoogle_execution_start_time=nh),Nr==null)hs(a),Or.push(a);else if(e===3){const f=Nr;tk(787,()=>{f.handleAdConfig(a)})}else vk(730,Nr.handleAdBreak(a));else{nh=(new Date).getTime();cp(c,d,$r(a));is();a:{if(!a.enable_ad_intent_display_ads&&a.enable_page_level_ads!= 
void 0){if(typeof a.google_ad_client==="string"){e=!0;break a}throw new W("'google_ad_client' is missing from the tag config.");}e=!1}if(e)nq(f=>{var g=Lf(f);Pc(g,4)||(f=Lf(f),Vc(f,4))}),js(a,d);else if((e=a.params)&&$d(e,(f,g)=>{b[g]=f}),b.google_ad_output==="js")console.warn("Ads with google_ad_output='js' have been deprecated and no longer work. Contact your AdSense account manager or switch to standard AdSense ads.");else{mr($r(a)||Go(O),b,d);e=ks(b,a);Xp(e,b);c=X(r).head_tag_slot_vars||{};$d(c, 
(f,g)=>{b.hasOwnProperty(g)||(b[g]=f)});if(e.hasAttribute("data-require-head")&&!X(r).head_tag_slot_vars)throw new W("AdSense head tag is missing. AdSense body tags don't work without the head tag. You can copy the head tag from your account on https://adsense.com.");if(!b.google_ad_client)throw new W("Ad client is missing from the slot.");if(c=(X(O).first_tag_on_page||0)===0&&Fn(b))nq(f=>{var g=Lf(f);Pc(g,5)||(f=Lf(f),Vc(f,5))}),ls(c);(X(O).first_tag_on_page||0)===0&&(X(O).first_tag_on_page=2);b.google_pause_ad_requests= 
!!X(O).pause_ad_requests;Sr(e,b,d)}}}function $r(a){return a.google_ad_client?a.google_ad_client:(a=a.params)&&a.google_ad_client?a.google_ad_client:""}function is(){if(T(ui)){const a=Nm(O);a&&a.bb||Om(O)}}function ls(a){oe(()=>{Cn(r).wasPlaTagProcessed||r.adsbygoogle&&r.adsbygoogle.push(a)})}function js(a,b){(X(O).first_tag_on_page||0)===0&&(X(O).first_tag_on_page=1);if(a.tag_partner){var c=a.tag_partner;const d=X(r);d.tag_partners=d.tag_partners||[];d.tag_partners.push(c)}Gn(a,b);as(a,b)} 
function ks(a,b){if(a.google_ad_format==="rewarded"){if(a.google_ad_slot==null)throw new W("Rewarded format does not have valid ad slot");if(a.google_ad_loaded_callback==null)throw new W("Rewarded format does not have ad loaded callback");a.google_reactive_ad_format=11;a.google_wrap_fullscreen_ad=!0;a.google_video_play_muted=!1;a.google_acr=a.google_ad_loaded_callback;delete a.google_ad_loaded_callback;delete a.google_ad_format}var c=!!a.google_wrap_fullscreen_ad;if(c)b=Yr(),b.dataset.adsbygoogleStatus= 
"reserved",ne.documentElement.appendChild(b);else if(b=b.element){if(!Rr(b)&&(b.id?b=Vr(b.id):b=null,!b))throw new W("'element' has already been filled.");if(!("innerHTML"in b))throw new W("'element' is not a good DOM element.");}else if(b=Vr(),!b)throw new W("All 'ins' elements in the DOM with class=adsbygoogle already have ads in them.");if(c){c=O;try{const e=(c||window).document,f=e.compatMode=="CSS1Compat"?e.documentElement:e.body;var d=(new xe(f.clientWidth,f.clientHeight)).round()}catch(e){d= 
new xe(-12245933,-12245933)}a.google_ad_height=d.height;a.google_ad_width=d.width;a.fsapi=!0}return b}function ms(a){cl().S[fl(26)]=!!Number(a)} 
function ns(a){Number(a)?X(O).pause_ad_requests=!0:(X(O).pause_ad_requests=!1,a=()=>{if(!X(O).pause_ad_requests){var b={};let c;typeof window.CustomEvent==="function"?c=new CustomEvent("adsbygoogle-pub-unpause-ad-requests-event",b):(c=document.createEvent("CustomEvent"),c.initCustomEvent("adsbygoogle-pub-unpause-ad-requests-event",!!b.bubbles,!!b.cancelable,b.detail));O.dispatchEvent(c)}},r.setTimeout(a,0),r.setTimeout(a,1E3))}function os(a){typeof a==="function"&&window.setTimeout(a,0)} 
function fs(a,b){const c={...xn()},d=U(Ki);[0,1].includes(d)&&(c.osttc=`${d}`);b=Bn(Sd(b.Xb,new Map(Object.entries(c)))).then(e=>{Nr==null&&(e.init(a),Nr=e,ps(e))});vk(723,b);b.finally(()=>{Or.length=0;wk("slotcar",{event:"api_ld",time:Date.now()-nh,time_pr:Date.now()-Qr});tq(N(oq),Pf(23))})} 
function ps(a){for(const [c,d]of Pr){var b=c;const e=d;e!==-1&&(r.clearTimeout(e),Pr.delete(b))}for(b=0;b<Or.length;b++){if(Pr.has(b))continue;const c=Or[b],d=gs(c);tk(723,()=>{d===3?a.handleAdConfig(c):d===2&&vk(730,a.handleAdBreakBeforeReady(c))})}} 
function hs(a){var b=Or.length;if(gs(a)===2&&a.type==="preroll"&&a.adBreakDone!=null){var c=a.adBreakDone;Qr===-1&&(Qr=Date.now());var d=r.setTimeout(()=>{try{c({breakType:"preroll",breakName:a.name,breakFormat:"preroll",breakStatus:"timeout"}),Pr.set(b,-1),wk("slotcar",{event:"pr_to",source:"adsbygoogle"}),tq(N(oq),Pf(22))}catch(e){console.error("[Ad Placement API] adBreakDone callback threw an error:",e instanceof Error?e:Error(String(e)))}},U(Mi)*1E3);Pr.set(b,d)}};(function(a,b,c,d=()=>{}){pk.F(yk);tk(166,()=>{const e=new Eg(2,a);try{$a(l=>{hk(e,1191,l)})}catch(l){}const f=kr(b);var g=wn(f);dr(f,g);d();we(16,[1,y(f)]);var h=ze(ye(O))||O,k=Ko({Fa:a,Ma:K(f,2)});const n=c(k,f);k=O.document.currentScript===null?1:wq(n.Zb);br(h,f,k);T(Hi)&&K(f,29)&&nr(h.document,or(K(f,29)));nq(l=>{var m=I(l,1)+1;Tc(l,1,m);O.top===O&&(m=I(l,2)+1,Tc(l,2,m));m=Lf(l);Pc(m,1)||(l=Lf(l),Vc(l,1))});vk(1086,rq(k===0));if(!Fa()||va(Ia(),11)>=0){rk(T(Pi));jp();xm(Gc(f,Xm,26));try{Er()}catch{}ip(); 
ds(n,f);h=window;k=h.adsbygoogle;if(!k||!k.loaded){g={push:l=>{Xr(l,n,f)},loaded:!0,pageState:JSON.stringify(y(g))};try{Object.defineProperty(g,"requestNonPersonalizedAds",{set:ms}),Object.defineProperty(g,"pauseAdRequests",{set:ns}),Object.defineProperty(g,"onload",{set:os})}catch{}if(k)for(const l of["requestNonPersonalizedAds","pauseAdRequests"])k[l]!==void 0&&(g[l]=k[l]);Wr(k,n,f);h.adsbygoogle=g;k&&(g.onload=k.onload);T(gi)&&Fl({Ea:Fo(f),u:window,X:f?.g()})}}})})(ng(),typeof sttc==="undefined"? 
void 0:sttc,function(a,b){b=I(b,1)>2012?`_fy${I(b,1)}`:"";Rd`data:text/javascript,//show_ads_impl_preview.js`;return{Xb:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${b}.js`,Vb:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl${b}.js`,Ub:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl_with_ama${b}.js`,Zb:/^(?:https?:)?\/\/(?:pagead2\.googlesyndication\.com|securepubads\.g\.doubleclick\.net)\/pagead\/(?:js\/)?(?:show_ads|adsbygoogle(_direct)?)\.js(?:[?#].*)?$/}}); 
}).call(this,"[2021,\"r20250505\",\"r20190131\",null,null,null,null,null,null,null,null,[[[698926295,null,null,[1]],[null,619278254,null,[null,10]],[676894296,null,null,[1]],[682658313,null,null,[1]],[null,1130,null,[null,100]],[45694447,null,null,[]],[null,1340,null,[null,0.2]],[null,1338,null,[null,0.3]],[null,1336,null,[null,1]],[null,1339,null,[null,0.3]],[null,1032,null,[null,200],[[[12,null,null,null,4,null,\"Android\",[\"navigator.userAgent\"]],[null,500]]]],[null,728201648,null,[null,100]],[null,1224,null,[null,0.01]],[null,1346,null,[null,6]],[null,1347,null,[null,3]],[null,1343,null,[null,300]],[1384,null,null,[]],[null,1263,null,[null,-1]],[null,1323,null,[null,-1]],[null,1265,null,[null,-1]],[null,1264,null,[null,-1]],[1267,null,null,[1]],[null,66,null,[null,-1]],[null,65,null,[null,-1]],[1241,null,null,[1]],[1300,null,null,[1]],[null,null,null,[null,null,null,[\"en\",\"de\",\"fr\",\"es\",\"ja\"]],null,1273],[null,null,null,[null,null,null,[\"44786015\",\"44786016\"]],null,1261],[1318,null,null,[1]],[1372,null,null,[1]],[45682913,null,null,[1]],[622128248,null,null,[]],[null,null,null,[null,null,null,[\"29_18\",\"30_19\"]],null,null,null,635821288],[null,null,716722045,[null,null,\"600px\"]],[636570127,null,null,[1]],[null,626062006,null,[null,670]],[null,666400580,null,[null,22]],[null,null,null,[null,null,null,[\"\",\"ar\",\"bn\",\"en\",\"es\",\"fr\",\"hi\",\"id\",\"ja\",\"ko\",\"mr\",\"pt\",\"ru\",\"sr\",\"te\",\"th\",\"tr\",\"uk\",\"vi\",\"zh\"]],null,712458671],[748685380,null,null,[1]],[748689018,null,null,[1]],[null,null,null,[],null,null,null,683929765],[742688665,null,null,[1]],[45683445,null,null,[1]],[655991266,null,null,[1]],[750577535,null,null,[]],[750973575,null,null,[1]],[null,717888910,null,[null,0.7]],[null,643258048,null,[null,0.15]],[null,643258049,null,[null,0.16]],[null,618163195,null,[null,15000]],[null,624950166,null,[null,15000]],[null,623405755,null,[null,300]],[null,508040914,null,[null,500]],[null,547455356,null,[null,49]],[null,717888911,null,[null,0.7]],[null,717888912,null,[null,0.7]],[null,727864505,null,[null,3]],[null,652486359,null,[null,3]],[null,748662193,null,[null,8]],[null,688905693,null,[null,2]],[null,650548030,null,[null,2]],[null,650548032,null,[null,300]],[null,650548031,null,[null,1]],[null,655966487,null,[null,300]],[null,655966486,null,[null,250]],[null,687270738,null,[null,500]],[null,469675170,null,[null,60000]],[675298507,null,null,[]],[711741274,null,null,[]],[null,684147713,null,[null,-1]],[null,684147711,null,[null,-1]],[null,684147712,null,[null,-1]],[45675667,null,null,[1]],[570863962,null,null,[1]],[null,null,570879859,[null,null,\"control_1\\\\.\\\\d\"]],[null,570863961,null,[null,50]],[570879858,null,null,[1]],[10024,null,null,[1]],[10020,null,null,[1]],[10018,null,null,[1]],[null,1085,null,[null,5]],[null,63,null,[null,30]],[null,1080,null,[null,5]],[null,10019,null,[null,5]],[null,1027,null,[null,10]],[null,57,null,[null,120]],[1134,null,null,[1]],[null,1079,null,[null,5]],[null,1050,null,[null,30]],[null,58,null,[null,120]],[null,10021,null,[null,1.5]],[751557128,null,null,[]],[715572365,null,null,[1]],[715572366,null,null,[1]],[555237685,null,null,[1]],[45460956,null,null,[]],[45414947,null,null,[1]],[null,472785970,null,[null,500]],[null,732217386,null,[null,10000]],[null,732217387,null,[null,500]],[null,733329086,null,[null,30000]],[null,629808663,null,[null,100]],[null,736623795,null,[null,250]],[null,745376892,null,[null,1]],[null,745376893,null,[null,2]],[null,550718588,null,[null,250]],[null,624290870,null,[null,50]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[10,[[50,[[31067422],[31067423,[[null,1032,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31083552],[44776368]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31084127],[31084128]]],[1,[[31089421],[31089422,[[676460084,null,null,[1]]]]],null,139,null,null,null,998,null,null,null,null,null,8],[1,[[31089423],[31089424]],[4,null,61],139,null,null,null,998,null,null,null,null,null,8],[1,[[31089628],[31089629,[[710737579,null,null,[1]]]]]],[50,[[31092050,[[null,null,null,[null,null,null,[\"31092050\",\"31092052\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"31092052\"]],null,null,null,630330362]]],[31092051,[[655991266,null,null,[]],[null,null,null,[null,null,null,[\"31092051\",\"31092053\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"31092053\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[31092192],[31092193,[[10025,null,null,[1]]]]]],[1,[[31092194],[31092195,[[751557128,null,null,[1]]]]]],[50,[[31092196],[31092197,[[750586557,null,null,[1]]]]]],[1,[[31092200],[31092201,[[45689742,null,null,[1]]]]]],[1000,[[31092247,[[null,null,14,[null,null,\"31092247\"]]],[6,null,null,null,6,null,\"31092247\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092248,[[null,null,14,[null,null,\"31092248\"]]],[6,null,null,null,6,null,\"31092248\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092280,[[null,null,14,[null,null,\"31092280\"]]],[6,null,null,null,6,null,\"31092280\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092281,[[null,null,14,[null,null,\"31092281\"]]],[6,null,null,null,6,null,\"31092281\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092319,[[null,null,14,[null,null,\"31092319\"]]],[6,null,null,null,6,null,\"31092319\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31092320,[[null,null,14,[null,null,\"31092320\"]]],[6,null,null,null,6,null,\"31092320\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1,[[42531513],[42531514,[[316,null,null,[1]]]]]],[1,[[42531644],[42531645,[[368,null,null,[1]]]],[42531646,[[369,null,null,[1]],[368,null,null,[1]]]]]],[50,[[42531705],[42531706]]],[1,[[42532242],[42532243,[[1256,null,null,[1]],[290,null,null,[1]]]]]],[50,[[42532523],[42532524,[[1300,null,null,[]]]]]],[null,[[42532525],[42532526]]],[100,[[42533293],[42533294,[[1383,null,null,[1]],[null,54,null,[null,100]],[null,66,null,[null,10]],[null,65,null,[null,1000]]]]],null,145],[1,[[44801778],[44801779,[[506914611,null,null,[1]]]]],[4,null,55],143],[50,[[95331832],[95331833,[[1342,null,null,[1]]]]]],[10,[[95332584],[95332585,[[null,1343,null,[null,600]]]],[95332586,[[null,1343,null,[null,900]]]],[95332587,[[null,1343,null,[null,1200]]]]]],[10,[[95332923],[95332924,[[null,1338,null,[null,0.8]]]],[95332925,[[null,1339,null,[null,0.8]]]],[95332926,[[null,1340,null,[null,0.8]]]],[95332927,[[null,1340,null,[null,0.8]],[null,1338,null,[null,0.8]],[null,1339,null,[null,0.8]]]]]],[10,[[95333409],[95333410,[[null,1346,null,[null,-1]],[null,1347,null,[null,-1]]]],[95333411,[[null,1346,null,[null,3]],[null,1347,null,[null,1]]]],[95333412,[[null,1346,null,[null,8]],[null,1347,null,[null,5]]]]]],[360,[[95334516,[[null,null,null,[null,null,null,[\"95334518\"]],null,null,null,630330362]]],[95334517,[[626390500,null,null,[1]],[null,null,null,[null,null,null,[\"95334519\"]],null,null,null,630330362]]]],[2,[[4,null,55],[12,null,null,null,2,null,\"buzzfun\\\\.me\/|diggfun\\\\.co\/|indiaimagine\\\\.com\/\"]]],143],[50,[[95344787,[[null,null,null,[null,null,null,[\"95344792\"]],null,null,null,630330362]]],[95344788,[[566279275,null,null,[1]],[622128248,null,null,[1]],[null,null,null,[null,null,null,[\"95344793\"]],null,null,null,630330362]]],[95344789,[[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344794\"]],null,null,null,630330362]]],[95344790,[[566279275,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344795\"]],null,null,null,630330362]]],[95344791,[[566279275,null,null,[1]],[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344796\"]],null,null,null,630330362]]]],[4,null,55],143],[1,[[95345037],[95345038,[[1377,null,null,[1]]]]],[4,null,55]],[10,[[95352051,[[null,null,null,[null,null,null,[\"95352054\"]],null,null,null,630330362]]],[95352052,[[null,643258048,null,[]],[null,null,null,[null,null,null,[\"95352055\"]],null,null,null,630330362]]],[95352053,[[null,643258048,null,[]],[null,643258049,null,[]],[null,null,null,[null,null,null,[\"95352056\"]],null,null,null,630330362]]]],[4,null,55],144],[100,[[95354562],[95354563,[[1382,null,null,[1]]]]],[4,null,55]],[100,[[95354564],[95354565]],[4,null,55]],[null,[[95358213,[[null,null,null,[null,null,null,[\"95358215\"]],null,null,null,630330362]]],[95358214,[[747408261,null,null,[1]],[null,null,null,[null,null,null,[\"95358216\"]],null,null,null,630330362]]]],[4,null,55]],[10,[[95358621],[95358622,[[1384,null,null,[1]]]]]],[125,[[95359114,[[null,null,null,[null,null,null,[\"95359114\"]],null,null,null,630330362]]],[95359115,[[null,508040914,null,[null,500]],[null,717888912,null,[null,0.65]],[null,650548030,null,[null,2]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359115\"]],null,null,null,630330362]]],[95359116,[[null,508040914,null,[null,606]],[null,717888912,null,[null,0.5865572741861419]],[null,650548030,null,[null,3]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359116\"]],null,null,null,630330362]]],[95359117,[[null,508040914,null,[null,388]],[null,717888912,null,[null,0.717174870428953]],[null,650548030,null,[null,2]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359117\"]],null,null,null,630330362]]],[95359118,[[null,508040914,null,[null,383]],[null,717888912,null,[null,0.57923590775794542]],[null,650548030,null,[null,1]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359118\"]],null,null,null,630330362]]],[95359119,[[null,508040914,null,[null,512]],[null,717888912,null,[null,0.643067974618898]],[null,650548030,null,[null,1]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359119\"]],null,null,null,630330362]]],[95359120,[[null,508040914,null,[null,565]],[null,717888912,null,[null,0.58827036950502865]],[null,650548030,null,[null,2]],[null,650548031,null,[null,1]],[null,null,null,[null,null,null,[\"95359120\"]],null,null,null,630330362]]],[95359121,[[null,508040914,null,[null,444]],[null,717888912,null,[null,0.63648377059048022]],[null,650548030,null,[null,2]],[null,650548031,null,[null,2]],[null,null,null,[null,null,null,[\"95359121\"]],null,null,null,630330362]]]],[4,null,55],146],[500,[[95359239,[[null,null,null,[null,null,null,[\"95359239\",\"95359241\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95359241\"]],null,null,null,630330362]]],[95359240,[[null,null,716722045,[null,null,\"max(600px, calc(\\u003cDH\\u003e - 102px))\"]],[741481545,null,null,[1]],[null,null,null,[null,null,null,[\"95359240\",\"95359242\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95359242\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[95359265],[95359266,[[45650867,null,null,[1]]]]],null,130,null,null,null,null,null,null,null,null,null,7]]],[17,[[10,[[31084487],[31084488]],null,null,null,null,32,null,null,142,1],[10,[[31089209],[31089210]],null,null,null,null,39,null,null,189,1],[10,[[31090956],[31090957,[[733329085,null,null,[1]]]],[31092112,[[733329085,null,null,[1]],[749213612,null,null,[1]]]]],null,null,null,null,null,500,null,198,1],[10,[[31091205],[31091206,[[732217385,null,null,[1]]]],[31091638,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]]]],[31091871,[[732217385,null,null,[1]],[745631622,null,null,[1]]]],[31091872,[[732217385,null,null,[1]],[null,732217386,null,[null,5000]],[745631622,null,null,[1]]]]],null,null,null,null,null,700,null,198,1],[10,[[31091243],[31091244,[[736623794,null,null,[1]]]],[31091873,[[736623794,null,null,[1]],[null,745376892,null,[null,2]],[null,745376893,null,[null,4]]]]],null,null,null,null,null,800,null,198,1],[1,[[31092293],[31092294,[[752401956,null,null,[1]]]],[31092295,[[752401956,null,null,[1]],[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,300,null,200,1],[50,[[95356797,[[null,652486359,null,[null,9]],[null,null,null,[null,null,null,[\"95356799\"]],null,null,null,630330362]]],[95356798,[[null,652486359,null,[null,11]],[null,null,null,[null,null,null,[\"95356800\"]],null,null,null,630330362]]],[95356807,[[null,null,null,[null,null,null,[\"95356810\"]],null,null,null,630330362]]],[95356808,[[null,652486359,null,[null,5]],[null,null,null,[null,null,null,[\"95356811\"]],null,null,null,630330362]]],[95356809,[[null,652486359,null,[null,7]],[null,null,null,[null,null,null,[\"95356812\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,204,1],[50,[[95359271],[95359272,[[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,200,null,200,1],[100,[[95359475],[95359476,[[null,1336,null,[null,1.2]]]]],null,null,null,null,null,null,null,211,1]]],[11,[[50,[[31092107],[31092108]],null,122,null,null,null,null,null,null,null,null,null,4]]]],null,null,[null,1000,1,1000]],null,null,\"31092248\",null,\"fauxid.com\",1126835814,[95358863,95358865],null,null,null,null,null,null,[0,0,1],[null,[\"ca-pub-0583047843012866\",1],1,null,[[[[null,0,null,null,null,null,\"DIV.main-content\\u003eDIV.core-msg.spacer.font-red\"],1,[\"32px\",\"32px\",1],[2],null,null,null,1],[[null,0,null,null,null,null,\"DIV.main-content\"],4,[\"10px\",\"128px\",1],[2],null,null,null,1]],[[null,[1,3,2],null,\"6246676918\",null,null,[0,2],null,null,[0.5,null,1]]]],[null,null,[1,2,7]]],null,\"m202505060101\"]");
