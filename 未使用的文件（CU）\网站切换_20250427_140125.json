{"Row": 3, "Col": 2, "ActionType": 24, "Title": "网站切换", "Description": "一个自定义窗口的例子", "Icon": "https://files.getquicker.net/_icons/69B58741ACCA8A951C59510CDC57084B22A38C3A.png", "Path": null, "DelayMs": 0, "Data": "{\"LimitSingleInstance\":true,\"SummaryExpression\":\"\",\"SubPrograms\":[{\"Id\":\"45ca9f9d-7edb-44f3-a4f6-f04705fecc2e\",\"Icon\":null,\"Name\":\"Add\",\"Description\":\"\",\"SummaryExpression\":\"\",\"CreateTimeUtc\":\"2021-05-28T05:32:53.35207Z\",\"LastEditTimeUtc\":\"2021-05-28T05:33:44.7849062Z\",\"IsLocalEdited\":true,\"Variables\":[{\"Key\":\"number1\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":true,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"number2\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":true,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"total\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":true,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null}],\"Steps\":[{\"StepRunnerKey\":\"sys:assign\",\"InputParams\":{\"input\":{\"VarKey\":null,\"Value\":\"$= {number1} + {number2}\"},\"stopIfFail\":{\"VarKey\":null,\"Value\":\"1\"}},\"OutputParams\":{\"isSuccess\":null,\"output\":\"total\"},\"IfSteps\":null,\"ElseSteps\":null,\"Note\":\"\",\"Disabled\":false,\"Collapsed\":false,\"DelayMs\":0}],\"SubPrograms\":null,\"TemplateId\":null,\"TemplateRevision\":0,\"UseServerVersion\":null,\"IsProtected\":null,\"SharedId\":null,\"ShareTimeUtc\":null},{\"Id\":\"d77d9836-86b1-4e29-a157-920fe0681e50\",\"Icon\":null,\"Name\":\"Multiply\",\"Description\":\"\",\"SummaryExpression\":\"\",\"CreateTimeUtc\":\"2021-05-28T05:55:39.3030815Z\",\"LastEditTimeUtc\":\"2021-05-28T05:55:48.9320734Z\",\"IsLocalEdited\":true,\"Variables\":[{\"Key\":\"number1\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":true,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"number2\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":true,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"total\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":true,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null}],\"Steps\":[{\"StepRunnerKey\":\"sys:assign\",\"InputParams\":{\"input\":{\"VarKey\":null,\"Value\":\"$= {number1} * {number2}\"},\"stopIfFail\":{\"VarKey\":null,\"Value\":\"1\"}},\"OutputParams\":{\"isSuccess\":null,\"output\":\"total\"},\"IfSteps\":null,\"ElseSteps\":null,\"Note\":\"\",\"Disabled\":false,\"Collapsed\":false,\"DelayMs\":0}],\"SubPrograms\":null,\"TemplateId\":null,\"TemplateRevision\":0,\"UseServerVersion\":null,\"IsProtected\":null,\"SharedId\":null,\"ShareTimeUtc\":null}],\"Variables\":[{\"Key\":\"text\",\"Type\":0,\"Desc\":\"默认的文本变量\",\"DefaultValue\":\"\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":false,\"ParamName\":null,\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"number\",\"Type\":1,\"Desc\":\"\",\"DefaultValue\":\"100\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"windowId\",\"Type\":0,\"Desc\":\"\",\"DefaultValue\":\"my_caps_indicator\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null},{\"Key\":\"url\",\"Type\":0,\"Desc\":\"\",\"DefaultValue\":\"https://127.0.0.1:8000\",\"SaveState\":false,\"IsInput\":false,\"IsOutput\":false,\"ParamName\":\"\",\"InputParamInfo\":null,\"OutputParamInfo\":null,\"TableDef\":null,\"CustomType\":null,\"Group\":null}],\"Steps\":[{\"StepRunnerKey\":\"sys:customwindow\",\"InputParams\":{\"type\":{\"VarKey\":null,\"Value\":\"Show\"},\"windowMarkup\":{\"VarKey\":null,\"Value\":\"<Window\\r\\n    xmlns=\\\"http://schemas.microsoft.com/winfx/2006/xaml/presentation\\\"\\r\\n    xmlns:x=\\\"http://schemas.microsoft.com/winfx/2006/xaml\\\"\\r\\n    xmlns:d=\\\"http://schemas.microsoft.com/expression/blend/2008\\\"\\r\\n    xmlns:local=\\\"clr-namespace:QuickerSampleProject\\\"\\r\\n    xmlns:mc=\\\"http://schemas.openxmlformats.org/markup-compatibility/2006\\\"\\r\\n    xmlns:qk=\\\"https://ping0.cc\\\"\\r\\n    Title=\\\"WebView2 网站切换器\\\"\\r\\n    Width=\\\"800\\\"\\r\\n    Height=\\\"600\\\"\\r\\n    WindowStartupLocation=\\\"CenterScreen\\\"\\r\\n    ShowActivated=\\\"True\\\"\\r\\n    ShowInTaskbar=\\\"True\\\"\\r\\n    mc:Ignorable=\\\"d\\\"\\r\\n    xmlns:wv2=\\\"clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf\\\">\\r\\n\\r\\n    <Grid Margin=\\\"10\\\">\\r\\n        <Grid.RowDefinitions>\\r\\n            <RowDefinition Height=\\\"Auto\\\" />  <!-- 用于按钮布局 -->\\r\\n            <RowDefinition Height=\\\"*\\\" />     <!-- 用于 WebView2 占据剩余空间 -->\\r\\n        </Grid.RowDefinitions>\\r\\n\\r\\n        <StackPanel Orientation=\\\"Horizontal\\\" Grid.Row=\\\"0\\\" Margin=\\\"5\\\">\\r\\n            <Button Name=\\\"buttonSite1\\\" Content=\\\"切换到 127.0.0.1:8000\\\" Width=\\\"150\\\" Height=\\\"30\\\" Margin=\\\"5\\\" />\\r\\n            <Button Name=\\\"buttonSite2\\\" Content=\\\"切换到 127.0.0.1:8001\\\" Width=\\\"150\\\" Height=\\\"30\\\" Margin=\\\"5\\\" />\\r\\n        </StackPanel>\\r\\n\\r\\n        <wv2:WebView2 Name=\\\"webview\\\" Grid.Row=\\\"1\\\" />\\r\\n    </Grid>\\r\\n</Window>\\r\\n\"},\"dataMapping\":{\"VarKey\":null,\"Value\":\"url:{url}\"},\"windowId\":{\"VarKey\":\"windowId\",\"Value\":null},\"closeWhenDeactivate\":{\"VarKey\":null,\"Value\":\"false\"},\"stopIfFail\":{\"VarKey\":null,\"Value\":\"1\"},\"cscode\":{\"VarKey\":null,\"Value\":\"using System;\\r\\nusing System.Text;\\r\\nusing System.Windows;\\r\\nusing System.Windows.Forms;\\r\\nusing System.Collections.Generic;\\r\\nusing MessageBox = System.Windows.Forms.MessageBox;\\r\\nusing Quicker.Public;\\r\\nusing Microsoft.Web.WebView2.Core;\\r\\nusing Microsoft.Web.WebView2.Wpf;\\r\\nusing System.IO;\\r\\n\\r\\npublic static void OnWindowCreated(Window win, IDictionary<string, object> dataContext, ICustomWindowContext winContext)\\r\\n{\\r\\n    var webView = (WebView2)win.FindName(\\\"webview\\\");\\r\\n\\r\\n    // 设置默认数据上下文，确保 URL 可用\\r\\n    if (!dataContext.ContainsKey(\\\"url\\\"))\\r\\n    {\\r\\n        dataContext[\\\"url\\\"] = \\\"http://127.0.0.1:8000\\\"; // 默认 URL\\r\\n    }\\r\\n\\r\\n    // 初始化 WebView2 和导航\\r\\n    win.SourceInitialized += async (sender, e) =>\\r\\n    {\\r\\n        try\\r\\n        {\\r\\n            // 数据目录\\r\\n            var folder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), \\\"Quicker\\\", \\\"WebView2\\\");\\r\\n            var env = await CoreWebView2Environment.CreateAsync(null, folder).ConfigureAwait(true);\\r\\n            await webView.EnsureCoreWebView2Async(env).ConfigureAwait(true);\\r\\n            webView.CoreWebView2.Navigate(dataContext[\\\"url\\\"].ToString());\\r\\n        }\\r\\n        catch (Exception ex)\\r\\n        {\\r\\n            MessageBox.Show(\\\"WebView2 初始化失败：\\\" + ex.Message, \\\"错误\\\", MessageBoxButtons.OK, MessageBoxIcon.Error);\\r\\n        }\\r\\n    };\\r\\n}\\r\\n\\r\\npublic static void OnWindowLoaded(Window win, IDictionary<string, object> dataContext, ICustomWindowContext winContext)\\r\\n{\\r\\n    // 可以添加加载后的额外逻辑，如果需要\\r\\n}\\r\\n\\r\\npublic static bool OnButtonClicked(string controlName, object controlTag, Window win, ICustomWindowContext winContext)\\r\\n{\\r\\n    var webView = (WebView2)win.FindName(\\\"webview\\\");\\r\\n    if (webView == null || webView.CoreWebView2 == null)\\r\\n    {\\r\\n        MessageBox.Show(\\\"WebView2 未初始化\\\", \\\"错误\\\", MessageBoxButtons.OK, MessageBoxIcon.Error);\\r\\n        return false;\\r\\n    }\\r\\n\\r\\n    if (controlName == \\\"buttonSite1\\\")\\r\\n    {\\r\\n        webView.CoreWebView2.Navigate(\\\"http://127.0.0.1:8000\\\"); // 切换到第一个网站\\r\\n        return true; // 事件处理完成\\r\\n    }\\r\\n    else if (controlName == \\\"buttonSite2\\\")\\r\\n    {\\r\\n        webView.CoreWebView2.Navigate(\\\"http://127.0.0.1:8001\\\"); // 切换到第二个网站\\r\\n        return true; // 事件处理完成\\r\\n    }\\r\\n\\r\\n    return false; // 如果不是目标按钮，不处理\\r\\n}\\r\\n\"},\"events\":{\"VarKey\":null,\"Value\":\"\"},\"autoCloseTime\":{\"VarKey\":null,\"Value\":\"\"},\"activateMode\":{\"VarKey\":null,\"Value\":\"AutoActivate\"},\"winLocation\":{\"VarKey\":null,\"Value\":\"BottomRight\"},\"winSize\":{\"VarKey\":null,\"Value\":\"\"}},\"OutputParams\":{\"isSuccess\":null,\"windowHandle\":null,\"errMessage\":null},\"IfSteps\":null,\"ElseSteps\":null,\"Note\":\"\",\"Disabled\":false,\"Collapsed\":false,\"DelayMs\":0}]}", "Data2": null, "Data3": null, "Children": [], "Id": "3c48824e-6baf-43e6-85c1-8a027935a060", "TemplateId": "bf944477-55fa-4800-2578-08d989f9005b", "TemplateRevision": 2, "UseTemplate": false, "LastEditTimeUtc": "2025-04-27T05:54:30.4621734+08:00", "SharedActionId": "", "ShareTimeUtc": null, "CreateTimeUtc": "2025-04-27T05:50:58.3446906Z", "AsSubProgram": false, "SkipWhenStopRunningActions": false, "SkipCheckUpdate": false, "AutoUpdate": true, "KeepInfoWhenUpdate": false, "MinQuickerVersion": "", "ContextMenuData": "", "AllowScrollTrigger": false, "EnableEvaluateVariable": false, "IsTextProcessor": false, "IsImageProcessor": false, "Association": {"MatchProcess": null, "IsImageProcessor": false, "ReturnImageFromFirstScreenShotStep": true, "IsTextProcessor": false, "ReturnTextFromGetSelectedTextStep": true, "TextMatchExpression": "", "TextMinLength": 0, "TextMaxLength": 0, "IsHtmlProcessor": false, "IsFileProcessor": false, "FileMinCount": 0, "FileMaxCount": 0, "AllowedFileExtensions": "", "RequireAllFileMatchExt": false, "SearchBoxPlaceholder": "", "IsWindowProcessor": false, "EnableRealtimeSearch": false, "BrowserContextMenu": null, "UrlPattern": ""}, "DoNotClosePanel": false, "UserLimitation": 0}