<script type="importmap">
{
  "imports": {
    "@/": "./",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0",
    "marked": "https://esm.sh/marked@^15.0.12",
    "dompurify": "https://esm.sh/dompurify@^3.2.6"
  }
}
</script>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MemoryAble: A Gemini VN</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Mochiy+Pop+One&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap" rel="stylesheet">
  <link href="https://cdn.bootcdn.net/ajax/libs/lxgw-wenkai-screen-webfont/1.7.0/style.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --header-height: 3.0rem;

      /* Light Theme */
      --bg-primary-light: transparent; 
      --bg-secondary-light: rgba(240, 244, 249, 0.80);
      --bg-secondary-rgb-light: 240, 244, 249;
      --bg-element-light: rgba(230, 236, 242, 0.75);
      --text-primary-light: #1E2124;
      --text-secondary-light: #4A5568;
      --text-accent-light: #5F9DF7;
      --border-color-light: #D1D9E3;
      --accent-color-light: #5F9DF7; 
      --accent-color-hover-light: #4A88E5; 
      --scrollbar-track-light: rgba(220, 228, 238, 0.30); 
      --scrollbar-thumb-light: #B0D4FF;
      --scrollbar-thumb-hover-light: #A0C8FF;
      --shadow-color-light: rgba(30, 33, 36, 0.08);
      --ring-color-light: #5F9DF7;
      --code-bg-light: #E9EDF2;
      --code-text-light: #D6336C;
      --blockquote-border-light: #A0C8FF;
      --bg-dialogue-player-light: rgba(95, 157, 247, 0.75); /* More saturated blue for player */   
      --text-dialogue-player-light: #FFFFFF; /* White text for better contrast on richer blue */
      --bg-dialogue-npc-light: rgba(215, 220, 228, 0.80); /* More opaque and distinct grey for NPC */      
      --text-dialogue-npc-light: #282C34;
      --bg-dialogue-narrator-light: rgba(245, 248, 252, 0.75); /* Slightly more opaque */ 
      --text-dialogue-narrator-light: #525F70;
      --accent-button-bg-start-light: rgba(170, 212, 255, 0.85); 
      --accent-button-bg-end-light: rgba(127, 184, 249, 0.85);   
      --accent-button-border-light: #9BC2F0;
      --accent-button-inner-shadow-light: rgba(255, 255, 255, 0.6); 
      --accent-button-text-light: #122A40; 
      --accent-button-shadow-color-light: rgba(95, 157, 247, 0.25);
      --npc-nameplate-bg-light: var(--accent-color-light);
      --npc-nameplate-text-light: #FFFFFF;
      --npc-nameplate-border-light: var(--accent-color-hover-light);
      /* Notification Light Theme */
      --notification-success-bg-light: rgba(76, 175, 80, 0.15);
      --notification-success-text-light: #2E7D32;
      --notification-success-border-light: rgba(76, 175, 80, 0.4);
      --notification-success-icon-light: #388E3C;
      --notification-error-bg-light: rgba(211, 47, 47, 0.15);
      --notification-error-text-light: #B71C1C;
      --notification-error-border-light: rgba(211, 47, 47, 0.4);
      --notification-error-icon-light: #C62828;
      --notification-warning-bg-light: rgba(251, 192, 45, 0.2);
      --notification-warning-text-light: #E65100;
      --notification-warning-border-light: rgba(251, 192, 45, 0.5);
      --notification-warning-icon-light: #F57F17;
      --notification-info-bg-light: rgba(30, 136, 229, 0.15);
      --notification-info-text-light: #0D47A1;
      --notification-info-border-light: rgba(30, 136, 229, 0.4);
      --notification-info-icon-light: #1565C0;
      --notification-achievement-bg-light: rgba(123, 31, 162, 0.15);
      --notification-achievement-text-light: #6A1B9A;
      --notification-achievement-border-light: rgba(123, 31, 162, 0.4);
      --notification-achievement-icon-light: #8E24AA;
      --notification-shadow-light: rgba(95, 157, 247, 0.15);

      /* Dark Theme */
      --bg-primary-dark: transparent; 
      --bg-secondary-dark: rgba(42, 47, 56, 0.75);
      --bg-secondary-rgb-dark: 42, 47, 56;
      --bg-element-dark: rgba(33, 37, 43, 0.70);    
      --text-primary-dark: #ECEFF4;
      --text-secondary-dark: #D8DEE9;
      --text-accent-dark: #B48EAD; /* A softer purple for general accents */
      --border-color-dark: #434C5E;
      --accent-color-dark: #88C0D0; /* Nord blue - for highlights like buttons, links */
      --accent-color-hover-dark: #7AA8B8;
      --scrollbar-track-dark: rgba(50, 55, 65, 0.30); 
      --scrollbar-thumb-dark: #6B7A90;
      --scrollbar-thumb-hover-dark: #88C0D0;
      --shadow-color-dark: rgba(0, 0, 0, 0.25);
      --ring-color-dark: #B48EAD;
      --code-bg-dark: #2E3440;
      --code-text-dark: #A3BE8C;
      --blockquote-border-dark: #88C0D0;
      --bg-dialogue-player-dark: rgba(136, 192, 208, 0.75); /* Player bubble - brighter, Nord blue */  
      --text-dialogue-player-dark: #2E3440; /* Darker text for contrast on lighter player bubble */
      --bg-dialogue-npc-dark: rgba(59, 66, 82, 0.75); /* NPC bubble - a standard dark Nord color */        
      --text-dialogue-npc-dark: #D8DEE9;
      --bg-dialogue-narrator-dark: rgba(46, 52, 64, 0.70); /* Narrator - subtle dark */   
      --text-dialogue-narrator-dark: #B0B8C4;
      --accent-button-bg-start-dark: rgba(159, 208, 224, 0.85); 
      --accent-button-bg-end-dark: rgba(136, 192, 208, 0.85); 
      --accent-button-border-dark: #81B6C6;
      --accent-button-inner-shadow-dark: rgba(220, 235, 240, 0.3);
      --accent-button-text-dark: #1E2B33;
      --accent-button-shadow-color-dark: rgba(136, 192, 208, 0.2);
      --npc-nameplate-bg-dark: var(--accent-color-dark);
      --npc-nameplate-text-dark: #2E3440;
      --npc-nameplate-border-dark: var(--accent-color-hover-dark);
      /* Notification Dark Theme */
      --notification-success-bg-dark: rgba(165, 214, 167, 0.2);
      --notification-success-text-dark: #A5D6A7;
      --notification-success-border-dark: rgba(165, 214, 167, 0.5);
      --notification-error-bg-dark: rgba(239, 154, 154, 0.2);
      --notification-error-text-dark: #EF9A9A;
      --notification-error-border-dark: rgba(239, 154, 154, 0.5);
      --notification-warning-bg-dark: rgba(255, 224, 130, 0.2);
      --notification-warning-text-dark: #FFE082;
      --notification-warning-border-dark: rgba(255, 224, 130, 0.5);
      --notification-info-bg-dark: rgba(144, 202, 249, 0.2);
      --notification-info-text-dark: #90CAF9;
      --notification-info-border-dark: rgba(144, 202, 249, 0.5);
      --notification-achievement-bg-dark: rgba(206, 147, 216, 0.2);
      --notification-achievement-text-dark: #CE93D8;
      --notification-achievement-border-dark: rgba(206, 147, 216, 0.5);
      --notification-shadow-dark: rgba(0,0,0, 0.3);
    }
    body {
      font-family: 'Noto Sans SC', 'LXGW WenKai Screen', sans-serif;
      margin: 0;
      padding: 0;
      color: var(--text-primary);
      background-color: var(--bg-primary); /* Will be transparent for image background */
      overflow: hidden; /* Prevent scrollbars on body */
      min-height: 100vh; /* Ensure body takes full viewport height */
    }
    /* Tailwind Base Styles Reset - applied by TailwindCDN typically, ensure these are effective */
    *, ::before, ::after { box-sizing: border-box; border-width: 0; border-style: solid; border-color: currentColor; }

    #root {
      width: 100vw;
      height: 100vh;
      overflow: hidden; /* Ensure root div also does not cause scrollbars */
      display: flex; 
      flex-direction: column; 
    }

    /* Custom Scrollbar Styling */
    /* For Webkit browsers */
    ::-webkit-scrollbar { width: 8px; height: 8px; }
    ::-webkit-scrollbar-track { background: var(--scrollbar-track, rgba(0,0,0,0.1)); border-radius: 4px; }
    ::-webkit-scrollbar-thumb { background: var(--scrollbar-thumb, #888); border-radius: 4px; }
    ::-webkit-scrollbar-thumb:hover { background: var(--scrollbar-thumb-hover, #555); }
    /* For Firefox */
    * { scrollbar-width: thin; scrollbar-color: var(--scrollbar-thumb, #888) var(--scrollbar-track, rgba(0,0,0,0.1)); }

    .app-title-gradient {
      background-image: linear-gradient(45deg, var(--accent-color), var(--text-accent));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-family: 'Mochiy Pop One', 'LXGW WenKai Screen', sans-serif;
    }
    .app-title-enhanced {
      font-family: 'Great Vibes', 'LXGW WenKai Screen', cursive; /* Using Great Vibes for more artistic title */
    }

    .rich-text-content strong, .rich-text-content b { font-weight: bold; }
    .rich-text-content em, .rich-text-content i { font-style: italic; }
    .rich-text-content u { text-decoration: underline; }
    .rich-text-content p { margin-bottom: 0.5em; }
    .rich-text-content ul, .rich-text-content ol { margin-left: 1.5em; margin-bottom: 0.5em; }
    .rich-text-content li { margin-bottom: 0.25em; }
    .rich-text-content pre {
      background-color: var(--code-bg, #f0f0f0);
      color: var(--code-text, #333);
      padding: 0.5em;
      border-radius: 4px;
      overflow-x: auto;
      font-family: 'Courier New', Courier, monospace;
      font-size: 0.9em;
      margin-bottom: 0.5em;
    }
    .rich-text-content code {
      background-color: var(--code-bg, #f0f0f0);
      color: var(--code-text, #D6336C); /* Using a distinct color for inline code */
      padding: 0.1em 0.3em;
      border-radius: 3px;
      font-family: 'Courier New', Courier, monospace;
      font-size: 0.9em;
    }
    .rich-text-content blockquote {
      border-left: 4px solid var(--blockquote-border, #ccc);
      padding-left: 1em;
      margin-left: 0;
      margin-right: 0;
      margin-bottom: 0.5em;
      font-style: italic;
      color: var(--text-secondary, #555);
    }
    .rich-text-content h1, .rich-text-content h2, .rich-text-content h3, .rich-text-content h4, .rich-text-content h5, .rich-text-content h6 {
      font-weight: bold;
      margin-top: 0.8em;
      margin-bottom: 0.4em;
    }
    .rich-text-content h1 { font-size: 1.8em; }
    .rich-text-content h2 { font-size: 1.5em; }
    .rich-text-content h3 { font-size: 1.3em; }
    .rich-text-content h4 { font-size: 1.1em; }
    
    .btn-dreamy {
      padding: 0.5rem 1rem;
      font-weight: 500;
      border-radius: 0.5rem;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      z-index: 1;
      border: 1px solid var(--accent-button-border, transparent);
      background-image: linear-gradient(145deg, var(--accent-button-bg-start), var(--accent-button-bg-end));
      color: var(--accent-button-text);
      box-shadow: 0 2px 8px -2px var(--accent-button-shadow-color), 
                  inset 0 1px 1px var(--accent-button-inner-shadow),
                  inset 0 -1px 0px rgba(0,0,0,0.05);
      text-shadow: 0 1px 1px rgba(255,255,255,0.1);
    }
    .btn-dreamy:hover {
      transform: translateY(-1px) scale(1.02);
      box-shadow: 0 4px 12px -2px var(--accent-button-shadow-color),
                  inset 0 1px 2px var(--accent-button-inner-shadow),
                  inset 0 -1px 0px rgba(0,0,0,0.05);
      filter: brightness(1.1);
    }
    .btn-dreamy:active {
      transform: translateY(0px) scale(0.98);
      box-shadow: 0 1px 4px -1px var(--accent-button-shadow-color),
                  inset 0 1px 1px var(--accent-button-inner-shadow),
                  inset 0 -1px 0px rgba(0,0,0,0.05);
      filter: brightness(0.95);
    }
    .btn-dreamy:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 1px 4px -1px var(--accent-button-shadow-color);
      filter: grayscale(30%);
    }
    .btn-dreamy-xs { /* Extra small variant */
      padding: 0.3rem 0.6rem;
      font-size: 0.75rem; /* Adjusted from 0.875rem */
      border-radius: 0.375rem; /* Smaller radius */
    }
    .btn-dreamy-icon { /* For icon-only buttons, might need specific padding adjustments */
      padding: 0.4rem; /* Adjusted for square-ish feel */
    }

    .resize-handle {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: nwse-resize;
      opacity: 0.3;
      transition: opacity 0.2s;
      z-index: 10; /* Ensure it's above content */
    }
    .resize-handle:hover {
      opacity: 0.7;
    }
    .resize-handle::after {
      content: '';
      position: absolute;
      bottom: 2px;
      right: 2px;
      width: 6px;
      height: 6px;
      border-bottom: 2px solid var(--text-secondary);
      border-right: 2px solid var(--text-secondary);
      border-top-left-radius: 2px;
    }

    .shadow-themed { box-shadow: 0 1px 3px 0 var(--shadow-color, rgba(0,0,0,0.1)), 0 1px 2px -1px var(--shadow-color, rgba(0,0,0,0.1)); }
    .shadow-themed-md { box-shadow: 0 4px 6px -1px var(--shadow-color, rgba(0,0,0,0.1)), 0 2px 4px -2px var(--shadow-color, rgba(0,0,0,0.1)); }
    .shadow-themed-lg { box-shadow: 0 10px 15px -3px var(--shadow-color, rgba(0,0,0,0.1)), 0 4px 6px -4px var(--shadow-color, rgba(0,0,0,0.1)); }
    .shadow-themed-xl { box-shadow: 0 20px 25px -5px var(--shadow-color, rgba(0,0,0,0.1)), 0 8px 10px -6px var(--shadow-color, rgba(0,0,0,0.1)); }

    .border-themed { border-color: var(--border-color); }
    .bg-primary-themed { background-color: var(--bg-primary); }
    .bg-secondary-themed { background-color: var(--bg-secondary); }
    .bg-element-themed { background-color: var(--bg-element); }
    .text-primary-themed { color: var(--text-primary); }
    .text-secondary-themed { color: var(--text-secondary); }
    .text-accent-themed { color: var(--text-accent); }
    .ring-accent-themed:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 3px var(--ring-color); }
    .placeholder-themed::placeholder { color: var(--text-secondary); opacity: 0.7; }
    .text-player-themed { color: var(--text-player); }
    .text-narrator-themed { color: var(--text-narrator); }
    
    .theme-light {
      --bg-primary: var(--bg-primary-light);
      --bg-secondary: rgba(240, 244, 249, 0.75);
      --bg-secondary-rgb: var(--bg-secondary-rgb-light);
      --bg-element: rgba(230, 236, 242, 0.70);
      --text-primary: var(--text-primary-light);
      --text-secondary: var(--text-secondary-light);
      --text-accent: var(--text-accent-light);
      --border-color: var(--border-color-light);
      --accent-color: var(--accent-color-light);
      --accent-color-hover: var(--accent-color-hover-light);
      --scrollbar-track: var(--scrollbar-track-light);
      --scrollbar-thumb: var(--scrollbar-thumb-light);
      --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
      --shadow-color: var(--shadow-color-light);
      --ring-color: var(--ring-color-light);
      --code-bg: var(--code-bg-light);
      --code-text: var(--code-text-light);
      --blockquote-border: var(--blockquote-border-light);
      --dialogue-player-bg: var(--bg-dialogue-player-light);
      --dialogue-player-text: var(--text-dialogue-player-light);
      --dialogue-npc-bg: var(--bg-dialogue-npc-light);
      --dialogue-npc-text: var(--text-dialogue-npc-light);
      --dialogue-narrator-bg: var(--bg-dialogue-narrator-light);
      --dialogue-narrator-text: var(--text-dialogue-narrator-light);
      --accent-button-bg-start: var(--accent-button-bg-start-light);
      --accent-button-bg-end: var(--accent-button-bg-end-light);
      --accent-button-border: var(--accent-button-border-light);
      --accent-button-inner-shadow: var(--accent-button-inner-shadow-light);
      --accent-button-text: var(--accent-button-text-light);
      --accent-button-shadow-color: var(--accent-button-shadow-color-light);
      --npc-nameplate-bg: var(--npc-nameplate-bg-light);
      --npc-nameplate-text: var(--npc-nameplate-text-light);
      --npc-nameplate-border: var(--npc-nameplate-border-light);
      /* Notifications */
      --notification-success-bg: var(--notification-success-bg-light);
      --notification-success-text: var(--notification-success-text-light);
      --notification-success-border: var(--notification-success-border-light);
      --notification-success-icon: var(--notification-success-icon-light);
      --notification-error-bg: var(--notification-error-bg-light);
      --notification-error-text: var(--notification-error-text-light);
      --notification-error-border: var(--notification-error-border-light);
      --notification-error-icon: var(--notification-error-icon-light);
      --notification-warning-bg: var(--notification-warning-bg-light);
      --notification-warning-text: var(--notification-warning-text-light);
      --notification-warning-border: var(--notification-warning-border-light);
      --notification-warning-icon: var(--notification-warning-icon-light);
      --notification-info-bg: var(--notification-info-bg-light);
      --notification-info-text: var(--notification-info-text-light);
      --notification-info-border: var(--notification-info-border-light);
      --notification-info-icon: var(--notification-info-icon-light);
      --notification-achievement-bg: var(--notification-achievement-bg-light);
      --notification-achievement-text: var(--notification-achievement-text-light);
      --notification-achievement-border: var(--notification-achievement-border-light);
      --notification-achievement-icon: var(--notification-achievement-icon-light);
      --notification-shadow: var(--notification-shadow-light);
    }
    .theme-dark {
      --bg-primary: var(--bg-primary-dark);
      --bg-secondary: rgba(36, 40, 48, 0.75);
      --bg-secondary-rgb: var(--bg-secondary-rgb-dark);
      --bg-element: rgba(28, 32, 38, 0.70);    
      --text-primary: var(--text-primary-dark);
      --text-secondary: var(--text-secondary-dark);
      --text-accent: var(--text-accent-dark);
      --border-color: var(--border-color-dark);
      --accent-color: var(--accent-color-dark);
      --accent-color-hover: var(--accent-color-hover-dark);
      --scrollbar-track: var(--scrollbar-track-dark);
      --scrollbar-thumb: var(--scrollbar-thumb-dark);
      --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
      --shadow-color: var(--shadow-color-dark);
      --ring-color: var(--ring-color-dark);
      --code-bg: var(--code-bg-dark);
      --code-text: var(--code-text-dark);
      --blockquote-border: var(--blockquote-border-dark);
      --dialogue-player-bg: var(--bg-dialogue-player-dark);
      --dialogue-player-text: var(--text-dialogue-player-dark);
      --dialogue-npc-bg: var(--bg-dialogue-npc-dark);
      --dialogue-npc-text: var(--text-dialogue-npc-dark);
      --dialogue-narrator-bg: var(--bg-dialogue-narrator-dark);
      --dialogue-narrator-text: var(--text-dialogue-narrator-dark);
      --accent-button-bg-start: var(--accent-button-bg-start-dark);
      --accent-button-bg-end: var(--accent-button-bg-end-dark);
      --accent-button-border: var(--accent-button-border-dark);
      --accent-button-inner-shadow: var(--accent-button-inner-shadow-dark);
      --accent-button-text: var(--accent-button-text-dark);
      --accent-button-shadow-color: var(--accent-button-shadow-color-dark);
      --npc-nameplate-bg: var(--npc-nameplate-bg-dark);
      --npc-nameplate-text: var(--npc-nameplate-text-dark);
      --npc-nameplate-border: var(--npc-nameplate-border-dark);
      /* Notifications */
      --notification-success-bg: var(--notification-success-bg-dark);
      --notification-success-text: var(--notification-success-text-dark);
      --notification-success-border: var(--notification-success-border-dark);
      --notification-error-bg: var(--notification-error-bg-dark);
      --notification-error-text: var(--notification-error-text-dark);
      --notification-error-border: var(--notification-error-border-dark);
      --notification-warning-bg: var(--notification-warning-bg-dark);
      --notification-warning-text: var(--notification-warning-text-dark);
      --notification-warning-border: var(--notification-warning-border-dark);
      --notification-info-bg: var(--notification-info-bg-dark);
      --notification-info-text: var(--notification-info-text-dark);
      --notification-info-border: var(--notification-info-border-dark);
      --notification-achievement-bg: var(--notification-achievement-bg-dark);
      --notification-achievement-text: var(--notification-achievement-text-dark);
      --notification-achievement-border: var(--notification-achievement-border-dark);
      --notification-shadow: var(--notification-shadow-dark);
    }
    /* Add other theme definitions (Sakura, Starry, etc.) here in a similar fashion */
    .theme-sakura { 
        --bg-primary: #F9F5F6; /* 非常柔和的粉白色背景 */
        --bg-secondary: rgba(242, 232, 235, 0.75); /* 柔和的粉灰色，半透明 */
        --bg-secondary-rgb: 242, 232, 235;
        --bg-element: rgba(235, 225, 228, 0.70); /* 更淡的粉灰色，半透明 */
        --text-primary: #5D4B4D; /* 暖灰褐色文本 */
        --text-secondary: #8A7477; /* 中等暖灰色次要文本 */
        --text-accent: #A67F8E; /* 柔和的梅红色强调 */
        --border-color: #D9CCD0; /* 淡粉灰色边框 */
        --accent-color: #B38EA9; /* 暗粉紫色强调 */
        --accent-color-hover: #A57E98; /* 稍深的粉紫色悬停状态 */
        --scrollbar-track: rgba(242, 232, 235, 0.40);
        --scrollbar-thumb: #C9B6BF; /* 较淡的粉紫色滚动条 */
        --scrollbar-thumb-hover: #B9A6AF;
        --shadow-color: rgba(166, 127, 142, 0.10); /* 淡粉色阴影 */
        --ring-color: #A67F8E;
        --code-bg: #F2E8EB;
        --code-text: #9E6A82;
        --blockquote-border: #D1BDC7;
        --bg-dialogue-player: rgba(179, 142, 169, 0.70); /* 玩家 - 柔和的粉紫色 */
        --text-dialogue-player: #F9F5F6;
        --bg-dialogue-npc: rgba(217, 204, 208, 0.75); /* NPC - 淡粉灰色 */
        --text-dialogue-npc: #5D4B4D;
        --bg-dialogue-narrator: rgba(242, 238, 240, 0.70); /* 旁白 - 非常淡的粉白色 */
        --text-dialogue-narrator: #7D686B; /* 暗粉灰色旁白文本 */
        --accent-button-bg-start: rgba(201, 182, 191, 0.80); /* 淡粉紫色渐变开始 */
        --accent-button-bg-end: rgba(179, 142, 169, 0.80); /* 粉紫色渐变结束 */
        --accent-button-border: #CABCC0;
        --accent-button-inner-shadow: rgba(249, 245, 246, 0.6);
        --accent-button-text: #4D3E45; /* 深褐紫色按钮文本 */
        --accent-button-shadow-color: rgba(179, 142, 169, 0.20);
        --npc-nameplate-bg: var(--accent-color);
        --npc-nameplate-text: #F9F5F6;
        --npc-nameplate-border: var(--accent-color-hover);
    }
    .theme-starry { 
        --bg-primary: #1A1D24; /* 深海军蓝背景 */
        --bg-secondary: rgba(26, 31, 40, 0.75); /* 深蓝黑色，半透明 */
        --bg-secondary-rgb: 26, 31, 40;
        --bg-element: rgba(32, 37, 46, 0.70); /* 稍浅的深蓝黑色，半透明 */
        --text-primary: #D8DEE9; /* 浅蓝灰色文本 */
        --text-secondary: #A7B5C9; /* 中蓝灰色次要文本 */
        --text-accent: #81A1C1; /* 柔和的北极蓝色强调 */
        --border-color: #3B4252; /* 深蓝灰色边框 */
        --accent-color: #5E81AC; /* 北极蓝色强调 */
        --accent-color-hover: #4C6F98; /* 深北极蓝色悬停状态 */
        --scrollbar-track: rgba(32, 37, 46, 0.50);
        --scrollbar-thumb: #4C566A; /* 深蓝灰色滚动条 */
        --scrollbar-thumb-hover: #5E6D8A;
        --shadow-color: rgba(0, 0, 0, 0.25);
        --ring-color: #81A1C1;
        --code-bg: #2E3440;
        --code-text: #8FBCBB;
        --blockquote-border: #4C566A;
        --bg-dialogue-player: rgba(94, 129, 172, 0.70); /* 玩家 - 北极蓝色 */
        --text-dialogue-player: #ECEFF4;
        --bg-dialogue-npc: rgba(59, 66, 82, 0.75); /* NPC - 深蓝灰色 */
        --text-dialogue-npc: #D8DEE9;
        --bg-dialogue-narrator: rgba(35, 40, 50, 0.70); /* 旁白 - 深蓝黑色 */
        --text-dialogue-narrator: #A7B5C9; /* 中蓝灰色旁白文本 */
        --accent-button-bg-start: rgba(108, 143, 186, 0.80); /* 浅北极蓝渐变开始 */
        --accent-button-bg-end: rgba(94, 129, 172, 0.80); /* 北极蓝渐变结束 */
        --accent-button-border: #546A87;
        --accent-button-inner-shadow: rgba(143, 188, 187, 0.2);
        --accent-button-text: #ECEFF4; /* 浅蓝白色按钮文本 */
        --accent-button-shadow-color: rgba(0, 0, 0, 0.20);
        --npc-nameplate-bg: var(--accent-color);
        --npc-nameplate-text: #ECEFF4;
        --npc-nameplate-border: var(--accent-color-hover);
    }
    .theme-candy {
        --bg-primary: #F6EFF1; /* 淡粉白色背景 */
        --bg-secondary: rgba(241, 227, 232, 0.75); /* 柔和的粉色，半透明 */
        --bg-secondary-rgb: 241, 227, 232;
        --bg-element: rgba(233, 219, 225, 0.70); /* 稍深的粉色，半透明 */
        --text-primary: #5E454B; /* 深褐红色文本 */
        --text-secondary: #8A6E76; /* 中等褐粉色次要文本 */
        --text-accent: #B07A8F; /* 柔和的玫瑰色强调 */
        --border-color: #D9C5CD; /* 淡玫瑰色边框 */
        --accent-color: #AD7A90; /* 暗玫瑰色强调 */
        --accent-color-hover: #9A6A80; /* 深玫瑰色悬停状态 */
        --scrollbar-track: rgba(241, 227, 232, 0.40);
        --scrollbar-thumb: #CBAEB8; /* 淡玫瑰色滚动条 */
        --scrollbar-thumb-hover: #BC9EA8;
        --shadow-color: rgba(176, 122, 143, 0.10); /* 淡玫瑰色阴影 */
        --ring-color: #B07A8F;
        --code-bg: #F1E3E8;
        --code-text: #9D5B74;
        --blockquote-border: #D4B6C3;
        --bg-dialogue-player: rgba(173, 122, 144, 0.70); /* 玩家 - 玫瑰色 */
        --text-dialogue-player: #F6EFF1;
        --bg-dialogue-npc: rgba(217, 197, 205, 0.75); /* NPC - 淡粉色 */
        --text-dialogue-npc: #5E454B;
        --bg-dialogue-narrator: rgba(241, 232, 236, 0.70); /* 旁白 - 非常淡的粉色 */
        --text-dialogue-narrator: #7D636A; /* 暗褐粉色旁白文本 */
        --accent-button-bg-start: rgba(203, 174, 184, 0.80); /* 淡玫瑰色渐变开始 */
        --accent-button-bg-end: rgba(173, 122, 144, 0.80); /* 玫瑰色渐变结束 */
        --accent-button-border: #C8B0BA;
        --accent-button-inner-shadow: rgba(246, 239, 241, 0.6);
        --accent-button-text: #4E3A40; /* 深褐红色按钮文本 */
        --accent-button-shadow-color: rgba(173, 122, 144, 0.20);
        --npc-nameplate-bg: var(--accent-color);
        --npc-nameplate-text: #F6EFF1;
        --npc-nameplate-border: var(--accent-color-hover);
    }
    .theme-forest {
        --bg-primary: #F0F4F0; /* 淡青白色背景 */
        --bg-secondary: rgba(232, 240, 232, 0.75); /* 柔和的浅绿色，半透明 */
        --bg-secondary-rgb: 232, 240, 232;
        --bg-element: rgba(222, 232, 222, 0.70); /* 稍深的浅绿色，半透明 */
        --text-primary: #415141; /* 深森林绿色文本 */
        --text-secondary: #677A67; /* 中等森林绿色次要文本 */
        --text-accent: #6B8E6B; /* 柔和的森林绿色强调 */
        --border-color: #C5D6C5; /* 淡森林绿色边框 */
        --accent-color: #5F8D5F; /* 森林绿色强调 */
        --accent-color-hover: #507A50; /* 深森林绿色悬停状态 */
        --scrollbar-track: rgba(232, 240, 232, 0.40);
        --scrollbar-thumb: #B1C9B1; /* 淡森林绿色滚动条 */
        --scrollbar-thumb-hover: #9EB69E;
        --shadow-color: rgba(107, 142, 107, 0.10); /* 淡森林绿色阴影 */
        --ring-color: #6B8E6B;
        --code-bg: #E8F0E8;
        --code-text: #4E7C4E;
        --blockquote-border: #B1C9B1;
        --bg-dialogue-player: rgba(95, 141, 95, 0.70); /* 玩家 - 森林绿色 */
        --text-dialogue-player: #F0F4F0;
        --bg-dialogue-npc: rgba(197, 214, 197, 0.75); /* NPC - 淡森林绿色 */
        --text-dialogue-npc: #415141;
        --bg-dialogue-narrator: rgba(236, 242, 236, 0.70); /* 旁白 - 非常淡的绿色 */
        --text-dialogue-narrator: #5E715E; /* 暗森林绿色旁白文本 */
        --accent-button-bg-start: rgba(177, 201, 177, 0.80); /* 淡森林绿色渐变开始 */
        --accent-button-bg-end: rgba(95, 141, 95, 0.80); /* 森林绿色渐变结束 */
        --accent-button-border: #B1C9B1;
        --accent-button-inner-shadow: rgba(240, 244, 240, 0.6);
        --accent-button-text: #384438; /* 深森林绿色按钮文本 */
        --accent-button-shadow-color: rgba(95, 141, 95, 0.20);
        --npc-nameplate-bg: var(--accent-color);
        --npc-nameplate-text: #F0F4F0;
        --npc-nameplate-border: var(--accent-color-hover);
    }

    /* General Animation Utility */
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    .animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
    @keyframes slideInFromRight { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }
    .animate-slideInFromRight { animation: slideInFromRight 0.3s ease-out forwards; }

    /* Application Layout Styles */
    .app-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      position: relative; /* For ImageDisplay positioning */
    }

    .app-header {
      height: var(--header-height); 
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1rem;
      background-color: rgba(var(--bg-secondary-rgb), 0.7); 
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border-bottom: 1px solid var(--border-color);
      color: var(--text-primary);
      z-index: 20; 
      flex-shrink: 0; 
    }
    .menu-button, .action-button {
      padding: 0.5rem;
      border-radius: 0.375rem;
      color: var(--text-secondary);
      transition: background-color 0.2s, color 0.2s;
    }
    .menu-button:hover, .action-button:hover {
      background-color: var(--bg-element);
      color: var(--text-accent);
    }
    .app-title-header {
      font-size: 1.5rem; 
      font-weight: 700; 
    }
    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .app-main {
      display: flex;
      flex-grow: 1; 
      overflow: hidden; 
      padding: 0.5rem; 
      gap: 0.5rem; 
    }

    .chat-interface-container {
      flex-grow: 1;
      display: flex; 
      justify-content: center; /* Horizontally centers its direct child (ChatInterface) */
      align-items: stretch;    /* Stretches its direct child (ChatInterface) to fill height */
      min-width: 0; 
    }
    
    .chat-interface-container > div { /* Assuming ChatInterface's root is a div */
        /* width: 100%; */ /* REMOVED: ChatInterface will manage its own width */
        height: 100%;    /* ChatInterface will take full height of its flex cell */
    }

    /* .status-panel-container styling is removed as it's no longer part of .app-main flex */
    /* The PersistentStatusDisplay is now positioned fixed via App.tsx */

  </style>
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="module" src="./index.tsx"></script>
</body>
</html>
