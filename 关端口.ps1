if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    exit
}

$targetConnections = Get-NetTCPConnection -State Listen, Established | Where-Object {
    $port = $_.LocalPort
    ($port -ge 5100 -and $port -le 5199) -or ($port -ge 8080 -and $port -le 8089)
}

if ($null -ne $targetConnections) {
    $pidsToKill = $targetConnections.OwningProcess | Sort-Object -Unique
    foreach ($processId in $pidsToKill) {
        Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
    }
}