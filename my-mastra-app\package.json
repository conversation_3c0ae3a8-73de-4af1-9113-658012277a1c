{"name": "my-mastra-app", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@mastra/core": "^0.10.5", "@mastra/libsql": "^0.10.2", "@mastra/loggers": "^0.10.2", "@mastra/memory": "^0.10.3", "zod": "^3.25.64"}, "devDependencies": {"@types/node": "^24.0.1", "mastra": "^0.10.5", "typescript": "^5.8.3"}}