/* 
 * 薄荷.css - CherryStudio薄荷绿主题
 * 优化版本：v11
 * 
 * 性能优化要点：
 * 1. 优先使用类选择器，避免过深的选择器嵌套
 * 2. 利用CSS变量统一管理颜色和尺寸，方便维护
 * 3. 合理使用硬件加速 (transform, opacity) 代替耗性能的属性
 * 4. 动画过渡使用简单属性，避免复合属性
 * 5. 使用border-radius代替复杂图形，减少渲染复杂度
 */

/* MaterialYou / Nord 转 薄荷绿 / 豆沙绿主题 CSS - Optimized v11 (增强视觉层次和按钮交互) */

/* 引入字体 */
@import url("https://fonts.googleapis.com/css2?family=Audiowide&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap");
@import url("http://cdn.bootcdn.net/ajax/libs/lxgw-wenkai-screen-webfont/1.7.0/style.min.css");
/* 霞鹜文楷 */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
/* JetBrains Mono */


/* 全局豆沙绿/薄荷绿主题变量 */
:root {
    /* --- 核心设计令牌 --- */
    /* 尺寸与间距 */
    --material-line-width: 0.5px;
    --container-border-radius: 14px;
    --list-item-border-radius: 10px;
    --button-border-radius: 8px;
    --spacing-xs: 0.3rem;
    --spacing-sm: 0.6rem;
    --spacing-md: 0.9rem;
    --spacing-lg: 1.2rem;
    --avatar-size: 3.2rem;
    --avatar-border: 1px;
    --avatar-bottom-margin: var(--spacing-sm);
    --message-padding: var(--spacing-lg);
    --message-content-padding: var(--spacing-md);
    --message-border-radius: 1rem;
    --reasoning-border-radius: var(--list-item-border-radius);
    --scrollbar-width: 8px;
    /* 滚动条宽度 */

    /* 过渡效果 */
    --duration-fast: 0.15s;
    --duration-normal: 0.25s;
    --easing-ease: ease;

    /* --- 薄荷/豆沙调色板 (暗色模式基础) --- */
    /* 基础绿色 */
    --color-mint-dark-1: #1a2c22;
    /* 最深的 */
    --color-mint-dark-2: #243f31;
    --color-mint-dark-3: #305241;
    --color-mint-dark-4: #406b55;
    /* 中等 */
    --color-mint-dark-5: #55856d;
    --color-mint-dark-6: #70a68a;
    /* 较浅的强调色 */

    /* 豆沙/抹茶绿色 */
    --color-bean-dark-1: #2a2e2a;
    --color-bean-dark-2: #3e473f;
    --color-bean-dark-3: #556357;

    /* 核心主题颜色 (暗色模式) */
    --paper-color: var(--color-mint-dark-1);
    /* 基础背景 */
    --ink-color: #c8d5cd;
    /* 主要文本 */
    --ink-color-secondary: #a0b8ac;
    /* 次要文本 */
    --accent-color: var(--color-mint-dark-6);
    /* 主要强调色 (较浅的绿色) */
    --accent-color-rgb: 112, 166, 138;
    --accent-color-secondary: var(--color-mint-dark-5);
    /* 次要强调色 (中等绿色) */
    --accent-color-tertiary: var(--color-bean-dark-3);
    /* 第三强调色 (豆沙绿) */

    --border-color: rgba(var(--accent-color-rgb), 0.3);
    /* 基于强调色的边框 */
    --shadow-color: rgba(0, 0, 0, 0.35);
    /* 稍深的阴影 */
    --dialogue-underline-color: var(--accent-color-secondary);
    --dialogue-bold-underline-color: var(--accent-color);
    --italics-text-color: var(--ink-color-secondary);

    /* UI 元素背景 (暗色模式) */
    --color-background-base-rgb: 30, 50, 39;
    --bg-opacity-low: 0.75;
    --bg-opacity-medium: 0.85;
    --bg-opacity-high: 0.9;

    /* 替换纯黑为深绿黑 */
    --color-black: #18251d;
    --color-black-rgb: 24, 37, 29;
    /* 替换纯白为柔和薄荷白 */
    --color-white: #e0e8e4;
    --color-white-rgb: 224, 232, 228;

    --color-background: transparent;
    --color-background-soft: rgba(var(--color-background-base-rgb), var(--bg-opacity-low));
    --color-background-mute: rgba(var(--color-background-base-rgb), var(--bg-opacity-low));
    --navbar-background: transparent;
    --chat-background: transparent;
    --chat-background-user: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    /* 用户气泡背景 */
    --chat-background-assistant: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    /* 助手气泡背景 */
    --color-background-container: rgba(var(--color-background-base-rgb), var(--bg-opacity-medium));
    --color-code-background: rgba(var(--color-background-base-rgb), 0.6);
    --color-input-background: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    --scrollbar-track-color: rgba(var(--color-background-base-rgb), 0.1);
    /* 滚动条轨道 */
    --scrollbar-thumb-color: rgba(var(--accent-color-rgb), 0.4);
    /* 滚动条滑块 */
    --scrollbar-thumb-hover-color: rgba(var(--accent-color-rgb), 0.6);
    /* 滚动条滑块悬停 */


    /* 渐变 (暗色模式) */
    --gradient-primary: linear-gradient(135deg, var(--color-mint-dark-1) 0%, var(--color-mint-dark-2) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-mint-dark-2) 0%, var(--color-mint-dark-3) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color-secondary) 0%, var(--accent-color) 100%);
    --gradient-neutral: linear-gradient(135deg, var(--color-bean-dark-1) 0%, var(--color-bean-dark-2) 100%);

    /* 纹理 (暗色模式) */
    --paper-texture-dark: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2355856d' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    --bubble-texture-dark: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23556357' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    --message-block-texture-dark: var(--bubble-texture-dark);
    --reasoning-texture-dark: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2370a68a' fill-opacity='0.06' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

    /* 字号 */
    --font-size-base: 1.0rem;
    --font-size-dialogue: 0.95rem;
    --font-size-title: 1.25rem;
    --font-size-reasoning: 0.9rem;
    --font-size-reasoning-icon: 1.0rem;

    /* 特定组件变量 */
    --chat-customize-collapse-background: var(--color-background-soft);
    --chat-text-user: var(--ink-color);
    --color-text-1: var(--ink-color);
    --color-text-2: var(--ink-color-secondary);
    --chat-customize-codeHeader: var(--ink-color-secondary);
    /* 代码块头部文本颜色 */
    --color-black: #18251d;
    --color-black-rgb: 24, 37, 29;
    --color-white: #e0e8e4;
    --color-white-rgb: 224, 232, 228;

    --chat-customize-box-shadow: 0 4px 12px var(--shadow-color);
    --chat-customize-box-shadow2: none;
    --chat-customize-box-shadow3: inset 0 1px 3px rgba(0, 0, 0, 0.25);
    --chat-customize-box-shadow4: inset 0 0 0 1px rgba(var(--accent-color-rgb), .3);

    /* 红绿灯颜色 - 使用主题调色板 */
    --traffic-light-1: var(--color-mint-dark-6);
    /* 绿 */
    --traffic-light-2: var(--color-mint-dark-5);
    /* 深绿 */
    --traffic-light-3: var(--color-mint-dark-4);
    /* 最深绿 */

    /* --- 亮色模式主题 --- */
    --color-mint-light-1: #f0f7f2;
    --color-mint-light-2: #e0f0e5;
    --color-mint-light-3: #c8e6d0;
    --color-mint-light-4: #aeddc0;
    --color-mint-light-5: #8dcbad;
    --color-mint-light-6: #6abf97;
    --color-bean-light-1: #eaf0ea;
    --color-bean-light-2: #dde5dd;
    --color-bean-light-3: #cdd8cd;
    --color-mint-light-6: #6abf97;
    --color-mint-light-5: #8dcbad;
    --color-mint-light-4: #aeddc0;
    --color-mint-light-3: #c8e6d0;
    --color-mint-light-2: #e0f0e5;
    --color-mint-light-1: #f0f7f2;

    /* --- 亮色模式主题 --- */
    --paper-color: var(--color-mint-light-1);
    --ink-color: #2a4032;
    --ink-color-secondary: #506a5a;
    --accent-color: var(--color-mint-light-6);
    --accent-color-rgb: 106, 191, 151;
    --accent-color-secondary: var(--color-mint-light-5);
    --accent-color-tertiary: var(--color-bean-light-3);

    --border-color: rgba(var(--accent-color-rgb), 0.4);
    --shadow-color: rgba(80, 120, 95, 0.15);
    --dialogue-underline-color: var(--accent-color-secondary);
    --dialogue-bold-underline-color: var(--accent-color);
    --italics-text-color: var(--ink-color-secondary);

    /* UI 元素背景 (亮色模式) */
    --color-background-base-rgb: 240, 247, 242;
    --bg-opacity-low: 0.85;
    --bg-opacity-medium: 0.92;
    --bg-opacity-high: 0.96;

    --color-background-soft: rgba(var(--color-background-base-rgb), var(--bg-opacity-low));
    --color-background-mute: rgba(var(--color-background-base-rgb), var(--bg-opacity-low));
    --chat-background-user: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    --chat-background-assistant: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    --color-background-container: rgba(var(--color-background-base-rgb), var(--bg-opacity-medium));
    --color-code-background: rgba(var(--color-background-base-rgb), 0.9);
    --color-input-background: rgba(var(--color-background-base-rgb), var(--bg-opacity-high));
    --scrollbar-track-color: rgba(var(--color-background-base-rgb), 0.3);
    /* 较浅的轨道 */
    --scrollbar-thumb-color: rgba(var(--accent-color-rgb), 0.4);
    --scrollbar-thumb-hover-color: rgba(var(--accent-color-rgb), 0.6);

    /* 渐变 (亮色模式) */
    --gradient-primary: linear-gradient(135deg, var(--color-mint-light-1) 0%, var(--color-mint-light-2) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-mint-light-2) 0%, var(--color-mint-light-3) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color-secondary) 0%, var(--accent-color) 100%);
    --gradient-neutral: linear-gradient(135deg, var(--color-bean-light-1) 0%, var(--color-bean-light-2) 100%);

    /* 纹理 (亮色模式) - 为提高可见性已修改，基于用户反馈增加了 fill-opacity */
    --paper-texture-light: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23aeddc0' fill-opacity='0.6' fill-rule='evenodd'/%3E%3C/svg%3E");
    --bubble-texture-light: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23c8e6d0' fill-opacity='0.3'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    --message-block-texture-light: var(--bubble-texture-light);
    --reasoning-texture-light: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238dcbad' fill-opacity='0.2' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

    /* 文本及其他 */
    --chat-text-user: var(--ink-color);
    --color-text-1: var(--ink-color);
    --color-text-2: var(--ink-color-secondary);
    --chat-customize-codeHeader: var(--ink-color);
    /* 代码块头部文本颜色 (亮色模式) */
    --color-black: #2a4032;
    --color-black-rgb: 42, 64, 50;
    --color-white: #f8faf9;
    --color-white-rgb: 248, 250, 249;

    /* 红绿灯颜色 - 亮色模式 */
    --traffic-light-1: var(--color-mint-light-5);
    --traffic-light-2: var(--color-mint-light-4);
    --traffic-light-3: var(--color-mint-light-3);

    /* 应用亮色模式背景 */
    background-color: var(--paper-color);
    background-image: var(--paper-texture-light);
    background-attachment: fixed;
    background-size: 100px 100px;
    background-position: center center;
    background-repeat: repeat;

    /* --- 亮色模式主题 --- */
    --material-tooltip-background-color: var(--color-mint-light-1);
    --material-tooltip-border-color: var(--color-mint-light-2);
}

/* --- 全局基础样式 --- */
body {
    box-sizing: border-box;
    background-color: var(--paper-color);
    background-image: var(--paper-texture-dark);
    background-attachment: fixed;
    background-size: 100px 100px;
    background-position: center center;
    background-repeat: repeat;
    font-family: 'LXGW WenKai Screen', sans-serif !important;
    font-weight: 400;
    line-height: 1.7;
    color: var(--ink-color);
    letter-spacing: normal;
    transition: background var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease);
}

/* --- 滚动条样式 --- */
::-webkit-scrollbar {
    width: var(--scrollbar-width);
    height: var(--scrollbar-width);
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
    border-radius: calc(var(--scrollbar-width) / 2);
    margin: 2px;
}

::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: calc(var(--scrollbar-width) / 2);
    border: 2px solid transparent;
    background-clip: content-box;
    transition: background-color var(--duration-fast) ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover-color);
}

* {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
}


/* --- 字体栈 --- */
.ant-modal-content,
.ant-popover-inner,
div[class^="InputContainer-"],
div[class^="OutputContainer-"],
div[class^="HistoryContainner-"],
.ant-notification-notice,
.ant-message-notice-content,
.ant-drawer-content,
.ant-modal-body .ant-input-affix-wrapper input,
.ant-segmented-group .ant-segmented-item-label,
.ant-btn,
.ant-dropdown-trigger,
.ant-modal-header .ant-modal-title,
.ant-collapse-header,
li[class^="MenuItem-"],
#content-container [class^="ListItemContainer-"],
div[class^="SettingGroup-"] label,
.ant-tooltip-inner,
.markdown th,
.ant-table-thead>tr>th,
/* .markdown pre [class^="CodeHeader-"], -- 已有特定规则 */
#app-sidebar,
.home-sidebar {
    font-family: "Noto Sans SC", sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: .02em;
    line-height: 1.5;
}

.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
    font-family: "Audiowide", sans-serif !important;
    font-weight: 700 !important;
    letter-spacing: .05em;
}

.bubble .message-content-container,
.inputbar-container textarea,
.ant-modal .ant-modal-body,
.ant-table-tbody>tr>td,
.markdown blockquote,
.markdown table,
.markdown p,
.markdown li,
.markdown strong,
.markdown b,
.markdown em,
.markdown i,
.markdown q,
.markdown u {
    font-family: "LXGW WenKai Screen", serif !important;
    letter-spacing: normal !important;
    line-height: 1.7 !important;
    font-size: var(--font-size-base) !important;
    font-weight: 400 !important;
}

code,
pre,
.markdown pre [class^="CodeContent-"] * {
    font-family: 'JetBrains Mono', monospace !important;
    font-weight: 400 !important;
    letter-spacing: normal !important;
    line-height: 1.5 !important;
    font-size: 0.95em !important;
}


/* == 全局组件样式 == */
/* --- 通用容器 --- */
.inputbar-container,
.ant-popover-inner,
div[class^="InputContainer-"],
div[class^="OutputContainer-"],
div[class^="HistoryContainner-"],
.ant-drawer-content,
.ant-modal .ant-modal-content,
div[class^="AgentCardContainer-"],
.ant-table-wrapper,
.ant-collapse-item {
    border-radius: var(--container-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow);
    background: var(--color-background-container) !important;
    border: var(--material-line-width) solid var(--border-color);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: background-color var(--duration-normal) var(--easing-ease), box-shadow var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease);
}

.ant-popover-inner,
.ant-tooltip {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* --- 对话气泡 --- */
.bubble .message-content-container {
    border-radius: var(--message-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow);
    border: none !important;
    position: relative;
    overflow: hidden;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    transition: background-color var(--duration-normal) var(--easing-ease), box-shadow var(--duration-normal) var(--easing-ease);
    background: var(--chat-background-assistant) var(--bubble-texture-dark) repeat !important;
    background-size: auto, 60px 60px !important;

    &::before,
    &::after {
        display: none;
    }
}

.bubble.user .message-content-container {
    background: var(--chat-background-user) var(--bubble-texture-dark) repeat !important;
    background-size: auto, 60px 60px !important;
}

body[theme-mode="light"] .bubble .message-content-container {
    background: var(--chat-background-assistant) var(--bubble-texture-light) repeat !important;
    background-size: auto, 60px 60px !important;
}

body[theme-mode="light"] .bubble.user .message-content-container {
    background: var(--chat-background-user) var(--bubble-texture-light) repeat !important;
    background-size: auto, 60px 60px !important;
}

.bubble .message-content-container .markdown {
    padding: var(--message-content-padding) !important;
    margin: 0 !important;
    background: transparent !important;
    color: var(--ink-color);
}

.bubble .message-content-container .markdown>p:first-child {
    margin-top: 0;
}

.bubble .message-content-container .markdown>p:last-child {
    margin-bottom: 0;
}

/* --- 输入栏 --- */
.inputbar-container {
    backdrop-filter: none !important;
    /* 通常通用容器有 backdrop-filter，这里特定移除 */
    background: var(--color-input-background) !important;
    /* 修正: 增加内边距为 textarea 的 focus shadow 提供空间 */
    padding: var(--spacing-xs) !important;
    /* 确保其作为通用容器的样式依然有效 (如 border-radius, border, box-shadow) */
    border-radius: var(--container-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow) !important;
    border: var(--material-line-width) solid var(--border-color) !important;
    /* overflow: hidden; /* 如果 padding 不足或阴影过大，可以重新启用 */
    margin-top: var(--spacing-sm) !important;
    /* 增加与上方元素的间距 */
    margin-bottom: var(--spacing-sm) !important;
    /* 增加与下方元素的间距 */
    transition: border-color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease);
    /* 添加过渡 */
}

.inputbar-container textarea {
    resize: none;
    display: block;
    width: 100%;
    color: var(--color-text-1) !important;
    background-color: transparent !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-base) !important;
    line-height: 1.6 !important;
    border: none !important;
    box-shadow: none !important;
}

/* 输入栏按钮 */
.inputbar-container .ant-btn {
    color: var(--ink-color-secondary) !important;
    background-color: rgba(var(--color-background-base-rgb), 0.2) !important;
    border-color: transparent !important;
    box-shadow: none !important;
    border-width: 0 !important;
    border-radius: var(--button-border-radius) !important;
    transition: background-color var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease);
}

.inputbar-container .ant-btn .anticon,
.inputbar-container .ant-btn .iconfont {
    color: var(--ink-color-secondary) !important;
    transition: color var(--duration-fast) var(--easing-ease);
}

.inputbar-container .ant-btn:hover {
    background-color: rgba(var(--accent-color-rgb), 0.2) !important;
    color: var(--accent-color) !important;
}

.inputbar-container .ant-btn:hover .anticon,
.inputbar-container .ant-btn:hover .iconfont {
    color: var(--accent-color) !important;
}

/* 修正: 输入栏容器聚焦时，改变边框和阴影 */
.inputbar-container:has(textarea:focus) {
    border-color: var(--accent-color) !important;
    box-shadow: var(--chat-customize-box-shadow), 0 0 0 2px rgba(var(--accent-color-rgb), 0.3) !important;
}


/* --- 通用按钮样式 (修复设置页面问题) --- */
.ant-btn:not(.inputbar-container .ant-btn) {
    border-radius: var(--button-border-radius) !important;
    border: var(--material-line-width) solid var(--border-color) !important;
    background-color: var(--color-background-soft) !important;
    color: var(--color-text-1) !important;
    box-shadow: var(--chat-customize-box-shadow3) !important;
    transition: background-color var(--duration-fast) var(--easing-ease), border-color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease), transform var(--duration-fast) var(--easing-ease);
}

.ant-btn:not(.inputbar-container .ant-btn):hover,
.ant-btn:not(.inputbar-container .ant-btn):focus {
    background-color: var(--color-background-container) !important;
    border-color: var(--accent-color-secondary) !important;
    color: var(--accent-color) !important;
    box-shadow: var(--chat-customize-box-shadow) !important;
    transform: translateY(-1px);
}

.ant-btn:not(.inputbar-container .ant-btn):active {
    background-color: rgba(var(--color-background-base-rgb), 0.9) !important;
    border-color: var(--accent-color) !important;
    box-shadow: var(--chat-customize-box-shadow3) !important;
    transform: translateY(0);
}

.ant-btn-primary:not(.inputbar-container .ant-btn) {
    background-color: var(--accent-color) !important;
    background-image: linear-gradient(135deg, var(--accent-color-secondary) 0%, var(--accent-color) 100%) !important;
    border-color: var(--accent-color) !important;
    color: var(--color-white) !important;
    box-shadow: var(--chat-customize-box-shadow), 0 2px 6px rgba(var(--accent-color-rgb), 0.3);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.ant-btn-primary:not(.inputbar-container .ant-btn):hover,
.ant-btn-primary:not(.inputbar-container .ant-btn):focus {
    background-color: var(--accent-color-secondary) !important;
    background-image: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-color-secondary) 100%) !important;
    border-color: var(--accent-color-secondary) !important;
    color: var(--color-white) !important;
    box-shadow: var(--chat-customize-box-shadow), 0 4px 10px rgba(var(--accent-color-rgb), 0.4);
    transform: translateY(-1px);
}

.ant-btn-primary:not(.inputbar-container .ant-btn):active {
    background-color: var(--accent-color) !important;
    background-image: none !important;
    border-color: var(--accent-color) !important;
    box-shadow: var(--chat-customize-box-shadow3), 0 1px 3px rgba(var(--accent-color-rgb), 0.3);
    transform: translateY(0);
}


/* --- 模态框 --- */
.ant-modal .ant-modal-content {
    background: var(--color-background-container) !important;
    border-color: var(--border-color);
    color: var(--color-text-1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.ant-modal-header {
    background-color: transparent !important;
    border-bottom: var(--material-line-width) solid var(--border-color);
    border-radius: var(--container-border-radius) var(--container-border-radius) 0 0 !important;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--color-text-1) !important;
    transition: border-color var(--duration-normal) var(--easing-ease);
}

.ant-modal-title {
    color: var(--color-text-1) !important;
}

.ant-modal-header::after {
    content: "";
    position: absolute;
    bottom: calc(-1 * var(--material-line-width));
    right: 0;
    width: 30%;
    height: var(--material-line-width);
    background: linear-gradient(90deg, transparent, var(--accent-color));
    opacity: .7;
    z-index: 1;
    transition: background var(--duration-normal) var(--easing-ease);
}

/* --- 列表项 / 菜单项 --- */
li[class^="MenuItem-"],
#content-container [class^="ListItemContainer-"] {
    border: 0 !important;
    border-radius: var(--list-item-border-radius) !important;
    color: var(--color-text-2);
    background-color: transparent !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    transition: background-color var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease);
    position: relative;
    overflow: hidden;
}

li[class^="MenuItem-"].active,
#content-container [class^="ListItemContainer-"].active {
    background: rgba(var(--accent-color-rgb), 0.15) !important;
    color: var(--color-text-1);
    box-shadow: var(--chat-customize-box-shadow4) !important;
}

li[class^="MenuItem-"].active::before,
#content-container [class^="ListItemContainer-"].active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 20%;
    bottom: 20%;
    width: 3px;
    background-color: var(--accent-color);
    border-radius: 0 3px 3px 0;
}

li[class^="MenuItem-"]:hover:not(.active),
#content-container [class^="ListItemContainer-"]:hover:not(.active) {
    background-color: rgba(var(--color-background-base-rgb), 0.2) !important;
    color: var(--color-text-1);
}


/* --- 分段控制器 (修复黑框问题) --- */
.ant-segmented-group .ant-segmented-item {
    border: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
}

.ant-segmented-group .ant-segmented-item-label {
    border: none !important;
    border-radius: var(--list-item-border-radius) !important;
    color: var(--color-text-2);
    background-color: transparent !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    transition: background-color var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease);
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 0 var(--material-line-width) var(--border-color) !important;
}

.ant-segmented-group .ant-segmented-item-label[aria-selected="true"] {
    background: rgba(var(--accent-color-rgb), 0.15) !important;
    color: var(--color-text-1);
    box-shadow: inset 0 0 0 var(--material-line-width) var(--accent-color-secondary) !important;
    border: none !important;
}

.ant-segmented-group .ant-segmented-item-label:not([aria-selected="true"]):hover {
    background-color: rgba(var(--color-background-base-rgb), 0.2) !important;
    color: var(--color-text-1);
    border: none !important;
    box-shadow: inset 0 0 0 var(--material-line-width) var(--border-color) !important;
}

.ant-segmented {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}


/* --- 输入框 (通用与模态框内) - 包括下拉菜单 (修复黑框问题) --- */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="time"],
textarea:not(.inputbar-container textarea),
select {
    background-color: var(--color-input-background) !important;
    box-shadow: none !important;
    border: var(--material-line-width) solid var(--border-color) !important;
    border-radius: var(--list-item-border-radius) !important;
    color: var(--color-text-1) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-base) !important;
    font-family: 'LXGW WenKai Screen', serif !important;
    min-height: 2.5rem !important;
    transition: background-color var(--duration-fast) var(--easing-ease), border-color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease);
}

.ant-input-affix-wrapper,
.ant-select .ant-select-selector {
    background-color: var(--color-input-background) !important;
    box-shadow: none !important;
    border: var(--material-line-width) solid var(--border-color) !important;
    border-radius: var(--list-item-border-radius) !important;
    color: var(--color-text-1) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    min-height: 2.5rem !important;
    transition: background-color var(--duration-fast) var(--easing-ease), border-color var(--duration-fast) var(--easing-ease), box-shadow var(--duration-fast) var(--easing-ease);
}

input:focus,
textarea:not(.inputbar-container textarea):focus,
select:focus,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector {
    background-color: var(--color-input-background) !important;
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.3) !important;
    border-color: var(--accent-color) !important;
    outline: none !important;
    /* 增加聚焦时的视觉反馈 */
    transform: translateY(-1px);
}

.ant-input-affix-wrapper input {
    background: transparent !important;
    color: var(--color-text-1) !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    height: auto;
    min-height: auto;
}

.ant-select-single .ant-select-selector .ant-select-selection-item {
    color: var(--color-text-1) !important;
}

.ant-select-arrow {
    color: var(--color-text-2) !important;
}


/* --- Markdown 内容样式 --- */
.markdown {
    color: var(--ink-color);
}

.markdown h1 {
    font-size: calc(var(--font-size-title) * 1.2);
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: .4em;
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    color: var(--accent-color) !important;
    transition: border-bottom-color var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease);
    position: relative;
}

.markdown h1::after {
    content: "";
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 30%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color));
    opacity: .6;
    z-index: 1;
    transition: background var(--duration-normal) var(--easing-ease);
}

.markdown h2 {
    font-size: var(--font-size-title);
    border-left: 4px solid var(--accent-color-secondary);
    padding-left: var(--spacing-sm);
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    color: var(--accent-color-secondary) !important;
    transition: border-left-color var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease);
}

.markdown h3 {
    font-size: calc(var(--font-size-title) * 0.9);
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    color: var(--accent-color-tertiary) !important;
    display: inline-block;
    background-color: rgba(var(--color-background-base-rgb), .1);
    padding: .2em .5em;
    border-radius: 4px;
    line-height: 1.5;
    transition: background-color var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease);
}

.markdown h4,
.markdown h5,
.markdown h6 {
    color: var(--color-text-1) !important;
    letter-spacing: normal;
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
    font-weight: 600;
    transition: color var(--duration-normal) var(--easing-ease);
}

.markdown h4 {
    font-size: 1.1em;
}

.markdown h5 {
    font-size: 1em;
}

.markdown h6 {
    font-size: .9em;
    opacity: 0.8;
}

.markdown blockquote {
    padding: var(--spacing-md) var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    background-color: rgba(var(--color-background-base-rgb), 0.15);
    background-image: var(--reasoning-texture-dark), linear-gradient(to right, rgba(var(--color-background-base-rgb), 0.15), rgba(var(--color-background-base-rgb), 0.1));
    background-size: auto, 40px 40px;
    background-repeat: no-repeat, repeat;
    font-style: italic;
    color: var(--color-text-2);
    border: var(--material-line-width) solid var(--border-color);
    border-left: 4px solid var(--accent-color-tertiary);
    border-radius: var(--reasoning-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow3), 0 1px 3px rgba(var(--color-black-rgb), 0.08);
    overflow: hidden;
    font-size: var(--font-size-reasoning);
    line-height: 1.6;
    transition: background-color var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease), box-shadow var(--duration-normal) var(--easing-ease);
}

body[theme-mode="light"] .markdown blockquote {
    background-image: var(--reasoning-texture-light), linear-gradient(to right, rgba(var(--color-background-base-rgb), 0.15), rgba(var(--color-background-base-rgb), 0.1));
}

.markdown strong,
.markdown b {
    font-weight: 700 !important;
    color: var(--accent-color);
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    line-height: inherit !important;
    text-decoration: none !important;
}

.markdown em,
.markdown i {
    font-style: italic;
    color: var(--italics-text-color);
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    border: none !important;
    line-height: inherit !important;
    text-decoration: none !important;
}

.bubble .message-content-container .markdown strong,
.bubble .message-content-container .markdown b {
    text-decoration-line: underline !important;
    text-decoration-color: var(--dialogue-bold-underline-color) !important;
    text-decoration-thickness: 1.5px !important;
    text-decoration-style: solid !important;
    text-underline-offset: 3px !important;
    background-color: transparent !important;
    color: var(--accent-color);
}

.markdown ul,
.markdown ol {
    padding-left: var(--spacing-lg);
    margin: var(--spacing-md) 0;
}

.markdown li {
    margin-bottom: var(--spacing-xs);
}

.markdown ul li::marker,
.markdown ol li::marker {
    color: var(--accent-color-secondary);
    transition: color var(--duration-fast) var(--easing-ease);
}

.markdown hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: var(--spacing-lg) 0;
    opacity: 0.8;
    position: relative;
    transition: background var(--duration-normal) var(--easing-ease);
}

.markdown hr::after {
    display: none;
}

a {
    color: var(--accent-color) !important;
    text-decoration: none;
    position: relative;
    transition: color var(--duration-fast) var(--easing-ease), opacity var(--duration-fast) var(--easing-ease);
    opacity: 0.9;
    font-weight: 500;
}

/* 增加白底下的链接对比度 */
body[theme-mode="light"] a {
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-color: rgba(var(--accent-color-rgb), 0.3);
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
}

a:hover {
    text-decoration: underline;
    text-decoration-color: var(--accent-color);
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    opacity: 1;
    color: var(--accent-color) !important;
}

.markdown q {
    font-family: "LXGW WenKai Screen", serif !important;
    font-style: normal !important;
    font-size: var(--font-size-dialogue) !important;
    border-bottom: 1.5px solid var(--dialogue-underline-color);
    padding-bottom: 1px;
    display: inline;
    line-height: 1.6 !important;
    color: var(--ink-color) !important;
    quotes: """""'""'";
}

.markdown q::before {
    content: open-quote;
}

.markdown q::after {
    content: close-quote;
}

.markdown u {
    text-decoration: underline;
    text-decoration-color: var(--dialogue-underline-color);
    text-decoration-thickness: 1.5px;
    text-underline-offset: 2px;
    color: inherit;
}

/* --- 代码块 --- */
.markdown pre {
    margin: var(--spacing-md) 0;
}

.markdown pre [class^="CodeBlockWrapper-"] {
    border-radius: var(--list-item-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow3), 0 2px 8px rgba(var(--color-black-rgb), 0.1) !important;
    border: var(--material-line-width) solid var(--border-color);
    position: relative;
    background-color: transparent !important;
    background-image: none !important;
    overflow: hidden !important;
    transition: box-shadow var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease);
}

/* 头部 */
.markdown pre [class^="CodeHeader-"] {
    border-radius: 0 !important;
    background-color: rgba(var(--accent-color-rgb), 0.2) !important;
    /* 暗色模式头部背景 */
    background-image: none !important;
    border-bottom: var(--material-line-width) solid var(--border-color);
    margin-bottom: 0 !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--chat-customize-codeHeader);
    padding: var(--spacing-xs) var(--spacing-md) !important;
    position: relative;
    overflow: hidden;
    padding-left: 65px;
    /* 为红绿灯留空间 */
    transition: background-color var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease);
    border-top-left-radius: var(--list-item-border-radius) !important;
    border-top-right-radius: var(--list-item-border-radius) !important;
}

body[theme-mode="light"] .markdown pre [class^="CodeHeader-"] {
    background-color: rgba(var(--accent-color-rgb), 0.15) !important;
    /* 亮色模式头部背景 */
    color: var(--chat-customize-codeHeader) !important;
    /* 确保文本颜色使用亮色模式的变量 */
}

/* 红绿灯 */
.markdown pre [class^="CodeHeader-"]::before {
    content: ' ';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: var(--spacing-md);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--traffic-light-1);
    box-shadow: 20px 0 var(--traffic-light-2), 40px 0 var(--traffic-light-3);
    z-index: 1;
}

.markdown pre [class^="CodeHeader-"]::after {
    display: none;
}

/* 代码语言显示元素样式 */
.markdown pre [class^="CodeHeader-"]>*:first-child {
    /* 通常是语言名称 */
    flex-grow: 1;
    text-align: center;
    background-color: transparent !important;
    /* 继承头部背景 */
    color: var(--accent-color) !important;
    /* 语言文本仍用强调色 */
    padding: 2px var(--spacing-sm);
    border-radius: var(--button-border-radius);
    /* margin-left: var(--spacing-md); 移到flex-grow和text-align:center后，这里不需要强制margin */
    font-weight: 600;
    transition: color var(--duration-fast) var(--easing-ease);
    flex-shrink: 0;
    /* 防止过度压缩 */
    /* 增加字体可见度 */
    letter-spacing: 0.02em;
}

/* 代码块复制按钮等图标/按钮 - 修正: 隐藏它们 */
.markdown pre [class^="CodeHeader-"] button,
.markdown pre [class^="CodeHeader-"] span[class*="icon"],
.markdown pre [class^="CodeHeader-"] .anticon,
.markdown pre [class^="CodeHeader-"] [class*="copy"] {
    display: none !important;
}

/* 内容区 */
.markdown pre [class^="CodeContent-"] {
    background-color: var(--color-code-background) !important;
    border-radius: 0 !important;
    border-top: none !important;
    margin-top: 0 !important;
    padding: var(--spacing-md) !important;
    overflow-x: auto !important;
    border-bottom-left-radius: var(--list-item-border-radius) !important;
    border-bottom-right-radius: var(--list-item-border-radius) !important;
}

/* --- 表格 --- */
.markdown table {
    border-radius: var(--list-item-border-radius) !important;
    box-shadow: var(--chat-customize-box-shadow3), 0 2px 8px rgba(var(--color-black-rgb), 0.08) !important;
    border: var(--material-line-width) solid var(--border-color);
    position: relative;
    background-color: transparent !important;
    background-image: none !important;
    overflow: hidden !important;
    margin: var(--spacing-lg) 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    transition: background-color var(--duration-normal) var(--easing-ease), box-shadow var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease);
}

.markdown th,
.ant-table-thead>tr>th {
    background-color: rgba(var(--accent-color-rgb), 0.1) !important;
    border-bottom: var(--material-line-width) solid var(--border-color) !important;
    color: var(--color-text-1);
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-left: var(--material-line-width) solid var(--border-color);
    text-align: left;
    font-weight: 600 !important;
    transition: background-color var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease);

    &:first-child {
        border-left: none;
        border-top-left-radius: var(--list-item-border-radius);
    }

    &:last-child {
        border-top-right-radius: var(--list-item-border-radius);
    }
}

.markdown td,
.ant-table-tbody>tr>td {
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-bottom: var(--material-line-width) solid var(--border-color) !important;
    border-left: var(--material-line-width) solid var(--border-color);
    color: var(--ink-color);
    background-color: var(--color-code-background);
    transition: border-color var(--duration-normal) var(--easing-ease), color var(--duration-normal) var(--easing-ease), background-color var(--duration-normal) var(--easing-ease);

    &:first-child {
        border-left: none;
    }
}

.markdown tr:last-child td,
.ant-table-tbody>tr:last-child>td {
    border-bottom: none !important;
}

.markdown tr:last-child td:first-child {
    border-bottom-left-radius: var(--list-item-border-radius);
}

.markdown tr:last-child td:last-child {
    border-bottom-right-radius: var(--list-item-border-radius);
}

.ant-table-tbody>tr:last-child>td:first-child {
    border-bottom-left-radius: var(--list-item-border-radius);
}

.ant-table-tbody>tr:last-child>td:last-child {
    border-bottom-right-radius: var(--list-item-border-radius);
}

.markdown tr:nth-child(even) td,
.ant-table-tbody>tr:nth-child(even)>td {
    background-color: rgba(var(--color-background-base-rgb), 0.4);
}

.markdown tr:hover td,
.ant-table-tbody>tr:hover>td {
    background-color: rgba(var(--accent-color-rgb), 0.05) !important;
}

/* --- 头像与消息信息 --- */
.mesAvatarWrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--avatar-bottom-margin);
    padding: var(--spacing-xs) 0;
}

.avatar,
.user_avatar {
    position: relative !important;
    display: block !important;
    width: var(--avatar-size) !important;
    height: var(--avatar-size) !important;
    min-width: var(--avatar-size) !important;
    min-height: var(--avatar-size) !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    background-size: cover !important;
    background-position: center !important;
    box-shadow: 0 2px 6px var(--shadow-color);
    transition: transform var(--duration-fast) ease, box-shadow var(--duration-fast) ease;
    border: var(--avatar-border) solid rgba(var(--color-background-base-rgb), 0.5) !important;
    margin-bottom: var(--spacing-xs);
}

body[theme-mode="light"] .avatar,
body[theme-mode="light"] .user_avatar {
    border-color: rgba(var(--color-background-base-rgb), 0.8) !important;
}

.avatar:hover,
.user_avatar:hover {
    transform: scale(1.08);
    box-shadow: 0 4px 10px var(--shadow-color);
}

.avatar img,
.user_avatar img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 50% !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
}

.mesAvatarWrapper .mesIDDisplay,
.mesAvatarWrapper .mes_timer,
.mesAvatarWrapper .tokenCounterDisplay {
    display: inline-block;
    font-size: 0.75em;
    margin: 0 var(--spacing-xs);
    color: var(--color-text-2);
    opacity: 0.8;
    font-family: 'Noto Sans SC', sans-serif;
    background-color: rgba(var(--color-background-base-rgb), 0.2);
    padding: 1px var(--spacing-xs);
    border-radius: 4px;
    transition: opacity var(--duration-fast) ease, background-color var(--duration-fast) ease;
    border: none;
}

body[theme-mode="light"] .mesAvatarWrapper .mesIDDisplay,
body[theme-mode="light"] .mesAvatarWrapper .mes_timer,
body[theme-mode="light"] .mesAvatarWrapper .tokenCounterDisplay {
    background-color: rgba(var(--color-background-base-rgb), 0.4) !important;
    color: var(--color-text-2) !important;
}

/* --- 表单控件 (复选框, 单选框, 开关) --- */
.ant-switch {
    transition: background-color var(--duration-fast) var(--easing-ease);
}

.ant-switch-checked {
    background-color: var(--accent-color) !important;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
    transition: background-color var(--duration-fast) var(--easing-ease), border-color var(--duration-fast) var(--easing-ease);
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus+.ant-checkbox-inner {
    border-color: var(--accent-color) !important;
}

.ant-checkbox-checked::after {
    border-color: var(--color-white) !important;
}

/* 修复：应为 var(--color-white) */
body[theme-mode="light"] .ant-checkbox-checked::after {
    border-color: var(--color-white) !important;
}

/* 亮色模式下复选框勾号也应为白色以对比强调色背景 */
.ant-radio-checked .ant-radio-inner {
    border-color: var(--accent-color) !important;
    transition: border-color var(--duration-fast) var(--easing-ease);
}

.ant-radio-inner::after {
    background-color: var(--accent-color) !important;
    transition: background-color var(--duration-fast) var(--easing-ease);
}

.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus+.ant-radio-inner {
    border-color: var(--accent-color) !important;
}

/* --- 选中文本高亮 --- */
::selection {
    background-color: rgba(var(--accent-color-rgb), 0.3);
    color: var(--color-white);
    text-shadow: 0 1px 1px rgba(var(--color-black-rgb), 0.2);
}

::-moz-selection {
    background-color: rgba(var(--accent-color-rgb), 0.3);
    color: var(--color-white);
    text-shadow: 0 1px 1px rgba(var(--color-black-rgb), 0.2);
}

body[theme-mode="light"] ::selection {
    background-color: rgba(var(--accent-color-rgb), 0.4);
    color: var(--color-black);
    text-shadow: none;
}

body[theme-mode="light"] ::-moz-selection {
    background-color: rgba(var(--accent-color-rgb), 0.4);
    color: var(--color-black);
    text-shadow: none;
}

/* --- 工具提示 --- */
.ant-tooltip-inner {
    background-color: var(--material-tooltip-background-color) !important;
    color: var(--color-text-1) !important;
    border: var(--material-line-width) solid var(--border-color);
    box-shadow: none !important; /* 移除阴影以避免分层 */
    border-radius: var(--button-border-radius) !important;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.9em;
}

/* Specific border adjustments for .ant-tooltip-inner based on placement */
/* When tooltip is on top, arrow points down, so hide bottom border of inner */
.ant-tooltip-placement-top .ant-tooltip-inner,
.ant-tooltip-placement-topLeft .ant-tooltip-inner,
.ant-tooltip-placement-topRight .ant-tooltip-inner {
    border-bottom-color: transparent !important;
}

/* When tooltip is on bottom, arrow points up, so hide top border of inner */
.ant-tooltip-placement-bottom .ant-tooltip-inner,
.ant-tooltip-placement-bottomLeft .ant-tooltip-inner,
.ant-tooltip-placement-bottomRight .ant-tooltip-inner {
    border-top-color: transparent !important;
}

/* When tooltip is on left, arrow points right, so hide right border of inner */
.ant-tooltip-placement-left .ant-tooltip-inner,
.ant-tooltip-placement-leftTop .ant-tooltip-inner,
.ant-tooltip-placement-leftBottom .ant-tooltip-inner {
    border-right-color: transparent !important;
}

/* When tooltip is on right, arrow points left, so hide left border of inner */
.ant-tooltip-placement-right .ant-tooltip-inner,
.ant-tooltip-placement-rightTop .ant-tooltip-inner,
.ant-tooltip-placement-rightBottom .ant-tooltip-inner {
    border-left-color: transparent !important;
}


/* 修复tooltip箭头颜色和整体连贯性 */
.ant-tooltip-arrow {
    background-color: transparent !important;
    color: transparent !important;
    z-index: 2;
}

.ant-tooltip-arrow-content {
    background-color: var(--material-tooltip-background-color) !important;
    box-shadow: none !important; /* 阴影由主tooltip提供，避免重叠 */
    border: 1px solid transparent !important; /* 基础边框，调整为1px */
    border-color: var(--material-tooltip-border-color) !important;
    border-width: 1px !important; /* 确保边框宽度一致 */
}

/* 根据不同方向调整箭头的边框颜色和宽度，使其与tooltip主体视觉融合 */
/* 核心思想：箭头连接主体的一侧边框设为与主体背景色相同，外部边框显示为主题边框色。 */

/* 箭头向下 (tooltip在上方) */
.ant-tooltip-placement-top .ant-tooltip-arrow-content,
.ant-tooltip-placement-topLeft .ant-tooltip-arrow-content,
.ant-tooltip-placement-topRight .ant-tooltip-arrow-content {
    border-top-color: var(--color-input-background) !important; /* 连接主体，与主体背景色融合 */
    border-right-color: var(--border-color) !important;
    border-bottom-color: var(--border-color) !important; /* 箭头尖端 */
    border-left-color: var(--border-color) !important;
    border-width: 1px !important; /* 调整为1px */
}

/* 箭头向上 (tooltip在下方) */
.ant-tooltip-placement-bottom .ant-tooltip-arrow-content,
.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow-content,
.ant-tooltip-placement-bottomRight .ant-tooltip-arrow-content {
    border-top-color: var(--border-color) !important; /* 箭头尖端 */
    border-right-color: var(--border-color) !important;
    border-bottom-color: var(--color-input-background) !important; /* 连接主体，与主体背景色融合 */
    border-left-color: var(--border-color) !important;
    border-width: 1px !important; /* 调整为1px */
}

/* 箭头向右 (tooltip在左侧) */
.ant-tooltip-placement-left .ant-tooltip-arrow-content,
.ant-tooltip-placement-leftTop .ant-tooltip-arrow-content,
.ant-tooltip-placement-leftBottom .ant-tooltip-arrow-content {
    border-top-color: var(--border-color) !important;
    border-right-color: var(--border-color) !important; /* 箭头尖端 */
    border-bottom-color: var(--border-color) !important;
    border-left-color: var(--color-input-background) !important; /* 连接主体，与主体背景色融合 */
    border-width: 1px !important; /* 调整为1px */
}

/* 箭头向左 (tooltip在右侧) */
.ant-tooltip-placement-right .ant-tooltip-arrow-content,
.ant-tooltip-placement-rightTop .ant-tooltip-arrow-content,
.ant-tooltip-placement-rightBottom .ant-tooltip-arrow-content {
    border-top-color: var(--border-color) !important;
    border-right-color: var(--color-input-background) !important; /* 连接主体，与主体背景色融合 */
    border-bottom-color: var(--border-color) !important;
    border-left-color: var(--border-color) !important; /* 箭头尖端 */
    border-width: 1px !important; /* 调整为1px */
}


/* 调整箭头的精确位置，使其与tooltip主体无缝连接 */
/* 这些值通常需要根据实际渲染效果微调 */
.ant-tooltip-placement-top .ant-tooltip-arrow,
.ant-tooltip-placement-topLeft .ant-tooltip-arrow,
.ant-tooltip-placement-topRight .ant-tooltip-arrow {
    bottom: -1px !important; /* 调整为-1px */
    border-width: 0 !important; /* 确保边框不突出 */
}

.ant-tooltip-placement-bottom .ant-tooltip-arrow,
.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow,
.ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
    top: -1px !important; /* 调整为-1px */
    border-width: 0 !important;
}

.ant-tooltip-placement-left .ant-tooltip-arrow,
.ant-tooltip-placement-leftTop .ant-tooltip-arrow,
.ant-tooltip-placement-leftBottom .ant-tooltip-arrow {
    right: -1px !important; /* 调整为-1px */
    border-width: 0 !important;
}

.ant-tooltip-placement-right .ant-tooltip-arrow,
.ant-tooltip-placement-rightTop .ant-tooltip-arrow,
.ant-tooltip-placement-rightBottom .ant-tooltip-arrow {
    left: -1px !important; /* 调整为-1px */
    border-width: 0 !important;
}

/* --- 侧边栏 --- */
#app-sidebar {
    background-color: var(--color-background-soft) !important;
    border-right: var(--material-line-width) solid var(--border-color) !important;
    transition: background-color var(--duration-normal) var(--easing-ease), border-color var(--duration-normal) var(--easing-ease);
}

body[theme-mode="light"] #app-sidebar {
    background-color: var(--color-background-soft) !important;
    border-right-color: rgba(var(--accent-color-rgb), 0.2) !important;
}

#app-sidebar [class^="Icon-"] {
    color: var(--color-text-2);
    border-radius: var(--button-border-radius);
    transition: box-shadow var(--duration-fast) var(--easing-ease), background var(--duration-fast) var(--easing-ease), border var(--duration-fast) var(--easing-ease), color var(--duration-fast) var(--easing-ease);
}

#app-sidebar [class^="Icon-"].active {
    background: rgba(var(--accent-color-rgb), 0.15) !important;
    color: var(--accent-color) !important;
    box-shadow: none !important;
    border: none !important;
}

/* 最终可读性调整 */
.markdown p {
    margin-top: 0.5em;
    margin-bottom: 1em;
    line-height: 1.7;
}

.markdown p:first-child {
    margin-top: 0;
}

.markdown p:last-child {
    margin-bottom: 0;
}

.markdown q {
    font-family: "LXGW WenKai Screen", serif !important;
    font-style: normal !important;
    font-size: var(--font-size-dialogue) !important;
    border-bottom: 1.5px solid var(--dialogue-underline-color);
    padding-bottom: 1px;
    display: inline;
    line-height: 1.6 !important;
    color: var(--ink-color) !important;
    quotes: """""'""'";
}

/* Tooltip/Dropdown 箭头修复 */
.ant-tooltip-inner {
  background-color: var(--material-tooltip-background-color) !important;
  box-shadow: none !important; /* 移除阴影以避免分层 */
  border: var(--material-line-width) solid var(--border-color); /* 主体有边框 */
}

.ant-tooltip-arrow-content {
  background-color: var(--material-tooltip-background-color) !important;
  box-shadow: none !important;
  border: none !important; /* 箭头没有边框，只依靠背景色 */
}

/* 确保箭头与主体无缝连接 */
.ant-tooltip-placement-top .ant-tooltip-arrow {
  bottom: -1px !important; /* 微调定位 */
}

.ant-tooltip-placement-bottom .ant-tooltip-arrow {
  top: -1px !important; /* 微调定位 */
}

.ant-tooltip-placement-left .ant-tooltip-arrow {
  right: -1px !important;
}

.ant-tooltip-placement-right .ant-tooltip-arrow {
  left: -1px !important;
}