@echo off
echo.
echo ====================================================
echo Attempting to enable Windows Location Service (Registry Modification)
echo ====================================================
echo.
echo IMPORTANT: Right-click this file and select "Run as administrator".
echo Otherwise, registry modification may fail and the script may close unexpectedly.
echo.

:: --- Command 1: Modify policy setting blocking apps from accessing location ---
:: Original command: reg add ... /d "2" (Force disable location)
:: Setting to 0: "Allow apps to access location, user can control"
:: If this policy value exists and is set to 2, it overrides user settings.
:: Setting it to 0 or deleting it removes the policy restriction. Setting to 0
:: is a clear "allow user control" state.
echo Setting location policy to "Allow Apps Access" (DWORD 0)...
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AppPrivacy" /v "LetAppsAccessLocation" /t REG_DWORD /d "0" /f
IF %ERRORLEVEL% EQU 0 (
    echo Successfully set location policy registry value.
) ELSE (
    echo Error: Failed to set location policy registry value. Error code: %ERRORLEVEL%
    echo Please ensure you are running the script as administrator.
    pause
    exit /b %ERRORLEVEL%
)

echo.

:: --- Command 2: Modify user permission to access location service ---
:: Original command: reg add ... /d "Deny" (Deny user access)
:: Setting to "Allow": Allow user access to location service
echo Setting user location access permission to "Allow" (SZ "Allow")...
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\location" /v "Value" /t "REG_SZ" /d "Allow" /f
IF %ERRORLEVEL% EQU 0 (
    echo Successfully set user location permission registry value.
) ELSE (
    echo Error: Failed to set user location permission registry value. Error code: %ERRORLEVEL%
    echo Please ensure you are running the script as administrator.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ====================================================
echo Operation complete.
echo ====================================================
echo Location service should now be enabled or controllable by the user.
echo You may need to verify that the location switch is turned on in Windows Settings.
echo Some changes may require a computer restart to take full effect.
echo.

pause