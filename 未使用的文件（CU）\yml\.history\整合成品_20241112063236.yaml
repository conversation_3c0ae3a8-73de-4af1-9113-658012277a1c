# true 是启用
# false 是禁用

# 分组
pr:
  &pr {
    type: select,
    proxies:
      [
        节点选择,
        香港,
        台湾,
        日本,
        新加坡,
        美国,
        英国,
        德国,
        法国,
        加拿大,
        韩国,
        澳大利亚,
        其它地区,
        全部节点,
        自动选择,
        DIRECT,
      ],
  }
# 延迟检测 URL
p: &p
  type: http
  # 自动更新订阅时间，单位为秒
  interval: 3600
  health-check:
    enable: true
    url: https://cp.cloudflare.com
    # 节点连通性检测时间，单位为秒
    interval: 300
    # 节点超时延迟，单位为毫秒
    timeout: 1000
    # 节点自动切换差值，单位为毫秒
    tolerance: 100

use: &use
  # 如果不希望自动切换请将下面两行注释对调
  # type: select
  type: url-test
  use:
    - 整合成品
    # - 本地配置

# 订阅链接
# 对于订阅来说，path 为选填项，但建议启用
# 本地配置可以只填 path
proxy-providers:
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
  整合成品:
    <<: *p
    # path: ./proxy_provider/整合成品.yaml
    url: "https://ghp.ci/https://gist.githubusercontent.com/mariahlamb/5fb527eca1bd136a622a096c59331c28/raw/groupyaggtg"
# 规则订阅
rule-providers:
  # 秋风广告拦截规则
  # https://awavenue.top
  # 由于 Anti-AD 误杀率高，本项目已在 1.11-241024 版本更换秋风广告规则
  AWAvenue-Ads:
    type: http
    behavior: domain
    format: yaml
    # path可为空(仅限clash.meta 1.15.0以上版本)
    path: ./rule_provider/AWAvenue-Ads.yaml
    url: "https://ghp.ci/https://raw.githubusercontent.com/TG-Twilight/AWAvenue-Ads-Rule/refs/heads/main/Filters/AWAvenue-Ads-Rule-Clash.yaml"
    interval: 600

mode: rule
# ipv6 支持
ipv6: true
log-level: info
# 允许局域网连接
allow-lan: true
# socks5/http 端口
mixed-port: 7890
# Meta 内核特性 https://wiki.metacubex.one/config/general
# 统一延迟
# 更换延迟计算方式,去除握手等额外延迟
unified-delay: true
# TCP 并发
# 同时对所有ip进行连接，返回延迟最低的地址
tcp-concurrent: true
# 外部控制端口
external-controller: :9090

geodata-mode: true
# 使用 FastGit 代理 (https://fgit.cf)
# 源地址 https://github.com/MetaCubeX/meta-rules-dat
# 可以更换镜像站但不要更换其他数据库，可能导致无法启动
geox-url:
  geoip: "https://ghp.ci/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip.dat"
  geosite: "https://ghp.ci/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat"
  mmdb: "https://ghp.ci/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country.mmdb"

# 进程匹配模式
# 路由器上请设置为 off
# always 开启，强制匹配所有进程
# strict 默认，由 Clash 判断是否开启
# off 不匹配进程，推荐在路由器上使用此模式
find-process-mode: always

# 缓解移动设备耗电问题
# https://github.com/vernesong/OpenClash/issues/2614
keep-alive-interval: 1800

# 全局客户端指纹
global-client-fingerprint: random # 随机指纹

# 缓存
profile:
  store-selected: true
  store-fake-ip: true

# 自动同步时间以防止时间不准导致无法正常联网
ntp:
  enable: true
  # 是否同步至系统时间，需要 root/管理员权限
  write-to-system: false
  server: time.apple.com
  port: 123
  interval: 30

# 域名嗅探
sniffer:
  enable: true
  sniff:
    TLS:
      ports: [443, 8443]
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true

# tun 模式
tun:
  enable: false  # enable 'true'
  stack: system  # or 'gvisor'
  dns-hijack:
    - "any:53"
    - "tcp://any:53"
  auto-route: true
  auto-detect-interface: true

# dns 设置
# 已配置 ipv6
dns:
  enable: true
  listen: :1053
  ipv6: true
  # 路由器个人建议使用 redir-host 以最佳兼容性
  # 其他设备可以使用 fake-ip
  enhanced-mode: redir-host
  fake-ip-range: ********/8
  fake-ip-filter:
    - '*'
    - '+.lan'
    - '+.local'
  default-nameserver:
    - *********
    - ************
    - ***************
    - '[2402:4e00::]'
    - '[2400:3200::1]'
  nameserver:
    - 'tls://*******#dns'
    - 'tls://*******#dns'
    - 'tls://[2001:4860:4860::8844]#dns'
    - 'tls://[2606:4700:4700::1001]#dns'
  proxy-server-nameserver:
    - https://doh.pub/dns-query
  nameserver-policy:
    "geosite:cn,private":
      - https://doh.pub/dns-query
      - https://dns.alidns.com/dns-query
    "geosite:!cn,!private": 
      - "tls://dns.google"
      - "tls://cloudflare-dns.com"

proxies:

proxy-groups:
  - {
      name: 节点选择,
      type: select,
      proxies:
        [全部节点, 自动选择, 香港, 台湾, 日本, 新加坡, 美国, 法国, 德国, 加拿大, 韩国, 澳大利亚, 其它地区, DIRECT],
    }
  # 这里的 dns 指海外解析 dns 走的节点，一般跟随节点选择即可
  - { name: dns, <<: *pr }
  - { name: 广告拦截, type: select, proxies: [REJECT, DIRECT, 节点选择] }
  - { name: OpenAI, <<: *pr }
  # Apple 推荐走全局直连
  - { name: Apple, <<: *pr }
  - { name: Google, <<: *pr }
  - { name: Telegram, <<: *pr }
  - { name: Twitter, <<: *pr }
  - { name: Pixiv, <<: *pr }
  - { name: ehentai, <<: *pr }
  # 下面两个看需求启用，打开之后会代理全站流量，可能导致部分版权视频反而无法播放或视频播放速度缓慢
  # 下面 rules 两条也要启用
  - {name: 哔哩哔哩, <<: *pr}
  - {name: 哔哩东南亚, <<: *pr}
  - { name: 巴哈姆特, <<: *pr }
  - { name: YouTube, <<: *pr }
  - { name: NETFLIX, <<: *pr }
  - { name: Spotify, <<: *pr }
  - { name: Github, <<: *pr }
  - { name: Steam, <<: *pr }
  - { name: OneDrive, <<: *pr }
  - { name: 微软服务, <<: *pr }
  - {
      name: 国内,
      type: select,
      proxies:
        [
          DIRECT,
          节点选择,
          香港,
          台湾,
          日本,
          新加坡,
          美国,
          英国,
          德国,
          法国,
          加拿大,
          韩国,
          澳大利亚,
          其它地区,
          全部节点,
          自动选择,
        ],
    }
  # 其他就是所有规则没匹配到的
  # 可以理解为 ACL4SSR 配置里的 漏网之鱼
  # 换言之，其他走代理就是绕过中国大陆地址，不走就是 GFWList 模式
  - { name: 其他, <<: *pr }

  # 分隔,下面是地区分组
  - { name: 香港, <<: *use, filter: "(?i)港|hk|hongkong|hong kong" }
  - { name: 台湾, <<: *use, filter: "(?i)台|tw|taiwan" }
  - { name: 日本, <<: *use, filter: "(?i)日本|jp|japan" }
  - { name: 美国, <<: *use, filter: "(?i)美|us|unitedstates|united states" }
  - { name: 新加坡, <<: *use, filter: "(?i)(新|sg|singapore)" }
  - { name: 英国, <<: *use, filter: "(?i)(英国|uk|united kingdom)" }
  - { name: 德国, <<: *use, filter: "(?i)(德国|de|germany)" }
  - { name: 法国, <<: *use, filter: "(?i)(法国|fr|france)" }
  - { name: 加拿大, <<: *use, filter: "(?i)(加拿大|ca|canada)" }
  - { name: 韩国, <<: *use, filter: "(?i)(韩国|kr|korea)" }
  - { name: 澳大利亚, <<: *use, filter: "(?i)(澳大利亚|au|australia)" }
  - {
      name: 其它地区,
      <<: *use,
      filter: "(?i)^(?!.*(?:🇭🇰|🇯🇵|🇺🇸|🇸🇬|🇨🇳|港|hk|hongkong|台|tw|taiwan|日|jp|japan|新|sg|singapore|美|us|unitedstates)).*",
    }
  - { name: 全部节点, <<: *use }
  - { name: 自动选择, <<: *use, tolerance: 2, type: url-test }

rules:
  # 若需禁用 QUIC 请取消注释 QUIC 两条规则
  # 防止 YouTube 等使用 QUIC 导致速度不佳, 禁用 443 端口 UDP 流量（不包括国内）

# - AND,(AND,(DST-PORT,443),(NETWORK,UDP)),(NOT,((GEOSITE,cn))),REJECT # quic
  - RULE-SET,AWAvenue-Ads,广告拦截
# - GEOSITE,biliintl,哔哩东南亚
# - GEOSITE,bilibili,哔哩哔哩

  - GEOSITE,openai,OpenAI
  - GEOSITE,apple,Apple
  - GEOSITE,apple-cn,Apple
  - GEOSITE,ehentai,ehentai
  - GEOSITE,github,Github
  - GEOSITE,twitter,Twitter
  - GEOSITE,youtube,YouTube
  - GEOSITE,google,Google
  - GEOSITE,google-cn,Google # Google CN 不走代理会导致香港等地区节点 Play Store 异常
  - GEOSITE,telegram,Telegram
  - GEOSITE,netflix,NETFLIX
  - GEOSITE,bahamut,巴哈姆特
  - GEOSITE,spotify,Spotify
  - GEOSITE,pixiv,Pixiv
  - GEOSITE,steam@cn,DIRECT
  - GEOSITE,steam,Steam
  - GEOSITE,onedrive,OneDrive
  - GEOSITE,microsoft,微软服务
  - GEOSITE,geolocation-!cn,其他
# - AND,(AND,(DST-PORT,443),(NETWORK,UDP)),(NOT,((GEOIP,CN))),REJECT # quic
  - GEOIP,google,Google
  - GEOIP,netflix,NETFLIX
  - GEOIP,telegram,Telegram
  - GEOIP,twitter,Twitter
  - GEOSITE,CN,国内
  - GEOIP,CN,国内
  # 绕过局域网地址
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,**********/10,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - MATCH,其他
