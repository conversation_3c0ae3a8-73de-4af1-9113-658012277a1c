/* Dracula Material Style Theme (Adapted from Gemini Template) */

:root {
  /* --- <PERSON> Color Palette --- */
  --dracula-bg: #282a36;        /* Background */
  --dracula-bg-hsl: 231 15% 18%;
  --dracula-current-line: #44475a; /* Lighter Background / Selection */
  --dracula-current-line-hsl: 232 14% 31%;
  --dracula-fg: #f8f8f2;        /* Foreground */
  --dracula-fg-hsl: 60 30% 96%;
  --dracula-comment: #6272a4;   /* Comment / Secondary Text / Borders */
  --dracula-comment-hsl: 225 27% 51%;
  --dracula-cyan: #8be9fd;
  --dracula-cyan-hsl: 195 93% 77%;
  --dracula-green: #50fa7b;
  --dracula-green-hsl: 135 94% 65%;
  --dracula-orange: #ffb86c;
  --dracula-orange-hsl: 31 100% 71%;
  --dracula-pink: #ff79c6;
  --dracula-pink-hsl: 326 100% 74%;
  --dracula-purple: #bd93f9;
  --dracula-purple-hsl: 265 89% 78%;
  --dracula-red: #ff5555;
  --dracula-red-hsl: 0 100% 67%;
  --dracula-yellow: #f1fa8c;
  --dracula-yellow-hsl: 65 92% 76%;

  /* --- Material Design Inspired Variables (Using Dracula Palette) --- */
  /* Shadows - Kept structure from Gemini, values might need slight tweak for dark bg */
  --material-shadow-small: 0px 1px 3px rgba(0, 0, 0, 0.2), 0px 2px 2px rgba(0, 0, 0, 0.14), 0px 3px 1px -2px rgba(0, 0, 0, 0.12);
  --material-shadow-medium: 0px 2px 4px rgba(0, 0, 0, 0.2), 0px 4px 5px rgba(0, 0, 0, 0.14), 0px 1px 10px rgba(0, 0, 0, 0.12);
  --material-shadow-large: 0px 5px 5px rgba(0, 0, 0, 0.2), 0px 8px 10px rgba(0, 0, 0, 0.14), 0px 3px 14px rgba(0, 0, 0, 0.12);

  /* Radii - Consistent with Material guidelines */
  --material-radius-small: 4px;  /* e.g., Chips, small buttons */
  --material-radius-medium: 8px; /* e.g., Cards, standard buttons */
  --material-radius-large: 12px; /* e.g., Dialogs */

  /* Opacity */
  --material-disabled-opacity: 0.38; /* Standard Material disabled opacity */
  --material-hover-opacity: 0.85; /* Slightly darker/lighter on hover */
}

/* --- Dark Theme (Dracula) --- */
.dark,
[data-theme="dark"] {
  color-scheme: dark !important;
  /* Base Colors */
  --heroui-background: var(--dracula-bg-hsl) !important; /* Dracula Background */
  --heroui-foreground: var(--dracula-fg-hsl) !important; /* Dracula Foreground */

  /* Content Backgrounds */
  --heroui-content1: var(--dracula-bg-hsl) !important;          /* Main Background */
  --heroui-content1-foreground: var(--dracula-fg-hsl) !important;
  --heroui-content2: var(--dracula-current-line-hsl) !important; /* Slightly Lighter */
  --heroui-content2-foreground: var(--dracula-fg-hsl) !important;
  --heroui-content3: 232 14% 25% !important; /* Darker shade of current-line */
  --heroui-content3-foreground: var(--dracula-fg-hsl) !important;
  --heroui-content4: 231 15% 15% !important; /* Darker shade of bg */
  --heroui-content4-foreground: var(--dracula-comment-hsl) !important; /* Use comment for less emphasis */

  /* Default Component Colors (Grays/Neutrals) */
  --heroui-default-50: 231 15% 10% !important;
  --heroui-default-100: 231 15% 12% !important;
  --heroui-default-200: var(--dracula-bg-hsl) !important;        /* Dracula Background */
  --heroui-default-300: 232 14% 25% !important;
  --heroui-default-400: var(--dracula-current-line-hsl)!important;/* Dracula Current Line */
  --heroui-default-500: var(--dracula-comment-hsl) !important;   /* Dracula Comment */
  --heroui-default-600: 225 27% 60% !important;
  --heroui-default-700: 225 27% 70% !important;
  --heroui-default-800: 60 30% 85% !important;
  --heroui-default-900: var(--dracula-fg-hsl) !important;        /* Dracula Foreground */
  --heroui-default-foreground: var(--dracula-fg-hsl) !important; /* Text on default elements */
  --heroui-default: var(--heroui-default-400) !important;      /* Default component bg */

  /* Primary Color (Dracula Purple) */
  --heroui-primary-50: 265 89% 20% !important;
  --heroui-primary-100: 265 89% 30% !important;
  --heroui-primary-200: 265 89% 40% !important;
  --heroui-primary-300: 265 89% 50% !important;
  --heroui-primary-400: 265 89% 60% !important;
  --heroui-primary-500: var(--dracula-purple-hsl) !important; /* Dracula Purple */
  --heroui-primary-600: 265 89% 82% !important;
  --heroui-primary-700: 265 89% 86% !important;
  --heroui-primary-800: 265 89% 90% !important;
  --heroui-primary-900: 265 89% 95% !important;
  --heroui-primary-foreground: var(--dracula-bg-hsl) !important; /* Dark text on Purple */
  --heroui-primary: var(--heroui-primary-500) !important;

  /* Secondary Color (Dracula Pink) */
  --heroui-secondary-50: 326 100% 20% !important;
  --heroui-secondary-100: 326 100% 30% !important;
  --heroui-secondary-200: 326 100% 40% !important;
  --heroui-secondary-300: 326 100% 50% !important;
  --heroui-secondary-400: 326 100% 60% !important;
  --heroui-secondary-500: var(--dracula-pink-hsl) !important; /* Dracula Pink */
  --heroui-secondary-600: 326 100% 78% !important;
  --heroui-secondary-700: 326 100% 82% !important;
  --heroui-secondary-800: 326 100% 86% !important;
  --heroui-secondary-900: 326 100% 90% !important;
  --heroui-secondary-foreground: var(--dracula-bg-hsl) !important; /* Dark text on Pink */
  --heroui-secondary: var(--heroui-secondary-500) !important;

  /* Success Color (Dracula Green) */
  --heroui-success-50: 135 94% 15% !important;
  --heroui-success-100: 135 94% 25% !important;
  --heroui-success-200: 135 94% 35% !important;
  --heroui-success-300: 135 94% 45% !important;
  --heroui-success-400: 135 94% 55% !important;
  --heroui-success-500: var(--dracula-green-hsl) !important; /* Dracula Green */
  --heroui-success-600: 135 94% 70% !important;
  --heroui-success-700: 135 94% 75% !important;
  --heroui-success-800: 135 94% 80% !important;
  --heroui-success-900: 135 94% 85% !important;
  --heroui-success-foreground: var(--dracula-bg-hsl) !important; /* Dark text on Green */
  --heroui-success: var(--heroui-success-500) !important;

  /* Warning Color (Dracula Orange) */
  --heroui-warning-50: 31 100% 20% !important;
  --heroui-warning-100: 31 100% 30% !important;
  --heroui-warning-200: 31 100% 40% !important;
  --heroui-warning-300: 31 100% 50% !important;
  --heroui-warning-400: 31 100% 60% !important;
  --heroui-warning-500: var(--dracula-orange-hsl) !important; /* Dracula Orange */
  --heroui-warning-600: 31 100% 75% !important;
  --heroui-warning-700: 31 100% 80% !important;
  --heroui-warning-800: 31 100% 85% !important;
  --heroui-warning-900: 31 100% 90% !important;
  --heroui-warning-foreground: var(--dracula-bg-hsl) !important; /* Dark text on Orange */
  --heroui-warning: var(--heroui-warning-500) !important;

  /* Danger Color (Dracula Red) */
  --heroui-danger-50: 0 100% 15% !important;
  --heroui-danger-100: 0 100% 25% !important;
  --heroui-danger-200: 0 100% 35% !important;
  --heroui-danger-300: 0 100% 45% !important;
  --heroui-danger-400: 0 100% 55% !important;
  --heroui-danger-500: var(--dracula-red-hsl) !important; /* Dracula Red */
  --heroui-danger-600: 0 100% 72% !important;
  --heroui-danger-700: 0 100% 77% !important;
  --heroui-danger-800: 0 100% 82% !important;
  --heroui-danger-900: 0 100% 87% !important;
  --heroui-danger-foreground: var(--dracula-fg-hsl) !important; /* Light text on Red */
  --heroui-danger: var(--heroui-danger-500) !important;

  /* Other UI Elements */
  --heroui-focus: var(--dracula-cyan-hsl) !important; /* Use Cyan for focus rings */
  --heroui-overlay: 0 0% 0% !important; /* Black overlay */
  --heroui-divider: var(--dracula-comment-hsl) !important; /* Comment color for dividers */
  --heroui-divider-opacity: 0.4 !important;
  --heroui-divider-weight: 1px !important;
  --heroui-disabled-opacity: var(--material-disabled-opacity) !important;
  --heroui-hover-opacity: var(--material-hover-opacity) !important;

  /* Sizing & Radius (Using Material Inspired) */
  --heroui-font-size-tiny: 0.75rem !important;
  --heroui-font-size-small: 0.875rem !important;
  --heroui-font-size-medium: 1rem !important;
  --heroui-font-size-large: 1.125rem !important;
  --heroui-line-height-tiny: 1rem !important;
  --heroui-line-height-small: 1.25rem !important;
  --heroui-line-height-medium: 1.5rem !important;
  --heroui-line-height-large: 1.75rem !important;
  --heroui-radius-small: var(--material-radius-small) !important;
  --heroui-radius-medium: var(--material-radius-medium) !important;
  --heroui-radius-large: var(--material-radius-large) !important;
  --heroui-border-width-small: 1px !important;
  --heroui-border-width-medium: 1px !important; /* Material usually uses 1px */
  --heroui-border-width-large: 2px !important;

  /* Shadows (Using Material Inspired) */
  --heroui-box-shadow-small: var(--material-shadow-small) !important;
  --heroui-box-shadow-medium: var(--material-shadow-medium) !important;
  --heroui-box-shadow-large: var(--material-shadow-large) !important;

  /* Custom Gemini Vars Mapped to Dracula */
  --custom-main-background: var(--dracula-bg) !important;
  --custom-side-background: var(--dracula-current-line) !important;
  --custom-bg-mask-background: rgba(0, 0, 0, 0.5) !important; /* Darker mask */
  --custom-bg-mask-foreground: var(--dracula-fg) !important;

  /* Default shades (raw values for overrides) */
  --custom-default-50: #1f212b !important; /* Darker BG */
  --custom-default-100: #242733 !important;
  --custom-default-200: var(--dracula-bg) !important;
  --custom-default-300: #3a3d4f !important;
  --custom-default-400: var(--dracula-current-line) !important;
  --custom-default-500: var(--dracula-comment) !important;
  --custom-default-600: #7a88c1 !important;
  --custom-default-700: #93a0d0 !important;
  --custom-default-800: #d5d5d2 !important; /* Lighter gray */
  --custom-default-900: var(--dracula-fg) !important;
  --custom-default-foreground: var(--dracula-fg) !important;
  --custom-default: var(--custom-default-400) !important;

  /* Gemini specific gradient mappings */
  --gemini-color-logo-gradient: linear-gradient(90deg, var(--dracula-purple) 0%, var(--dracula-pink) 50%, var(--dracula-orange) 100%);
  --gemini-branding-button-gradient: linear-gradient(15deg, var(--dracula-purple) 0%, var(--dracula-pink) 100%);
  --gemini-branding-text-gradient: linear-gradient(90deg, var(--dracula-purple) 0%, var(--dracula-pink) 50%, var(--dracula-red) 100%);
  --gemini-gradient-linear: linear-gradient(53deg, var(--dracula-cyan) 9.29%, var(--dracula-green) 48.23%, var(--dracula-purple) 82.56%);
  --gemini-text-gradient-light-blue: linear-gradient(69deg, var(--dracula-comment) 16.42%, var(--dracula-fg) 77.56%, var(--dracula-cyan) 124.91%);

  /* --- Component Overrides for Dracula Dark --- */
  .main .content .bg-default,
  .main .bg-primary, /* Let primary elements use primary color */
  .side .bg-primary { /* Let primary elements use primary color */
    background-color: hsl(var(--heroui-content2)) !important; /* Use Current Line for default card/section bg */
  }

  .main .bg-default-100 {
    background-color: hsl(var(--heroui-content1)) !important; /* Use main BG */
  }

  /* Selected items - Use Primary color */
  .group[data-selected=true] .group-data-\[selected\=true\]\:bg-primary {
    background-color: hsl(var(--heroui-primary)) !important;
    color: hsl(var(--heroui-primary-foreground)) !important;
  }
  .group-data-\[selected\=true\]\:bg-primary {
     background-color: hsl(var(--heroui-default-300)) !important; /* Slightly lighter bg for non-selected state */
  }
  .group-data-\[selected\=true\]\:bg-primary.border-2 {
    border-color: transparent !important;
  }

  /* Floating Action Button (FAB) like element */
  .floating-bg {
    background-color: hsl(var(--heroui-primary)) !important; /* FAB uses primary */
    box-shadow: var(--heroui-box-shadow-medium);
  }
  .floating-bg .floating-icon {
    color: hsl(var(--heroui-primary-foreground)) !important;
  }
}

/* --- Light Theme (Dracula Inspired) --- */
.light,
[data-theme="light"] {
  color-scheme: light !important;
  /* Base Colors */
  --heroui-background: 60 30% 98% !important; /* Very light, almost white */
  --heroui-foreground: var(--dracula-bg-hsl) !important; /* Dark text */

  /* Content Backgrounds */
  --heroui-content1: 0 0% 100% !important; /* White */
  --heroui-content1-foreground: var(--dracula-bg-hsl) !important;
  --heroui-content2: 60 30% 96% !important; /* Off-white, like Dracula FG */
  --heroui-content2-foreground: var(--dracula-bg-hsl) !important;
  --heroui-content3: 60 30% 92% !important; /* Slightly darker gray */
  --heroui-content3-foreground: var(--dracula-bg-hsl) !important;
  --heroui-content4: 60 30% 88% !important; /* Darker gray */
  --heroui-content4-foreground: var(--dracula-current-line-hsl) !important;

  /* Default Component Colors (Light Grays) */
  --heroui-default-50: 60 30% 98% !important;
  --heroui-default-100: 60 30% 96% !important;
  --heroui-default-200: 60 30% 92% !important;
  --heroui-default-300: 60 30% 88% !important;
  --heroui-default-400: 225 27% 80% !important; /* Lighter comment */
  --heroui-default-500: var(--dracula-comment-hsl) !important; /* Dracula Comment */
  --heroui-default-600: var(--dracula-current-line-hsl) !important; /* Dracula Current Line */
  --heroui-default-700: 232 14% 25% !important;
  --heroui-default-800: var(--dracula-bg-hsl) !important; /* Dracula Background */
  --heroui-default-900: 231 15% 10% !important;
  --heroui-default-foreground: var(--dracula-bg-hsl) !important; /* Text on default elements */
  --heroui-default: var(--heroui-default-200) !important; /* Default component bg */

  /* Primary Color (Dracula Purple) - Foreground changes */
  --heroui-primary-50: 265 89% 95% !important;
  --heroui-primary-100: 265 89% 90% !important;
  --heroui-primary-200: 265 89% 86% !important;
  --heroui-primary-300: 265 89% 82% !important;
  --heroui-primary-400: var(--dracula-purple-hsl) !important; /* Dracula Purple */
  --heroui-primary-500: 265 89% 60% !important; /* Darker Purple */
  --heroui-primary-600: 265 89% 50% !important;
  --heroui-primary-700: 265 89% 40% !important;
  --heroui-primary-800: 265 89% 30% !important;
  --heroui-primary-900: 265 89% 20% !important;
  --heroui-primary-foreground: var(--dracula-fg-hsl) !important; /* Light text on Darker Purple */
  --heroui-primary: var(--heroui-primary-500) !important; /* Use the darker shade */

  /* Secondary Color (Dracula Pink) - Foreground changes */
  --heroui-secondary-50: 326 100% 90% !important;
  --heroui-secondary-100: 326 100% 86% !important;
  --heroui-secondary-200: 326 100% 82% !important;
  --heroui-secondary-300: 326 100% 78% !important;
  --heroui-secondary-400: var(--dracula-pink-hsl) !important; /* Dracula Pink */
  --heroui-secondary-500: 326 100% 60% !important; /* Darker Pink */
  --heroui-secondary-600: 326 100% 50% !important;
  --heroui-secondary-700: 326 100% 40% !important;
  --heroui-secondary-800: 326 100% 30% !important;
  --heroui-secondary-900: 326 100% 20% !important;
  --heroui-secondary-foreground: var(--dracula-fg-hsl) !important; /* Light text on Darker Pink */
  --heroui-secondary: var(--heroui-secondary-500) !important;

  /* Success Color (Dracula Green) - Foreground changes */
  --heroui-success-50: 135 94% 85% !important;
  --heroui-success-100: 135 94% 80% !important;
  --heroui-success-200: 135 94% 75% !important;
  --heroui-success-300: 135 94% 70% !important;
  --heroui-success-400: var(--dracula-green-hsl) !important; /* Dracula Green */
  --heroui-success-500: 135 94% 45% !important; /* Darker Green */
  --heroui-success-600: 135 94% 35% !important;
  --heroui-success-700: 135 94% 25% !important;
  --heroui-success-800: 135 94% 15% !important;
  --heroui-success-900: 135 94% 10% !important;
  --heroui-success-foreground: var(--dracula-fg-hsl) !important; /* Light text on Darker Green */
  --heroui-success: var(--heroui-success-500) !important;

  /* Warning Color (Dracula Orange) - Foreground changes */
  --heroui-warning-50: 31 100% 90% !important;
  --heroui-warning-100: 31 100% 85% !important;
  --heroui-warning-200: 31 100% 80% !important;
  --heroui-warning-300: 31 100% 75% !important;
  --heroui-warning-400: var(--dracula-orange-hsl) !important; /* Dracula Orange */
  --heroui-warning-500: 31 100% 50% !important; /* Darker Orange */
  --heroui-warning-600: 31 100% 40% !important;
  --heroui-warning-700: 31 100% 30% !important;
  --heroui-warning-800: 31 100% 20% !important;
  --heroui-warning-900: 31 100% 15% !important;
  --heroui-warning-foreground: var(--dracula-bg-hsl) !important; /* Dark text on Darker Orange */
  --heroui-warning: var(--heroui-warning-500) !important;

  /* Danger Color (Dracula Red) - Foreground changes */
  --heroui-danger-50: 0 100% 87% !important;
  --heroui-danger-100: 0 100% 82% !important;
  --heroui-danger-200: 0 100% 77% !important;
  --heroui-danger-300: 0 100% 72% !important;
  --heroui-danger-400: var(--dracula-red-hsl) !important; /* Dracula Red */
  --heroui-danger-500: 0 100% 55% !important; /* Darker Red */
  --heroui-danger-600: 0 100% 45% !important;
  --heroui-danger-700: 0 100% 35% !important;
  --heroui-danger-800: 0 100% 25% !important;
  --heroui-danger-900: 0 100% 15% !important;
  --heroui-danger-foreground: var(--dracula-fg-hsl) !important; /* Light text on Darker Red */
  --heroui-danger: var(--heroui-danger-500) !important;

  /* Other UI Elements */
  --heroui-focus: var(--dracula-cyan-hsl) !important; /* Use Cyan for focus rings */
  --heroui-overlay: 0 0% 0% !important; /* Black overlay */
  --heroui-divider: var(--dracula-comment-hsl) !important; /* Comment color for dividers */
  --heroui-divider-opacity: 0.2 !important;
  --heroui-divider-weight: 1px !important;
  --heroui-disabled-opacity: var(--material-disabled-opacity) !important;
  --heroui-hover-opacity: 0.9 !important; /* Lighten slightly on hover */

  /* Sizing & Radius (Using Material Inspired) - Same as dark */
  --heroui-font-size-tiny: 0.75rem !important;
  --heroui-font-size-small: 0.875rem !important;
  --heroui-font-size-medium: 1rem !important;
  --heroui-font-size-large: 1.125rem !important;
  --heroui-line-height-tiny: 1rem !important;
  --heroui-line-height-small: 1.25rem !important;
  --heroui-line-height-medium: 1.5rem !important;
  --heroui-line-height-large: 1.75rem !important;
  --heroui-radius-small: var(--material-radius-small) !important;
  --heroui-radius-medium: var(--material-radius-medium) !important;
  --heroui-radius-large: var(--material-radius-large) !important;
  --heroui-border-width-small: 1px !important;
  --heroui-border-width-medium: 1px !important;
  --heroui-border-width-large: 2px !important;

  /* Shadows (Material Inspired - Light theme versions) */
  --heroui-box-shadow-small: 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20) !important;
  --heroui-box-shadow-medium: 0px 2px 2px rgba(0, 0, 0, 0.14), 0px 3px 1px rgba(0, 0, 0, 0.12), 0px 1px 5px rgba(0, 0, 0, 0.20) !important;
  --heroui-box-shadow-large: 0px 3px 4px rgba(0, 0, 0, 0.14), 0px 3px 3px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.20) !important;

  /* Custom Gemini Vars Mapped to Dracula (Light Adaptation) */
  --custom-main-background: hsl(var(--heroui-content1)) !important; /* White */
  --custom-side-background: hsl(var(--heroui-content2)) !important; /* Off-white */
  --custom-bg-mask-background: rgba(255, 255, 255, 0.7) !important; /* Light mask */
  --custom-bg-mask-foreground: hsl(var(--heroui-foreground)) !important; /* Dark text */

  /* Default shades (raw values for overrides) */
  --custom-default-50: #ffffff !important;
  --custom-default-100: #f8f8f2 !important; /* Dracula FG */
  --custom-default-200: #f0f0ea !important;
  --custom-default-300: #e8e8e2 !important;
  --custom-default-400: #d0d0c8 !important;
  --custom-default-500: #b8b8b0 !important;
  --custom-default-600: #909088 !important;
  --custom-default-700: #6272a4 !important; /* Dracula Comment */
  --custom-default-800: #44475a !important; /* Dracula Current Line */
  --custom-default-900: #282a36 !important; /* Dracula Background */
  --custom-default-foreground: var(--custom-default-900) !important;
  --custom-default: var(--custom-default-200) !important;

  /* Gemini specific gradient mappings (Light Adaptation) */
  --gemini-color-logo-gradient: linear-gradient(90deg, hsl(var(--heroui-primary)) 0%, hsl(var(--heroui-secondary)) 50%, hsl(var(--heroui-danger)) 100%);
  --gemini-branding-button-gradient: linear-gradient(15deg, hsl(var(--heroui-primary)) 0%, hsl(var(--heroui-secondary)) 100%);
  --gemini-branding-text-gradient: linear-gradient(90deg, hsl(var(--heroui-primary)) 0%, hsl(var(--heroui-secondary)) 50%, hsl(var(--heroui-danger)) 100%);
  --gemini-gradient-linear: linear-gradient(53deg, hsl(var(--heroui-success)) 9.29%, hsl(var(--heroui-warning)) 48.23%, hsl(var(--heroui-primary)) 82.56%);
  --gemini-text-gradient-light-blue: linear-gradient(69deg, hsl(var(--heroui-default-400)) 16.42%, hsl(var(--heroui-background)) 77.56%, hsl(var(--heroui-primary-200)) 124.91%);

  /* --- Component Overrides for Dracula Light --- */
  .main .content .bg-default,
  .main .bg-primary,
  .side .bg-primary {
    background-color: hsl(var(--heroui-content1)) !important; /* White for cards/sections */
    border: 1px solid hsl(var(--heroui-divider) / 0.3) !important; /* Subtle border */
    box-sizing: border-box !important;
  }

  .hover\:bg-primary\/30:hover {
    background: hsl(var(--heroui-primary-100)) !important; /* Light primary hover */
  }

  .main .text-primary-foreground,
  .side .text-primary-foreground,
  .main .text-foreground,
  .side .text-foreground,
  .side .text-primary {
    color: hsl(var(--heroui-foreground)) !important; /* Ensure dark text */
  }

  .main .bg-default-100 {
    background-color: hsl(var(--heroui-content2)) !important; /* Off-white */
  }

  .main .group-data-\[selected\=true\]\:bg-primary .bg-white,
  .side .group-data-\[selected\=true\]\:bg-primary .bg-white {
    background-color: hsl(var(--heroui-content1)) !important; /* White bg inside selected item */
  }

  /* Selected items - Use Primary color (darker shade) */
  .group[data-selected=true] .group-data-\[selected\=true\]\:bg-primary {
    background-color: hsl(var(--heroui-primary)) !important;
    color: hsl(var(--heroui-primary-foreground)) !important;
  }
  .group-data-\[selected\=true\]\:bg-primary {
     background-color: hsl(var(--heroui-default-100)) !important; /* Lighter gray for non-selected state */
  }

   /* Floating Action Button (FAB) like element */
  .floating-bg {
    background-color: hsl(var(--heroui-primary)) !important; /* FAB uses primary */
     box-shadow: var(--heroui-box-shadow-medium);
  }
  .floating-bg .floating-icon {
    color: hsl(var(--heroui-primary-foreground)) !important;
  }
}

/* --- General Overrides & Adjustments --- */

/* Apply Material Radii */
button, .button, input, textarea, select, .card, [role="dialog"], [role="tooltip"], .badge {
  border-radius: var(--heroui-radius-medium) !important;
}
[class*="rounded-full"] {
  border-radius: 9999px !important;
}
/* Smaller radius for specific small elements if needed */
.badge, [role="tooltip"] {
   border-radius: var(--heroui-radius-small) !important;
}

/* Ensure buttons have Material feel */
button:not([disabled]), .button:not([disabled]) {
  box-shadow: var(--heroui-box-shadow-small) !important;
  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.1s ease !important;
  text-transform: uppercase; /* Material often uses uppercase */
  font-weight: 500; /* Medium weight */
  padding: 8px 16px; /* Typical Material padding */
}
button:not([disabled]):hover, .button:not([disabled]):hover {
  box-shadow: var(--heroui-box-shadow-medium) !important;
  /* Background hover handled by opacity/color vars */
}
button:not([disabled]):active, .button:not([disabled]):active {
  box-shadow: none !important; /* Flatten on press */
  transform: translateY(1px);
}
button.bg-primary:not([disabled]), .button.bg-primary:not([disabled]){
  background-color: hsl(var(--heroui-primary)) !important;
  color: hsl(var(--heroui-primary-foreground)) !important;
}
/* Add other button types (.bg-secondary, .bg-danger etc.) if needed */

/* Input field styling */
input, textarea, select {
  background-color: hsl(var(--heroui-content2)) !important;
  border: 1px solid hsl(var(--heroui-divider) / 0.5) !important;
  padding: 10px 12px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: hsl(var(--heroui-primary)) !important;
  box-shadow: 0 0 0 1px hsl(var(--heroui-primary) / 0.5) !important; /* Focus ring */
}


/* Title Gradient Handling */
.main .title.text-lg.leading-\[32px\],
.main .h-\[32px\].leading-\[32px\],
.main .text-default-500 .ml-2,
.font-bold {
  /* Use Dracula-themed gradient defined in :root/.dark/.light */
  background-image: var(--gemini-branding-text-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* General Layout Backgrounds */
.main {
  background: var(--custom-main-background) !important;
}
.main .content .bg-content1, /* This selector was in original, might target specific areas */
.side {
  background: var(--custom-side-background) !important;
}

/* Ensure consistent text colors */
.main .text-primary-foreground,
.side .text-primary-foreground,
.main .text-foreground,
.side .text-foreground,
.side .text-primary {
  color: hsl(var(--heroui-foreground)); /* Use the main foreground color */
}
.main .text-default-500 {
   color: hsl(var(--heroui-default-500)) !important; /* Use default text color */
}

/* Tab specific styling - adapt background/text */
.side [data-slot="tabList"] {
  /* Maybe use a subtle gradient or just a solid color */
   background: hsl(var(--heroui-content3)); /* Example: slightly darker bg */
}
.side [data-slot="tabList"] .bg-primary {
  /* Style for the selected tab indicator */
  background: hsl(var(--heroui-primary)); /* Use primary color */
}
.side [data-slot="tabList"] [data-slot="tabContent"] {
  /* Text color for tabs */
  color: hsl(var(--heroui-foreground));
  background-image: none; /* Remove gradient from original */
  background-clip: initial;
  -webkit-background-clip: initial;
  -webkit-text-fill-color: currentColor; /* Use the element's color */
}
.side [data-slot="tabList"] [aria-selected="true"] [data-slot="tabContent"] {
    color: hsl(var(--heroui-primary-foreground)); /* Text color for selected tab if bg is primary */
    /* Or keep it foreground if indicator is just an underline */
}

/* Remove Gemini-specific blue/cyan borders if not desired */
.main .border-primary,
.main .border-default {
   border-color: hsl(var(--heroui-divider) / 0.5); /* Use standard divider color */
}

/* Specific overrides cleanup */
.main .rounded-full.bg-primary {
  background-color: hsl(var(--heroui-primary));
}
.main .rounded-full.bg-danger {
  background-color: hsl(var(--heroui-danger));
}
.side .h-\[32px\].leading-\[32px\] {
  color: hsl(var(--heroui-primary)); /* Use primary color for side titles */
}

/* Ensure hover states use appropriate backgrounds */
.hover\:bg-primary\/30:hover {
  background: hsl(var(--heroui-primary) / 0.15) !important; /* Subtle primary hover */
}

