/* 现代化Dracula主题 - 高级材质风格 */

.light,
[data-theme="light"] {
    /* Dracula Light - 现代化调整 */
    --heroui-background: 250 100% 96% !important;
    --heroui-default-foreground: 265 89.5% 10% !important;
    --heroui-foreground: 265 89.5% 10% !important;

    /* 现代化材质风格 - 亮色模式 */
    --glass-blur: 5px; /* 适度模糊 */
    --glass-border: 1px solid rgba(200, 200, 200, 0.2); /* 柔和边框 */
    --glass-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); /* 轻微阴影 */
    --card-border-radius: 14px; /* 增加圆角 */
    --custom-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 轻微文字阴影 */
    --neon-glow: 0 0 5px rgba(var(--heroui-primary-rgb), 0.3); /* 柔和霓虹 */
    --single-line-border-color: transparent; /* 移除单线框 */

    /* 亮色模式开关颜色 - Dracula */
    --switch-on-bg: hsla(var(--heroui-primary), 0.8) !important; /* 主题色强调 */
    --switch-off-bg: rgba(200, 200, 200, 0.6) !important; /* 柔和灰色 */
    --switch-on-thumb: rgba(255, 255, 255, 1) !important;
    --switch-off-thumb: rgba(150, 150, 150, 0.8) !important;

    /* 现代化阴影 - 亮色模式 */
    --modern-shadow-light: 0 8px 24px -8px rgba(0, 0, 0, 0.08),
                          0 2px 4px rgba(0, 0, 0, 0.03);
    --modern-shadow-light-focus: 0 0 0 2px hsla(var(--heroui-primary), 0.3),
                               0 8px 24px -8px rgba(0, 0, 0, 0.08);
}

.dark,
[data-theme="dark"] {
    /* Dracula Dark - 现代化调整 */
    --heroui-background: 235 14.3% 15.1% !important;
    --heroui-default: 233 13% 39% !important;
    --heroui-default-100: 232 13.9% 31% !important;
    --heroui-default-200: 233 13% 39% !important;
    --heroui-content1: 231 14.9% 18.4% !important;
    --heroui-content2: 230 14.8% 23.9% !important;
    --heroui-default-foreground: 265 89.5% 95% !important;
    --heroui-foreground: 265 89.5% 95% !important;

    /* 现代化材质风格 - 暗黑模式 */
    --glass-blur: 8px; /* 增加模糊 */
    --glass-border: 1px solid rgba(80, 80, 80, 0.3); /* 柔和边框 */
    --glass-shadow: 0 6px 20px rgba(0, 0, 0, 0.2); /* 增强阴影 */
    --card-border-radius: 14px; /* 增加圆角 */
    --custom-text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* 增强文字阴影 */
    --neon-glow: 0 0 8px rgba(var(--heroui-primary-rgb), 0.8); /* 增强霓虹 */
    --single-line-border-color: transparent; /* 移除单线框 */

    /* 深色模式开关颜色 - Dracula */
    --switch-on-bg: hsla(var(--heroui-primary), 0.9) !important; /* 主题色强调 */
    --switch-off-bg: rgba(50, 50, 50, 0.4) !important; /* 柔和深灰 */
    --switch-on-thumb: rgba(255, 255, 255, 1) !important;
    --switch-off-thumb: rgba(120, 120, 120, 0.7) !important;

    /* 现代化阴影 - 暗色模式 */
    --modern-shadow-dark: 0 10px 30px -10px rgba(0, 0, 0, 0.3),
                         0 2px 5px rgba(0, 0, 0, 0.1);
    --modern-shadow-dark-focus: 0 0 0 2px hsla(var(--heroui-primary), 0.5),
                               0 10px 30px -10px rgba(0, 0, 0, 0.3);
}

html {
    --heroui-primary: 265 89.5% 77.6% !important;
    --heroui-primary-rgb: 188, 222, 245 !important; /* 计算RGB for primary */
    --heroui-secondary: 330 100% 73.7% !important;
    --heroui-success: 135 94.4% 64.7% !important;
    --heroui-warning: 30 100% 66.9% !important;
    --heroui-danger: 3 68.7% 58.6% !important;
    background-color: var(--heroui-background) !important; /* 应用背景色 */
    color: var(--heroui-foreground) !important; /* 应用前景色 */
    transition: background-color 0.3s ease, color 0.3s ease !important; /* 平滑过渡 */
}

/* 磨砂玻璃效果 - 应用于背景类 */
.bg-default,
.bg-default-100,
.bg-default-200,
.bg-content1,
.bg-content2,
.bg-primary {
    backdrop-filter: blur(var(--glass-blur)) !important;
    -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
    border: var(--glass-border) !important;
    box-shadow: var(--glass-shadow) !important;
    border-radius: var(--card-border-radius) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

/* 顶部导航栏效果 - 现代化风格 */
.dark .main .sticky,
[data-theme="dark"] .main .sticky,
.light .main .sticky,
[data-theme="light"] .main .sticky {
    backdrop-filter: blur(var(--glass-blur)) !important;
    -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
    box-shadow: var(--glass-shadow) !important;
    border-bottom: var(--glass-border) !important;
    border-radius: 0 0 var(--card-border-radius) var(--card-border-radius) !important;
    background-color: inherit !important; /* 继承背景色 */
}

/* 卡片样式 - 现代化风格 */
.card,
.nextui-c-PJLV-ieCTeYP-css,
[class*="card"] {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    border-radius: var(--card-border-radius) !important;
    border: var(--glass-border) !important;
    box-shadow: var(--modern-shadow-dark) !important;
    overflow: hidden !important;
}


.dark .card:hover,
.dark .nextui-c-PJLV-ieCTeYP-css:hover,
.dark [class*="card"]:hover,
[data-theme="dark"] .card:hover,
[data-theme="dark"] .nextui-c-PJLV-ieCTeYP-css:hover,
[data-theme="dark"] [class*="card"]:hover,
.light .card:hover,
.light .nextui-c-PJLV-ieCTeYP-css:hover,
.light [class*="card"]:hover,
[data-theme="light"] .card:hover,
[data-theme="light"] .nextui-c-PJLV-ieCTeYP-css:hover,
[data-theme="light"] [class*="card"]:hover {
    transform: translateY(-3px) !important;
    box-shadow: var(--glass-shadow), var(--neon-glow) !important;
    border-radius: var(--card-border-radius) !important;
    border: var(--glass-border) !important;
}


/* 选中高亮状态样式修复 - 增强辨识度 */
.active,
.selected,
.is-active {
    background-color: hsla(var(--heroui-primary), 0.75) !important; /* 更明显选中色 */
    opacity: 1 !important;
    box-shadow: var(--glass-shadow), var(--neon-glow), 0 0 6px hsla(var(--heroui-primary), 0.9) inset !important; /* 增强阴影和发光 */
    transform: scale(1.03) !important;
    border-color: var(--heroui-primary) !important;
    z-index: 10 !important;
    border-radius: 12px !important;
}


/* 按钮效果增强 - 现代化风格 */
.dark button,
.dark [class*="button"],
[data-theme="dark"] button,
[data-theme="dark"] [class*="button"],
.light button,
.light [class*="button"],
[data-theme="light"] button,
[data-theme="light"] [class*="button"] {
    backdrop-filter: blur(var(--glass-blur)) !important;
    -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
    border: var(--glass-border) !important;
    transition: all 0.2s ease !important;
    border-radius: 12px !important;
    box-shadow: var(--modern-shadow-dark) !important;
    color: inherit !important; /* 继承文本颜色 */
}


.dark button:hover,
.dark [class*="button"]:hover,
[data-theme="dark"] button:hover,
[data-theme="dark"] [class*="button"]:hover,
.light button:hover,
.light [class*="button"]:hover,
[data-theme="light"] button:hover,
[data-theme="light"] [class*="button"]:hover {
    box-shadow: var(--glass-shadow), var(--neon-glow) !important;
    transform: translateY(-2px) !important;
    border-radius: 12px !important;
}


/* 文本增强 - 调整不同模式下的文字阴影 */
h1, h2, h3, h4, h5, h6 {
    text-shadow: var(--custom-text-shadow) !important;
    letter-spacing: 0.05px !important;
}

/* 滚动条美化 - 调整暗色模式 - Dracula */
.dark::-webkit-scrollbar-track,
[data-theme="dark"]::-webkit-scrollbar-track {
    background: rgba(50, 50, 50, 0.6); /* 更深的滚动条轨道 */
    border-radius: 12px;
}

.light::-webkit-scrollbar-track,
[data-theme="light"]::-webkit-scrollbar-track {
    background: rgba(220, 220, 220, 0.8); /* 更浅的滚动条轨道 */
    border-radius: 12px;
}


::-webkit-scrollbar-thumb {
    background: hsla(var(--heroui-primary), 0.7);
    border-radius: 12px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsla(var(--heroui-primary), 0.9);
    border-radius: 12px;
}

/* 加载动画效果 - 增强暗色模式下的动画可见性 - Dracula */
.dark [class*="loading"],
[data-theme="dark"] [class*="loading"] {
    filter: drop-shadow(0 0 6px hsla(var(--heroui-primary), 0.7)) !important;
}

.light [class*="loading"],
[data-theme="light"] [class*="loading"] {
    filter: drop-shadow(0 0 4px hsla(var(--heroui-primary), 0.5)) !important;
}

/* 输入框美化 - 优化暗色模式 - Dracula */
.dark input, .dark textarea, .dark select,
[data-theme="dark"] input, [data-theme="dark"] textarea, [data-theme="dark"] select,
.light input, .light textarea, .light select,
[data-theme="light"] input, [data-theme="light"] textarea, [data-theme="light"] select {
    backdrop-filter: blur(var(--glass-blur)) !important;
    -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
    border: var(--glass-border) !important;
    background-color: inherit !important; /* 继承背景色 */
    color: inherit !important; /* 继承文本颜色 */
    transition: all 0.3s ease !important;
    border-radius: 12px !important;
    box-shadow: var(--neumorphic-shadow-dark) !important; /* 应用拟态阴影 */
    padding: 0.5rem 0.75rem !important; /* 增加内边距 */
}


.dark input:focus, .dark textarea:focus, .dark select:focus,
[data-theme="dark"] input:focus, [data-theme="dark"] textarea:focus, [data-theme="dark"] select:focus,
.light input:focus, .light textarea:focus, .light select:focus,
[data-theme="light"] input:focus, [data-theme="light"] textarea:focus, [data-theme="light"] select:focus {
    box-shadow: var(--neumorphic-shadow-dark-focus) !important;
    border-color: var(--heroui-primary) !important; /* 焦点时主题色边框 */
    border-radius: 12px !important;
    outline: none !important;
}

/* 开关样式 - 明确区分开关状态 - Dracula */
/* (保持之前的开关样式代码 -  确保开关颜色正确应用) */
/*  ===  开关样式 - 高优先级 - 确保颜色生效 - 基于用户模板结构 - 移除开关外圈框线 === */
/* 为各种可能的开关选择器添加样式 - 基于用户模板 */
.dark [role="switch"], .dark .switch, .dark [type="checkbox"], .dark [class*="switch"], .dark [class*="toggle"], [data-theme="dark"] [role="switch"], [data-theme="dark"] .switch, [data-theme="dark"] [type="checkbox"], [data-theme="dark"] [class*="switch"], [data-theme="dark"] [class*="toggle"], .light [role="switch"], .light .switch, .light [type="checkbox"], .light [class*="switch"], .light [class*="toggle"], [data-theme="light"] [role="switch"], [data-theme="light"] .switch, [data-theme="light"] [type="checkbox"], [data-theme="light"] [class*="switch"], [data-theme="light"] [class*="toggle"] {
    transition: all 0.3s ease !important; border-radius: 16px !important; overflow: hidden;
}
.dark [role="switch"][aria-checked="true"], .dark .switch[aria-checked="true"], .dark [type="checkbox"]:checked + *, .dark [class*="switch"][data-state="checked"], .dark [class*="toggle"][data-state="checked"], .dark [class*="switch"][aria-checked="true"], .dark [class*="toggle"][aria-checked="true"], [data-theme="dark"] [role="switch"][aria-checked="true"], [data-theme="dark"] .switch[aria-checked="true"], [data-theme="dark"] [type="checkbox"]:checked + *, [data-theme="dark"] [class*="switch"][data-state="checked"], [data-theme="dark"] [class*="toggle"][data-state="checked"], [data-theme="dark"] [class*="switch"][aria-checked="true"], [data-theme="dark"] [class*="toggle"][aria-checked="true"] {
    background-color: var(--switch-on-bg) !important; border-color: transparent !important; box-shadow: 0 0 8px rgba(186, 104, 200, 0.6) !important; border-radius: 16px !important;
}
.dark [role="switch"][aria-checked="false"], .dark .switch[aria-checked="false"], .dark [type="checkbox"]:not(:checked) + *, .dark [class*="switch"]:not([data-state="checked"]), .dark [class*="toggle"]:not([data-state="checked"]), .dark [class*="switch"][aria-checked="false"], .dark [class*="toggle"][aria-checked="false"], [data-theme="dark"] [role="switch"][aria-checked="false"], [data-theme="dark"] .switch[aria-checked="false"], [data-theme="dark"] [type="checkbox"]:not(:checked) + *, [data-theme="dark"] [class*="switch"]:not([data-state="checked"]), [data-theme="dark"] [class*="toggle"]:not([data-state="checked"]), [data-theme="dark"] [class*="switch"][aria-checked="false"], [data-theme="dark"] [class*="toggle"][aria-checked="false"] {
    background-color: var(--switch-off-bg) !important; border-color: transparent !important; border-radius: 16px !important;
}
.dark [role="switch"]::before, .dark .switch::before, .dark [class*="switch"]::before, .dark [class*="toggle"]::before, .dark [role="switch"] > span, .dark .switch > span, .dark [class*="switch"] > span, .dark [class*="toggle"] > span, [data-theme="dark"] [role="switch"]::before, [data-theme="dark"] .switch::before, [data-theme="dark"] [class*="switch"]::before, [data-theme="dark"] [class*="toggle"]::before, [data-theme="dark"] [role="switch"] > span, [data-theme="dark"] .switch > span, [data-theme="dark"] [class*="switch"] > span, [data-theme="dark"] [class*="toggle"] > span {
    background-color: var(--switch-off-thumb) !important; border-radius: 50% !important;
}
.light [role="switch"][aria-checked="true"]::before, .light .switch[aria-checked="true"]::before, .light [class*="switch"][data-state="checked"]::before, .light [class*="toggle"][data-state="checked"]::before, .light [role="switch"][aria-checked="true"] > span, .light .switch[aria-checked="true"] > span, .light [class*="switch"][data-state="checked"] > span, .light [class*="toggle"][data-state="checked"] > span, .light [class*="switch"][aria-checked="true"]::before, .light [class*="toggle"][aria-checked="true"]::before, .light [class*="switch"][aria-checked="true"] > span, .light [class*="toggle"][aria-checked="true"] > span, [data-theme="light"] [role="switch"][aria-checked="true"]::before, [data-theme="light"] .switch[aria-checked="true"]::before, [data-theme="light"] [class*="switch"][data-state="checked"]::before, [data-theme="light"] [class*="toggle"][data-state="checked"]::before, [data-theme="light"] [role="switch"][aria-checked="true"] > span, [data-theme="light"] .switch[aria-checked="true"] > span, [data-theme="light"] [class*="switch"][data-state="checked"] > span, [data-theme="light"] [class*="toggle"][data-state="checked"] > span, [data-theme="light"] [class*="switch"][aria-checked="true"]::before, [data-theme="light"] [class*="toggle"][aria-checked="true"]::before, [data-theme="light"] [class*="switch"][aria-checked="true"] > span, [data-theme="light"] [class*="toggle"][aria-checked="true"] > span {
    background-color: var(--switch-on-thumb) !important; border-radius: 50% !important;
}
.light [role="switch"][aria-checked="true"], .light .switch[aria-checked="true"], .light [type="checkbox"]:checked + *, .light [class*="switch"][data-state="checked"], .light [class*="toggle"][data-state="checked"], .light [class*="switch"][aria-checked="true"], .light [class*="toggle"][aria-checked="true"], [data-theme="light"] [role="switch"][aria-checked="true"], [data-theme="light"] .switch[aria-checked="true"], [data-theme="light"] [type="checkbox"]:checked + *, [data-theme="light"] [class*="switch"][data-state="checked"], [data-theme="light"] [class*="toggle"][data-state="checked"], [data-theme="light"] [class*="switch"][aria-checked="true"], [data-theme="light"] [class*="toggle"][aria-checked="true"] {
    background-color: var(--switch-on-bg) !important; border-color: transparent !important; border-radius: 16px !important;
}
.light [role="switch"][aria-checked="false"], .light .switch[aria-checked="false"], .light [type="checkbox"]:not(:checked) + *, .light [class*="switch"]:not([data-state="checked"]), .light [class*="toggle"]:not([data-state="checked"]), .light [class*="switch"][aria-checked="false"], .light [class*="toggle"][aria-checked="false"], [data-theme="light"] [role="switch"][aria-checked="false"], [data-theme="light"] .switch[aria-checked="false"], [data-theme="light"] [type="checkbox"]:not(:checked) + *, [data-theme="light"] [class*="switch"]:not([data-state="checked"]), [data-theme="light"] [class*="toggle"]:not([data-state="checked"]), [data-theme="light"] [class*="switch"][aria-checked="false"], [data-theme="light"] [class*="toggle"][aria-checked="false"] {
    background-color: var(--switch-off-bg) !important; border-color: transparent !important; border-radius: 16px !important;
}
/*  ===  开关样式代码结束 - 基于用户模板结构 - 移除开关外圈框线！ === */