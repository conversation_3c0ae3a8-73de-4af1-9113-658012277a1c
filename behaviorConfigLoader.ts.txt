// src/rpgSystem/utils/behaviorConfigLoader.ts

import { BehaviorMappingRule } from './dialogueBehaviorAnalyzer';
import { CoreAttributes_RPG } from '../types_rpg';

/**
 * YAML配置文件结构接口
 */
export interface BehaviorConfig {
  behaviors: {
    behavior: string;
    description: string;
    keywords: string[];
    effects: Partial<Record<keyof CoreAttributes_RPG, number>>;
    conditions?: string[];
  }[];
  metadata?: {
    version: string;
    description: string;
    author: string;
    created_at: string;
  };
  settings?: {
    max_attribute_change: number;
    debug_logging: boolean;
    enable_conditions: boolean;
  };
}

/**
 * 行为配置加载器
 * MVP版本：使用硬编码配置，后续可扩展为从YAML文件加载
 */
export class BehaviorConfigLoader {
  private static instance: BehaviorConfigLoader;
  private config: BehaviorConfig | null = null;

  private constructor() {}

  public static getInstance(): BehaviorConfigLoader {
    if (!BehaviorConfigLoader.instance) {
      BehaviorConfigLoader.instance = new BehaviorConfigLoader();
    }
    return BehaviorConfigLoader.instance;
  }

  /**
   * 加载行为配置
   * MVP版本：使用硬编码配置
   */
  public async loadConfig(): Promise<BehaviorConfig> {
    if (this.config) {
      return this.config;
    }

    console.log('📁 [BehaviorConfigLoader] 加载行为配置...');

    // MVP版本：硬编码配置，避免YAML解析的复杂性
    this.config = {
      behaviors: [
        {
          behavior: 'Brave',
          description: '表现出勇气和决心，面对困难不退缩',
          keywords: ['brave', 'courage', 'face it', 'not afraid', 'handle this', 'I will', '勇敢', '不怕', '面对', '我来'],
          effects: {
            strength: 1,
            sanity: 1
          }
        },
        {
          behavior: 'Cunning',
          description: '表现出狡猾和欺骗，使用智谋解决问题',
          keywords: ['clever', 'trick', 'deceive', 'lie', 'cunning', 'outsmart', '聪明', '欺骗', '谎言', '狡猾', '骗'],
          effects: {
            intelligence: 1,
            charisma: -1
          }
        },
        {
          behavior: 'Compassionate',
          description: '表现出同情和关怀，关心他人福祉',
          keywords: ['help', 'care', 'sorry', 'understand', 'compassion', 'sympathy', '帮助', '关心', '抱歉', '理解', '同情'],
          effects: {
            charisma: 1,
            sanity: 1
          }
        },
        {
          behavior: 'Aggressive',
          description: '表现出攻击性和愤怒，倾向于使用武力',
          keywords: ['fight', 'attack', 'kill', 'destroy', 'angry', 'rage', '战斗', '攻击', '杀死', '摧毁', '愤怒'],
          effects: {
            strength: 1,
            charisma: -1,
            sanity: -1
          }
        },
        {
          behavior: 'Wise',
          description: '表现出智慧和深思熟虑，善于分析问题',
          keywords: ['think', 'analyze', 'consider', 'wisdom', 'logical', 'reason', '思考', '分析', '考虑', '智慧', '逻辑'],
          effects: {
            intelligence: 1,
            sanity: 1
          }
        },
        {
          behavior: 'Inquisitive',
          description: '表现出好奇心和探索欲，主动询问和调查',
          keywords: ['ask', 'question', 'inquire', 'investigate', 'explore', 'wonder', 'curious', '询问', '问', '调查', '探索', '好奇', '疑问', '了解'],
          effects: {
            intelligence: 1,
            charisma: 1
          }
        },
        {
          behavior: 'Social',
          description: '表现出社交能力，善于与他人交流沟通',
          keywords: ['talk', 'communicate', 'discuss', 'chat', 'conversation', 'dialogue', '交流', '沟通', '对话', '聊天', '讨论', '交谈'],
          effects: {
            charisma: 2
          }
        },
        {
          behavior: 'Decisive',
          description: '表现出决断力，能够快速做出决定',
          keywords: ['decide', 'choose', 'select', 'determine', 'resolve', '决定', '选择', '确定', '解决', '判断'],
          effects: {
            strength: 1,
            intelligence: 1
          }
        }
      ],
      metadata: {
        version: '1.0.0',
        description: 'MVP版本的行为映射配置',
        author: 'Claude 4.0 Sonnet',
        created_at: '2025-01-13'
      },
      settings: {
        max_attribute_change: 3,
        debug_logging: true,
        enable_conditions: false
      }
    };

    console.log(`✅ [BehaviorConfigLoader] 配置加载完成，包含 ${this.config.behaviors.length} 个行为规则`);
    return this.config;
  }

  /**
   * 将配置转换为BehaviorMappingRule数组
   */
  public async getBehaviorRules(): Promise<BehaviorMappingRule[]> {
    const config = await this.loadConfig();
    
    return config.behaviors.map(behavior => ({
      behavior: behavior.behavior,
      keywords: behavior.keywords,
      effects: behavior.effects,
      description: behavior.description
    }));
  }

  /**
   * 获取配置设置
   */
  public async getSettings() {
    const config = await this.loadConfig();
    return config.settings || {
      max_attribute_change: 3,
      debug_logging: true,
      enable_conditions: false
    };
  }

  /**
   * 重新加载配置
   */
  public async reloadConfig(): Promise<BehaviorConfig> {
    this.config = null;
    return this.loadConfig();
  }
}

/**
 * 全局配置加载器实例
 */
export const behaviorConfigLoader = BehaviorConfigLoader.getInstance();
