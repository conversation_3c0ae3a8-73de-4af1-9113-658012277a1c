### 🤖 Assistant



To create a text-based game that is compatible with all importable SillyTavern files (worldbooks, character cards, presets, etc.), you should consider the following features:

1. **Compatibility with SillyTavern**: Ensure the game can directly import files like worldbooks, character cards, and other settings from SillyTavern [1][2]. Use JSON format for settings as it is clear, but mindful of the potential token overhead [2].

2. **Interface Design**: 
   - The user interface should have a visually appealing anime-style design.
   - It should be entirely in Chinese to cater to the local audience [2].

3. **Dynamic Visuals**:
   - Incorporate images that dynamically change according to the current scene in the game.

4. **Side Display with Metrics**:
   - Include a sidebar that shows relevant metrics in line with the storyline.
   - Use appropriate emojis to enhance visibility and readability [7].

5. **Dialogue System**:
   - After each dialogue, provide players with four options to choose from or allow them to input their own custom output.
   - Ensure this system is smooth and compatible with SillyTavern's interaction capabilities [9].

By focusing on these elements, you create an immersive and user-friendly text-based game that fully leverages SillyTavern's existing ecosystem and capabilities.

[1] [https://github.com/SillyTavern/SillyTavern](SillyTavern/SillyTavern: LLM Frontend for Power Users. - GitHub)

[2] [https://www.hqshi.cn/info/knowledge/sillytavern](酒馆 (sillytavern)的简单攻略 | 空桑)

[3] [https://sillytavern.wiki/for-contributors/writing-extensions/](编写扩展 | SillyTavern傻酒馆中文文档)

[4] [https://www.hqshi.cn/info/knowledge/sillytavern-guide](酒馆 (sillytavern)的使用说明 | 空桑)

[5] [https://pt4300.github.io/Sillytavern-docker-tutorial/full_docker/](SillyTavern 酒馆完整部署教程 - 保姆级安装指南 - SERN酒馆妙妙屋)

[6] [https://www.rainlain.com/index.php/2024/11/19/2645/](WINNAS轻松搭：SillyTavern（酒馆）从入门到精通 - 梦雨玲音)

[7] [http://sillytavern.wiki/usage/](使用指南 | SillyTavern傻酒馆中文文档)

[8] [https://st-docs.role.fun/](什么是 SillyTavern? | SillyTavern 中文文档)

[9] [https://sillytavern.wiki/](什么是 SillyTavern？ | SillyTavern傻酒馆中文文档)

[10] [https://www.erocraft.com/silly-tavern/](艾萝工坊 Silly Tavern (AI酒馆) 使用帮助 - 艾萝工坊)

[11] [https://docs.eigeen.cc/for-contributors/writing-extensions/](Writing Extensions | SillyTavern 中文文档)

[12] [http://sillytavern.wiki/installation/](安装指南 | SillyTavern傻酒馆中文文档)

[13] [https://www.meoai.net/sillytavern-ai.html](SillyTavern深度指南：AI酒馆+DeepSeek本地部署，连接各种本地和云端AI模型 | MeoAI)

[14] [https://st-docs.role.fun/usage/api-connections/](API 连接 | SillyTavern 中文文档)

[15] [https://github.com/hypier/SillyTavern_doc_cn](GitHub - hypier/SillyTavern_doc_cn: SillyTavern 中文文档)

[16] [https://sillytavernai.com/how-to-install-sillytavern/](How to Install SillyTavern - SillyTavern)

[17] [https://sechub.in/view/2929231]([分享发现] 一个比较小众的 Ai 应用——角色扮演《云酒馆》)