rule-providers:
  private:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.yaml
    path: ./ruleset/private.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.yaml
    path: ./ruleset/cn_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  telegram_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.yaml
    path: ./ruleset/telegram_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  google_domain:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.yaml
    path: ./ruleset/google_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  geolocation-!cn:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.yaml
    path: ./ruleset/geolocation-!cn.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.yaml
    path: ./ruleset/cn_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  telegram_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.yaml
    path: ./ruleset/telegram_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  google_ip:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.yaml
    path: ./ruleset/google_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  bing:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Bing/Bing.yaml
    path: ./ruleset/bing.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  copilot:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Copilot/Copilot.yaml
    path: ./ruleset/copilot.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  claude:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Claude/Claude.yaml
    path: ./ruleset/claude.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  bard:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/BardAI/BardAI.yaml
    path: ./ruleset/bard.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  openai:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/OpenAI/OpenAI.yaml
    path: ./ruleset/openai.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  steam:
    url: https://mirror.ghproxy.com/https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Steam/Steam.yaml
    path: ./ruleset/steam.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http

proxy-groups:
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Static.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    name: PROXY
    type: select
    proxies:
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Urltest.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire||Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    name: AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/OpenAI.png
    name: AIGC
    type: select
    proxies:
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Telegram.png
    name: Telegram
    type: select
    proxies:
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Google.png
    name: Google
    type: select
    proxies:
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/HK.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    filter: (?i)香港|Hong Kong|HK|🇭🇰
    name: HK AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/SG.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    filter: (?i)新加坡|Singapore|🇸🇬
    name: SG AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/JP.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    filter: (?i)日本|Japan|🇯🇵
    name: JP AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/US.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    filter: (?i)美国|USA|🇺🇸
    name: US AUTO
    type: url-test
    interval: 300
  - icon: https://mirror.ghproxy.com/https://raw.githubusercontent.com/Orz-3/mini/master/Color/Global.png
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium频道|订阅|ISP|流量|到期|重置|到期|优惠|套餐|传家宝|剩余|更新|客户端|免费|导航|通知|白嫖|台湾|公益|❗سرور متصله 💜|抽奖|官网|实验|发布|活动|促销|限时|特价
    proxies:
      - PROXY
      - AUTO
      - AIGC
      - Telegram
      - Google
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
    name: GLOBAL
    type: select

rules:
  - "RULE-SET,private,DIRECT"
  - "RULE-SET,bing,AIGC"
  - "RULE-SET,copilot,AIGC"
  - "RULE-SET,bard,AIGC"
  - "RULE-SET,openai,AIGC"
  - "RULE-SET,claude,AIGC"
  - "RULE-SET,steam,PROXY"
  - "RULE-SET,telegram_domain,Telegram"
  - "RULE-SET,telegram_ip,Telegram"
  - "RULE-SET,google_domain,Google"
  - "RULE-SET,google_ip,Google"
  - "RULE-SET,geolocation-!cn,PROXY"
  - "RULE-SET,cn_domain,DIRECT"
  - "RULE-SET,cn_ip,DIRECT"
  - "MATCH,PROXY"
  - "PROCESS-NAME,NeteaseMusic,DIRECT"
  - "PROCESS-NAME,steam,DIRECT"
  - "PROCESS-NAME,QQ.exe,DIRECT"
  - "PROCESS-NAME,WeChat.exe,DIRECT"
  - "PROCESS-NAME,yida.exe,PROXY"
  - "PROCESS-NAME,MuMuPlayer.exe,PROXY"
  - "IP-CIDR,10.0.0.0/8,DIRECT"
  - "IP-CIDR,**********/12,DIRECT"
  - "IP-CIDR,***********/16,DIRECT"
  - "IP-CIDR,**********/10,DIRECT"
  - "IP-CIDR,*********/8,DIRECT"
  - "PROCESS-NAME,QQ.exe,DIRECT"
  - "DOMAIN,linux.do,PROXY"
  - "DOMAIN,127.0.0.1:18080,PROXY"
  - "DOMAIN,127.0.0.1:8080,PROXY"
  - "DOMAIN-SUFFIX,linux.do,PROXY"
  - "PROCESS-NAME,docker,PROXY"
  - "PROCESS-NAME,dockerd,PROXY"
  - "PROCESS-NAME,Docker Desktop.exe,PROXY"
  - "MATCH,其他"
